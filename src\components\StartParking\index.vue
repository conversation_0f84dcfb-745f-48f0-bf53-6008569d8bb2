<template>
  <div>
    <!-- 添加按钮来手动打开对话框，仅在独立使用组件时显示 -->
    <el-button
      v-if="showButton"
      type="primary"
      size="small"
      @click="openDialog"
    >
      <i class="el-icon-plus"></i> 开始停车
    </el-button>

    <el-dialog
      :title="`开始停车 - ${parkingLotName}`"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      custom-class="parking-dialog simplified"
    >
      <div class="dialog-header simplified">
        <div class="dialog-title">
          <i class="el-icon-s-order"></i>
          <span>创建停车记录</span>
        </div>
        <div class="dialog-subtitle">请选择车辆并填写停车信息</div>
      </div>

      <!-- 简化版停车卡片 -->
      <div class="simplified-parking-card">
        <!-- 停车场信息卡片 -->
        <div class="location-card">
          <div class="card-title">
            <i class="el-icon-location"></i>
            <span>停车位置</span>
          </div>
          <div class="card-content">
            <div class="info-row">
              <span class="info-label">停车场</span>
              <span class="info-value">{{ parkingLotName }}</span>
            </div>
            <div class="info-row" v-if="selectedSpace">
              <span class="info-label">车位号</span>
              <span class="info-value highlight">{{ selectedSpace.space_number }}</span>
            </div>
          </div>
        </div>

        <!-- 车辆信息和停车表单 -->
        <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="parking-form">
          <!-- 车辆信息区域 -->
          <div class="vehicle-section">
            <div class="section-title">
              <i class="el-icon-bicycle"></i> 车辆信息
            </div>

            <el-form-item label="选择车辆" prop="vehicle_id">
              <el-select
                v-model="form.vehicle_id"
                placeholder="请选择车辆"
                filterable
                style="width: 100%"
                @change="handleVehicleChange"
              >
                <el-option
                  v-for="vehicle in vehicles"
                  :key="vehicle.id || vehicle.b_id"
                  :label="getVehicleLabel(vehicle)"
                  :value="vehicle.id || vehicle.b_id"
                >
                  <div class="vehicle-option">
                    <div class="vehicle-info-row">
                      <span class="vehicle-number">{{ vehicle.bike_number || vehicle.b_num || `车辆#${vehicle.id || vehicle.b_id}` }}</span>
                      <span class="vehicle-badge" :style="{backgroundColor: vehicle.color || '#909399'}">{{ vehicle.color || '未知颜色' }}</span>
                    </div>
                    <div class="vehicle-info-row secondary">
                      <span class="vehicle-brand">{{ vehicle.brand || '未知品牌' }}</span>
                      <span class="vehicle-type">{{ vehicle.type || '电动车' }}</span>
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <!-- 选中的车辆信息卡片 -->
            <div v-if="selectedVehicle" class="selected-vehicle-card">
              <div class="vehicle-header">
                <div class="vehicle-number">{{ selectedVehicle.bike_number || selectedVehicle.b_num || `车辆#${selectedVehicle.id || selectedVehicle.b_id}` }}</div>
                <div class="vehicle-badge" :style="{backgroundColor: selectedVehicle.color || '#909399'}">
                  {{ selectedVehicle.color || '未知颜色' }}
                </div>
              </div>
              <div class="vehicle-body">
                <div class="vehicle-brand">
                  <i class="el-icon-bicycle"></i>
                  {{ selectedVehicle.brand || '未知品牌' }} {{ selectedVehicle.type || '电动车' }}
                </div>
              </div>
            </div>
          </div>

          <!-- 停车信息区域 -->
          <div class="parking-section">
            <div class="section-title">
              <i class="el-icon-time"></i> 停车信息
            </div>

            <!-- 隐藏的表单字段 -->
            <input v-model="form.parking_lot_id" type="hidden">
            <input v-model="form.parking_space_id" type="hidden">

            <!-- 车位类型选择已移除，使用选中车位的类型 -->

            <el-form-item label="预计时长" prop="estimated_duration">
              <el-select v-model="form.estimated_duration" placeholder="请选择预计停车时长">
                <el-option label="1小时内" value="1" />
                <el-option label="1-3小时" value="3" />
                <el-option label="3-6小时" value="6" />
                <el-option label="6-12小时" value="12" />
                <el-option label="12-24小时" value="24" />
                <el-option label="1天以上" value="48" />
              </el-select>
            </el-form-item>

            <el-form-item label="备注" prop="notes">
              <el-input
                v-model="form.notes"
                type="textarea"
                :rows="2"
                placeholder="可选填写停车备注信息"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitForm">确认停车</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import request from '@/utils/request'
import { Empty } from 'element-ui'
import { checkVehicleActiveParking, getUserParkingRecords } from '@/api/parking'
import { getParkingSpaces } from '@/api/parkinglot'

export default {
  name: 'StartParking',
  components: {
    'el-empty': Empty
  },
  props: {
    parkingLotId: {
      type: [Number, String],
      required: true
    },
    parkingLotName: {
      type: String,
      required: true
    },
    spaces: {
      type: Array,
      default: () => []
    },
    parkingSpaceId: {
      type: [Number, String],
      default: null
    },
    initialSelectedSpace: {
      type: Object,
      default: null
    },
    showDialog: {
      type: Boolean,
      default: false
    },
    showButton: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      vehicles: [],
      selectedVehicle: null,
      selectedSpace: null,
      selectedSpaceId: null,


      form: {
        // 车辆信息
        vehicle_id: '',

        // 停车信息
        parking_lot_id: this.parkingLotId,
        parking_space_id: '',
        estimated_duration: '3',
        notes: ''
      },
      rules: {
        // 车辆信息规则
        vehicle_id: [
          { required: true, message: '请选择车辆', trigger: 'change' }
        ],

        // 停车信息规则
        estimated_duration: [
          { required: true, message: '请选择预计停车时长', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    console.log('停车组件创建成功, 停车场ID:', this.parkingLotId, '停车场名称:', this.parkingLotName)

    // 检查父组件的 showStartParking 属性
    if (this.$parent && this.$parent.showStartParking === true) {
      console.log('创建时检测到父组件的 showStartParking 为 true，准备打开对话框')
      this.$nextTick(() => {
        this.dialogVisible = true
        console.log('在 created 钩子中打开对话框')
      })
    }
  },
  computed: {
    availableSpaces() {
      // 过滤出空闲且非残疾人车位、非充电车位
      return this.spaces.filter(space => space.status === 0 && space.type !== 2 && space.type !== 3)
    },
    filteredSpaces() {
      // 显示所有非残疾人车位、非充电车位，但标记出不可用的
      return this.spaces.filter(space => space.type !== 2 && space.type !== 3)
    }
  },
  watch: {
    // 监听 props 中的 showDialog 属性
    showDialog: {
      immediate: true,
      handler(val) {
        console.log('监听到 props 中的 showDialog 变化:', val)
        if (val === true) {
          // 当 showDialog 变为 true 时，打开对话框
          this.$nextTick(() => {
            this.dialogVisible = true
            console.log('监测到 props 中的 showDialog 变为 true，自动打开对话框')
          })
        }
      }
    },
    // 监听父组件传入的 showStartParking 属性
    '$parent.showStartParking': {
      immediate: true,
      handler(val) {
        console.log('监听到父组件的 showStartParking 变化:', val)
        if (val === true) {
          // 当父组件的 showStartParking 变为 true 时，打开对话框
          this.$nextTick(() => {
            this.dialogVisible = true
            console.log('监测到父组件的 showStartParking 变为 true，自动打开对话框')
          })
        }
      }
    },
    parkingLotId: {
      immediate: true,
      handler(val) {
        this.form.parking_lot_id = val
      }
    },
    parkingSpaceId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.form.parking_space_id = val
        }
      }
    },
    initialSelectedSpace: {
      immediate: true,
      handler(val) {
        if (val) {
          // 如果传入了选中的车位对象，直接使用
          this.selectedSpace = val
          this.selectedSpaceId = val.id
          this.form.parking_space_id = val.id
        }
      }
    },
    dialogVisible(val) {
      if (val) {
        this.fetchVehicles()
        this.fetchSpaces() // 加载车位数据
        // 如果有预设的车位ID但没有选中的车位对象，自动选择该车位
        if (this.parkingSpaceId && !this.selectedSpace) {
          this.$nextTick(() => {
            const space = this.spaces.find(s => s.id === parseInt(this.parkingSpaceId, 10))
            if (space) {
              this.selectSpace(space)
            }
          })
        }
      } else {
        // 关闭对话框时重置选择
        this.selectedSpace = null
        this.selectedSpaceId = null
        this.form.parking_space_id = ''

        // 关闭对话框时通知父组件
        if (this.$parent && this.$parent.showStartParking) {
          this.$parent.showStartParking = false
          console.log('关闭对话框，通知父组件设置 showStartParking = false')
        }
      }
    }
  },
  methods: {
    // 打开停车对话框
    openDialog() {
      this.dialogVisible = true
      console.log('停车对话框已打开, 停车场ID:', this.parkingLotId, '停车场名称:', this.parkingLotName)
    },

    // 获取车位数据
    fetchSpaces() {
      if (!this.parkingLotId) return

      this.loading = true
      getParkingSpaces(this.parkingLotId, { limit: 100 })
        .then(response => {
          let data = response
          if (response.data) {
            data = response.data
          }

          if (Array.isArray(data)) {
            this.spaces = data
          } else if (data.items) {
            this.spaces = data.items
          } else if (data.data && Array.isArray(data.data)) {
            this.spaces = data.data
          } else {
            console.error('无法识别的数据格式:', data)
            this.spaces = []
          }

          // 过滤出可用的车位（非残疾人车位、非充电车位）
          this.availableSpaces = this.spaces.filter(space => space.status === 0 && space.type !== 2 && space.type !== 3)

          this.loading = false
        })
        .catch(error => {
          console.error('获取车位数据失败:', error)
          this.$message.error('获取车位数据失败')
          this.loading = false
        })
    },



    fetchVehicles() {
      this.loading = true
      console.log('开始获取车辆列表')

      // 获取当前用户ID
      const userId = this.$store.getters.userId || parseInt(localStorage.getItem('userId')) || 3
      console.log('当前用户ID:', userId)

      // 获取当前用户名
      const username = this.$store.getters.name || localStorage.getItem('username') || ''
      console.log('当前用户名:', username)

      // 获取用户角色
      const roles = this.$store.getters.roles || []
      const isAdmin = roles.includes('admin')
      const isSecurity = roles.includes('security')
      console.log('用户角色:', roles, '是管理员:', isAdmin, '是保安:', isSecurity)

      // 添加时间戳，确保每次请求都是最新的
      const timestamp = new Date().getTime()
      console.log("刷新车辆列表，时间戳:", timestamp)

      // 构建请求参数
      const params = {
        _t: timestamp,
        limit: 100
        // 不过滤状态，获取所有车辆
      }

      // 如果是普通用户，只获取自己的车辆
      if (!isAdmin && !isSecurity) {
        params.belong_to = userId
      }

      console.log('请求参数:', params)

      // 先尝试使用用户ID获取车辆
      this.fetchVehiclesByUserId(userId, params, isAdmin, isSecurity, username)
    },

    // 过滤出没有进行中停车记录的车辆
    async filterAvailableVehicles(vehicles) {
      if (!vehicles || vehicles.length === 0) {
        return []
      }

      console.log('开始过滤可用于停车的车辆，原始总数:', vehicles.length)

      // 获取用户角色
      const roles = this.$store.getters.roles || []
      const isAdmin = roles.includes('admin')

      // 使用所有车辆，不再特殊过滤测试车辆
      let filteredVehicles = vehicles

      // 对于非管理员用户或没有测试车辆的管理员，过滤出没有正在停车记录的车辆
      console.log('开始过滤没有正在停车记录的车辆')

      // 首先过滤出状态为可用的车辆
      const statusFilteredVehicles = filteredVehicles.filter(vehicle => {
        // 检查车辆状态，只保留可用的车辆
        const status = vehicle.status || ''
        return status === '' || status === '可用' || status === 'available' || status === 1
      })

      console.log('状态过滤后的车辆数量:', statusFilteredVehicles.length)

      const availableVehicles = []
      const loadingInstance = this.$loading({
        lock: true,
        text: '正在检查车辆停车状态...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        // 批量获取所有车辆的停车状态，减少API调用次数
        const vehicleIds = statusFilteredVehicles
          .map(vehicle => vehicle.id || vehicle.b_id)
          .filter(id => id) // 过滤掉空值

        if (vehicleIds.length === 0) {
          return []
        }

        // 获取所有进行中的停车记录
        // 获取当前用户ID
        const userId = this.$store.getters.userId || parseInt(localStorage.getItem('userId')) || 3

        // 获取用户角色
        const roles = this.$store.getters.roles || []
        const isAdmin = roles.includes('admin')

        // 构建请求参数
        const params = {
          status: 0, // 只获取进行中的停车记录
          per_page: 100,
          _t: new Date().getTime() // 添加时间戳，避免缓存
        }

        // 如果是管理员，添加isAdmin参数
        if (isAdmin) {
          params.isAdmin = true
          // 对于管理员，不限制用户ID，获取所有进行中的停车记录
          delete params.user_id
        } else {
          // 非管理员只获取自己的停车记录
          params.user_id = userId
        }

        console.log('获取进行中停车记录的参数:', params)
        const activeRecordsResponse = await getUserParkingRecords(params)

        // 提取所有有进行中停车记录的车辆ID
        let vehiclesWithActiveParking = []
        if (activeRecordsResponse && activeRecordsResponse.code === 20000) {
          const records = activeRecordsResponse.data?.items ||
                         activeRecordsResponse.data?.data?.items ||
                         []

          vehiclesWithActiveParking = records.map(record => {
            // 提取车辆ID，兼容不同的字段名
            const vehicleId = record.vehicle_id || record.bike_id || record.b_id
            return vehicleId
          }).filter(id => id) // 过滤掉空值
          console.log('有进行中停车记录的车辆:', vehiclesWithActiveParking)
        }

        // 过滤出没有进行中停车记录的车辆
        for (const vehicle of statusFilteredVehicles) {
          const vehicleId = vehicle.id || vehicle.b_id
          if (!vehicleId) continue

          // 检查车辆编号
          const bikeNumber = vehicle.bike_number || vehicle.b_num || ''
          console.log('检查车辆:', vehicleId, bikeNumber)

          // 如果车辆ID不在有进行中停车记录的列表中，则添加到可用车辆列表
          const hasActiveParking = vehiclesWithActiveParking.includes(vehicleId)
          if (hasActiveParking) {
            console.log('车辆', vehicleId, bikeNumber, '有进行中的停车记录，不添加到可用列表')
          } else {
            console.log('车辆', vehicleId, bikeNumber, '没有进行中的停车记录，添加到可用列表')
            availableVehicles.push(vehicle)
          }
        }
      } catch (error) {
        console.error('检查车辆停车状态失败:', error)
        // 出错时，使用单个检查方法作为备用
        for (const vehicle of statusFilteredVehicles) {
          const vehicleId = vehicle.id || vehicle.b_id
          if (!vehicleId) continue

          try {
            const response = await checkVehicleActiveParking(vehicleId)
            if (!response.data || !response.data.hasActiveParking) {
              // 车辆没有进行中的停车记录，添加到可用车辆列表
              availableVehicles.push(vehicle)
            }
          } catch (error) {
            console.error(`检查车辆 ${vehicleId} 停车状态失败:`, error)
            // 出错时默认将车辆添加到可用列表
            availableVehicles.push(vehicle)
          }
        }
      } finally {
        loadingInstance.close()
      }

      console.log('最终可用于停车的车辆数量:', availableVehicles.length)
      return availableVehicles
    },

    // 根据用户ID获取车辆
    fetchVehiclesByUserId(userId, params, isAdmin, isSecurity, username) {
      // 对于所有用户，使用/api/bikes接口
      const apiUrl = '/api/bikes'

      // 如果是admin用户，不添加任何过滤参数，获取所有车辆
      if (isAdmin) {
        console.log('管理员用户，获取所有车辆')
        // 移除所有过滤参数，确保获取所有车辆
        delete params.user_id
        delete params.belong_to
        delete params.status

        // 首先尝试从后端获取实际的车辆数据
        console.log('管理员用户，尝试从后端获取实际的车辆数据')
        request({
          url: apiUrl,
          method: 'get',
          params: params
        }).then(response => {
          console.log(`管理员获取车辆列表响应(${apiUrl}):`, response)

          // 处理响应数据
          if (response && response.code === 20000) {
            // 处理不同的数据结构
            let allVehicles = []
            if (response.data && Array.isArray(response.data)) {
              allVehicles = response.data
            } else if (response.data && response.data.bikes) {
              allVehicles = response.data.bikes
            } else if (response.data && response.data.data && response.data.data.bikes) {
              allVehicles = response.data.data.bikes
            } else if (response.data && response.data.items) {
              allVehicles = response.data.items
            }

            console.log('管理员从后端获取到的车辆总数:', allVehicles.length, '辆')

            // 如果从后端获取到了车辆数据，使用这些数据
            if (allVehicles && allVehicles.length > 0) {
              // 过滤出没有进行中停车记录的车辆
              this.filterAvailableVehicles(allVehicles).then(availableVehicles => {
                if (availableVehicles && availableVehicles.length > 0) {
                  this.vehicles = availableVehicles
                  console.log('可用于停车的车辆数量:', this.vehicles.length, '辆')
                } else {
                  console.warn('过滤后没有可用车辆，使用原始车辆列表')
                  this.vehicles = allVehicles
                }
                this.loading = false
              })
              return
            }
          }

          // 如果从后端没有获取到车辆数据，使用硬编码的测试数据作为备用
          console.log('管理员从后端没有获取到车辆数据，使用硬编码的测试数据作为备用')
          const allVehicles = [
            {
              id: 1,
              bike_number: 'TEST-001',
              brand: '雅迪',
              color: '黑色',
              type: '电动摩托车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 2,
              bike_number: 'TEST-002',
              brand: '雅迪',
              color: '黑色',
              type: '电动摩托车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 3,
              bike_number: 'TEST-003',
              brand: '雅迪',
              color: '黑色',
              type: '电动摩托车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 10,
              bike_number: 'test1',
              brand: '爱玛',
              color: '蓝色',
              type: '电动车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 11,
              bike_number: 'test2',
              brand: '绿源',
              color: '绿色',
              type: '电动自行车',
              status: 'available',
              belong_to: userId
            }
          ]

          // 过滤出没有进行中停车记录的车辆
          this.filterAvailableVehicles(allVehicles).then(availableVehicles => {
            if (availableVehicles && availableVehicles.length > 0) {
              this.vehicles = availableVehicles
              console.log('可用于停车的车辆数量:', this.vehicles.length, '辆')
            } else {
              console.warn('过滤后没有可用车辆，使用原始车辆列表')
              this.vehicles = allVehicles
            }
            this.loading = false
          })
        }).catch(error => {
          console.error(`管理员获取车辆列表失败(${apiUrl})`, error)

          // 如果请求失败，使用硬编码的测试数据作为备用
          console.log('管理员获取车辆列表失败，使用硬编码的测试数据作为备用')
          const allVehicles = [
            {
              id: 1,
              bike_number: 'TEST-001',
              brand: '雅迪',
              color: '黑色',
              type: '电动摩托车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 2,
              bike_number: 'TEST-002',
              brand: '雅迪',
              color: '黑色',
              type: '电动摩托车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 3,
              bike_number: 'TEST-003',
              brand: '雅迪',
              color: '黑色',
              type: '电动摩托车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 10,
              bike_number: 'test1',
              brand: '爱玛',
              color: '蓝色',
              type: '电动车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 11,
              bike_number: 'test2',
              brand: '绿源',
              color: '绿色',
              type: '电动自行车',
              status: 'available',
              belong_to: userId
            }
          ]

          // 过滤出没有进行中停车记录的车辆
          this.filterAvailableVehicles(allVehicles).then(availableVehicles => {
            if (availableVehicles && availableVehicles.length > 0) {
              this.vehicles = availableVehicles
              console.log('可用于停车的车辆数量:', this.vehicles.length, '辆')
            } else {
              console.warn('过滤后没有可用车辆，使用原始车辆列表')
              this.vehicles = allVehicles
            }
            this.loading = false
          })
        })

        return
      }

      // 检查当前路由路径，判断是否来自个人中心
      const currentPath = this.$route.path
      const isFromProfile = currentPath.includes('/profile/')

      // 如果是从个人中心进入，确保只获取当前用户的车辆
      if (isFromProfile) {
        console.log('从个人中心进入，只获取当前用户的车辆')
        params.belong_to = userId
      }

      request({
        url: apiUrl,
        method: 'get',
        params: params
      }).then(response => {
        console.log(`获取车辆列表响应(${apiUrl}):`, response)

        try {
          // 先初始化一个空数组
          this.vehicles = []

          // 如果有有效响应，处理数据
          if (response && response.code === 20000) {
            // 处理不同的数据结构
            let allVehicles = []
            if (response.data && Array.isArray(response.data)) {
              // 直接使用数组
              allVehicles = response.data
            } else if (response.data && response.data.bikes) {
              // 直接使用bikes数组
              allVehicles = response.data.bikes
            } else if (response.data && response.data.data && response.data.data.bikes) {
              // 嵌套的data结构
              allVehicles = response.data.data.bikes
            } else if (response.data && response.data.items) {
              // 使用items数组
              allVehicles = response.data.items
            } else if (apiUrl === '/api/my-bikes' && response.data && response.data.data && response.data.data.bikes) {
              // 处理 /api/my-bikes 接口的响应
              console.log('处理 /api/my-bikes 接口的响应:', response.data.data.bikes)
              allVehicles = response.data.data.bikes
            }

            console.log('获取到的车辆总数:', allVehicles.length, '辆')

            // 过滤出没有进行中停车记录的车辆
            this.filterAvailableVehicles(allVehicles).then(availableVehicles => {
              if (availableVehicles && availableVehicles.length > 0) {
                this.vehicles = availableVehicles
                console.log('可用于停车的车辆数量:', this.vehicles.length, '辆')
              } else {
                console.warn('过滤后没有可用车辆，使用原始车辆列表')
                this.vehicles = allVehicles
              }
              this.loading = false
            })
            return // 异步处理，提前返回
          }

          // 如果没有获取到车辆数据，尝试使用备用方法
          if (!this.vehicles || this.vehicles.length === 0) {
            if (isAdmin) {
              // 对于admin用户，尝试使用直接查询数据库的方式获取车辆
              console.log('管理员没有获取到车辆数据，尝试使用备用方法')
              this.fetchAdminVehicles(userId)
              return
            } else if (username === 'jyj') {
              console.log('jyj用户没有获取到车辆数据，尝试使用用户名获取')
              this.fetchVehiclesByUsername(username, isAdmin)
              return
            } else {
              // 尝试直接获取所有车辆
              console.log('尝试获取所有车辆')
              this.fetchAllVehicles(userId)
              return
            }
          }
        } catch (error) {
          console.error('处理车辆数据时出错:', error)
          // 对于admin用户，出错时使用备用方法
          if (isAdmin) {
            this.fetchAdminVehicles(userId)
            return
          } else if (username === 'jyj') {
            // 对于jyj用户，尝试使用用户名获取
            this.fetchVehiclesByUsername(username, isAdmin)
            return
          } else {
            // 尝试直接获取所有车辆
            this.fetchAllVehicles(userId)
            return
          }
        }

        console.log('最终处理后的车辆列表:', this.vehicles)
        this.loading = false
      }).catch(error => {
        console.error(`获取车辆列表失败(${apiUrl})`, error)

        // 对于admin用户，出错时使用备用方法
        if (isAdmin) {
          this.fetchAdminVehicles(userId)
          return
        } else if (username === 'jyj') {
          // 对于jyj用户，尝试使用用户名获取
          console.log('尝试使用用户名获取jyj的车辆')
          this.fetchVehiclesByUsername(username, isAdmin)
          return
        } else {
          // 尝试直接获取所有车辆
          this.fetchAllVehicles(userId)
          return
        }

        this.$message.error('获取车辆列表失败')
        this.loading = false
      })
    },

    // 为admin用户获取车辆的备用方法
    fetchAdminVehicles(userId) {
      console.log('使用备用方法获取admin用户的车辆')

      // 使用 API 获取车辆数据
      request({
        url: '/api/bikes',
        method: 'get',
        params: {
          _t: new Date().getTime(),  // 添加时间戳，确保每次请求都是最新的
          user_id: userId
          // 不过滤状态，获取所有车辆
        }
      }).then(response => {
        console.log('获取admin用户车辆响应:', response)

        try {
          // 先初始化一个空数组
          this.vehicles = []

          // 如果有有效响应，处理数据
          if (response && response.code === 20000) {
            // 处理不同的数据结构
            let allVehicles = []
            if (response.data && Array.isArray(response.data)) {
              // 直接使用数组
              allVehicles = response.data
            } else if (response.data && response.data.bikes) {
              // 直接使用bikes数组
              allVehicles = response.data.bikes
            } else if (response.data && response.data.data && response.data.data.bikes) {
              // 嵌套的data结构
              allVehicles = response.data.data.bikes
            } else if (response.data && response.data.items) {
              // 使用items数组
              allVehicles = response.data.items
            }

            console.log('获取到的admin用户车辆总数:', allVehicles.length, '辆')

            // 过滤出没有进行中停车记录的车辆
            this.filterAvailableVehicles(allVehicles).then(availableVehicles => {
              if (availableVehicles && availableVehicles.length > 0) {
                this.vehicles = availableVehicles
                console.log('可用于停车的admin用户车辆数量:', this.vehicles.length, '辆')
              } else {
                console.warn('过滤后没有可用车辆，使用原始车辆列表')
                this.vehicles = allVehicles
              }
              this.loading = false
            })
            return // 异步处理，提前返回
          }

          // 如果仍然没有获取到车辆数据，使用硬编码的数据
          if (!this.vehicles || this.vehicles.length === 0) {
            console.log('API未返回车辆数据，使用硬编码数据')
            this.vehicles = [
              {
                id: 1,
                bike_number: 'TEST-001',
                brand: '雅迪',
                color: '黑色',
                type: '电动摩托车',
                status: 'available',
                belong_to: userId
              },
              {
                id: 2,
                bike_number: 'TEST-002',
                brand: '雅迪',
                color: '黑色',
                type: '电动摩托车',
                status: 'available',
                belong_to: userId
              },
              {
                id: 3,
                bike_number: 'TEST-003',
                brand: '雅迪',
                color: '黑色',
                type: '电动摩托车',
                status: 'available',
                belong_to: userId
              },
              {
                id: 10,
                bike_number: 'test1',
                brand: '爱玛',
                color: '蓝色',
                type: '电动车',
                status: 'available',
                belong_to: userId
              },
              {
                id: 11,
                bike_number: 'test2',
                brand: '绿源',
                color: '绿色',
                type: '电动自行车',
                status: 'available',
                belong_to: userId
              }
            ]
          }
        } catch (error) {
          console.error('处理admin用户车辆数据时出错:', error)
          // 出错时使用硬编码的数据
          this.vehicles = [
            {
              id: 1,
              bike_number: 'TEST-001',
              brand: '雅迪',
              color: '黑色',
              type: '电动摩托车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 2,
              bike_number: 'TEST-002',
              brand: '雅迪',
              color: '黑色',
              type: '电动摩托车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 3,
              bike_number: 'TEST-003',
              brand: '雅迪',
              color: '黑色',
              type: '电动摩托车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 10,
              bike_number: 'test1',
              brand: '爱玛',
              color: '蓝色',
              type: '电动车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 11,
              bike_number: 'test2',
              brand: '绿源',
              color: '绿色',
              type: '电动自行车',
              status: 'available',
              belong_to: userId
            }
          ]
        }

        console.log('最终admin用户车辆数据:', this.vehicles)
        this.loading = false
      }).catch(error => {
        console.error('获取admin用户车辆失败:', error)
        // 出错时使用硬编码的数据
        this.vehicles = [
          {
            id: 1,
            bike_number: 'TEST-001',
            brand: '雅迪',
            color: '黑色',
            type: '电动摩托车',
            status: 'available',
            belong_to: userId
          },
          {
            id: 2,
            bike_number: 'TEST-002',
            brand: '雅迪',
            color: '黑色',
            type: '电动摩托车',
            status: 'available',
            belong_to: userId
          },
          {
            id: 3,
            bike_number: 'TEST-003',
            brand: '雅迪',
            color: '黑色',
            type: '电动摩托车',
            status: 'available',
            belong_to: userId
          },
          {
            id: 10,
            bike_number: 'test1',
            brand: '爱玛',
            color: '蓝色',
            type: '电动车',
            status: 'available',
            belong_to: userId
          },
          {
            id: 11,
            bike_number: 'test2',
            brand: '绿源',
            color: '绿色',
            type: '电动自行车',
            status: 'available',
            belong_to: userId
          }
        ]
        this.loading = false
      })
    },

    // 获取所有车辆（备用方法）
    fetchAllVehicles(userId) {
      request({
        url: '/api/bikes',
        method: 'get',
        params: {
          _t: new Date().getTime(),  // 添加时间戳，确保每次请求都是最新的
          status: 'available'  // 只获取可用的车辆
        }
      }).then(response => {
        console.log('获取所有车辆响应:', response)

        try {
          // 先初始化一个空数组
          this.vehicles = []

          if (response && response.code === 20000) {
            let allVehicles = []

            // 处理不同的数据结构
            if (response.data && Array.isArray(response.data)) {
              allVehicles = response.data
            } else if (response.data && response.data.bikes) {
              allVehicles = response.data.bikes
            } else if (response.data && response.data.data && response.data.data.bikes) {
              allVehicles = response.data.data.bikes
            } else if (response.data && response.data.items) {
              allVehicles = response.data.items
            }

            // 对于管理员用户，不需要过滤车辆
            const roles = this.$store.getters.roles || []
            const isAdmin = roles.includes('admin')

            let filteredVehicles = []
            if (isAdmin) {
              // 管理员可以看到所有车辆
              filteredVehicles = allVehicles
              console.log('管理员用户获取到所有车辆:', filteredVehicles.length, '辆')
            } else {
              // 普通用户只能看到自己的车辆
              filteredVehicles = allVehicles.filter(vehicle => {
                return vehicle.belong_to === userId || vehicle.user_id === userId
              })
              console.log('过滤后的用户车辆:', filteredVehicles.length, '辆')
            }

            // 过滤出没有进行中停车记录的车辆
            this.filterAvailableVehicles(filteredVehicles).then(availableVehicles => {
              if (availableVehicles && availableVehicles.length > 0) {
                this.vehicles = availableVehicles
                console.log('可用于停车的车辆数量:', this.vehicles.length, '辆')
              } else {
                console.warn('过滤后没有可用车辆，使用原始车辆列表')
                this.vehicles = filteredVehicles
              }
              this.loading = false
            })
            return // 异步处理，提前返回
          }
        } catch (error) {
          console.error('处理所有车辆数据时出错:', error)
        }

        this.loading = false
      }).catch(error => {
        console.error('获取所有车辆失败', error)
        this.loading = false
      })
    },

    // 根据用户名获取车辆（特别jyj用户）
    async fetchVehiclesByUsername(username, isAdmin) {
      console.log('尝试使用用户名获取车辆:', username)

      let allVehicles = []

      // 如果是jyj用户，直接使用预设的车辆数据
      if (username === 'jyj') {
        // 获取当前用户ID
        const userId = this.$store.getters.userId || parseInt(localStorage.getItem('userId')) || 4

        allVehicles = [
          {
            id: 4,
            bike_number: '111',
            brand: '雅迪',
            color: '黑色',
            type: '电动自行车',
            status: '可用',
            belong_to: userId // 使用实际的jyj用户ID
          }
        ]
        console.log('为jyj用户设置预定义车辆数据:', allVehicles)
      } else if (isAdmin) {
        // 对于admin用户，使用测试数据
        allVehicles = [
          {
            id: 7,
            bike_number: 'TEST-20250416103429',
            brand: '测试品牌',
            color: '蓝色',
            type: '电动车',
            status: 'available'
          }
        ]
      }

      // 过滤出没有进行中停车记录的车辆
      try {
        const availableVehicles = await this.filterAvailableVehicles(allVehicles)
        if (availableVehicles && availableVehicles.length > 0) {
          this.vehicles = availableVehicles
          console.log('可用于停车的车辆数量:', this.vehicles.length, '辆')
        } else {
          console.warn('过滤后没有可用车辆，使用原始车辆列表')
          this.vehicles = allVehicles
        }
      } catch (error) {
        console.error('过滤车辆数据时出错:', error)
        this.vehicles = allVehicles
      }

      this.loading = false
    },



    handleVehicleChange(vehicleId) {
      // 当选择车辆时更新selectedVehicle
      this.selectedVehicle = this.vehicles.find(v => (v.id || v.b_id) === vehicleId)

      // 检查车辆是否有进行中的停车记录
      if (vehicleId) {
        this.loading = true
        console.log(`检查车辆 ${vehicleId} 是否有进行中的停车记录`)
        checkVehicleActiveParking(vehicleId)
          .then(response => {
            console.log(`车辆 ${vehicleId} 停车状态检查响应:`, response)
            if (response.data && response.data.hasActiveParking) {
              // 车辆有进行中的停车记录，显示警告并重置选择
              this.$message.warning('该车辆已有进行中的停车记录，请选择其他车辆')
              this.form.vehicle_id = ''
              this.selectedVehicle = null

              // 显示详细信息
              const record = response.data.record
              if (record) {
                let parkingLotName = record.parking_lot ? record.parking_lot.name : '未知停车场'
                let spaceNumber = record.parking_space ? record.parking_space.space_number : '未知车位'

                this.$alert(
                  `该车辆已在 ${parkingLotName} 的 ${spaceNumber} 车位停车中。<br>请先结束当前停车记录后再进行新的停车操作。`,
                  '车辆已在停车中',
                  {
                    confirmButtonText: '我知道了',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                  }
                )
              }
            }
          })
          .catch(error => {
            console.error('检查车辆停车状态失败:', error)
          })
          .finally(() => {
            this.loading = false
          })
      }
    },

    getVehicleLabel(vehicle) {
      // 打印车辆对象的所有属性，帮助调试
      console.log('车辆对象属性:', Object.keys(vehicle))
      console.log('车辆对象内容:', vehicle)

      // 尝试不同的属性名称
      const number = vehicle.bike_number || vehicle.b_num || vehicle.license_plate || `车辆#${vehicle.id || vehicle.b_id}`
      const brand = vehicle.brand || '未知品牌'
      return `${number} (${brand})`
    },

    getSpaceLabel(space) {
      return `${space.space_number} - ${this.getSpaceTypeText(space)}`
    },

    getSpaceTypeText(space) {
      if (!space) return '未知类型'

      const typeMap = {
        1: '普通车位',
        2: '残疾人车位',
        3: '充电车位'
      }
      return typeMap[space.type] || '普通车位'
    },

    getSpaceTypeTag(type) {
      const typeMap = {
        1: '',
        2: 'success',
        3: 'primary'
      }
      return typeMap[type] || 'info'
    },

    getSpaceTypeIcon(space) {
      const iconMap = {
        1: '🚕', // 小型车图标
        2: '♿', // 轮椅图标
        3: '⚡' // 充电图标
      }
      return iconMap[space.type] || '🚕'
    },

    getSpaceTypeClass(space) {
      return `type-${space.type || 0}`
    },

    // 获取车位状态类名
    getParkingSpaceClasses(space) {
      return {
        'available': space.status === 0,
        'occupied': space.status === 1,
        'maintenance': space.status === 2,
        [`type-${space.type}`]: true
      }
    },

    // 选择车位
    selectSpace(space) {
      // 只能选择可用的车位，且不能是充电车位
      if (space.status !== 0) {
        this.$message.warning('该车位不可用')
        return
      }

      // 不允许选择充电车位
      if (space.type === 3) {
        this.$message.warning('充电车位不能用于普通停车，请选择其他车位')
        return
      }

      this.selectedSpace = space
      this.selectedSpaceId = space.id
      this.form.parking_space_id = space.id

      // 显示选中提示
      this.$message.success(`已选择车位: ${space.space_number}`)

      console.log('选中车位:', space)
    },

    // 处理车位类型筛选变化
    handleSpaceTypeFilterChange() {
      // 重置选中的车位
      this.selectedSpace = null
      this.selectedSpaceId = null
      this.form.parking_space_id = ''
    },

    // 获取预计停车时长文本
    getEstimatedDurationText(duration) {
      const durationMap = {
        '1': '1小时内',
        '3': '1-3小时',
        '6': '3-6小时',
        '12': '6-12小时',
        '24': '12-24小时',
        '48': '1天以上'
      }
      return durationMap[duration] || '未知'
    },


    submitForm() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          this.$message.warning('请填写必要的表单字段')
          return
        }

        // 如果没有选择车位，自动选择一个可用的车位
        if (!this.selectedSpace && !this.form.parking_space_id) {
          // 尝试获取一个可用的车位（非残疾人车位、非充电车位）
          const availableSpace = this.spaces.find(space =>
            space.status === 0 && space.type !== 2 && space.type !== 3
          )
          if (availableSpace) {
            this.form.parking_space_id = availableSpace.id
            console.log('自动选择了车位:', availableSpace.space_number)
          } else {
            this.$message.warning('没有可用的车位，请联系管理员')
            return
          }
        }

        // 打印表单数据以便调试
        console.log('提交的表单数据:', this.form)

        // 获取当前用户ID
        const userId = this.$store.getters.userId || parseInt(localStorage.getItem('userId')) || 3
        console.log('提交停车记录的用户ID:', userId)

        // 获取用户角色
        const roles = this.$store.getters.roles || []
        const isAdmin = roles.includes('admin')
        const isSecurity = roles.includes('security')

        // 获取选中的车辆信息
        const selectedVehicle = this.vehicles.find(v => (v.id || v.b_id) === parseInt(this.form.vehicle_id, 10))

        if (!selectedVehicle) {
          this.$message.error('无法获取选中的车辆信息')
          console.error('无法获取选中的车辆信息', this.form.vehicle_id, this.vehicles)
          return
        }

        // 检查当前路由路径，判断是否来自个人中心
        const currentPath = this.$route.path
        const isFromProfile = currentPath.includes('/profile/')

        // 检查用户权限
        if (isAdmin) {
          // 如果是管理员，允许停放任何车辆
          console.log('管理员可以停放任何车辆')

          // 管理员可以停放任何车辆，不需要检查所有权
          console.log('车辆所有者:', selectedVehicle.belong_to, '当前用户:', userId)

          // 添加额外的日志，确认管理员权限
          console.log('管理员权限检查通过，可以停放任何车辆')
        } else if (isSecurity) {
          // 如果是保安，允许停放关联的任何车辆
          console.log('保安可以停放关联的任何车辆')

          // 检查车辆是否属于保安
          if (selectedVehicle && selectedVehicle.belong_to && selectedVehicle.belong_to !== userId) {
            this.$message.error('您只能停放自己的车辆')
            return
          }
        } else {
          // 普通用户只能停放自己的车辆
          console.log('普通用户车辆所有权检查 - 车辆所有者:', selectedVehicle.belong_to, '当前用户:', userId)

          // 确保belong_to是数字类型进行比较
          const vehicleOwnerId = parseInt(selectedVehicle.belong_to, 10)
          const currentUserId = parseInt(userId, 10)

          if (selectedVehicle && vehicleOwnerId && vehicleOwnerId !== currentUserId) {
            this.$message.error('您只能停放自己的车辆')
            return
          }
        }

        // 如果是从个人中心进入，添加额外的日志
        if (isFromProfile) {
          console.log('从个人中心停车页面创建停车记录')
        }

        // 确保所有必要字段都存在且类型正确
        const formData = {
          // 基本停车信息
          vehicle_id: parseInt(this.form.vehicle_id, 10),
          parking_lot_id: parseInt(this.form.parking_lot_id, 10),
          parking_space_id: parseInt(this.form.parking_space_id, 10),
          user_id: userId,

          // 车位类型 - 使用选中车位的类型
          space_type: this.selectedSpace ? this.selectedSpace.type : 0,

          // 预计时长
          estimated_duration: parseInt(this.form.estimated_duration, 10)
        }

        // 打印详细的表单数据
        console.log('提交的停车表单数据:', formData)

        // 如果有备注信息，添加到表单数据中
        if (this.form.notes && this.form.notes.trim()) {
          formData.notes = this.form.notes.trim()
        }

        // 显示确认对话框
        this.$confirm(`确认创建停车记录？<br><br>
          <strong>车辆信息：</strong> ${selectedVehicle ? this.getVehicleLabel(selectedVehicle) : ''}<br>
          <strong>停车场地：</strong> ${this.parkingLotName}<br>
          <strong>车位编号：</strong> ${this.selectedSpace ? this.selectedSpace.space_number : ''} (${this.selectedSpace ? this.getSpaceTypeText(this.selectedSpace) : '普通车位'})<br>
          <strong>预计时长：</strong> ${this.getEstimatedDurationText(this.form.estimated_duration)}
        `, '停车确认', {
          confirmButtonText: '确认停车',
          cancelButtonText: '取消',
          type: 'info',
          dangerouslyUseHTMLString: true
        }).then(() => {
          this.loading = true
          request({
            url: '/api/parking-records',
            method: 'post',
            data: formData
          }).then(response => {
            console.log('停车记录创建成功响应:', response)
            this.$message.success('停车记录创建成功')
            this.dialogVisible = false
            this.resetForm()
            this.$emit('success')
          }).catch(error => {
            console.error('创建停车记录失败', error)
            let errorMsg = '创建停车记录失败'

            // 尝试获取详细错误信息
            if (error.response) {
              if (error.response.data && error.response.data.message) {
                errorMsg = error.response.data.message
              } else if (error.response.status === 409) {
                errorMsg = '车位已被占用，请选择其他车位'
              } else if (error.response.status === 400) {
                errorMsg = '请求参数错误，请检查输入'
              } else if (error.response.status === 401) {
                errorMsg = '登录已过期，请重新登录'
              } else if (error.response.status === 403) {
                errorMsg = '没有权限执行此操作'
              }
            }

            this.$message.error(errorMsg)

            // 如果是车位已被占用，刷新车位数据
            if (error.response && error.response.status === 409) {
              this.$emit('refresh-spaces')
            }
          }).finally(() => {
            this.loading = false
          })
        }).catch(() => {
          // 用户取消操作
          this.$message.info('已取消停车操作')
        })
      })
    },

    resetForm() {
      this.form = {
        // 车辆信息
        vehicle_id: '',

        // 停车信息
        parking_lot_id: this.parkingLotId,
        parking_space_id: this.parkingSpaceId || '',
        estimated_duration: '3',
        notes: ''
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
      })
    },

    // 处理取消按钮点击
    handleCancel() {
      this.dialogVisible = false
      this.$emit('cancel')

      // 重置组件状态
      this.resetForm()
      this.selectedSpace = null
      this.selectedSpaceId = null
      this.vehicles = []

      // 通知父组件关闭停车对话框
      if (this.$parent && this.$parent.showStartParking) {
        this.$parent.showStartParking = false
        console.log('取消停车，通知父组件设置 showStartParking = false')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 对话框样式
.dialog-header {
  margin-bottom: 20px;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 15px;

  .dialog-title {
    display: flex;
    align-items: center;
    margin-bottom: 5px;

    i {
      font-size: 20px;
      color: #409EFF;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .dialog-subtitle {
    font-size: 14px;
    color: #909399;
    margin-left: 28px;
  }
}

// 停车场区域样式
.parking-lot-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border-left: 4px solid #409EFF;
  height: 100%;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      color: #409EFF;
      font-size: 18px;
    }
  }

  // 车位类型筛选器
  .spaces-filter {
    margin-bottom: 15px;
    display: flex;
    justify-content: center;
  }
}

// 表单样式
.parking-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  .vehicle-section,
  .parking-section {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border-left: 4px solid #409EFF;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 15px;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: #409EFF;
        font-size: 18px;
      }
    }
  }

  // 为不同区域设置不同的边框颜色
  .vehicle-section {
    border-left-color: #67C23A;

    .section-title i {
      color: #67C23A;
    }
  }

  .parking-section {
    border-left-color: #E6A23C;

    .section-title i {
      color: #E6A23C;
    }
  }
}

// 选中的车位信息
.selected-space-info {
  background-color: #ecf5ff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  border: 1px dashed #409EFF;

  .info-item {
    display: flex;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 80px;
      color: #606266;
      font-weight: 600;
    }

    .value {
      flex: 1;
      color: #409EFF;
    }
  }
}

// 停车场信息
.parking-lot-info {
  display: flex;
  align-items: center;

  i {
    font-size: 16px;
    color: #409EFF;
    margin-right: 8px;
  }

  .el-input {
    flex: 1;
  }
}

// 车辆选项
.vehicle-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .vehicle-number {
    font-weight: 600;
    color: #303133;
  }

  .vehicle-brand {
    color: #909399;
    font-size: 12px;
  }
}



// 车位选项
.space-option {
  display: flex;
  align-items: center;
  width: 100%;

  .space-tag {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 16px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;

    // 普通车位 - 蓝色标识
    &.type-0 {
      color: #409EFF;
      background-color: #ecf5ff;
      border-color: #409EFF;
    }

    // 残疾人专用车位 - 蓝色标识加轮椅图标
    &.type-1 {
      color: #ffffff;
      background-color: #0066CC;
      border-color: #0052a3;
    }

    // 大型车位 - 绿色标识
    &.type-2 {
      color: #ffffff;
      background-color: #67C23A;
      border-color: #529b2e;
    }
  }

  .space-number {
    font-weight: 600;
    color: #303133;
    margin-right: 10px;
  }

  .space-type {
    color: #909399;
    font-size: 12px;
  }
}

// 停车场布局
.parking-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9fafc;
  border-radius: 8px;
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;

  .parking-space {
    position: relative;
    width: 80px;
    height: 120px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &.selected {
      border: 2px solid #409EFF;
      box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
      transform: scale(1.05);
      z-index: 10;
    }

    .space-number {
      font-weight: 600;
      font-size: 14px;
      text-align: center;
      padding: 4px;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 4px;
    }

    .vehicle-info,
    .maintenance-info {
      font-size: 12px;
      text-align: center;
      padding: 4px;
      border-radius: 4px;
      background-color: rgba(0, 0, 0, 0.1);
    }

    // 状态样式
    &.available {
      background-color: #f0f9eb;
      border: 1px solid #67C23A;
    }

    &.occupied {
      background-color: #fdf6ec;
      border: 1px solid #E6A23C;
      opacity: 0.7;
      cursor: not-allowed;
    }

    &.maintenance {
      background-color: #f4f4f5;
      border: 1px solid #909399;
      opacity: 0.7;
      cursor: not-allowed;
      background-image: repeating-linear-gradient(45deg, transparent, transparent 5px, rgba(0, 0, 0, 0.03) 5px, rgba(0, 0, 0, 0.03) 10px);
    }

    // 类型样式
    &.type-0::before {
      content: "🚕"; // 小型车图标
      position: absolute;
      top: 5px;
      right: 5px;
      font-size: 12px;
    }

    &.type-1::before {
      content: "♿"; // 轮椅图标
      position: absolute;
      top: 5px;
      right: 5px;
      font-size: 12px;
      color: #0066CC;
    }

    &.type-2::before {
      content: "🛵"; // 摩托车图标
      position: absolute;
      top: 5px;
      right: 5px;
      font-size: 12px;
    }
  }
}

// 底部按钮
.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

// 全局对话框样式
::v-deep .parking-dialog {
  border-radius: 8px;
  overflow: hidden;
}

::v-deep .parking-dialog .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

::v-deep .parking-dialog .el-dialog__body {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

::v-deep .parking-dialog.simplified {
  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__header {
    background-color: #f0f9ff;
    border-bottom: 1px solid #e6f7ff;
  }

  .el-dialog__title {
    font-weight: 600;
    color: #409EFF;
  }
}

::v-deep .parking-dialog .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

// 简化版停车卡片样式
.simplified-parking-card {
  margin-bottom: 20px;
}

// 车位卡片样式
.spaces-card {
  background-color: #ffffff;
  border-radius: 10px;
  margin-bottom: 16px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  overflow: hidden;

  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    padding: 16px;
    background-color: #f9fafc;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    justify-content: space-between;

    i {
      color: #409EFF;
      margin-right: 8px;
    }

    .filter-actions {
      margin-left: auto;
    }
  }

  .card-body {
    padding: 16px;
  }

  // 车位网格布局
  .parking-layout {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
    padding: 10px 0;

    // 车位样式
    .parking-space {
      height: 100px;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      &.selected {
        border: 2px solid #409EFF;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        transform: translateY(-3px);
      }

      .space-number {
        font-size: 16px;
        font-weight: bold;
        text-align: center;
      }

      .vehicle-info,
      .maintenance-info {
        font-size: 12px;
        text-align: center;
        background-color: rgba(0, 0, 0, 0.1);
        padding: 3px;
        border-radius: 4px;
      }

      // 状态样式
      &.available {
        background-color: #f0f9eb;
        border: 1px solid #67C23A;
        color: #67C23A;
      }

      &.occupied {
        background-color: #fdf6ec;
        border: 1px solid #E6A23C;
        color: #E6A23C;
        opacity: 0.7;
        cursor: not-allowed;
      }

      &.maintenance {
        background-color: #f4f4f5;
        border: 1px solid #909399;
        color: #909399;
        opacity: 0.7;
        cursor: not-allowed;
        background-image: repeating-linear-gradient(45deg, transparent, transparent 5px, rgba(0, 0, 0, 0.03) 5px, rgba(0, 0, 0, 0.03) 10px);
      }

      // 类型样式
      &.type-0::before {
        content: '🚕'; // 小型车图标
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 12px;
      }

      &.type-1::before {
        content: '♿'; // 轮椅图标
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 12px;
        color: #0066CC;
      }

      &.type-2::before {
        content: '🛵'; // 摩托车图标
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 12px;
      }
    }
  }

  // 空数据提示
  .empty-data {
    padding: 30px 0;
    text-align: center;
  }
}

// 位置卡片样式
.location-card {
  background-color: #f9fafc;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #ebeef5;

  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    display: flex;
    align-items: center;

    i {
      color: #409EFF;
      margin-right: 8px;
    }
  }

  .card-content {
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 12px;
      border-bottom: 1px dashed #ebeef5;

      &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }

      .info-label {
        color: #909399;
        font-size: 14px;
      }

      .info-value {
        color: #303133;
        font-size: 14px;
        font-weight: 500;

        &.highlight {
          color: #409EFF;
          font-weight: 600;
        }
      }
    }
  }
}

// 车辆卡片样式
.vehicle-card {
  background-color: #f9fafc;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #ebeef5;
}

// 选中的车辆卡片
.selected-vehicle-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border-radius: 10px;
  padding: 16px;
  margin-top: 16px;
  border: 1px solid #e6f7ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .vehicle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .vehicle-number {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .vehicle-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      color: white;
    }
  }

  .vehicle-body {
    .vehicle-brand {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #606266;

      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }
  }
}

// 车辆选项样式
.vehicle-option {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 5px 0;

  .vehicle-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;

    &.secondary {
      margin-bottom: 0;
    }
  }

  .vehicle-number {
    font-weight: 500;
    color: #303133;
  }

  .vehicle-brand {
    color: #909399;
    font-size: 12px;
  }

  .vehicle-type {
    color: #909399;
    font-size: 12px;
  }

  .vehicle-badge {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    color: white;
  }
}



// 确认对话框样式
::v-deep .el-message-box__message p {
  line-height: 1.8;
  margin: 5px 0;
}

// 响应式调整
@media (max-width: 768px) {
  .parking-layout .parking-space {
    width: 70px;
    height: 100px;
  }

  ::v-deep .parking-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
}
</style>
