"""
前端适配器工具 - 用于处理不同格式的API响应

该模块提供了在前端适配后端API响应格式的方法，
可以在Vue项目中使用这些方法来统一处理响应。
"""

/**
 * 标准化API响应格式
 * 将各种API响应格式转换为前端期望的统一格式:
 * {
 *   code: 20000,
 *   data: {
 *     token: '...',
 *     user: {...}
 *   }
 * }
 * 
 * @param {Object} response - axios响应对象
 * @returns {Object} 标准化后的响应对象
 */
export function normalizeResponse(response) {
  // 已经是标准格式
  if (response.data && response.data.code === 20000) {
    return response;
  }
  
  // 登录响应的特殊处理
  if (response.data && response.data.access_token) {
    const token = response.data.access_token;
    const userData = response.data.user;
    
    // 转换为标准格式
    response.data = {
      code: 20000,
      data: {
        token: token,
        user: userData
      }
    };
    return response;
  }
  
  // 其他响应格式，使用通用转换
  if (response.data) {
    const originalData = response.data;
    response.data = {
      code: 20000,
      data: originalData
    };
  }
  
  return response;
}

/**
 * 适配登录流程的代码示例 (store/modules/user.js)
 */
/*
import { normalizeResponse } from '@/utils/request'

// 登录动作
login({ commit }, userInfo) {
  return new Promise((resolve, reject) => {
    login(userInfo)
      .then(response => {
        // 标准化响应格式
        const normalizedResponse = normalizeResponse(response);
        const { data } = normalizedResponse.data;
        
        // 提取token并存储
        commit('SET_TOKEN', data.token);
        setToken(data.token);
        resolve(normalizedResponse);
      })
      .catch(error => {
        reject(error);
      });
  });
}
*/ 