<template>
  <div class="active-parking-card" :class="{ 'loading': loading }">
    <div class="card-header">
      <div class="vehicle-info">
        <i class="el-icon-bicycle"></i>
        <span class="vehicle-number">{{ vehicleNumber }}</span>
      </div>
      <el-tag type="success" size="mini" effect="dark">进行中</el-tag>
    </div>

    <div class="card-body">
      <div class="info-row">
        <div class="info-label">
          <i class="el-icon-truck"></i>
          <span>车辆信息</span>
        </div>
        <div class="info-value">
          <span class="brand">{{ vehicleBrand }}</span>
          <div class="color-tag" :style="{ backgroundColor: vehicleColor }">
            <span>{{ vehicleColorName }}</span>
          </div>
        </div>
      </div>

      <div class="info-row">
        <div class="info-label">
          <i class="el-icon-location"></i>
          <span>停车场</span>
        </div>
        <div class="info-value">{{ parkingLotName }}</div>
      </div>

      <div class="info-row">
        <div class="info-label">
          <i class="el-icon-s-grid"></i>
          <span>车位号</span>
        </div>
        <div class="info-value">{{ spaceNumber }}</div>
      </div>

      <div class="info-row">
        <div class="info-label">
          <i class="el-icon-time"></i>
          <span>开始时间</span>
        </div>
        <div class="info-value">{{ formatTime(startTime) }}</div>
      </div>

      <div class="info-row">
        <div class="info-label">
          <i class="el-icon-timer"></i>
          <span>已停车</span>
        </div>
        <div class="info-value duration">{{ formatDuration }}</div>
      </div>
    </div>

    <div class="card-footer">
      <el-button type="danger" size="mini" icon="el-icon-finished" @click="handleEndParking" style="width: 100%">结束停车</el-button>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 添加dayjs插件
dayjs.extend(duration)
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

export default {
  name: 'ActiveParkingCard',
  props: {
    record: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      timer: null,
      currentTime: new Date()
    }
  },
  computed: {
    vehicleNumber() {
      if (!this.record || !this.record.vehicle) {
        return '未知车牌'
      }
      return this.record.vehicle.bike_number || this.record.vehicle.b_num || this.record.vehicle.number || '未知车牌'
    },
    vehicleBrand() {
      if (!this.record || !this.record.vehicle) {
        return '未知品牌'
      }
      return this.record.vehicle.brand || '未知品牌'
    },
    vehicleColor() {
      if (!this.record || !this.record.vehicle) {
        return '#909399'
      }
      // 根据颜色名称返回对应的颜色代码
      const colorMap = {
        '红色': '#f56c6c',
        '蓝色': '#409EFF',
        '绿色': '#67C23A',
        '黄色': '#E6A23C',
        '黑色': '#303133',
        '白色': '#f5f7fa', // 改为浅灰色，以便于显示
        '灰色': '#909399',
        '粉色': '#F9A8D4',
        '棕色': '#A78BFA',
        // 添加更多颜色
        '深蓝色': '#324157',
        '浅蓝色': '#58B7FF',
        '深绿色': '#13CE66',
        '浅绿色': '#8CC152',
        '橙色': '#FF9900',
        '紫色': '#9B59B6',
        '青色': '#00BCD4',
        '金色': '#F7BA2A'
      }

      // 获取颜色代码，如果没有匹配则使用默认颜色
      const colorCode = colorMap[this.record.vehicle.color]
      if (colorCode) {
        return colorCode
      }

      // 如果没有匹配的颜色名称，根据车牌号生成一个随机颜色
      const vehicleNumber = this.vehicleNumber
      if (vehicleNumber && vehicleNumber !== '未知车牌') {
        // 使用车牌号的字符码生成颜色
        let hash = 0
        for (let i = 0; i < vehicleNumber.length; i++) {
          hash = vehicleNumber.charCodeAt(i) + ((hash << 5) - hash)
        }

        // 生成HSL颜色，限制色相在一个好看的范围内
        const h = Math.abs(hash) % 360
        const s = 65 + (Math.abs(hash) % 20) // 65-85%的饱和度
        const l = 45 + (Math.abs(hash) % 10) // 45-55%的亮度

        return `hsl(${h}, ${s}%, ${l}%)`
      }

      // 默认颜色
      return '#409EFF'
    },
    vehicleColorName() {
      if (!this.record || !this.record.vehicle) {
        return '未知颜色'
      }
      return this.record.vehicle.color || '未知颜色'
    },
    parkingLotName() {
      if (!this.record) {
        return '未知停车场'
      }
      if (this.record.parking_lot) {
        return this.record.parking_lot.name || '未知停车场'
      }
      return this.record.parking_lot_name || '未知停车场'
    },
    spaceNumber() {
      if (!this.record) {
        return '未知车位'
      }
      if (this.record.parking_space) {
        return this.record.parking_space.space_number || this.record.parking_space.number || '未知车位'
      }
      return this.record.parking_space_number || '未知车位'
    },
    startTime() {
      if (!this.record) {
        return new Date()
      }
      // 优先使用数据库字段名 entry_time
      return this.record.entry_time || this.record.start_time || this.record.created_at || new Date()
    },
    formatDuration() {
      if (!this.record) {
        return '0分钟'
      }

      const start = dayjs(this.startTime)
      const now = dayjs(this.currentTime)
      const diff = now.diff(start)

      // 如果时间差小于0，返回0分钟
      if (diff < 0) {
        return '0分钟'
      }

      // 计算时间差
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((diff % (1000 * 60)) / 1000)

      // 格式化时间差，使用更直观的显示方式
      if (days > 0) {
        // 如果超过1天，显示天、小时、分钟
        return `${days}天 ${hours.toString().padStart(2, '0')}:小时 ${minutes.toString().padStart(2, '0')}:分钟`
      } else if (hours > 0) {
        // 如果超过1小时，显示小时、分钟、秒
        return `${hours}:小时 ${minutes.toString().padStart(2, '0')}:分钟 ${seconds.toString().padStart(2, '0')}:秒`
      } else if (minutes > 0) {
        // 如果超过1分钟，显示分钟、秒
        return `${minutes}:分钟 ${seconds.toString().padStart(2, '0')}:秒`
      } else {
        // 如果不超过1分钟，只显示秒
        return `${seconds}秒`
      }
    }
  },
  mounted() {
    // 启动定时器，每秒更新一次当前时间，使得秒数显示更准确
    this.timer = setInterval(() => {
      this.currentTime = new Date()
    }, 1000) // 1000毫秒 = 1秒

    // 初始化时立即设置当前时间
    this.currentTime = new Date()
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    formatTime(time) {
      return dayjs(time).format('YYYY-MM-DD HH:mm')
    },
    handleEndParking() {
      this.$emit('end-parking', this.record)
    }
  }
}
</script>

<style lang="scss" scoped>
.active-parking-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  border-left: 4px solid #67C23A;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  }

  &.loading {
    opacity: 0.7;
    pointer-events: none;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;

    .vehicle-info {
      display: flex;
      align-items: center;

      i {
        font-size: 18px;
        color: #409EFF;
        margin-right: 8px;
      }

      .vehicle-number {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  .card-body {
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .info-label {
        display: flex;
        align-items: center;
        color: #606266;

        i {
          margin-right: 5px;
          font-size: 14px;
        }
      }

      .info-value {
        color: #303133;
        font-weight: 500;

        &.duration {
          color: #E6A23C;
          font-weight: 600;
        }

        .brand {
          margin-right: 8px;
          font-weight: 600;
        }

        .color-tag {
          display: inline-block;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          color: #fff;
          text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(255, 255, 255, 0.2), transparent);
            pointer-events: none;
          }

          span {
            position: relative;
            z-index: 1;
          }
        }
      }
    }
  }

  .card-footer {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #ebeef5;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>
