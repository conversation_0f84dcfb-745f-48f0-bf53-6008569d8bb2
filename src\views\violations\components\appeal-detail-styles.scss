// 证据展示区域样式
.evidence-gallery {
  .evidence-col {
    margin-bottom: 20px;
  }
  
  .evidence-card {
    position: relative;
    border: 1px solid #EBEEF5;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    background-color: #fff;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }
    
    .evidence-type-tag {
      position: absolute;
      top: 8px;
      right: 8px;
      display: flex;
      align-items: center;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      z-index: 1;
      
      i {
        margin-right: 4px;
      }
      
      &.image {
        background-color: rgba(64, 158, 255, 0.8);
        color: #fff;
      }
      
      &.video {
        background-color: rgba(103, 194, 58, 0.8);
        color: #fff;
      }
    }
    
    .evidence-image, .evidence-video {
      width: 100%;
      height: 160px;
      overflow: hidden;
      
      .el-image {
        width: 100%;
        height: 100%;
      }
      
      .image-error {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background-color: #f5f7fa;
        color: #909399;
        
        i {
          font-size: 32px;
          margin-bottom: 8px;
        }
      }
    }
    
    .evidence-info {
      padding: 12px;
      background-color: #f5f7fa;
      
      .evidence-meta {
        .evidence-uploader, .evidence-time {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #606266;
          margin-bottom: 4px;
          
          i {
            margin-right: 6px;
            color: #909399;
          }
        }
      }
    }
  }
}

// 空状态样式
.empty-evidence {
  color: #909399;
  text-align: center;
  padding: 24px 0;
}

// 处理结果样式
.appeal-result {
  .result-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
    
    .result-item {
      &.full-width {
        grid-column: 1 / -1;
      }
      
      .result-label {
        font-size: 13px;
        color: #909399;
        margin-bottom: 6px;
      }
      
      .result-value {
        font-size: 14px;
        color: #303133;
        
        &.comment {
          padding: 10px;
          background-color: #f8f8f8;
          border-radius: 4px;
          line-height: 1.6;
          white-space: pre-line;
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .result-grid {
    grid-template-columns: 1fr !important;
  }
}
