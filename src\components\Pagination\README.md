# Pagination 分页组件

基于 Element UI 的分页组件封装，用于简化分页操作。

## 使用方法

```vue
<template>
  <div>
    <!-- 数据表格 -->
    <el-table :data="list" />
    
    <!-- 分页组件 -->
    <pagination 
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="fetchData"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'

export default {
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      }
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      // 这里发送API请求，获取数据
      // 使用 this.listQuery.page 和 this.listQuery.limit 作为分页参数
    }
  }
}
</script>
```

## 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| total | 总条目数 | Number | - |
| page | 当前页码，支持 .sync 修饰符 | Number | 1 |
| limit | 每页显示条目个数，支持 .sync 修饰符 | Number | 10 |
| pageSizes | 每页显示个数选择器的选项设置 | Array | [10, 20, 30, 50, 100] |
| layout | 组件布局，子组件名用逗号分隔 | String | 'total, sizes, prev, pager, next, jumper' |
| background | 是否为分页按钮添加背景色 | Boolean | true |
| autoScroll | 分页变化时是否自动滚动到顶部 (暂未实现) | Boolean | true |
| hidden | 是否隐藏分页组件 (可通过 v-show 控制) | Boolean | false |

## 事件

| 事件名称 | 说明 | 回调参数 |
| --- | --- | --- |
| pagination | 页码或每页条数改变时触发 | { page, limit } |

## 注意事项

1. 请确保在使用前已引入 Element UI 的分页组件
2. 使用 .sync 修饰符可以实现双向绑定页码和每页显示条数
3. 监听 pagination 事件来处理分页变化 