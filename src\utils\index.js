/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/11/18.
 */

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return '未知时间'
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date

  try {
    if (typeof time === 'object') {
      date = time
    } else {
      if ((typeof time === 'string')) {
        if ((/^[0-9]+$/.test(time))) {
          // support "1548221490638"
          time = parseInt(time)
        } else {
          // 添加调试信息
          console.log('处理日期字符串:', time)

          // 标准化ISO日期格式
          // 处理混合格式的日期时间字符串（如2025/04/23T16:10:39.147432）
          if (/^\d{4}\/\d{2}\/\d{2}T\d{2}:\d{2}:\d{2}/.test(time)) {
            console.log('检测到混合格式日期')
            // 将混合格式转换为标准ISO格式
            time = time.replace(/\//g, '-')
            console.log('转换后:', time)
          }

          // 如果是ISO格式但没有时区信息，将其视为本地时间
          // 同时处理带毫秒和不带毫秒的情况
          if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?$/.test(time)) {
            console.log('检测到ISO格式无时区信息')
            // 不添加Z，将其视为本地时间
            // 创建一个新的Date对象，保持原始时间不变
            const localDate = new Date(time)
            console.log('本地时间对象:', localDate)
          }

          // 支持Safari的日期解析（仅对非ISO格式的日期应用此转换）
          if (!/T\d{2}:\d{2}:\d{2}/.test(time)) {
            console.log('应用Safari兼容转换')
            time = time.replace(new RegExp(/-/gm), '/')
            console.log('转换后:', time)
          }
        }
      }

      if ((typeof time === 'number') && (time.toString().length === 10)) {
        time = time * 1000
      }
      date = new Date(time)
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('\n 无效的日期时间:', time)
      return '未知时间'
    }

    const formatObj = {
      y: date.getFullYear(),
      m: date.getMonth() + 1,
      d: date.getDate(),
      h: date.getHours(),
      i: date.getMinutes(),
      s: date.getSeconds(),
      a: date.getDay()
    }
    const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
      const value = formatObj[key]
      // Note: getDay() returns 0 on Sunday
      if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value ] }
      return value.toString().padStart(2, '0')
    })
    return time_str
  } catch (error) {
    console.error('日期格式化出错:', error.message, '原始值:', time, '类型:', typeof time)
    return '未知时间'
  }
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * 格式化日期时间
 * @param {(Object|string|number)} dateTime 日期时间对象、字符串或时间戳
 * @param {string} format 格式化模板，默认为 '{y}-{m}-{d} {h}:{i}:{s}'
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTime(dateTime, format) {
  return parseTime(dateTime, format)
}
