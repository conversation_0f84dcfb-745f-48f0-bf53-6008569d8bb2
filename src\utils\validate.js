/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  // 基本用户名验证规则
  if (!str) {
    return false
  }

  const username = str.trim()

  // 长度验证：至少3个字符，最多20个字符
  if (username.length < 3 || username.length > 20) {
    return false
  }

  // 字符验证：只允许字母、数字、下划线和中文
  const usernameRegex = /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/
  if (!usernameRegex.test(username)) {
    return false
  }

  return true
}
