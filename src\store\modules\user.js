import { login, logout, getInfo, register } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { resetRouter } from '@/router'
import {
  extractToken,
  extractUserInfo,
  extractUserId,
  extractUserName,
  extractRoles
} from '@/utils/token-helper'

const getDefaultState = () => {
  // 尝试从localStorage恢复用户信息
  const savedUserName = localStorage.getItem('userName')
  const savedUserId = localStorage.getItem('userId')

  return {
    token: getToken(),
    name: savedUserName || '',
    avatar: '',
    role: '',
    userId: savedUserId ? parseInt(savedUserId) : null,
    roles: []
  }
}

const state = getDefaultState()

const getters = {
  token: state => state.token,
  avatar: state => state.avatar,
  name: state => state.name,
  role: state => state.role,
  userId: state => state.userId,
  roles: state => state.roles && Array.isArray(state.roles) ? state.roles : [state.role || 'user'],
  isAdmin: state => {
    const roles = state.roles && Array.isArray(state.roles) ? state.roles : [state.role || 'user']
    return roles.includes('admin')
  }
}

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLE: (state, role) => {
    state.role = role
    state.roles = Array.isArray(role) ? role : [role]
  },
  SET_USERID: (state, userId) => {
    state.userId = userId
  },
  SET_ROLES: (state, roles) => {
    state.roles = Array.isArray(roles) ? roles : [roles].filter(Boolean)
    state.role = state.roles[0] || 'user'
  }
}

const actions = {
  // 用户登录
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    console.log('尝试登录用户:', username)

    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password }).then(response => {
        console.log('=== 登录响应详细信息 ===')
        console.log('完整响应对象:', response)
        console.log('响应数据:', response.data)
        console.log('响应状态:', response.status)
        console.log('响应头:', response.headers)

        // 1. 获取响应数据
        const data = response.data || response

        if (!data) {
          reject('验证失败，请重新登录')
          return
        }

        // 2. 提取 Token
        console.log('=== 提取Token ===')
        console.log('用于提取Token的数据:', data)
        const token = extractToken(data)
        console.log('提取到的Token:', token)

        if (!token) {
          console.error('响应中没有token字段')
          console.error('数据结构:', JSON.stringify(data, null, 2))
          reject('登录成功但未返回token')
          return
        }

        // 3. 保存 Token
        commit('SET_TOKEN', token)
        setToken(token)

        // 4. 提取用户信息
        console.log('登录响应完整数据:', data)
        const userData = extractUserInfo(data)
        console.log('提取到的用户信息:', userData)

        if (userData) {
          // 5. 提取并设置用户ID
          const userId = extractUserId(userData)
          if (userId) {
            commit('SET_USERID', userId)
            // 保存到localStorage，防止刷新后丢失
            localStorage.setItem('userId', userId)
          }

          // 6. 提取并设置用户名
          const name = extractUserName(userData)
          if (name) {
            console.log('登录时设置用户名:', name)
            commit('SET_NAME', name)
            // 保存到localStorage，防止刷新后丢失
            localStorage.setItem('userName', name)
          } else {
            console.warn('登录时未能提取到用户名')
          }

          // 7. 提取并设置角色
          const roles = extractRoles(userData)
          if (roles.length > 0) {
            console.log('登录时设置用户角色:', roles)
            commit('SET_ROLES', roles)
            commit('SET_ROLE', roles[0] || 'user')
          } else {
            console.log('登录时未找到角色信息，设置为默认用户角色')
            commit('SET_ROLES', ['user'])
            commit('SET_ROLE', 'user')
          }
        }

        // 8. 立即获取用户详细信息
        if (userData && userData.id) {
          return getInfo(userData.id).then(response => {
            const userInfo = response.data
            if (userInfo) {
              commit('SET_ROLE', userInfo.u_role || userInfo.role)
              const detailedName = userInfo.u_name || userInfo.name
              if (detailedName) {
                console.log('登录后获取详细信息，更新用户名:', detailedName)
                commit('SET_NAME', detailedName)
                localStorage.setItem('userName', detailedName)
              }
              commit('SET_AVATAR', userInfo.avatar || 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif')
            }
            resolve()
          }).catch(error => {
            console.error('获取用户详细信息失败:', error)
            resolve() // 即使获取详细信息失败，也允许登录成功
          })
        }

        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 获取用户信息
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      // 1. 尝试从localStorage恢复用户ID和用户名
      const savedUserId = localStorage.getItem('userId')
      const savedUserName = localStorage.getItem('userName')

      if (savedUserId && !state.userId) {
        commit('SET_USERID', parseInt(savedUserId))
      }

      if (savedUserName && !state.name) {
        console.log('从localStorage恢复用户名:', savedUserName)
        commit('SET_NAME', savedUserName)
      }

      // 2. 确保有用户ID
      if (!state.userId) {
        reject('未找到用户ID')
        return
      }

      // 3. 获取用户信息
      getInfo(state.userId).then(response => {
        const { data } = response

        if (!data) {
          reject('验证失败，请重新登录')
          return
        }

        // 4. 提取用户信息
        const userInfo = data.user || data

        // 5. 提取基本信息
        const name = userInfo.u_name || userInfo.name
        const avatar = userInfo.avatar || 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif'
        const userId = userInfo.u_id || userInfo.id
        const role = userInfo.u_role || userInfo.role || 'user'

        // 6. 更新store
        if (userId) {
          commit('SET_USERID', userId)
          localStorage.setItem('userId', userId)
        }

        // 设置角色信息
        if (role) {
          console.log('设置用户角色:', role)
          commit('SET_ROLE', role)

          // 确保roles数组也被设置
          const roles = Array.isArray(role) ? role : [role]
          commit('SET_ROLES', roles)
        } else {
          console.log('未找到角色信息，设置为默认用户角色')
          commit('SET_ROLE', 'user')
          commit('SET_ROLES', ['user'])
        }

        if (name) {
          console.log('getInfo: 设置用户名:', name)
          commit('SET_NAME', name)
          // 保存到localStorage
          localStorage.setItem('userName', name)
        }
        commit('SET_AVATAR', avatar)

        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 用户登出
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      // 导入socketService
      const socketService = require('@/utils/socket').default

      console.log('开始登出流程')

      try {
        // 1. 首先设置登出状态，防止在登出过程中建立新连接
        if (socketService) {
          console.log('设置WebSocket登出状态为true')
          socketService.setLoggingOut(true)
        }

        // 2. 立即断开WebSocket连接
        if (socketService) {
          console.log('立即断开WebSocket连接')
          socketService.disconnect()
        }

        // 3. 立即移除token，防止后续操作使用旧token
        console.log('移除token')
        removeToken()

        // 4. 立即重置状态，确保前端状态一致性
        console.log('重置用户状态')
        commit('RESET_STATE')

        // 5. 重置路由
        console.log('重置路由')
        resetRouter()

        // 6. 清除localStorage中的用户信息
        console.log('清除localStorage中的用户信息')
        localStorage.removeItem('userId')
        localStorage.removeItem('userName')

        // 7. 调用登出API（即使失败也不影响前端登出）
        console.log('调用后端登出API')
        logout().then(() => {
          console.log('后端登出API调用成功')

          // 8. 延迟重置登出状态，确保所有操作完成
          setTimeout(() => {
            if (socketService) {
              console.log('登出完成，重置WebSocket登出状态')
              socketService.setLoggingOut(false)
            }
            resolve()
          }, 1000) // 减少到1秒，因为我们已经提前断开了连接
        }).catch(error => {
          console.error('后端登出API调用失败，但不影响前端登出:', error)

          // 延迟重置登出状态，确保所有操作完成
          setTimeout(() => {
            if (socketService) {
              console.log('登出完成（尽管API失败），重置WebSocket登出状态')
              socketService.setLoggingOut(false)
            }
            // 即使API调用失败，也视为登出成功
            resolve()
          }, 1000) // 减少到1秒，因为我们已经提前断开了连接
        })
      } catch (error) {
        console.error('登出过程中发生错误:', error)

        // 确保即使出错也重置登出状态
        setTimeout(() => {
          if (socketService) {
            console.log('登出过程出错，重置WebSocket登出状态')
            socketService.setLoggingOut(false)
          }
          resolve()
        }, 1000)
      }
    })
  },

  // 重置Token
  resetToken({ commit }) {
    return new Promise(resolve => {
      // 导入socketService
      const socketService = require('@/utils/socket').default

      console.log('开始重置Token流程')

      try {
        // 1. 首先设置登出状态，防止在重置token过程中建立新连接
        if (socketService) {
          console.log('设置WebSocket登出状态为true')
          socketService.setLoggingOut(true)
        }

        // 2. 立即断开WebSocket连接
        if (socketService) {
          console.log('立即断开WebSocket连接')
          socketService.disconnect()
        }

        // 3. 立即移除token
        console.log('移除token')
        removeToken()

        // 4. 重置状态
        console.log('重置用户状态')
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_USERID', null) // 重置用户ID为null
        commit('SET_NAME', '')
        commit('SET_AVATAR', '')
        commit('SET_ROLE', '')

        // 5. 清除localStorage中的用户信息
        console.log('清除localStorage中的用户信息')
        localStorage.removeItem('userId')
        localStorage.removeItem('userName')

        // 6. 重置路由
        console.log('重置路由')
        resetRouter()

        // 7. 延迟重置登出状态，确保所有操作完成
        setTimeout(() => {
          if (socketService) {
            console.log('Token重置完成，重置WebSocket登出状态')
            socketService.setLoggingOut(false)
          }
          resolve()
        }, 1000) // 减少到1秒，因为我们已经提前断开了连接
      } catch (error) {
        console.error('重置Token过程中发生错误:', error)

        // 确保即使出错也重置登出状态
        setTimeout(() => {
          if (socketService) {
            console.log('重置Token过程出错，重置WebSocket登出状态')
            socketService.setLoggingOut(false)
          }
          resolve()
        }, 1000)
      }
    })
  },

  // 用户注册
  register({ commit, dispatch }, userInfo) {
    const { username, password, confirmPassword, belong, phone } = userInfo
    return new Promise((resolve, reject) => {
      register({
        username: username.trim(),
        password,
        confirmPassword,
        belong,
        phone
      }).then(response => {
        // 响应已经被适配器标准化
        const { data } = response

        if (process.env.NODE_ENV === 'development') {
          console.log('注册成功，获取到响应:', data)
        }

        try {
          // 1. 提取 Token
          const token = extractToken(data)

          if (token) {
            console.log('注册返回了token，自动登录')
            commit('SET_TOKEN', token)
            setToken(token)

            // 在注册后立即获取最新的用户信息，确保用户角色正确设置
            return dispatch('getInfo').then(() => {
              resolve(data)
            }).catch(infoError => {
              console.warn('注册后获取用户信息失败，但不影响注册流程:', infoError)
              resolve(data)
            })
          } else {
            console.log('注册成功，但未返回token，需要单独登录')
          }

          // 2. 提取用户信息
          const userInfo = extractUserInfo(data)

          if (userInfo) {
            // 3. 提取基本信息
            const name = extractUserName(userInfo)
            const roles = extractRoles(userInfo)
            const userId = extractUserId(userInfo, 1)

            // 4. 提交到store
            if (name) {
              console.log('注册时设置用户名:', name)
              commit('SET_NAME', name)
              localStorage.setItem('userName', name)
            }
            commit('SET_USERID', userId)
            commit('SET_ROLES', roles)

            // 如果有头像，也设置它
            if (userInfo.avatar) {
              commit('SET_AVATAR', userInfo.avatar)
            }
          }

          resolve(data)
        } catch (error) {
          console.error('处理注册响应时出错:', error)
          // 即使处理出错，如果成功注册，仍然返回成功
          if (data && (data.message || data.msg || '').includes('成功')) {
            resolve(data)
          } else {
            reject({
              data: {
                message: '注册响应处理失败: ' + (error.message || '未知错误')
              }
            })
          }
        }
      }).catch(error => {
        console.error('注册请求失败:', error)
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}

