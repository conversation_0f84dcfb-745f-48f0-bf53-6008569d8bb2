"""
添加违规类型表和初始数据
"""
import os
import sqlite3
from datetime import datetime

def upgrade():
    """升级数据库"""
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'sys.db')
    print(f"数据库路径: {db_path}")

    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # 创建违规类型表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS violation_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(50) NOT NULL UNIQUE,
            description TEXT,
            needs_admin INTEGER NOT NULL DEFAULT 0,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # 检查 violation_records 表中是否已存在 violation_type_id 列
        cursor.execute("PRAGMA table_info(violation_records)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        # 如果不存在，添加该列
        if 'violation_type_id' not in column_names:
            cursor.execute('''
            ALTER TABLE violation_records ADD COLUMN violation_type_id INTEGER REFERENCES violation_types(id)
            ''')

        # 添加初始数据
        initial_types = [
            {
                'name': '违规停车',
                'description': '在非指定区域停放电动车',
                'needs_admin': 0
            },
            {
                'name': '占用消防通道',
                'description': '电动车占用消防通道',
                'needs_admin': 0
            },
            {
                'name': '无牌无证',
                'description': '电动车无牌照或无证件',
                'needs_admin': 0
            },
            {
                'name': '超速行驶',
                'description': '电动车在校园内超速行驶',
                'needs_admin': 0
            },
            {
                'name': '违规充电',
                'description': '在非指定区域为电动车充电',
                'needs_admin': 0
            },
            {
                'name': '申诉处理',
                'description': '用户对违规记录提出申诉，需要管理员处理',
                'needs_admin': 1
            },
            {
                'name': '其他违规',
                'description': '其他需要管理员处理的违规情况',
                'needs_admin': 1
            }
        ]

        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        for type_data in initial_types:
            # 检查是否已存在该类型
            cursor.execute("SELECT id FROM violation_types WHERE name = ?", (type_data['name'],))
            existing = cursor.fetchone()

            if not existing:
                cursor.execute('''
                INSERT INTO violation_types (name, description, needs_admin, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
                ''', (
                    type_data['name'],
                    type_data['description'],
                    type_data['needs_admin'],
                    now,
                    now
                ))

        # 提交事务
        conn.commit()
        print("违规类型表创建成功，初始数据添加完成")
    except Exception as e:
        conn.rollback()
        print(f"错误: {str(e)}")
        raise
    finally:
        cursor.close()
        conn.close()

def downgrade():
    """回滚数据库"""
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'sys.db')

    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # 创建临时表，不包含 violation_type_id 字段
        cursor.execute('''
        CREATE TABLE temp_violation_records AS
        SELECT id, bike_number, bike_id, user_id, violation_time, location, violation_type,
               description, status, result, fine_amount, recorder_id, handler_id, created_at, updated_at
        FROM violation_records
        ''')

        # 删除原表
        cursor.execute('DROP TABLE violation_records')

        # 创建新表，不包含 violation_type_id 字段
        cursor.execute('''
        CREATE TABLE violation_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bike_number VARCHAR(50) NOT NULL,
            bike_id INTEGER REFERENCES bikes(b_id),
            user_id INTEGER NOT NULL REFERENCES users(u_id),
            violation_time TIMESTAMP NOT NULL,
            location VARCHAR(100) NOT NULL,
            violation_type VARCHAR(50) NOT NULL,
            description TEXT,
            status INTEGER NOT NULL DEFAULT 0,
            result TEXT,
            fine_amount FLOAT DEFAULT 0,
            recorder_id INTEGER NOT NULL REFERENCES users(u_id),
            handler_id INTEGER REFERENCES users(u_id),
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # 将数据从临时表复制到新表
        cursor.execute('''
        INSERT INTO violation_records
        SELECT id, bike_number, bike_id, user_id, violation_time, location, violation_type,
               description, status, result, fine_amount, recorder_id, handler_id, created_at, updated_at
        FROM temp_violation_records
        ''')

        # 删除临时表
        cursor.execute('DROP TABLE temp_violation_records')

        # 删除违规类型表
        cursor.execute('DROP TABLE IF EXISTS violation_types')

        # 提交事务
        conn.commit()
        print("违规类型表删除成功")
    except Exception as e:
        conn.rollback()
        print(f"错误: {str(e)}")
        raise
    finally:
        cursor.close()
        conn.close()

if __name__ == '__main__':
    upgrade()
