<template>
  <div class="parking-statistics">
    <el-card class="statistics-card">
      <div slot="header" class="card-header">
        <span><i class="el-icon-data-analysis"></i> 停车统计概览</span>
        <div class="header-controls">
          <el-select v-model="dateRange" placeholder="时间范围" size="small" @change="fetchData">
            <el-option v-for="item in dateRangeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select
            v-if="isAdmin"
            v-model="scope"
            placeholder="统计范围"
            size="small"
            @change="fetchData"
          >
            <el-option label="全部用户" value="all" />
            <el-option label="仅我的记录" value="user" />
          </el-select>
        </div>
      </div>

      <div v-loading="loading">
        <!-- 统计概览 -->
        <div class="overview-cards">
          <el-row :gutter="20">
            <el-col :xs="12" :sm="8" :md="6" :lg="4" v-for="(item, index) in overviewItems" :key="index">
              <div class="stat-card" :style="{ borderColor: item.color }">
                <div class="stat-icon" :style="{ backgroundColor: item.color }">
                  <i :class="item.icon"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ item.value }}</div>
                  <div class="stat-label">{{ item.label }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 图表区域 -->
        <div class="charts-container">
          <el-row :gutter="20">
            <!-- 每日停车记录图表 -->
            <el-col :xs="24" :sm="24" :md="12">
              <div class="chart-wrapper">
                <h3>每日停车记录</h3>
                <div ref="dailyChart" class="chart"></div>
              </div>
            </el-col>

            <!-- 车辆类型分布图表 -->
            <el-col :xs="24" :sm="24" :md="12">
              <div class="chart-wrapper">
                <h3>车辆类型分布</h3>
                <div ref="vehicleTypeChart" class="chart"></div>
              </div>
            </el-col>
          </el-row>

          <!-- 停车场使用率图表 -->
          <el-row>
            <el-col :span="24">
              <div class="chart-wrapper">
                <h3>停车场使用率</h3>
                <div ref="parkingLotChart" class="chart"></div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getParkingStats } from '@/api/statistics'
import * as echarts from 'echarts'

export default {
  name: 'ParkingStatistics',
  props: {
    userId: {
      type: [Number, String],
      default: null
    },
    isAdmin: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      dateRange: 'week',
      scope: 'user',
      dateRangeOptions: [
        { label: '今日', value: 'today' },
        { label: '本周', value: 'week' },
        { label: '本月', value: 'month' },
        { label: '本年', value: 'year' }
      ],
      stats: {
        overview: {
          total_records: 0,
          active_records: 0,
          completed_records: 0,
          abnormal_records: 0,
          today_records: 0,
          avg_duration_hours: 0
        },
        period_stats: {
          label: '本周',
          start_date: '',
          end_date: '',
          total_records: 0
        },
        parking_lots: [],
        vehicle_types: [],
        daily_stats: []
      },
      charts: {
        dailyChart: null,
        vehicleTypeChart: null,
        parkingLotChart: null
      }
    }
  },
  computed: {
    overviewItems() {
      // 确保数据存在，避免 undefined 错误
      const overview = this.stats.overview || {
        total_records: 0,
        active_records: 0,
        completed_records: 0,
        abnormal_records: 0,
        today_records: 0,
        avg_duration_hours: 0
      }

      const periodStats = this.stats.period_stats || {
        label: this.dateRangeOptions.find(item => item.value === this.dateRange)?.label || '本周',
        total_records: 0
      }

      // 格式化平均停车时长
      const formatDuration = (hours) => {
        if (hours === undefined || hours === null || hours === 0) return '0 小时'

        // 确保 hours 是数字
        const numHours = parseFloat(hours)
        if (isNaN(numHours)) return '0 小时'

        const h = Math.floor(numHours)
        const m = Math.round((numHours - h) * 60)

        if (h > 0) {
          return `${h}小时${m > 0 ? ` ${m}分钟` : ''}`
        } else {
          return `${m}分钟`
        }
      }

      return [
        {
          label: '总停车记录',
          value: overview.total_records || 0,
          icon: 'el-icon-s-data',
          color: '#409EFF'
        },
        {
          label: '进行中记录',
          value: overview.active_records || 0,
          icon: 'el-icon-time',
          color: '#67C23A'
        },
        {
          label: '已完成记录',
          value: overview.completed_records || 0,
          icon: 'el-icon-check',
          color: '#E6A23C'
        },
        {
          label: '今日停车',
          value: overview.today_records || 0,
          icon: 'el-icon-date',
          color: '#F56C6C'
        },
        {
          label: '平均停车时长',
          value: formatDuration(overview.avg_duration_hours),
          icon: 'el-icon-stopwatch',
          color: '#909399'
        },
        {
          label: periodStats.label || '本周',
          value: periodStats.total_records || 0,
          icon: 'el-icon-data-line',
          color: '#9B59B6'
        }
      ]
    }
  },
  mounted() {
    this.fetchData()
    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.resizeCharts)
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
  },
  methods: {
    fetchData() {
      this.loading = true

      // 构建查询参数
      const params = {
        date_range: this.dateRange,
        _t: new Date().getTime() // 添加时间戳防止缓存
      }

      // 如果是管理员，添加scope参数
      if (this.isAdmin) {
        params.scope = this.scope
      }

      // 如果指定了用户ID，添加user_id参数
      if (this.userId) {
        params.user_id = this.userId
      }

      console.log('ParkingStatistics 组件发送请求参数:', params)

      // 使用原始的 API 调用
      console.log('使用原始的 API 调用')
      getParkingStats(params)
        .then(response => {
          console.log('ParkingStatistics 组件收到响应:', response)

          if (response.code === 20000 && response.data) {
            console.log('ParkingStatistics 组件处理数据:', response.data)

            // 处理后端返回的数据，确保数据格式正确
            const data = response.data

            // 确保 overview 数据存在
            if (!data.overview) {
              data.overview = {
                total_records: 0,
                active_records: 0,
                completed_records: 0,
                abnormal_records: 0,
                today_records: 0,
                avg_duration_hours: 0
              }
            } else {
              // 确保 avg_duration_hours 是有效数字
              if (data.overview.avg_duration_hours === null ||
                  data.overview.avg_duration_hours === undefined ||
                  isNaN(parseFloat(data.overview.avg_duration_hours))) {
                console.warn('平均停车时长数据无效，设置为默认值0')
                data.overview.avg_duration_hours = 0
              } else {
                // 确保是数字类型
                data.overview.avg_duration_hours = parseFloat(data.overview.avg_duration_hours)

                // 检查值是否合理
                if (data.overview.avg_duration_hours < 0 || data.overview.avg_duration_hours > 1000) {
                  console.warn(`平均停车时长数据异常: ${data.overview.avg_duration_hours}，设置为默认值0`)
                  data.overview.avg_duration_hours = 0
                } else {
                  console.log('平均停车时长:', data.overview.avg_duration_hours)
                }
              }

              // 如果没有已完成的记录，平均停车时长应该为0
              if (data.overview.completed_records === 0) {
                console.log('没有已完成的停车记录，平均停车时长设为0')
                data.overview.avg_duration_hours = 0
              }
            }

            // 确保 period_stats 数据存在
            if (!data.period_stats) {
              data.period_stats = {
                label: this.dateRangeOptions.find(item => item.value === this.dateRange)?.label || '本周',
                start_date: '',
                end_date: '',
                total_records: 0
              }
            }

            // 确保 daily_stats 数据存在且格式正确
            if (!data.daily_stats || !Array.isArray(data.daily_stats)) {
              console.warn('警告: 返回的数据中缺少 daily_stats 或格式不正确')
              data.daily_stats = []
            } else {
              // 处理每日统计数据，确保包含 completed 和 active 字段
              data.daily_stats = data.daily_stats.map(item => {
                // 如果没有 completed 和 active 字段，根据总体比例估算
                if (item.completed === undefined || item.active === undefined) {
                  const totalRecords = data.overview.total_records || 0
                  const completedRecords = data.overview.completed_records || 0
                  const activeRecords = data.overview.active_records || 0

                  const completedRatio = totalRecords > 0 ? completedRecords / totalRecords : 0.9
                  const activeRatio = totalRecords > 0 ? activeRecords / totalRecords : 0.1

                  return {
                    ...item,
                    completed: item.completed !== undefined ? item.completed : Math.round(item.count * completedRatio),
                    active: item.active !== undefined ? item.active : Math.round(item.count * activeRatio)
                  }
                }
                return item
              })
            }

            // 确保 vehicle_types 数据存在
            if (!data.vehicle_types || !Array.isArray(data.vehicle_types)) {
              console.warn('警告: 返回的数据中缺少 vehicle_types 或格式不正确')
              data.vehicle_types = []
            }

            // 确保 parking_lots 数据存在
            if (!data.parking_lots || !Array.isArray(data.parking_lots)) {
              console.warn('警告: 返回的数据中缺少 parking_lots 或格式不正确')
              data.parking_lots = []
            }

            // 更新组件数据
            this.stats = data

            // 初始化图表
            this.$nextTick(() => {
              this.initCharts()
            })
          } else {
            console.error('获取统计数据失败，响应格式不正确:', response)
            this.$message.error('获取统计数据失败')
          }
        })
        .catch(error => {
          console.error('直接 axios 请求失败:', error)
          this.$message.error('获取统计数据失败: ' + (error.message || '未知错误'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    initCharts() {
      this.initDailyChart()
      this.initVehicleTypeChart()
      this.initParkingLotChart()
    },
    initDailyChart() {
      // 初始化每日停车记录图表
      if (this.charts.dailyChart) {
        this.charts.dailyChart.dispose()
      }

      const chartDom = this.$refs.dailyChart
      this.charts.dailyChart = echarts.init(chartDom)

      // 确保 daily_stats 存在且有数据
      if (!this.stats.daily_stats || this.stats.daily_stats.length === 0) {
        console.warn('每日停车记录数据为空')
        // 显示空图表
        this.charts.dailyChart.setOption({
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center'
          }
        })
        return
      }

      // 如果数据存在但为空或不足7天，创建默认数据
      if (this.stats.daily_stats.length < 7) {
        console.warn('每日停车记录数据不足7天，添加默认数据')

        // 获取最近7天的日期
        const dates = []
        for (let i = 6; i >= 0; i--) {
          const date = new Date()
          date.setDate(date.getDate() - i)
          dates.push(date.toISOString().split('T')[0]) // 格式: YYYY-MM-DD
        }

        // 创建默认数据
        const existingDates = this.stats.daily_stats.map(item => item.date)

        // 添加缺失的日期
        dates.forEach(date => {
          if (!existingDates.includes(date)) {
            this.stats.daily_stats.push({
              date: date,
              count: 0,
              completed: 0,
              active: 0
            })
          }
        })

        // 按日期排序
        this.stats.daily_stats.sort((a, b) => new Date(a.date) - new Date(b.date))

        console.log('补充后的每日停车记录数据:', this.stats.daily_stats)
      }

      // 确保日期格式一致，并且是最近7天的数据
      // 有时后端返回的日期可能格式不一致或不是最近7天
      const now = new Date()
      const oneWeekAgo = new Date(now)
      oneWeekAgo.setDate(now.getDate() - 6)

      // 过滤并确保只保留最近7天的数据
      this.stats.daily_stats = this.stats.daily_stats.filter(item => {
        const itemDate = new Date(item.date)
        return itemDate >= oneWeekAgo && itemDate <= now
      })

      // 如果过滤后数据不足7天，再次补充
      if (this.stats.daily_stats.length < 7) {
        const existingDates = this.stats.daily_stats.map(item => item.date)

        for (let i = 6; i >= 0; i--) {
          const date = new Date(now)
          date.setDate(now.getDate() - i)
          const dateStr = date.toISOString().split('T')[0]

          if (!existingDates.includes(dateStr)) {
            this.stats.daily_stats.push({
              date: dateStr,
              count: 0,
              completed: 0,
              active: 0
            })
          }
        }

        // 再次排序
        this.stats.daily_stats.sort((a, b) => new Date(a.date) - new Date(b.date))
      }

      const dates = this.stats.daily_stats.map(item => item.date)

      // 分别获取总数、已完成和进行中的数据
      const totalCounts = this.stats.daily_stats.map(item => item.count || 0)
      const completedCounts = this.stats.daily_stats.map(item => item.completed || 0)
      const activeCounts = this.stats.daily_stats.map(item => item.active || 0)

      // 检查数据是否合理
      console.log('每日停车记录数据:', {
        dates,
        totalCounts,
        completedCounts,
        activeCounts
      })

      // 格式化日期显示，只显示月和日
      const formattedDates = dates.map(date => {
        const parts = date.split('-')
        if (parts.length === 3) {
          return `${parts[1]}-${parts[2]}` // MM-DD 格式
        }
        return date
      })

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            // 自定义提示框内容
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              const colorSpan = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>`
              result += colorSpan + param.seriesName + ': ' + param.value + '<br/>'
            })
            return result
          }
        },
        legend: {
          data: ['总记录', '已完成', '进行中'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: formattedDates,
          axisLabel: {
            rotate: 45,
            interval: 0 // 强制显示所有标签
          }
        },
        yAxis: {
          type: 'value',
          name: '停车记录数',
          minInterval: 1, // 最小间隔为1，确保显示整数
          min: 0 // 从0开始
        },
        series: [
          {
            name: '总记录',
            type: 'bar',
            data: totalCounts,
            itemStyle: {
              color: '#409EFF'
            },
            emphasis: {
              itemStyle: {
                color: '#66b1ff' // 高亮颜色
              }
            }
          },
          {
            name: '已完成',
            type: 'bar',
            data: completedCounts,
            itemStyle: {
              color: '#67C23A'
            },
            stack: 'status',
            emphasis: {
              itemStyle: {
                color: '#85ce61' // 高亮颜色
              }
            }
          },
          {
            name: '进行中',
            type: 'bar',
            data: activeCounts,
            itemStyle: {
              color: '#E6A23C'
            },
            stack: 'status',
            emphasis: {
              itemStyle: {
                color: '#ebb563' // 高亮颜色
              }
            }
          }
        ]
      }

      this.charts.dailyChart.setOption(option)
    },
    initVehicleTypeChart() {
      // 初始化车辆类型分布图表
      if (this.charts.vehicleTypeChart) {
        this.charts.vehicleTypeChart.dispose()
      }

      const chartDom = this.$refs.vehicleTypeChart
      this.charts.vehicleTypeChart = echarts.init(chartDom)

      const data = this.stats.vehicle_types.map(item => ({
        name: item.type,
        value: item.count
      }))

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '车辆类型',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      }

      this.charts.vehicleTypeChart.setOption(option)
    },
    initParkingLotChart() {
      // 初始化停车场使用率图表
      if (this.charts.parkingLotChart) {
        this.charts.parkingLotChart.dispose()
      }

      const chartDom = this.$refs.parkingLotChart
      this.charts.parkingLotChart = echarts.init(chartDom)

      const parkingLots = this.stats.parking_lots.map(item => item.name)
      const utilizationRates = this.stats.parking_lots.map(item => item.utilization_rate)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: '{b}: {c}%'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: parkingLots,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          name: '使用率 (%)',
          max: 100
        },
        series: [
          {
            name: '使用率',
            type: 'bar',
            data: utilizationRates,
            itemStyle: {
              color: function(params) {
                // 根据使用率设置不同的颜色
                const value = params.value
                if (value < 30) {
                  return '#67C23A' // 绿色，使用率低
                } else if (value < 70) {
                  return '#E6A23C' // 黄色，使用率中等
                } else {
                  return '#F56C6C' // 红色，使用率高
                }
              }
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%'
            }
          }
        ]
      }

      this.charts.parkingLotChart.setOption(option)
    },
    resizeCharts() {
      // 重新调整所有图表大小
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.resize()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.parking-statistics {
  .statistics-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-controls {
        display: flex;
        gap: 10px;
      }
    }

    .overview-cards {
      margin-bottom: 20px;

      .stat-card {
        display: flex;
        align-items: center;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        margin-bottom: 15px;
        border-left: 4px solid;

        .stat-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;

          i {
            color: white;
            font-size: 20px;
          }
        }

        .stat-content {
          flex: 1;

          .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #303133;
          }

          .stat-label {
            font-size: 14px;
            color: #909399;
            margin-top: 5px;
          }
        }
      }
    }

    .charts-container {
      .chart-wrapper {
        background-color: #f5f7fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;

        h3 {
          margin-top: 0;
          margin-bottom: 15px;
          font-size: 16px;
          color: #303133;
        }

        .chart {
          height: 300px;
          width: 100%;
        }
      }
    }
  }
}
</style>
