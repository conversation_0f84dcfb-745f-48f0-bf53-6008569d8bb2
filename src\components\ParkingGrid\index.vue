<template>
  <div class="parking-grid-component">
    <div class="grid-header">
      <div class="title-section">
        <h3 v-if="parkingLotName">{{ parkingLotName }} - 车位状态</h3>
        <h3 v-else>停车场车位</h3>
        <div class="legend">
          <div class="legend-item">
            <div class="space-indicator available"></div>
            <span>空闲</span>
          </div>
          <div class="legend-item">
            <div class="space-indicator occupied"></div>
            <span>已占用</span>
          </div>
          <div class="legend-item">
            <div class="space-indicator maintenance"></div>
            <span>维护中</span>
          </div>
        </div>
      </div>

      <div class="filter-section">
        <div class="controls">
          <el-button size="mini" icon="el-icon-refresh" @click="refreshSpaces">刷新</el-button>
          <el-button-group>
            <el-button size="mini" icon="el-icon-zoom-in" @click="zoomIn"></el-button>
            <el-button size="mini" icon="el-icon-zoom-out" @click="zoomOut"></el-button>
            <el-button size="mini" icon="el-icon-refresh-right" @click="resetZoom"></el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <div class="grid-container" ref="container">
      <div v-if="loading" class="loading-overlay">
        <i class="el-icon-loading"></i>
        <span>加载车位信息...</span>
      </div>

      <div v-if="!loading && filteredSpaces.length === 0" class="no-spaces">
        <el-empty description="没有可用车位" :image-size="100">
          <template #description>
            <p>当前停车场没有符合条件的车位</p>
          </template>
          <el-button type="primary" size="small" @click="refreshSpaces">刷新</el-button>
        </el-empty>
      </div>

      <div
        v-else
        class="parking-lot-grid"
        :style="{ transform: `scale(${zoomLevel})` }"
      >
        <div
          v-for="space in filteredSpaces"
          :key="space.id"
          class="parking-space"
          :class="getSpaceClasses(space)"
          @click="handleSpaceClick(space)"
        >
          <div class="space-number">{{ space.space_number }}</div>
          <div class="space-type-icon">{{ getSpaceTypeIcon(space) }}</div>
          <div v-if="space.status === 1" class="vehicle-info">
            {{ getVehicleInfo(space) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 车位详情对话框 -->
    <el-dialog
      :title="`车位详情 - ${currentSpace ? currentSpace.space_number : ''}`"
      :visible.sync="spaceDialogVisible"
      width="400px"
      custom-class="space-dialog"
    >
      <div v-if="currentSpace" class="space-details">
        <div class="space-info">
          <div class="info-row">
            <span class="label">车位编号:</span>
            <span class="value">{{ currentSpace.space_number }}</span>
          </div>
          <div class="info-row">
            <span class="label">车位类型:</span>
            <span class="value">{{ getSpaceTypeName(currentSpace.type) }}</span>
          </div>
          <div class="info-row">
            <span class="label">当前状态:</span>
            <span class="value" :class="getStatusClass(currentSpace.status)">
              {{ getStatusText(currentSpace.status) }}
            </span>
          </div>

          <template v-if="currentSpace.status === 1 && currentSpace.vehicle">
            <div class="vehicle-section">
              <div class="section-title">车辆信息</div>
              <div class="info-row">
                <span class="label">车牌号:</span>
                <span class="value">{{ currentSpace.vehicle.bike_number }}</span>
              </div>
              <div class="info-row">
                <span class="label">品牌:</span>
                <span class="value">{{ currentSpace.vehicle.brand }}</span>
              </div>
              <div class="info-row">
                <span class="label">颜色:</span>
                <span class="value">{{ currentSpace.vehicle.color }}</span>
              </div>
            </div>
          </template>
        </div>

        <!-- 操作按钮 -->
        <div class="space-actions" v-if="currentSpace.status === 0">
          <el-button
            type="primary"
            icon="el-icon-parking"
            @click="startParking(currentSpace)"
            class="action-button"
          >开始停车</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 停车对话框 -->
    <el-dialog
      :title="`开始停车 - ${parkingLotName}`"
      :visible.sync="parkingDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      custom-class="parking-dialog simplified"
    >
      <div class="dialog-header simplified">
        <div class="dialog-title">
          <i class="el-icon-s-order"></i>
          <span>创建停车记录</span>
        </div>
        <div class="dialog-subtitle">请选择车辆并填写停车信息</div>
      </div>

      <!-- 简化版停车卡片 -->
      <div class="simplified-parking-card">
        <!-- 停车场信息卡片 -->
        <div class="location-card">
          <div class="card-title">
            <i class="el-icon-location"></i>
            <span>停车位置</span>
          </div>
          <div class="card-content">
            <div class="info-row">
              <span class="info-label">停车场</span>
              <span class="info-value">{{ parkingLotName }}</span>
            </div>
            <div class="info-row" v-if="currentSpace">
              <span class="info-label">车位号</span>
              <span class="info-value highlight">{{ currentSpace.space_number }}</span>
            </div>
          </div>
        </div>

        <!-- 车辆信息和停车表单 -->
        <el-form ref="parkingForm" :model="parkingForm" :rules="parkingRules" label-width="100px" class="parking-form">
          <!-- 车辆信息区域 -->
          <div class="vehicle-section">
            <div class="section-title">
              <i class="el-icon-bicycle"></i> 车辆信息
            </div>

            <!-- 车辆选择 -->
            <el-form-item label="选择车辆" prop="vehicle_id">
              <el-select
                v-model="parkingForm.vehicle_id"
                filterable
                placeholder="请选择车辆"
                style="width: 100%"
                @change="handleVehicleChange"
                :loading="vehiclesLoading"
              >
                <el-option
                  v-for="vehicle in vehicles"
                  :key="vehicle.id || vehicle.b_id"
                  :label="getVehicleLabel(vehicle)"
                  :value="vehicle.id || vehicle.b_id"
                >
                  <div class="vehicle-option">
                    <span class="vehicle-number">{{ vehicle.bike_number || vehicle.b_num }}</span>
                    <span class="vehicle-info">
                      {{ vehicle.brand }} {{ vehicle.color }}
                    </span>
                  </div>
                </el-option>

                <!-- 没有车辆时显示提示 -->
                <template v-if="vehicles.length === 0 && !vehiclesLoading">
                  <el-option disabled value="" label="没有可用的车辆">
                    <div class="no-vehicle-tip">
                      <i class="el-icon-warning-outline"></i> 没有可用的车辆
                    </div>
                  </el-option>
                </template>
              </el-select>
            </el-form-item>
          </div>

          <!-- 停车信息区域 -->
          <div class="parking-info-section">
            <div class="section-title">
              <i class="el-icon-time"></i> 停车信息
            </div>

            <!-- 预计停车时长 -->
            <el-form-item label="预计时长" prop="estimated_duration">
              <el-select v-model="parkingForm.estimated_duration" placeholder="选择预计停车时长">
                <el-option label="1小时" value="1" />
                <el-option label="2小时" value="2" />
                <el-option label="3小时" value="3" />
                <el-option label="4小时" value="4" />
                <el-option label="5小时" value="5" />
                <el-option label="6小时" value="6" />
                <el-option label="12小时" value="12" />
                <el-option label="1天" value="24" />
                <el-option label="2天" value="48" />
                <el-option label="3天" value="72" />
              </el-select>
            </el-form-item>

            <!-- 备注 -->
            <el-form-item label="备注" prop="notes">
              <el-input
                v-model="parkingForm.notes"
                type="textarea"
                :rows="2"
                placeholder="可选填写备注信息"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="parkingDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="parkingLoading" @click="submitParkingForm">确认停车</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getParkingSpaces } from '@/api/parkinglot'
import { getBikeById, getBikesByUser } from '@/api/bike'
import { checkVehicleActiveParking } from '@/api/parking'
import request from '@/utils/request'
import socketService from '@/utils/socket'
import { Empty } from 'element-ui'

export default {
  name: 'ParkingGrid',
  components: {
    'el-empty': Empty
  },
  props: {
    parkingLotId: {
      type: [Number, String],
      required: true
    },
    parkingLotName: {
      type: String,
      default: ''
    },
    initialSpaces: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      spaces: this.initialSpaces || [],
      loading: false,
      zoomLevel: 1,
      spaceTypeFilter: 0, // 0表示全部，1-3表示不同类型的车位
      spaceDialogVisible: false,
      currentSpace: null,
      selectedSpaceId: null,
      vehicleCache: {}, // 缓存车辆信息

      // 停车操作相关数据
      parkingDialogVisible: false,
      parkingLoading: false,
      vehicles: [],
      vehiclesLoading: false,
      selectedVehicle: null,

      // 停车表单
      parkingForm: {
        vehicle_id: '',
        parking_lot_id: this.parkingLotId,
        parking_space_id: '',
        estimated_duration: '3',
        notes: ''
      },
      parkingRules: {
        vehicle_id: [
          { required: true, message: '请选择车辆', trigger: 'change' }
        ],
        estimated_duration: [
          { required: true, message: '请选择预计停车时长', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'isAdmin'
    ]),
    filteredSpaces() {
      // 过滤掉充电车位（type=2）
      return this.spaces.filter(space => space.type !== 2)
    }
  },
  created() {
    // 初始化时自动加载车位数据
    this.fetchSpaces()
  },
  mounted() {
    // 订阅停车场更新
    this.subscribeToUpdates()
  },
  beforeDestroy() {
    // 取消订阅停车场更新
    this.unsubscribeFromUpdates()
  },
  watch: {
    parkingLotId: {
      immediate: true,
      handler(val, oldVal) {
        if (val) {
          this.parkingForm.parking_lot_id = val

          // 当停车场ID变化时重新加载车位数据
          if (val !== oldVal && oldVal !== undefined) {
            console.log('停车场ID变化，重新加载车位数据')
            this.fetchSpaces()
          }
        }
      }
    },
    currentSpace: {
      handler(val) {
        if (val) {
          this.parkingForm.parking_space_id = val.id
        }
      }
    }
  },
  methods: {
    // 获取车位数据
    fetchSpaces() {
      if (!this.parkingLotId) {
        console.error('未提供停车场ID，无法获取车位数据')
        return
      }

      this.loading = true
      getParkingSpaces(this.parkingLotId, {
        limit: 100,
        // 如果是管理员，获取所有状态的车位，否则只获取空闲车位
        status: this.isAdmin ? '' : 0
      }).then(response => {
        let data = response
        if (response.data) {
          data = response.data
        }

        if (Array.isArray(data)) {
          this.spaces = data
        } else if (data.spaces) {
          this.spaces = data.spaces
        } else if (data.items) {
          this.spaces = data.items
        } else if (data.data && Array.isArray(data.data)) {
          this.spaces = data.data
        } else {
          console.error('无法识别的车位数据格式:', data)
          this.spaces = []
        }

        // 获取已占用车位的车辆信息
        this.fetchVehicleInfo()
      }).catch(error => {
        console.error('获取车位列表失败', error)
        this.$message.error('获取车位列表失败')
      }).finally(() => {
        this.loading = false
      })
    },

    // 刷新车位数据
    refreshSpaces() {
      this.fetchSpaces()
    },

    // 获取已占用车位的车辆信息
    async fetchVehicleInfo() {
      const occupiedSpaces = this.spaces.filter(space =>
        space.status === 1 && space.current_vehicle_id
      )

      for (const space of occupiedSpaces) {
        try {
          // 如果已经有缓存的车辆信息，直接使用
          if (this.vehicleCache[space.current_vehicle_id]) {
            space.vehicle = this.vehicleCache[space.current_vehicle_id]
            continue
          }

          // 获取车辆信息
          const response = await getBikeById(space.current_vehicle_id)
          if (response && response.data) {
            space.vehicle = response.data
            // 缓存车辆信息
            this.vehicleCache[space.current_vehicle_id] = response.data
          }
        } catch (error) {
          console.error(`获取车位 ${space.id} 的车辆信息失败:`, error)
        }
      }
    },

    // 获取车位类名
    getSpaceClasses(space) {
      return {
        'available': space.status === 0,
        'occupied': space.status === 1,
        'maintenance': space.status === 2,
        [`type-${space.type}`]: true,
        'selected': this.currentSpace && this.currentSpace.id === space.id
      }
    },

    // 获取车辆信息
    getVehicleInfo(space) {
      if (!space.vehicle) {
        return '加载中...'
      }
      return space.vehicle.bike_number
    },

    // 获取车位类型名称
    getSpaceTypeName(type) {
      const typeMap = {
        1: '普通车位',
        2: '残疾人车位',
        3: '充电车位'
      }
      return typeMap[type] || '未知类型'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '空闲',
        1: '已占用',
        2: '维护中'
      }
      return statusMap[status] || '未知状态'
    },

    // 获取状态类名
    getStatusClass(status) {
      const classMap = {
        0: 'status-available',
        1: 'status-occupied',
        2: 'status-maintenance'
      }
      return classMap[status] || ''
    },

    // 获取车位类型图标
    getSpaceTypeIcon(space) {
      const iconMap = {
        0: '🚕', // 小型车图标
        1: '♿', // 轮椅图标
        2: '🏍' // 摩托车图标
      }
      return iconMap[space.type] || '🚕'
    },

    // 处理车位点击
    handleSpaceClick(space) {
      // 只处理空闲车位的点击，且不处理充电车位
      if (space.type === 2) {
        this.$message.warning('充电车位请在充电中心使用')
        return
      }

      if (space.status !== 0) {
        if (space.status === 1) {
          this.$message.warning('该车位已被占用')
        } else if (space.status === 2) {
          this.$message.warning('该车位正在维护中')
        }
        return
      }

      // 直接开始停车流程
      this.currentSpace = space
      this.selectedSpaceId = space.id
      this.openParkingDialog()
    },

    // 开始停车
    startParking(space) {
      if (space.status !== 0) {
        this.$message.warning('只能在空闲车位停车')
        return
      }

      this.selectedSpaceId = space.id
      this.currentSpace = space
      this.spaceDialogVisible = false

      // 打开停车对话框
      this.openParkingDialog()
    },

    // 打开停车对话框
    openParkingDialog() {
      // 重置表单
      this.resetParkingForm()

      // 获取用户车辆
      this.fetchUserVehicles()

      // 打开对话框
      this.parkingDialogVisible = true
    },

    // 重置停车表单
    resetParkingForm() {
      this.parkingForm = {
        vehicle_id: '',
        parking_lot_id: this.parkingLotId,
        parking_space_id: this.selectedSpaceId || '',
        estimated_duration: '3',
        notes: ''
      }

      // 重置选中的车辆
      this.selectedVehicle = null

      // 如果表单引用存在，重置字段
      this.$nextTick(() => {
        if (this.$refs.parkingForm) {
          this.$refs.parkingForm.resetFields()
        }
      })
    },

    // 获取用户车辆
    fetchUserVehicles() {
      this.vehiclesLoading = true

      // 获取当前用户ID
      const userId = this.userId || parseInt(localStorage.getItem('userId'))

      // 获取用户角色
      const isAdmin = this.isAdmin

      console.log('获取用户车辆，用户ID:', userId, '是管理员:', isAdmin)

      // 构建请求参数
      const params = {
        _t: new Date().getTime(), // 添加时间戳，确保每次请求都是最新的
        limit: 100
      }

      // 如果是管理员，不添加用户ID过滤，获取所有车辆
      if (!isAdmin) {
        params.user_id = userId
      }

      // 使用API获取车辆数据
      getBikesByUser(userId)
        .then(async response => {
          console.log('获取用户车辆响应:', response)

          try {
            let allVehicles = []

            // 处理不同的响应格式
            if (response.data && response.data.bikes) {
              allVehicles = response.data.bikes
            } else if (response.bikes) {
              allVehicles = response.bikes
            } else if (Array.isArray(response)) {
              allVehicles = response
            } else if (response.data && Array.isArray(response.data)) {
              allVehicles = response.data
            }

            console.log('获取到的车辆总数:', allVehicles.length, '辆')

            // 过滤出没有进行中停车记录的车辆
            this.filterAvailableVehicles(allVehicles).then(availableVehicles => {
              this.vehicles = availableVehicles
              console.log('可用于停车的车辆数量:', this.vehicles.length, '辆')
              this.vehiclesLoading = false
            })
          } catch (error) {
            console.error('处理车辆数据时出错:', error)
            this.vehiclesLoading = false
            this.$message.error('获取车辆数据失败')
          }
        })
        .catch(error => {
          console.error('获取用户车辆失败:', error)
          this.vehiclesLoading = false
          this.$message.error('获取车辆数据失败')

          // 如果是管理员，提供测试数据
          if (isAdmin) {
            this.vehicles = [
              {
                id: 7,
                bike_number: 'TEST-20250416103429',
                brand: '测试品牌',
                color: '蓝色',
                type: '电动车',
                status: 'available'
              }
            ]
          }
        })
    },

    // 过滤出没有进行中停车记录的车辆
    async filterAvailableVehicles(vehicles) {
      if (!vehicles || vehicles.length === 0) {
        return []
      }

      console.log('开始过滤可用于停车的车辆，原始总数:', vehicles.length)

      // 获取用户角色
      const isAdmin = this.isAdmin

      // 如果是管理员，不过滤车辆，允许停放所有车辆
      if (isAdmin) {
        console.log('管理员用户，不过滤车辆，允许停放所有车辆')
        return vehicles
      }

      // 首先过滤出状态为可用的车辆
      const statusFilteredVehicles = vehicles.filter(vehicle => {
        // 检查车辆状态，只保留可用的车辆
        const status = vehicle.status || ''
        return status === '' || status === '可用' || status === 'available' || status === 1
      })

      console.log('状态过滤后的车辆数量:', statusFilteredVehicles.length)

      // 然后过滤出没有进行中停车记录的车辆
      const availableVehicles = []

      for (const vehicle of statusFilteredVehicles) {
        try {
          // 检查车辆是否有进行中的停车记录
          const response = await checkVehicleActiveParking(vehicle.id || vehicle.b_id)

          if (response && response.data && response.data.hasActiveParking) {
            // 车辆有进行中的停车记录，不添加到可用车辆列表
            console.log(`车辆 ${vehicle.bike_number || vehicle.b_num} 有进行中的停车记录，不可用于停车`)
          } else {
            // 车辆没有进行中的停车记录，添加到可用车辆列表
            availableVehicles.push(vehicle)
          }
        } catch (error) {
          console.error(`检查车辆 ${vehicle.bike_number || vehicle.b_num} 的停车状态失败:`, error)
          // 出错时仍然添加到可用车辆列表，以便用户可以尝试停车
          availableVehicles.push(vehicle)
        }
      }

      console.log('最终可用于停车的车辆数量:', availableVehicles.length)
      return availableVehicles
    },

    // 处理车辆选择变化
    handleVehicleChange(vehicleId) {
      if (!vehicleId) {
        this.selectedVehicle = null
        return
      }

      // 查找选中的车辆
      this.selectedVehicle = this.vehicles.find(v => (v.id || v.b_id) == vehicleId)
      console.log('选中的车辆:', this.selectedVehicle)
    },

    // 获取车辆标签显示
    getVehicleLabel(vehicle) {
      if (!vehicle) return ''

      const bikeNumber = vehicle.bike_number || vehicle.b_num || ''
      const brand = vehicle.brand || ''
      const color = vehicle.color || ''

      return `${bikeNumber} (${brand} ${color})`.trim()
    },

    // 获取预计时长文本
    getEstimatedDurationText(duration) {
      const durationMap = {
        '1': '1小时',
        '2': '2小时',
        '3': '3小时',
        '4': '4小时',
        '5': '5小时',
        '6': '6小时',
        '12': '12小时',
        '24': '1天',
        '48': '2天',
        '72': '3天'
      }

      return durationMap[duration] || `${duration}小时`
    },

    // 提交停车表单
    submitParkingForm() {
      this.$refs.parkingForm.validate(valid => {
        if (!valid) {
          this.$message.warning('请填写必要的表单字段')
          return
        }

        // 获取当前用户ID
        const userId = this.userId || parseInt(localStorage.getItem('userId'))

        // 获取选中的车辆
        const selectedVehicle = this.selectedVehicle
        if (!selectedVehicle) {
          this.$message.warning('请选择车辆')
          return
        }

        // 确保所有必要字段都存在且类型正确
        const formData = {
          // 基本停车信息
          vehicle_id: parseInt(this.parkingForm.vehicle_id, 10),
          parking_lot_id: parseInt(this.parkingForm.parking_lot_id, 10),
          parking_space_id: parseInt(this.parkingForm.parking_space_id, 10),
          user_id: userId,

          // 预计时长
          estimated_duration: parseInt(this.parkingForm.estimated_duration, 10)
        }

        // 打印详细的表单数据
        console.log('提交的停车表单数据:', formData)

        // 如果有备注信息，添加到表单数据中
        if (this.parkingForm.notes && this.parkingForm.notes.trim()) {
          formData.notes = this.parkingForm.notes.trim()
        }

        // 显示确认对话框
        this.$confirm(`确认创建停车记录？<br><br>
          <strong>车辆信息：</strong> ${this.getVehicleLabel(selectedVehicle)}<br>
          <strong>停车场地：</strong> ${this.parkingLotName}<br>
          <strong>车位编号：</strong> ${this.currentSpace ? this.currentSpace.space_number : ''}<br>
          <strong>预计时长：</strong> ${this.getEstimatedDurationText(this.parkingForm.estimated_duration)}
        `, '停车确认', {
          confirmButtonText: '确认停车',
          cancelButtonText: '取消',
          type: 'info',
          dangerouslyUseHTMLString: true
        }).then(() => {
          this.parkingLoading = true
          request({
            url: '/api/parking-records',
            method: 'post',
            data: formData
          }).then(response => {
            console.log('停车记录创建成功响应:', response)
            this.$message.success('停车记录创建成功')
            this.parkingDialogVisible = false
            this.resetParkingForm()
            this.handleParkingSuccess()
          }).catch(error => {
            console.error('创建停车记录失败', error)
            let errorMsg = '创建停车记录失败'

            // 尝试获取详细错误信息
            if (error.response) {
              if (error.response.data && error.response.data.message) {
                errorMsg = error.response.data.message
              } else if (error.response.status === 409) {
                errorMsg = '车位已被占用，请选择其他车位'
              } else if (error.response.status === 400) {
                errorMsg = '请求参数错误，请检查输入'
              } else if (error.response.status === 401) {
                errorMsg = '登录已过期，请重新登录'
              } else if (error.response.status === 403) {
                errorMsg = '没有权限执行此操作'
              }
            }

            this.$message.error(errorMsg)

            // 如果是车位已被占用，刷新车位数据
            if (error.response && error.response.status === 409) {
              this.refreshSpaces()
            }
          }).finally(() => {
            this.parkingLoading = false
          })
        }).catch(() => {
          // 用户取消操作
          this.$message.info('已取消停车操作')
        })
      })
    },

    // 处理停车成功
    handleParkingSuccess() {
      this.$message.success('停车成功')
      this.refreshSpaces()
      this.$emit('parking-success')
    },

    // 处理筛选变化
    handleFilterChange() {
      // 筛选是本地进行的，不需要重新请求数据
    },

    // 缩放控制
    zoomIn() {
      if (this.zoomLevel < 2) {
        this.zoomLevel += 0.1
      }
    },

    zoomOut() {
      if (this.zoomLevel > 0.5) {
        this.zoomLevel -= 0.1
      }
    },

    resetZoom() {
      this.zoomLevel = 1
    },

    // 订阅停车场更新
    subscribeToUpdates() {
      // 订阅停车场
      socketService.subscribeParkingLot(this.parkingLotId)

      // 监听车位更新事件
      socketService.on('parking_space_updated', this.handleSpaceUpdate)
    },

    // 取消订阅停车场更新
    unsubscribeFromUpdates() {
      // 取消订阅停车场
      socketService.unsubscribeParkingLot(this.parkingLotId)

      // 移除事件监听
      socketService.off('parking_space_updated', this.handleSpaceUpdate)
    },

    // 处理车位更新事件
    handleSpaceUpdate(data) {
      console.log('收到车位更新:', data)

      // 确保是当前停车场的更新
      if (data.parking_lot_id != this.parkingLotId) {
        return
      }

      // 更新车位状态
      const spaceIndex = this.spaces.findIndex(space => space.id == data.id)
      if (spaceIndex !== -1) {
        // 更新车位状态
        this.spaces[spaceIndex].status = data.status

        // 如果车位变为已占用，更新车辆信息
        if (data.status === 1 && data.current_vehicle_id) {
          this.spaces[spaceIndex].current_vehicle_id = data.current_vehicle_id
          // 获取车辆信息
          this.fetchVehicleInfo()
        } else if (data.status === 0) {
          // 如果车位变为空闲，清除车辆信息
          this.spaces[spaceIndex].current_vehicle_id = null
          this.spaces[spaceIndex].vehicle = null
        }

        // 如果当前选中的车位被更新，也更新详情对话框中的信息
        if (this.currentSpace && this.currentSpace.id == data.id) {
          this.currentSpace = { ...this.spaces[spaceIndex] }
        }

        // 显示通知
        const statusText = this.getStatusText(data.status)
        this.$notify({
          title: '车位状态更新',
          message: `车位 ${this.spaces[spaceIndex].space_number} 状态已更新为 ${statusText}`,
          type: data.status === 0 ? 'success' : data.status === 1 ? 'warning' : 'info',
          duration: 3000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.parking-grid-component {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
  margin-bottom: 20px;

  .grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;

    .title-section {
      h3 {
        margin: 0 0 10px 0;
        font-size: 18px;
        color: #303133;
      }

      .legend {
        display: flex;
        gap: 15px;

        .legend-item {
          display: flex;
          align-items: center;
          font-size: 14px;

          .space-indicator {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            margin-right: 5px;

            &.available {
              background-color: #67C23A;
            }

            &.occupied {
              background-color: #F56C6C;
            }

            &.maintenance {
              background-color: #E6A23C;
            }
          }
        }
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 10px;

      .controls {
        display: flex;
        gap: 5px;
      }
    }
  }

  .grid-container {
    position: relative;
    min-height: 200px;
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    padding: 15px;
    background-color: #F5F7FA;
    overflow: auto;
    max-height: 400px;

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.7);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 10;

      i {
        font-size: 32px;
        color: #409EFF;
        margin-bottom: 10px;
      }
    }

    .no-spaces {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }

    .parking-lot-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      transform-origin: top left;
      transition: transform 0.3s ease;
    }
  }

  .parking-space {
    width: 100px;
    height: 150px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &.selected {
      border: 2px solid #409EFF;
      box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
    }

    &.available {
      background-color: #67C23A;
      color: white;
    }

    &.occupied {
      background-color: #F56C6C;
      color: white;
    }

    &.maintenance {
      background-color: #E6A23C;
      color: white;

      &::after {
        content: '维护中';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-30deg);
        font-size: 18px;
        font-weight: bold;
        white-space: nowrap;
      }
    }

    // 不同类型车位的样式
    &.type-0 {
      border: 2px solid #409EFF;
    }

    &.type-1 {
      border: 2px solid #67C23A;
    }

    &.type-2 {
      border: 2px solid #E6A23C;
    }

    .space-number {
      font-weight: 600;
      font-size: 14px;
      text-align: center;
      padding: 4px;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 4px;
      color: #303133;
    }

    .space-type-icon {
      position: absolute;
      top: 10px;
      right: 10px;
      font-size: 18px;
    }

    .vehicle-info {
      font-size: 12px;
      text-align: center;
      padding: 4px;
      border-radius: 4px;
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
}

// 车位详情对话框样式
.space-dialog {
  .space-details {
    .space-info {
      margin-bottom: 20px;

      .info-row {
        display: flex;
        margin-bottom: 10px;

        .label {
          width: 100px;
          color: #606266;
        }

        .value {
          flex: 1;
          font-weight: 500;

          &.status-available {
            color: #67C23A;
          }

          &.status-occupied {
            color: #F56C6C;
          }

          &.status-maintenance {
            color: #E6A23C;
          }
        }
      }

      .vehicle-section {
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #EBEEF5;

        .section-title {
          font-weight: 600;
          margin-bottom: 10px;
          color: #303133;
        }
      }
    }

    .space-actions {
      display: flex;
      justify-content: center;
      margin-top: 20px;

      .action-button {
        min-width: 120px;
      }
    }
  }
}

/* 停车对话框样式 */
.parking-dialog.simplified {
  .dialog-header {
    margin-bottom: 20px;

    .dialog-title {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 5px;

      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }

    .dialog-subtitle {
      color: #909399;
      font-size: 14px;
    }
  }

  .simplified-parking-card {
    .location-card {
      background-color: #f5f7fa;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;

      .card-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;

        i {
          margin-right: 8px;
          color: #409EFF;
        }
      }

      .card-content {
        .info-row {
          display: flex;
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }

          .info-label {
            width: 80px;
            color: #606266;
          }

          .info-value {
            flex: 1;
            font-weight: 500;

            &.highlight {
              color: #409EFF;
              font-weight: 600;
            }
          }
        }
      }
    }

    .parking-form {
      .section-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: #409EFF;
        }
      }

      .vehicle-section, .parking-info-section {
        margin-bottom: 20px;
      }

      .vehicle-option {
        display: flex;
        flex-direction: column;

        .vehicle-number {
          font-weight: 500;
        }

        .vehicle-info {
          font-size: 12px;
          color: #909399;
        }
      }

      .no-vehicle-tip {
        color: #E6A23C;
        text-align: center;
        padding: 10px 0;

        i {
          margin-right: 5px;
        }
      }
    }
  }
}
</style>
