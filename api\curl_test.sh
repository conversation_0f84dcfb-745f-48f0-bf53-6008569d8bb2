#!/bin/bash

# 测试违规记录创建API
echo "测试违规记录创建API..."

# 创建测试数据
TEST_DATA='{
  "bike_number": "TEST-CURL",
  "user_id": 1,
  "violation_type": "违规停车",
  "violation_time": "2025-04-23 15:50:00",
  "location": "校园南门",
  "description": "这是一条使用curl测试的违规记录"
}'

# 模拟一个有效的JWT令牌
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NTM4OTMwMywianRpIjoiYzAxODY5ODEtYjJmOC00ZWZiLWE0NjQtNTIzZjYxMjA3MzI2IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5IjoxLCJuYmYiOjE3NDUzODkzMDMsImNzcmYiOiI2ODA3NTc5MC1mNDJiLTQxMjItYjEzNy0wNmQ1ZGI5ZmZkODgiLCJleHAiOjE3NDU0NzU3MDMsInJvbGUiOiJhZG1pbiJ9.BxAAl1tWeBRNpZJlzyQy0QGtALZXHD46UBaNHiK1TzA"

# 发送请求
echo "发送请求..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "$TEST_DATA" \
  http://127.0.0.1:5000/api/violations/records

echo -e "\n测试完成"
