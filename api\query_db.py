import sqlite3

# 连接到数据库
conn = sqlite3.connect('sys.db')
cursor = conn.cursor()

# 查询北校区停车场
print("查询北校区停车场:")
cursor.execute("SELECT id, name FROM parking_lots WHERE name LIKE '%北校区%'")
parking_lots = cursor.fetchall()
for lot in parking_lots:
    print(f"ID: {lot[0]}, 名称: {lot[1]}")

# 如果找到停车场，查询其充电车位
if parking_lots:
    lot_id = parking_lots[0][0]
    print(f"\n查询停车场ID {lot_id} 的充电车位 (type=3):")
    cursor.execute("SELECT * FROM parking_spaces WHERE parking_lot_id = ? AND type = 3", (lot_id,))
    columns = [col[0] for col in cursor.description]
    print(f"列名: {columns}")
    
    spaces = cursor.fetchall()
    for space in spaces:
        print(space)
    
    print(f"\n查询停车场ID {lot_id} 的充电车位 (type=2):")
    cursor.execute("SELECT * FROM parking_spaces WHERE parking_lot_id = ? AND type = 2", (lot_id,))
    spaces = cursor.fetchall()
    for space in spaces:
        print(space)

# 关闭连接
conn.close()
