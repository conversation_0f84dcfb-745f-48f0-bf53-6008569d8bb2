import os
import logging
from logging.handlers import RotatingFileHandler
import datetime
from flask import Flask, request, jsonify, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_marshmallow import Marshmallow
from flask_cors import CORS
from flask_migrate import Migrate
from flask_jwt_extended import J<PERSON>TManager
from flask_socketio import Socket<PERSON>
from .config import Config, log_dir
from .utils.log import DayRotatingHandler
from .utils.response import api_response
from datetime import timedelta
import json

db = SQLAlchemy()
jwt = JWTManager()  # 与db放在一起
cors = CORS()
migrate = Migrate()
marshmallow = Marshmallow()
# 初始化SocketIO，但不绑定到应用，在create_app中绑定
socketio = SocketIO()  # 在create_app中完成完整配置

app = None  # 将在create_app中初始化

def create_app(config_name='default'):
  global app
  app = Flask(__name__, static_folder='../../dist', template_folder="../../dist", static_url_path='/')
  # app = Flask(__name__)
  app.config.from_object(Config)  # 直接使用Config类，而不是从config_name获取

  # 配置文件上传
  app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 限制上传文件大小为16MB
  app.config['UPLOAD_FOLDER'] = os.path.join(app.root_path, 'static', 'uploads')

  # 确保上传目录存在
  os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

  # 注册静态文件路由
  @app.route('/uploads/<path:filename>')
  def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

  # 在插件注册前初始化数据库
  db.init_app(app)
  migrate.init_app(app, db)

  # 设置JWT配置
  app.config["JWT_SECRET_KEY"] = "f8s9df89s7df9s87df98s7df98sd7f9s8df"  # 在生产环境中应使用环境变量
  app.config["JWT_IDENTITY_CLAIM"] = "identity"  # 使用"identity"作为用户标识声明
  app.config["JWT_ACCESS_TOKEN_EXPIRES"] = timedelta(hours=24)  # 设置令牌24小时过期
  app.config["JWT_TOKEN_LOCATION"] = ["headers", "query_string"]  # 允许从请求头和查询参数获取令牌
  app.config["JWT_HEADER_NAME"] = "Authorization"  # 请求头名称
  app.config["JWT_HEADER_TYPE"] = "Bearer"  # 令牌类型前缀
  app.config["JWT_QUERY_STRING_NAME"] = "token"  # 查询参数名称
  app.config["JWT_ERROR_MESSAGE_KEY"] = "message"  # 错误消息的键名
  app.config["JWT_BLACKLIST_ENABLED"] = False  # 暂不启用黑名单功能

  # 注册插件
  register_plugins(app)

  # 注册JWT回调函数
  register_jwt_callbacks(app)

  # 注册蓝图
  register_blueprints(app)

  # 注册日志处理器
  register_logging(app)

  # 注册错误处理函数
  register_errors(app)

  # 查看是否有响应拦截器
  print("检查应用是否有响应拦截器...")
  if hasattr(app, 'after_request_funcs'):
    print(f"after_request_funcs: {app.after_request_funcs}")

  app.logger.info('Flask Rest Api startup')
  print('flask_api __init__.py[{}] {}() INFO: Flask Rest Api startup'.format(
    __file__.split('/')[-1].split('\\')[-1].split('.')[0], create_app.__name__
  ))

  return app


def register_logging(app):
  app.logger.name = 'flask_api'
  log_level = app.config.get("LOG_LEVEL", logging.INFO)
  cls_handler = logging.StreamHandler()
  log_file = os.path.join(log_dir, datetime.date.today().strftime("%Y-%m-%d.log"))
  file_handler = DayRotatingHandler(log_file, mode="a", encoding="utf-8")

  logging.basicConfig(level=log_level,
                      format="%(asctime)s %(name)s "
                             "%(filename)s[%(lineno)d] %(funcName)s() %(levelname)s: %(message)s",
                      datefmt="%Y/%m/%d %H:%M:%S",
                      handlers=[cls_handler, file_handler])

  if not app.debug and not app.testing:
    if app.config['LOG_TO_STDOUT']:
      stream_handler = logging.StreamHandler()
      stream_handler.setLevel(logging.INFO)
      app.logger.addHandler(stream_handler)
    else:
      if not os.path.exists('logs'):
        os.mkdir('logs')
      file_handler = RotatingFileHandler(os.path.join(log_dir, 'flask_api.log'), maxBytes=1024 * 1024 * 50,
                                         backupCount=5, encoding='utf-8')
      file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(name)s %(levelname)s: %(message)s '
        '[in %(pathname)s:%(lineno)d]'))

      file_handler.setLevel(logging.INFO)
      app.logger.addHandler(file_handler)

    app.logger.setLevel(logging.INFO)


def register_plugins(app):
  # 修正CORS配置格式
  cors.init_app(
        app,
        supports_credentials=True,
        resources={
            r"/*": {
                "origins": "*",
                "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                "allow_headers": ["Content-Type", "Authorization", "Accept", "X-Requested-With", "Cache-Control"]
            }
        }
  )

  # 添加CORS响应头
  @app.after_request
  def add_cors_headers(response):
      response.headers.add('Access-Control-Allow-Origin', '*')
      response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-Requested-With,Cache-Control')
      response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
      return response

  marshmallow.init_app(app)
  jwt.init_app(app)

  # 初始化SocketIO，并设置完整配置
  socketio.init_app(app, cors_allowed_origins="*", async_mode='eventlet', logger=True, engineio_logger=True, ping_timeout=60, ping_interval=25)

def register_jwt_callbacks(app):
  """注册JWT回调函数"""
  from flask import jsonify
  from flask_jwt_extended import JWTManager

  @jwt.user_identity_loader
  def user_identity_lookup(user):
    """
    用于将用户对象转换为JWT身份标识

    Args:
        user: 用户对象或用户ID

    Returns:
        int: 用户ID
    """
    # 如果user是整数，直接返回
    if isinstance(user, int):
      return user

    # 如果user是字典，尝试获取u_id或id
    if isinstance(user, dict):
      return user.get('u_id') or user.get('id')

    # 如果user是对象，尝试获取u_id或id属性
    if hasattr(user, 'u_id'):
      return user.u_id
    if hasattr(user, 'id'):
      return user.id

    # 默认返回None
    return None

  @jwt.user_lookup_loader
  def user_lookup_callback(_jwt_header, jwt_data):
    """
    用于根据JWT数据查找用户对象

    Args:
        _jwt_header: JWT头部
        jwt_data: JWT数据

    Returns:
        Users: 用户对象
    """
    from app.users.models import Users

    # 尝试从不同的字段获取用户标识
    identity = None

    # 首先尝试从标准的 'sub' 字段获取
    if "sub" in jwt_data:
        identity = jwt_data["sub"]
        # 如果 sub 是字符串类型，尝试转换为整数
        if isinstance(identity, str):
            try:
                identity = int(identity)
            except ValueError:
                print(f"[ERROR] 无法将 sub 字段转换为整数: {identity}")
                return None
    # 如果没有 'sub' 字段，尝试从 'identity' 字段获取
    elif "identity" in jwt_data:
        identity = jwt_data["identity"]
    # 如果都没有，记录错误并返回 None
    else:
        print(f"[ERROR] JWT 数据中没有找到用户标识字段: {jwt_data}")
        return None

    return Users.query.filter_by(u_id=identity).one_or_none()

  @jwt.expired_token_loader
  def expired_token_callback(_jwt_header, _jwt_data):
    """
    处理过期的令牌

    Args:
        _jwt_header: JWT头部
        _jwt_data: JWT数据

    Returns:
        tuple: (JSON响应, 状态码)
    """
    return jsonify({
      "code": 40100,
      "message": "令牌已过期，请重新登录",
      "status": "error"
    }), 401

  @jwt.invalid_token_loader
  def invalid_token_callback(error):
    """
    处理无效的令牌

    Args:
        error: 错误信息

    Returns:
        tuple: (JSON响应, 状态码)
    """
    return jsonify({
      "code": 40100,
      "message": "无效的令牌",
      "status": "error"
    }), 401

  @jwt.unauthorized_loader
  def unauthorized_callback(error):
    """
    处理未授权的请求

    Args:
        error: 错误信息

    Returns:
        tuple: (JSON响应, 状态码)
    """
    return jsonify({
      "code": 40100,
      "message": "请先登录",
      "status": "error"
    }), 401


def register_blueprints(app):
  # 注册蓝图之前先建立模型关系
  from app.users.models import Users
  from app.players.models import Players
  from app.players.routes import setup_users_model, init_auth_helpers

  # 设置模型引用
  setup_users_model(Users)

  # 注册蓝图
  from .users import users_bp, users
  app.register_blueprint(users_bp, url_prefix='/api')
  app.register_blueprint(users)  # 这个蓝图已经包含了url_prefix

  from .bikes import bikes_bp
  app.register_blueprint(bikes_bp, url_prefix='/api')

  from .players import players_bp
  app.register_blueprint(players_bp, url_prefix='/api')

  # 注册停车场管理相关蓝图
  from .parkinglots import parkinglots_bp
  app.register_blueprint(parkinglots_bp)

  from .parking_records import parking_records_bp
  app.register_blueprint(parking_records_bp, url_prefix='/api/parking-records')

  # 注册WebSocket蓝图
  try:
    from .websocket import websocket_bp
    app.register_blueprint(websocket_bp, url_prefix='/api/ws')
    app.logger.info('WebSocket蓝图注册成功')
  except ImportError as e:
    app.logger.error(f'WebSocket蓝图注册失败: {str(e)}')

  # 注册违规管理相关蓝图
  try:
    from .violations import violations_bp
    app.register_blueprint(violations_bp)  # 不添加url_prefix，因为蓝图已经定义了前缀
    app.logger.info('违规管理蓝图注册成功')
  except ImportError as e:
    app.logger.error(f'违规管理蓝图注册失败: {str(e)}')

  # 注册充电管理相关蓝图
  try:
    from .charging import charging_bp
    app.register_blueprint(charging_bp, url_prefix='/api')
    app.logger.info('充电管理蓝图注册成功')
  except ImportError as e:
    app.logger.error(f'充电管理蓝图注册失败: {str(e)}')

  # 注册公告蓝图
  from app.announcements.routes import announcements as announcements_blueprint
  app.register_blueprint(announcements_blueprint, url_prefix='/api/announcements')

  # 注册仪表盘蓝图
  try:
    from .dashboard import dashboard_bp
    app.register_blueprint(dashboard_bp, url_prefix='/api')
    app.logger.info('仪表盘蓝图注册成功')
  except ImportError as e:
    app.logger.error(f'仪表盘蓝图注册失败: {str(e)}')

  # 初始化辅助函数
  init_auth_helpers()


def register_errors(app):
  @app.after_request
  def add_header(response):
    # 不要在这里添加CORS头，因为CORS插件已经添加了
    return response

  @app.after_request
  def normalize_response(response):
    """确保所有响应都采用统一的格式"""
    # 如果是OPTIONS请求或非JSON响应，不进行处理
    if request.method == 'OPTIONS' or not response.content_type.startswith('application/json'):
      return response

    # 对于特定API路径，直接返回原始响应(这些路径已在各自的处理器中使用了standard_response格式)
    special_paths = ['/api/login', '/api/logout', '/api/register', '/api/check-username']
    if any(request.path.startswith(path) for path in special_paths):
      return response

    try:
      # 尝试获取当前响应内容
      data = json.loads(response.get_data(as_text=True))

      # 如果已经是标准格式，不处理
      if 'code' in data:
        return response

      # 获取标准状态码
      std_code = 20000  # 默认成功状态码
      if response.status_code >= 400:
        if response.status_code == 400:
          std_code = 40000
        elif response.status_code == 401:
          std_code = 40100
        elif response.status_code == 403:
          std_code = 40300
        elif response.status_code == 404:
          std_code = 40400
        elif response.status_code == 422:
          std_code = 42200
        else:
          std_code = 50000  # 所有其他错误均为服务器错误

      # 构建错误信息
      message = None
      if response.status_code >= 400 and isinstance(data, dict):
        if 'message' in data:
          message = data['message']
        elif 'error' in data:
          message = data['error']

      # 转换为标准格式
      normalized_data = {
        'code': std_code,
        'data': data
      }

      # 添加消息(如果有)
      if message:
        normalized_data['message'] = message

      # 更新响应，保持状态码不变
      response.set_data(json.dumps(normalized_data))

      # 输出调试信息
      print(f"规范化响应: 路径={request.path}, 状态码={response.status_code}, 标准码={std_code}")

      return response
    except Exception as e:
      # 如果处理失败，记录错误并返回原始响应
      print(f"响应格式化错误: {str(e)}")
      return response

  @app.errorhandler(404)
  def not_found(e):
    app.logger.error(f"路由未找到: {request.path}, 方法: {request.method}, 头信息: {request.headers}")
    logging.error(e)
    return api_response(message="资源不存在", status="error", code=404)

  @app.errorhandler(500)
  def server_error(e):
    app.logger.error(f"服务器错误: {str(e)}")
    logging.error(e)
    return api_response(message="服务器内部错误", status="error", code=500)

  @app.errorhandler(400)
  def bad_request(e):
    app.logger.error(f"错误请求: {str(e)}")
    logging.error(e)
    return api_response(message="错误请求", status="error", code=400)

  # 捕获所有未处理的异常
  @app.errorhandler(Exception)
  def handle_exception(e):
    app.logger.error(f"未处理的异常: {str(e)}")
    import traceback
    app.logger.error(traceback.format_exc())
    return api_response(message="服务器内部错误", status="error", code=500)
