"""
系统常量定义

定义系统中使用的各种常量，确保前后端一致性。
"""

# 车辆状态常量
class VehicleStatus:
    # 后端状态值
    AVAILABLE = "可用"  # 车辆可用状态
    DISABLED = "废弃"   # 车辆禁用状态

    # 前端状态值
    FRONTEND_AVAILABLE = "available"  # 前端可用状态
    FRONTEND_DISABLED = "unavailable" # 前端禁用状态

    # 显示文本
    TEXT_AVAILABLE = "正常"  # 可用状态显示文本
    TEXT_DISABLED = "停用"   # 禁用状态显示文本

    # 状态映射（后端 -> 前端）
    BACKEND_TO_FRONTEND = {
        AVAILABLE: FRONTEND_AVAILABLE,
        DISABLED: FRONTEND_DISABLED
    }

    # 状态映射（前端 -> 后端）
    FRONTEND_TO_BACKEND = {
        FRONTEND_AVAILABLE: AVAILABLE,
        FRONTEND_DISABLED: DISABLED,
        "正常": AVAILABLE,
        "停用": DISABLED
    }

    # 状态映射（后端 -> 显示文本）
    BACKEND_TO_TEXT = {
        AVAILABLE: TEXT_AVAILABLE,
        DISABLED: TEXT_DISABLED
    }

    # 状态映射（前端 -> 显示文本）
    FRONTEND_TO_TEXT = {
        FRONTEND_AVAILABLE: TEXT_AVAILABLE,
        FRONTEND_DISABLED: TEXT_DISABLED
    }

    @classmethod
    def convert_to_frontend(cls, backend_status):
        """将后端状态值转换为前端状态值"""
        return cls.BACKEND_TO_FRONTEND.get(backend_status, cls.FRONTEND_AVAILABLE)

    @classmethod
    def convert_to_backend(cls, frontend_status):
        """将前端状态值转换为后端状态值"""
        return cls.FRONTEND_TO_BACKEND.get(frontend_status, cls.AVAILABLE)

    @classmethod
    def get_text(cls, status, is_backend=True):
        """获取状态的显示文本"""
        if is_backend:
            return cls.BACKEND_TO_TEXT.get(status, cls.TEXT_AVAILABLE)
        else:
            return cls.FRONTEND_TO_TEXT.get(status, cls.TEXT_AVAILABLE)

    @classmethod
    def is_available(cls, status, is_backend=True):
        """检查状态是否为可用状态"""
        if is_backend:
            return status == cls.AVAILABLE
        else:
            return status == cls.FRONTEND_AVAILABLE

    @classmethod
    def is_disabled(cls, status, is_backend=True):
        """检查状态是否为禁用状态"""
        if is_backend:
            return status == cls.DISABLED
        else:
            return status == cls.FRONTEND_DISABLED

# 停车场状态常量
class ParkingLotStatus:
    # 后端状态值
    ACTIVE = 1    # 停车场激活状态
    INACTIVE = 0  # 停车场未激活状态

    # 前端状态值
    FRONTEND_ACTIVE = "active"      # 前端激活状态
    FRONTEND_INACTIVE = "inactive"  # 前端未激活状态

    # 显示文本
    TEXT_ACTIVE = "正常"    # 激活状态显示文本
    TEXT_INACTIVE = "停用"  # 未激活状态显示文本

# 车位状态常量
class ParkingSpaceStatus:
    # 后端状态值
    AVAILABLE = 0      # 车位空闲状态
    OCCUPIED = 1       # 车位已占用状态
    MAINTENANCE = 2    # 车位维护中状态
    DISABLED = 3       # 车位禁用状态

    # 前端状态值
    FRONTEND_AVAILABLE = "available"      # 前端空闲状态
    FRONTEND_OCCUPIED = "occupied"        # 前端已占用状态
    FRONTEND_MAINTENANCE = "maintenance"  # 前端维护中状态
    FRONTEND_DISABLED = "disabled"        # 前端禁用状态

    # 显示文本
    TEXT_AVAILABLE = "空闲"    # 空闲状态显示文本
    TEXT_OCCUPIED = "已占用"   # 已占用状态显示文本
    TEXT_MAINTENANCE = "维护中" # 维护中状态显示文本
    TEXT_DISABLED = "禁用"     # 禁用状态显示文本

# 停车记录状态常量
class ParkingRecordStatus:
    # 后端状态值
    ACTIVE = 0      # 停车记录进行中状态
    COMPLETED = 1   # 停车记录已完成状态
    ABNORMAL = 2    # 停车记录异常状态

    # 前端状态值
    FRONTEND_ACTIVE = "active"        # 前端进行中状态
    FRONTEND_COMPLETED = "completed"  # 前端已完成状态
    FRONTEND_ABNORMAL = "abnormal"    # 前端异常状态

    # 显示文本
    TEXT_ACTIVE = "进行中"    # 进行中状态显示文本
    TEXT_COMPLETED = "已完成" # 已完成状态显示文本
    TEXT_ABNORMAL = "异常"    # 异常状态显示文本

# 校区类型常量
class CampusTypes:
    # 校区选项
    OPTIONS = [
        {"label": "南湖校区", "value": "南湖校区"},
        {"label": "文昌校区", "value": "文昌校区"}
    ]

    # 校区值
    NANHU = "南湖校区"
    WENCHANG = "文昌校区"

    @classmethod
    def get_all_values(cls):
        """获取所有校区值"""
        return [option["value"] for option in cls.OPTIONS]

    @classmethod
    def get_label(cls, value):
        """获取校区标签"""
        for option in cls.OPTIONS:
            if option["value"] == value:
                return option["label"]
        return value

    @classmethod
    def is_valid(cls, value):
        """验证校区值是否有效"""
        return value in cls.get_all_values()

# 区域类型常量
class AreaTypes:
    # 区域选项
    OPTIONS = [
        {"label": "教学楼区", "value": "教学楼区"},
        {"label": "宿舍区", "value": "宿舍区"},
        {"label": "图书馆区", "value": "图书馆区"},
        {"label": "食堂区", "value": "食堂区"},
        {"label": "运动场区", "value": "运动场区"}
    ]

    # 区域值
    TEACHING_BUILDING = "教学楼区"
    DORMITORY = "宿舍区"
    LIBRARY = "图书馆区"
    CANTEEN = "食堂区"
    SPORTS_FIELD = "运动场区"

    @classmethod
    def get_all_values(cls):
        """获取所有区域值"""
        return [option["value"] for option in cls.OPTIONS]

    @classmethod
    def get_label(cls, value):
        """获取区域标签"""
        for option in cls.OPTIONS:
            if option["value"] == value:
                return option["label"]
        return value

    @classmethod
    def is_valid(cls, value):
        """验证区域值是否有效"""
        return value in cls.get_all_values()
