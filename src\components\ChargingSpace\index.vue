<template>
  <div
    class="space-item"
    :class="{
      'status-available': space.status === 0,
      'status-occupied': space.status === 1,
      'status-maintenance': space.status === 2,
      'selected': selected
    }"
    @click="$emit('click', space)"
  >
    <div class="space-content">
      <div class="space-number">{{ space.space_number }}</div>
      <div class="space-icon">
        <i class="el-icon-s-opportunity"></i>
      </div>
      <div class="space-power" v-if="space.power">{{ space.power }}kW</div>
      <div v-if="space.status === 1 && space.occupied_by" class="space-vehicle">
        <i class="el-icon-truck"></i> {{ space.occupied_by }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChargingSpace',
  props: {
    space: {
      type: Object,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.space-item {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  margin: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);

  &.status-available {
    background-color: #f0f9eb;
    border: 1px solid #67c23a;

    &:hover {
      background-color: #e1f3d8;
      transform: translateY(-2px);
    }
  }

  &.status-occupied {
    background-color: #fef0f0;
    border: 1px solid #f56c6c;
    cursor: not-allowed;
  }

  &.status-maintenance {
    background-color: #fdf6ec;
    border: 1px solid #e6a23c;
    cursor: not-allowed;
  }

  &.selected {
    background-color: #ecf5ff;
    border: 2px solid #409eff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  }

  .space-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 3px;

    .space-number {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 2px;
    }

    .space-icon {
      font-size: 18px;
      margin-bottom: 2px;
      color: #409eff;
    }

    .space-power {
      font-size: 11px;
      margin-bottom: 0;
    }

    .space-type {
      font-size: 11px;
      color: #606266;
    }

    .space-vehicle {
      font-size: 11px;
      color: #f56c6c;
      margin-top: 2px;
      padding: 1px 3px;
      background-color: rgba(245, 108, 108, 0.1);
      border-radius: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 95%;
    }
  }
}
</style>
