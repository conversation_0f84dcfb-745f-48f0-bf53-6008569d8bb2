# 校园电动车管理系统 - 简化Chen表示法E-R图

## 实体-联系图 (只显示实体和联系)

```mermaid
graph TD
    %% 实体定义 (矩形)
    A[用户]
    B[车辆]
    C[停车场]
    D[车位]
    E[停车记录]
    F[违规记录]
    G[违规类型]
    H[申诉]
    I[充电记录]
    J[公告]
    K[证据]
    L[充电故障]
    M[充电异常]

    %% 联系定义 (菱形)
    R1{拥有}
    R2{停车}
    R3{包含}
    R4{使用}
    R5{违规}
    R6{分类}
    R7{申诉}
    R8{充电}
    R9{发布}
    R10{上传}
    R11{故障}
    R12{异常}
    R13{关联充电}
    R14{当前停放}

    %% 实体与联系的连接关系
    A ---|1| R1
    R1 ---|m| B

    A ---|1| R2
    R2 ---|m| E

    C ---|1| R3
    R3 ---|m| D

    D ---|1| R4
    R4 ---|m| E

    B ---|1| R5
    R5 ---|m| F

    G ---|1| R6
    R6 ---|m| F

    F ---|1| R7
    R7 ---|1| H

    B ---|1| R8
    R8 ---|m| I

    A ---|1| R9
    R9 ---|m| J

    A ---|1| R10
    R10 ---|m| K

    D ---|1| R11
    R11 ---|m| L

    I ---|1| R12
    R12 ---|m| M

    E ---|1| R13
    R13 ---|m| I

    D ---|1| R14
    R14 ---|1| B

    %% 样式定义
    classDef entity fill:#e1f5fe,stroke:#01579b,stroke-width:3px,font-size:14px
    classDef relationship fill:#fff3e0,stroke:#e65100,stroke-width:2px,font-size:12px

    class A,B,C,D,E,F,G,H,I,J,K,L,M entity
    class R1,R2,R3,R4,R5,R6,R7,R8,R9,R10,R11,R12,R13,R14 relationship
```

## 核心实体说明

### 主要实体 (13个)

1. **用户** - 系统使用者 (管理员、保安、普通用户)
2. **车辆** - 电动车信息
3. **停车场** - 停车场基本信息
4. **车位** - 具体停车位置
5. **停车记录** - 停车过程记录
6. **违规记录** - 违规行为记录
7. **违规类型** - 违规分类标准
8. **申诉** - 违规申诉处理
9. **充电记录** - 充电过程记录
10. **公告** - 系统通知公告
11. **证据** - 违规和申诉证据
12. **充电故障** - 充电设备故障
13. **充电异常** - 充电过程异常

### 主要联系 (14个)

1. **拥有** (1:m) - 用户拥有车辆
2. **停车** (1:m) - 用户进行停车
3. **包含** (1:m) - 停车场包含车位
4. **使用** (1:m) - 车位被使用
5. **违规** (1:m) - 车辆发生违规
6. **分类** (1:m) - 违规类型分类违规记录
7. **申诉** (1:1) - 违规记录对应申诉
8. **充电** (1:m) - 车辆进行充电
9. **发布** (1:m) - 用户发布公告
10. **上传** (1:m) - 用户上传证据
11. **故障** (1:m) - 车位发生故障
12. **异常** (1:m) - 充电记录产生异常
13. **关联充电** (1:m) - 停车记录关联充电记录
14. **当前停放** (1:1) - 车位当前停放车辆

## 联系基数说明

### 一对多联系 (1:m)
- 用户 ──拥有── 车辆
- 用户 ──停车── 停车记录
- 停车场 ──包含── 车位
- 车位 ──使用── 停车记录
- 车辆 ──违规── 违规记录
- 违规类型 ──分类── 违规记录
- 车辆 ──充电── 充电记录
- 用户 ──发布── 公告
- 用户 ──上传── 证据
- 车位 ──故障── 充电故障
- 充电记录 ──异常── 充电异常
- 停车记录 ──关联充电── 充电记录

### 一对一联系 (1:1)
- 违规记录 ──申诉── 申诉
- 车位 ──当前停放── 车辆

## 业务逻辑体现

### 核心业务流程
1. **停车流程**: 用户 → 停车记录 → 车位 → 停车场
2. **违规处理**: 车辆 → 违规记录 → 违规类型 → 申诉
3. **充电管理**: 停车记录 → 充电记录 → 充电异常/故障
4. **证据管理**: 用户 → 证据 → 违规记录/申诉

### 关键约束
- 一个车位同时只能停放一辆车 (当前停放 1:1)
- 一条违规记录最多一次申诉 (申诉 1:1)
- 充电必须基于停车记录 (关联充电 1:m)
- 故障和异常分别管理设备和过程问题

## 核心业务E-R图 (紧凑版)

```mermaid
graph TD
    %% 核心实体定义 (矩形)
    A[用户]
    B[车辆]
    C[停车场]
    D[车位]
    E[停车记录]
    F[违规记录]
    G[违规类型]
    H[申诉]
    I[充电记录]

    %% 核心联系定义 (菱形)
    R1{拥有}
    R2{停车}
    R3{包含}
    R4{使用}
    R5{违规}
    R6{分类}
    R7{申诉}
    R8{充电}
    R9{关联}

    %% 实体与联系的连接关系
    A ---|1| R1
    R1 ---|m| B

    A ---|1| R2
    R2 ---|m| E
    B ---|1| R2

    C ---|1| R3
    R3 ---|m| D

    D ---|1| R4
    R4 ---|m| E

    B ---|1| R5
    R5 ---|m| F

    G ---|1| R6
    R6 ---|m| F

    F ---|1| R7
    R7 ---|1| H

    B ---|1| R8
    R8 ---|m| I

    E ---|1| R9
    R9 ---|m| I

    %% 样式定义
    classDef entity fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,font-size:16px,font-weight:bold
    classDef relationship fill:#fff8e1,stroke:#f57c00,stroke-width:2px,font-size:14px,font-weight:bold

    class A,B,C,D,E,F,G,H,I entity
    class R1,R2,R3,R4,R5,R6,R7,R8,R9 relationship
```

## 设计特点

### 符合Chen表示法标准
- **蓝色矩形** 🔲：表示实体
- **橙色菱形** 🔶：表示联系
- **标注基数** 1 和 m：表示联系的多重性
- **无属性显示**：专注于实体间的联系关系

### 核心业务逻辑
1. **用户管理**: 用户拥有车辆，用户进行停车
2. **停车管理**: 停车场包含车位，车位被使用产生停车记录
3. **违规管理**: 车辆违规产生违规记录，按违规类型分类，可以申诉
4. **充电管理**: 车辆充电产生充电记录，与停车记录关联

### 关键联系说明
- **拥有 (1:m)**: 一个用户可以拥有多辆车
- **停车 (m:m)**: 用户和车辆都参与停车过程
- **包含 (1:m)**: 一个停车场包含多个车位
- **使用 (1:m)**: 一个车位可以被多次使用
- **违规 (1:m)**: 一辆车可能有多次违规
- **分类 (1:m)**: 一种违规类型对应多条违规记录
- **申诉 (1:1)**: 一条违规记录最多一次申诉
- **充电 (1:m)**: 一辆车可以有多次充电
- **关联 (1:m)**: 一次停车可能包含多次充电
