<template>
  <div class="forbidden-container">
    <div class="forbidden-content">
      <div class="icon-container">
        <i class="el-icon-warning-outline"></i>
      </div>
      <h1 class="title">403</h1>
      <h2 class="subtitle">权限不足</h2>
      <p class="message">抱歉，您没有权限访问此页面的数据</p>
      <div class="actions">
        <el-button type="primary" @click="goBack">返回上一页</el-button>
        <el-button @click="goHome">返回首页</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Forbidden',
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goHome() {
      this.$router.push('/')
    }
  }
}
</script>

<style lang="scss" scoped>
.forbidden-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.forbidden-content {
  text-align: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 40px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.icon-container {
  margin-bottom: 20px;
}

.icon-container i {
  font-size: 64px;
  color: #E6A23C;
}

.title {
  font-size: 72px;
  color: #E6A23C;
  margin: 0 0 10px;
  line-height: 1;
}

.subtitle {
  font-size: 28px;
  color: #303133;
  margin-bottom: 16px;
}

.message {
  font-size: 16px;
  color: #606266;
  margin-bottom: 30px;
  line-height: 1.6;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style>
