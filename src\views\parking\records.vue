<template>
  <div class="app-container">
    <permission-wrapper :allowed-roles="['admin']" message="您没有权限访问停车记录管理数据，请联系管理员获取权限。">
    <!-- 搜索过滤区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.vehicle_id"
        placeholder="车辆ID"
        clearable
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.status"
        placeholder="记录状态"
        clearable
        class="filter-item"
      >
        <el-option label="进行中" :value="0" />
        <el-option label="已完成" :value="1" />
        <el-option label="异常" :value="2" />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        class="filter-item"
        @change="handleDateChange"
      />

      <!-- 排序选择器 -->
      <el-select
        v-model="listQuery.sort_by"
        placeholder="排序字段"
        clearable
        class="filter-item"
        @change="handleSortChange"
      >
        <el-option label="入场时间" value="entry_time" />
        <el-option label="出场时间" value="exit_time" />
      </el-select>

      <el-select
        v-model="listQuery.sort_order"
        placeholder="排序方式"
        clearable
        class="filter-item"
        @change="handleSortChange"
      >
        <el-option label="升序" value="asc" />
        <el-option label="降序" value="desc" />
      </el-select>

      <el-button
        type="primary"
        icon="el-icon-search"
        class="filter-item"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        type="success"
        icon="el-icon-refresh"
        class="filter-item"
        @click="fetchData"
      >
        刷新
      </el-button>
    </div>

    <!-- 停车记录列表 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template slot-scope="scope">
          {{ scope.row.id }}
        </template>
      </el-table-column>
      <el-table-column label="车牌号" min-width="120" align="center">
        <template slot-scope="scope">
          <div class="vehicle-cell">
            <i class="el-icon-bicycle" style="color: #409EFF; margin-right: 5px;"></i>
            <!-- 优先显示车位上的当前车辆信息 -->
            <span v-if="scope.row.parking_space && scope.row.parking_space.current_vehicle_id && vehicles[scope.row.parking_space.current_vehicle_id] && vehicles[scope.row.parking_space.current_vehicle_id].b_num !== '加载中...'">
              {{ getVehicleNumber(vehicles[scope.row.parking_space.current_vehicle_id]) }}
            </span>
            <!-- 其次显示停车记录中的车辆信息 -->
            <span v-else-if="scope.row.vehicle && (scope.row.vehicle.b_num || scope.row.vehicle.bike_number)">
              {{ getVehicleNumber(scope.row.vehicle) }}
            </span>
            <!-- 再次尝试通过车位上的车辆ID获取信息 -->
            <span v-else-if="scope.row.parking_space && scope.row.parking_space.current_vehicle_id">
              {{ getVehicleNumberById(scope.row.parking_space.current_vehicle_id) }}
            </span>
            <!-- 最后尝试通过停车记录中的车辆ID获取信息 -->
            <span v-else>
              {{ getVehicleNumberById(scope.row.vehicle_id) }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="停车场" min-width="150">
        <template slot-scope="scope">
          <el-link
            type="primary"
            @click.native.stop="viewParkingLot(scope.row.parking_lot_id)"
          >
            {{ getParkingLotName(scope.row) }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="车位编号" min-width="120" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.parking_space">{{ scope.row.parking_space.number || scope.row.parking_space.space_number }}</span>
          <span v-else>{{ scope.row.parking_space_id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="入场时间" min-width="170" align="center">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.entry_time) }}
        </template>
      </el-table-column>
      <el-table-column label="出场时间" min-width="170" align="center">
        <template slot-scope="scope">
          {{ scope.row.exit_time ? formatDateTime(scope.row.exit_time) : '停车中' }}
        </template>
      </el-table-column>
      <el-table-column label="停车时长" min-width="120" align="center">
        <template slot-scope="scope">
          {{ scope.row.duration_formatted || formatDuration(scope.row.duration) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.status_text || getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="180">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button
              v-if="scope.row.status === 0"
              size="mini"
              type="warning"
              @click.stop="handleEndParking(scope.row)"
            >
              结束停车
            </el-button>
            <el-button
              v-if="isAdmin"
              size="mini"
              type="danger"
              @click.stop="handleDeleteRecord(scope.row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="fetchData"
    />

    <!-- 分页信息显示 -->
    <div class="pagination-info">
      <span>共 {{ total }} 条记录，当前显示第 {{ (listQuery.page - 1) * listQuery.limit + 1 }} 至
      {{ Math.min(listQuery.page * listQuery.limit, total) }} 条</span>
    </div>



    <!-- 结束停车对话框 -->
    <el-dialog
      title="结束停车"
      :visible.sync="endParkingDialogVisible"
      width="30%"
    >
      <el-form :model="endParkingForm" label-width="100px">
        <el-form-item label="备注信息">
          <el-input
            v-model="endParkingForm.remarks"
            type="textarea"
            placeholder="可选填写备注信息"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="endParkingDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="confirmEndParking">确定</el-button>
      </span>
    </el-dialog>

    <!-- 删除停车记录确认对话框 -->
    <el-dialog
      title="删除停车记录"
      :visible.sync="deleteDialogVisible"
      width="30%"
    >
      <div class="delete-confirm">
        <i class="el-icon-warning-outline warning-icon"></i>
        <div class="confirm-content">
          <p class="confirm-title">确定要删除此停车记录吗？</p>
          <p class="confirm-desc">记录ID: {{ deleteForm.id }}</p>
          <p class="confirm-warning">此操作不可逆，删除后数据将无法恢复！</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" :loading="deleteLoading" @click="confirmDeleteRecord">确定删除</el-button>
      </span>
    </el-dialog>
    </permission-wrapper>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import request from '@/utils/request'
import PermissionWrapper from '@/components/PermissionWrapper'

export default {
  name: 'ParkingRecords',
  components: { Pagination, PermissionWrapper },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20, // 设置每页显示数量为20条
        status: '',
        vehicle_id: '',
        parking_lot_id: '',
        start_date: '',
        end_date: '',
        sort_by: '', // 排序字段：entry_time或exit_time
        sort_order: '' // 排序方式：asc或desc
      },
      dateRange: [],
      parkingLots: {}, // 缓存停车场信息
      vehicles: {}, // 缓存车辆信息
      endParkingDialogVisible: false,
      endParkingForm: {
        id: null,
        remarks: ''
      },
      submitLoading: false,
      deleteDialogVisible: false,
      deleteLoading: false,
      deleteForm: {
        id: null
      }
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'userId'
    ]),
    isAdmin() {
      return this.roles.includes('admin')
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.listLoading = true

      // 准备API请求路径
      let apiPath = '/api/parking-records/user'
      if (this.isAdmin) {
        apiPath = '/api/parking-records/all'
      }

      // 添加参数，确保返回包含车位和车辆信息
      const params = {
        ...this.listQuery,
        include_relations: true, // 请求包含关联数据
        per_page: this.listQuery.limit, // 确保后端接收正确的分页参数
        page: this.listQuery.page,
        _t: new Date().getTime() // 添加时间戳防止缓存
      }

      request({
        url: apiPath,
        method: 'get',
        params: params
      }).then(response => {
        // 处理响应数据
        let responseData = response
        if (response.data) {
          responseData = response.data
        }

        console.log('处理响应数据:', JSON.stringify(responseData))

        if (responseData.items) {
          this.list = responseData.items
          this.total = responseData.total || responseData.pagination?.total || this.list.length
          console.log(`从items字段获取数据，总数: ${this.total}`)
        } else if (Array.isArray(responseData)) {
          this.list = responseData
          this.total = responseData.length
          console.log(`从数组获取数据，总数: ${this.total}`)
        } else if (responseData.data && Array.isArray(responseData.data)) {
          this.list = responseData.data
          this.total = responseData.total || this.list.length
          console.log(`从data数组字段获取数据，总数: ${this.total}`)
        } else if (responseData.data && responseData.data.items) {
          this.list = responseData.data.items
          this.total = responseData.data.total || this.list.length
          console.log(`从data.items字段获取数据，总数: ${this.total}`)
        } else {
          this.list = []
          this.total = 0
          console.warn('无法识别的停车记录数据格式:', responseData)
        }

        // 确保total是数字类型
        this.total = parseInt(this.total) || 0
        console.log(`最终处理后的数据: 列表长度=${this.list.length}, 总记录数=${this.total}`)

        // 处理每条记录，确保获取车辆信息
        console.log('fetchData: 开始处理停车记录列表，获取车辆信息')
        this.list.forEach((record, index) => {
          console.log(`fetchData: 处理第 ${index+1} 条记录，ID=${record.id}`)

          // 记录调试信息
          if (record.parking_space) {
            console.log(`fetchData: 记录 ${record.id} 包含车位信息:`, record.parking_space)
          }
          if (record.vehicle) {
            console.log(`fetchData: 记录 ${record.id} 包含车辆信息:`, record.vehicle)
          }

          // 优先获取车位上的当前车辆信息
          if (record.parking_space && record.parking_space.current_vehicle_id) {
            console.log(`fetchData: 记录 ${record.id} 的车位上有车辆 ${record.parking_space.current_vehicle_id}，获取该车辆信息`)
            this.fetchVehicleInfo(record.parking_space.current_vehicle_id)
          }

          // 如果记录有车辆ID但没有车辆对象，获取车辆信息
          if (record.vehicle_id && !record.vehicle) {
            console.log(`fetchData: 记录 ${record.id} 有车辆ID ${record.vehicle_id} 但没有车辆对象，获取该车辆信息`)
            this.fetchVehicleInfo(record.vehicle_id)
          }

          // 如果记录有车辆对象但没有车牌号，尝试获取更完整的车辆信息
          if (record.vehicle && !record.vehicle.b_num && !record.vehicle.bike_number) {
            console.log(`fetchData: 记录 ${record.id} 的车辆对象没有车牌号，获取完整车辆信息`)
            this.fetchVehicleInfo(record.vehicle.b_id || record.vehicle.id)
          }
        })

        this.listLoading = false
      }).catch(error => {
        console.error('获取停车记录失败', error)
        this.$message.error('获取停车记录失败')
        this.list = []
        this.total = 0
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      console.log('应用筛选，重置页码为1，查询参数:', JSON.stringify(this.listQuery))
      this.fetchData()
    },
    handleDateChange(val) {
      if (val) {
        this.listQuery.start_date = val[0]
        this.listQuery.end_date = val[1]
      } else {
        this.listQuery.start_date = ''
        this.listQuery.end_date = ''
      }
    },
    handleSortChange() {
      // 如果选择了排序字段但没有选择排序方式，默认使用降序
      if (this.listQuery.sort_by && !this.listQuery.sort_order) {
        this.listQuery.sort_order = 'desc'
      }

      // 如果选择了排序方式但没有选择排序字段，默认使用入场时间
      if (!this.listQuery.sort_by && this.listQuery.sort_order) {
        this.listQuery.sort_by = 'entry_time'
      }

      // 重新加载数据
      this.fetchData()
    },

    handleEndParking(row) {
      this.endParkingForm.id = row.id
      this.endParkingForm.remarks = ''
      this.endParkingDialogVisible = true
    },
    confirmEndParking() {
      this.submitLoading = true

      request({
        url: `/api/parking-records/${this.endParkingForm.id}/end`,
        method: 'put',
        data: {
          remarks: this.endParkingForm.remarks
        }
      }).then(response => {
        this.$message.success('停车记录已成功结束')
        this.endParkingDialogVisible = false

        // 更新列表中的记录
        this.fetchData()
      }).catch(error => {
        console.error('结束停车失败', error)
        this.$message.error(error.response?.data?.message || '结束停车失败')
      }).finally(() => {
        this.submitLoading = false
      })
    },
    viewParkingLot(lotId) {
      this.$router.push(`/parking/details/${lotId}`)
    },
    getParkingLotName(record) {
      if (record.parking_lot) {
        return record.parking_lot.name
      }

      // 如果没有关联的停车场对象，检查缓存或显示ID
      if (this.parkingLots[record.parking_lot_id]) {
        return this.parkingLots[record.parking_lot_id].name
      }

      // 尝试获取停车场信息并缓存
      this.fetchParkingLotInfo(record.parking_lot_id)
      return `停车场#${record.parking_lot_id}`
    },
    fetchParkingLotInfo(lotId) {
      if (!lotId || this.parkingLots[lotId]) return

      request({
        url: `/api/parkinglots/${lotId}`,
        method: 'get'
      }).then(response => {
        const lotData = response.data || response
        if (lotData) {
          this.$set(this.parkingLots, lotId, lotData)
        }
      }).catch(error => {
        console.error('获取停车场信息失败', error)
      })
    },
    getVehicleNumber(vehicle) {
      if (!vehicle) {
        console.warn('getVehicleNumber: 车辆对象为空')
        return '未知车辆'
      }

      // 记录调试信息
      console.log('getVehicleNumber: 车辆对象:', vehicle)

      // 优先使用数据库字段名 b_num
      const vehicleNumber = vehicle.b_num || vehicle.bike_number || vehicle.number

      if (vehicleNumber) {
        console.log(`getVehicleNumber: 找到车牌号 ${vehicleNumber}`)
        return vehicleNumber
      }

      // 如果没有找到车牌号，使用ID
      const vehicleId = vehicle.b_id || vehicle.id
      console.log(`getVehicleNumber: 未找到车牌号，使用ID ${vehicleId}`)
      return `车辆#${vehicleId || '未知'}`
    },
    getVehicleNumberById(vehicleId) {
      if (!vehicleId) {
        console.warn('getVehicleNumberById: 车辆ID为空')
        return '未知车辆'
      }

      console.log(`getVehicleNumberById: 开始获取车辆 ${vehicleId} 的车牌号`)

      // 检查缓存
      if (this.vehicles[vehicleId]) {
        const cachedVehicle = this.vehicles[vehicleId]
        console.log(`getVehicleNumberById: 使用缓存的车辆信息，ID=${vehicleId}`, cachedVehicle)

        // 如果缓存中的车牌号是"加载中..."，则返回车辆ID
        if (cachedVehicle.b_num === '加载中...') {
          console.log(`getVehicleNumberById: 车辆信息正在加载中，返回临时ID`)
          return `车辆#${vehicleId}`
        }

        return this.getVehicleNumber(cachedVehicle)
      }

      // 尝试获取车辆信息并缓存
      console.log(`getVehicleNumberById: 未找到缓存，开始获取车辆信息，ID=${vehicleId}`)
      this.fetchVehicleInfo(vehicleId)

      // 立即返回一个临时值
      return `车辆#${vehicleId}`
    },
    fetchVehicleInfo(vehicleId) {
      if (!vehicleId) {
        console.warn('fetchVehicleInfo: 车辆ID为空')
        return
      }

      console.log(`fetchVehicleInfo: 开始获取车辆信息，ID=${vehicleId}`)

      // 如果已经有缓存且不是占位符，则不重复获取
      if (this.vehicles[vehicleId] && this.vehicles[vehicleId].b_num && this.vehicles[vehicleId].b_num !== '加载中...') {
        console.log(`fetchVehicleInfo: 使用缓存的车辆信息，ID=${vehicleId}, 车牌号=${this.vehicles[vehicleId].b_num}`)
        return
      }

      // 设置一个占位符，防止重复请求
      if (!this.vehicles[vehicleId]) {
        console.log(`fetchVehicleInfo: 设置占位符，ID=${vehicleId}`)
        this.$set(this.vehicles, vehicleId, { b_id: vehicleId, b_num: `加载中...` })
      }

      // 使用模拟数据进行测试功能已移除

      console.log(`fetchVehicleInfo: 发送API请求获取车辆信息，ID=${vehicleId}`)
      request({
        url: `/api/bikes/${vehicleId}`,
        method: 'get'
      }).then(response => {
        console.log(`fetchVehicleInfo: 获取车辆信息成功，ID=${vehicleId}，响应:`, response)

        // 尝试从不同的响应格式中提取车辆数据
        let vehicleData = null

        if (response.data && response.data.bike) {
          vehicleData = response.data.bike
          console.log(`fetchVehicleInfo: 从response.data.bike中提取数据`)
        } else if (response.data) {
          vehicleData = response.data
          console.log(`fetchVehicleInfo: 从response.data中提取数据`)
        } else {
          vehicleData = response
          console.log(`fetchVehicleInfo: 从response中提取数据`)
        }

        if (vehicleData) {
          // 统一字段名称
          const normalizedVehicle = {
            b_id: vehicleData.b_id || vehicleData.id || vehicleId,
            b_num: vehicleData.b_num || vehicleData.bike_number || vehicleData.number,
            brand: vehicleData.brand || '未知品牌',
            color: vehicleData.color || '未知颜色',
            status: vehicleData.status || '可用'
          }

          // 确保有车牌号
          if (!normalizedVehicle.b_num) {
            normalizedVehicle.b_num = `车辆#${vehicleId}`
          }

          console.log(`fetchVehicleInfo: 处理后的车辆数据，ID=${vehicleId}, 车牌号=${normalizedVehicle.b_num}`)
          this.$set(this.vehicles, vehicleId, normalizedVehicle)
        } else {
          console.warn(`fetchVehicleInfo: 无法从响应中提取车辆数据，ID=${vehicleId}`)
          this.$set(this.vehicles, vehicleId, {
            b_id: vehicleId,
            b_num: `车辆#${vehicleId}`,
            brand: '未知品牌',
            color: '未知颜色'
          })
        }
      }).catch(error => {
        console.error(`fetchVehicleInfo: 获取车辆信息失败，ID=${vehicleId}`, error)
        // 设置一个默认值，避免显示"加载中..."
        this.$set(this.vehicles, vehicleId, {
          b_id: vehicleId,
          b_num: `车辆#${vehicleId}`,
          brand: '未知品牌',
          color: '未知颜色'
        })
      })
    },
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '未知时间'

      try {
        const date = new Date(dateTimeStr)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      } catch (e) {
        return dateTimeStr
      }
    },
    formatDuration(hours) {
      if (hours === undefined || hours === null) return '未知'

      // 如果已经是格式化的字符串，直接返回
      if (typeof hours === 'string' && hours.includes('时') || hours.includes('分')) {
        return hours
      }

      // 转换为小时、分钟格式
      const totalMinutes = Math.floor(hours * 60)
      const days = Math.floor(totalMinutes / (24 * 60))
      const remainingMinutes = totalMinutes % (24 * 60)
      const hoursValue = Math.floor(remainingMinutes / 60)
      const minutesValue = remainingMinutes % 60

      if (days > 0) {
        return `${days}天${hoursValue}小时${minutesValue}分钟`
      } else if (hoursValue > 0) {
        return `${hoursValue}小时${minutesValue}分钟`
      } else {
        return `${minutesValue}分钟`
      }
    },
    getStatusText(status) {
      const statusMap = {
        0: '进行中',
        1: '已完成',
        2: '异常'
      }
      return statusMap[status] || '未知状态'
    },
    getStatusType(status) {
      const typeMap = {
        0: 'warning',
        1: 'success',
        2: 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 处理删除停车记录
    handleDeleteRecord(row) {
      if (!this.isAdmin) {
        this.$message.warning('只有管理员才能删除停车记录')
        return
      }

      this.deleteForm.id = row.id
      this.deleteDialogVisible = true
    },

    // 确认删除停车记录
    confirmDeleteRecord() {
      if (!this.deleteForm.id) {
        this.$message.error('记录ID不能为空')
        return
      }

      this.deleteLoading = true

      // 发送删除请求
      request({
        url: `/api/parking-records/${this.deleteForm.id}`,
        method: 'delete'
      }).then(response => {
        this.$message.success('停车记录删除成功')
        this.deleteDialogVisible = false

        // 重新加载数据
        this.fetchData()
      }).catch(error => {
        console.error('删除停车记录失败', error)
        this.$message.error(error.response?.data?.message || '删除停车记录失败')
      }).finally(() => {
        this.deleteLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;

  .filter-item {
    margin-right: 10px;
    margin-bottom: 10px;
    width: 200px;
  }

  .el-button {
    margin-left: 0;
  }
}


.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  gap: 5px;

  .el-button {
    padding: 5px 10px;
    font-size: 12px;
    margin-left: 0;
    height: 28px;
    line-height: 1;
  }
}

.pagination-info {
  text-align: right;
  padding: 0 16px 16px;
  color: #606266;
  font-size: 13px;
}

.vehicle-cell {
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    color: #409EFF;
    margin-right: 5px;
  }
}

/* 确保对话框中的按钮也是水平排列 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;

  .el-button {
    margin-left: 0;
  }
}

/* 删除确认对话框样式 */
.delete-confirm {
  display: flex;
  align-items: flex-start;
  padding: 20px 0;

  .warning-icon {
    font-size: 24px;
    color: #F56C6C;
    margin-right: 15px;
  }

  .confirm-content {
    flex: 1;

    .confirm-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .confirm-desc {
      color: #606266;
      margin-bottom: 10px;
    }

    .confirm-warning {
      color: #F56C6C;
      font-weight: bold;
    }
  }
}
</style>
