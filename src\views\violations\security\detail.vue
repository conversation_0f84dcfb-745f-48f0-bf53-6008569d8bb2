<template>
  <div class="app-container">
    <div v-loading="loading">
      <violation-detail
        v-if="!loading && violationDetail.id"
        :violation-detail="violationDetail"
        :appeals="appeals"
        :evidences="evidences"
        :can-edit="false"
        :can-upload="true"
        :can-appeal="false"
        :can-handle-appeal="false"
        @refresh="fetchData"
      />
    </div>
  </div>
</template>

<script>
import { getViolationDetail } from '@/api/violations'
import ViolationDetail from '../components/ViolationDetail'

export default {
  name: 'SecurityViolationDetail',
  components: {
    ViolationDetail
  },
  data() {
    return {
      loading: true,
      violationDetail: {},
      appeals: [],
      evidences: []
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      const id = this.$route.params.id

      getViolationDetail(id)
        .then(response => {
          this.violationDetail = response.data.violation
          this.appeals = response.data.appeals
          this.evidences = response.data.evidences
          this.loading = false
        })
        .catch(error => {
          console.error(error)
          this.$message.error('获取违规详情失败')
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;
  }
}
</style>
