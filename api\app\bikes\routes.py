# api/app/bikes/routes.py

from flask import request, Response
from flask_jwt_extended import jwt_required
from app import db
from app.bikes import bikes_bp
from app.bikes.models import Bikes
from app.bikes.schema import BikeSchema
from app.utils.response import api_response
from app.utils.auth_helpers import get_current_user, get_current_user_id, is_admin
from app.utils.bike_permissions import bike_owner_or_admin_required, ensure_bike_ownership, filter_bikes_by_ownership
from marshmallow import ValidationError
from datetime import datetime
import csv
import io

# 获取所有自行车或根据条件筛选 - 添加权限控制
@bikes_bp.route('/bikes', methods=['GET'])
@filter_bikes_by_ownership
def get_bikes():
    """获取自行车列表，可选择按用户ID过滤

    普通用户只能看到自己的车辆，管理员可以看到所有车辆或按用户ID过滤
    """
    # 查询参数 - 统一参数名称
    user_id = request.args.get('user_id', type=int) or request.args.get('belong_to', type=int)
    print(f"get_bikes 函数接收到的查询参数: {dict(request.args)}")
    print(f"解析后的 user_id: {user_id}")
    status = request.args.get('status')  # 状态
    search = request.args.get('search')  # 搜索关键词
    b_num = request.args.get('b_num')    # 车牌号

    # 构建查询
    query = Bikes.query

    # 应用过滤条件
    if user_id:
        print(f"根据用户ID过滤车辆: {user_id}")
        query = query.filter_by(belong_to=user_id)
    if status:
        query = query.filter_by(status=status)
    if search:
        print(f"根据搜索关键词过滤车辆: {search}")
        # 使用更宽松的搜索条件
        query = query.filter(Bikes.b_num.like(f'%{search}%'))
    if b_num:
        print(f"根据车牌号过滤车辆: {b_num}")
        # 使用更宽松的搜索条件
        query = query.filter(Bikes.b_num.like(f'%{b_num}%'))

    # 如果没有任何过滤条件，限制返回数量以避免返回过多数据
    if not user_id and not status and not search and not b_num:
        query = query.limit(50)

    # 获取结果
    bikes = query.all()
    print(f"查询到 {len(bikes)} 辆车")

    # 序列化
    schema = BikeSchema(many=True)
    bikes_data_raw = schema.dump(bikes)

    # 统一字段名称
    bikes_data = []
    for bike in bikes_data_raw:
        bikes_data.append({
            'id': bike.get('b_id'),
            'bike_number': bike.get('b_num'),
            'user_id': bike.get('belong_to'),
            'brand': bike.get('brand'),
            'color': bike.get('color'),
            'type': bike.get('b_type'),
            'status': bike.get('status'),
            'created_at': bike.get('created_at'),
            'updated_at': bike.get('updated_at')
        })

    return api_response(
        data={'bikes': bikes_data},
        message="获取车辆列表成功"
    )

# 获取当前用户的自行车列表
@bikes_bp.route('/my-bikes', methods=['GET'])
@jwt_required()
def get_my_bikes():
    """获取当前用户的自行车列表"""
    user_id = get_current_user_id()
    print(f"get_my_bikes 函数被调用，当前用户ID: {user_id}")

    if not user_id:
        return api_response(message="未登录或无法获取用户信息", status="error", code=401)

    # 查询参数
    status = request.args.get('status')  # 可用/废弃
    print(f"get_my_bikes 查询参数: status={status}")

    # 构建查询
    query = Bikes.query.filter_by(belong_to=user_id)

    # 应用过滤条件
    if status:
        query = query.filter_by(status=status)

    # 获取结果
    bikes = query.all()
    print(f"get_my_bikes 查询到 {len(bikes)} 辆车")

    # 如果是admin用户且没有车辆，添加测试数据
    if is_admin() and len(bikes) == 0:
        print("为admin用户添加测试车辆数据")
        test_bikes = [
            {
                'b_num': '京A12345',
                'belong_to': user_id,
                'brand': '特斯拉',
                'color': '白色',
                'b_type': '电动车',
                'status': '可用'
            },
            {
                'b_num': '京B67890',
                'belong_to': user_id,
                'brand': '比亚迪',
                'color': '蓝色',
                'b_type': '电动车',
                'status': '可用'
            },
            {
                'b_num': '京C13579',
                'belong_to': user_id,
                'brand': '小鹏',
                'color': '红色',
                'b_type': '电动车',
                'status': '可用'
            }
        ]

        for bike_data in test_bikes:
            try:
                new_bike = Bikes(**bike_data)
                db.session.add(new_bike)
            except Exception as e:
                print(f"添加测试车辆失败: {str(e)}")

        try:
            db.session.commit()
            # 重新查询
            bikes = Bikes.query.filter_by(belong_to=user_id).all()
            print(f"添加测试数据后，查询到 {len(bikes)} 辆车")
        except Exception as e:
            db.session.rollback()
            print(f"提交测试车辆数据失败: {str(e)}")

    # 序列化
    schema = BikeSchema(many=True)
    bikes_data_raw = schema.dump(bikes)
    print(f"get_my_bikes 序列化后的原始数据: {bikes_data_raw}")

    # 统一字段名称
    bikes_data = []
    for bike in bikes_data_raw:
        bikes_data.append({
            'id': bike.get('b_id'),
            'bike_number': bike.get('b_num'),
            'user_id': bike.get('belong_to'),
            'brand': bike.get('brand'),
            'color': bike.get('color'),
            'type': bike.get('b_type'),
            'status': bike.get('status'),
            'created_at': bike.get('created_at'),
            'updated_at': bike.get('updated_at')
        })

    print(f"get_my_bikes 返回的最终数据: {bikes_data}")
    return api_response(
        data={'bikes': bikes_data},
        message="获取车辆列表成功"
    )

# 添加新自行车
@bikes_bp.route('/bikes', methods=['POST'])
@jwt_required()
def create_bike():
    """添加新自行车

    普通用户只能为自己创建车辆，管理员可以为任何用户创建车辆
    """
    user_id = get_current_user_id()
    if not user_id:
        return api_response(message="未登录或无法获取用户信息", status="error", code=401)

    data = request.get_json() or {}

    # 统一字段名称映射
    field_mapping = {
        'bike_number': 'b_num',
        'user_id': 'belong_to',
        'type': 'b_type'
    }

    # 将前端字段名转换为后端字段名
    backend_data = {}
    for frontend_field, backend_field in field_mapping.items():
        if frontend_field in data:
            backend_data[backend_field] = data[frontend_field]

    # 添加未映射的字段
    for field in ['brand', 'color', 'status']:
        if field in data:
            backend_data[field] = data[field]

    # 强制设置所属用户
    if is_admin() and ('user_id' in data or 'belong_to' in data):
        # 管理员可以为任何用户创建车辆
        backend_data['belong_to'] = data.get('user_id') or data.get('belong_to')
    else:
        # 普通用户只能为自己创建车辆
        backend_data['belong_to'] = user_id

    # 检查车牌号是否已存在
    if 'b_num' in backend_data:
        existing_bike = Bikes.query.filter_by(b_num=backend_data['b_num']).first()
        if existing_bike:
            return api_response(message="该车牌号已存在，请使用其他车牌号", status="error", code=400)

    try:
        # 验证数据
        schema = BikeSchema()
        validated_data = schema.load(backend_data)

        # 创建新自行车
        new_bike = Bikes(**validated_data)
        db.session.add(new_bike)
        db.session.commit()

        # 统一返回数据字段
        bike_data = schema.dump(new_bike)
        formatted_bike = {
            'id': bike_data.get('b_id'),
            'bike_number': bike_data.get('b_num'),
            'user_id': bike_data.get('belong_to'),
            'brand': bike_data.get('brand'),
            'color': bike_data.get('color'),
            'type': bike_data.get('b_type'),
            'status': bike_data.get('status'),
            'created_at': bike_data.get('created_at'),
            'updated_at': bike_data.get('updated_at')
        }

        # 返回创建的自行车
        return api_response(
            data={'bike': formatted_bike},
            message="车辆添加成功",
            code=201
        )
    except ValidationError as e:
        return api_response(message="数据验证失败", errors=e.messages, status="error", code=400)
    except Exception as e:
        db.session.rollback()
        return api_response(message=f"添加车辆失败: {str(e)}", status="error", code=500)

# 获取特定自行车信息
@bikes_bp.route('/bikes/<int:bike_id>', methods=['GET'])
@bike_owner_or_admin_required()
def get_bike(bike_id):
    """获取特定自行车信息

    普通用户只能查看自己的车辆，管理员可以查看任何车辆
    """
    # 查找自行车
    bike = Bikes.query.get(bike_id)

    if not bike:
        return api_response(message="车辆不存在", status="error", code=404)

    # 序列化
    schema = BikeSchema()
    bike_data = schema.dump(bike)

    # 统一字段名称
    formatted_bike = {
        'id': bike_data.get('b_id'),
        'bike_number': bike_data.get('b_num'),
        'user_id': bike_data.get('belong_to'),
        'brand': bike_data.get('brand'),
        'color': bike_data.get('color'),
        'type': bike_data.get('b_type'),
        'status': bike_data.get('status'),
        'created_at': bike_data.get('created_at'),
        'updated_at': bike_data.get('updated_at')
    }

    return api_response(
        data={'bike': formatted_bike},
        message="获取车辆信息成功"
    )

# 更新自行车信息
@bikes_bp.route('/bikes/<int:bike_id>', methods=['PUT'])
@bike_owner_or_admin_required()
def update_bike(bike_id):
    """更新自行车信息

    普通用户只能更新自己的车辆，管理员可以更新任何车辆
    """
    # 查找自行车
    bike = Bikes.query.get(bike_id)

    if not bike:
        return api_response(message="车辆不存在", status="error", code=404)

    data = request.get_json()

    # 统一字段名称映射
    field_mapping = {
        'bike_number': 'b_num',
        'user_id': 'belong_to',
        'type': 'b_type'
    }

    # 将前端字段名转换为后端字段名
    backend_data = {}
    for frontend_field, backend_field in field_mapping.items():
        if frontend_field in data:
            backend_data[backend_field] = data[frontend_field]

    # 添加未映射的字段
    for field in ['brand', 'color', 'status']:
        if field in data:
            backend_data[field] = data[field]

    # 不允许非管理员修改所属用户
    if 'belong_to' in backend_data and not is_admin():
        del backend_data['belong_to']

    # 检查车牌号是否已存在（排除当前车辆）
    if 'b_num' in backend_data and backend_data['b_num'] != bike.b_num:
        existing_bike = Bikes.query.filter_by(b_num=backend_data['b_num']).first()
        if existing_bike and existing_bike.b_id != bike_id:
            return api_response(message="该车牌号已存在，请使用其他车牌号", status="error", code=400)

    try:
        # 更新车辆信息
        for key, value in backend_data.items():
            setattr(bike, key, value)

        # 更新修改时间
        bike.updated_at = datetime.now()

        db.session.commit()

        # 格式化返回数据
        formatted_bike = {
            'id': bike.b_id,
            'bike_number': bike.b_num,
            'user_id': bike.belong_to,
            'brand': bike.brand,
            'color': bike.color,
            'type': bike.b_type,
            'status': bike.status,
            'created_at': bike.created_at,
            'updated_at': bike.updated_at
        }

        return api_response(
            data={'bike': formatted_bike},
            message="更新车辆信息成功"
        )
    except Exception as e:
        db.session.rollback()
        print(f"更新车辆失败: {str(e)}")
        return api_response(message=f"更新车辆失败: {str(e)}", status="error", code=500)

# 删除自行车
@bikes_bp.route('/bikes/<int:bike_id>', methods=['DELETE'])
@bike_owner_or_admin_required()
def delete_bike(bike_id):
    """删除自行车

    普通用户只能删除自己的车辆，管理员可以删除任何车辆
    """
    # 查找自行车
    bike = Bikes.query.get(bike_id)

    if not bike:
        return api_response(message="车辆不存在", status="error", code=404)

    try:
        # 删除车辆
        db.session.delete(bike)
        db.session.commit()

        return api_response(message="车辆删除成功")
    except Exception as e:
        db.session.rollback()
        return api_response(message=f"删除车辆失败: {str(e)}", status="error", code=500)

# 管理员获取所有自行车
@bikes_bp.route('/admin/bikes', methods=['GET'])
@jwt_required()
def admin_get_bikes():
    """管理员获取所有自行车"""
    # 权限检查
    if not is_admin():
        return api_response(message="需要管理员权限", status="error", code=403)

    # 分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)

    # 过滤参数
    status = request.args.get('status')
    b_num = request.args.get('b_num')
    user_id = request.args.get('user_id')

    # 构建查询
    query = Bikes.query

    # 应用过滤条件
    if status:
        query = query.filter_by(status=status)
    if b_num:
        query = query.filter(Bikes.b_num.like(f'%{b_num}%'))
    if user_id:
        query = query.filter_by(belong_to=user_id)

    # 分页
    bikes_page = query.paginate(page=page, per_page=per_page)

    # 序列化
    schema = BikeSchema(many=True)
    bikes_data = schema.dump(bikes_page.items)

    return api_response(
        data={
            'bikes': bikes_data,
            'pagination': {
                'total': bikes_page.total,
                'pages': bikes_page.pages,
                'current_page': page,
                'per_page': per_page
            }
        },
        message="获取车辆列表成功"
    )

@bikes_bp.route('/bikes/stats', methods=['GET'])
@jwt_required()
def get_bike_stats():
    """获取车辆统计信息"""
    user_id = get_current_user_id()

    # 检查用户角色
    from flask_jwt_extended import get_jwt
    jwt_claims = get_jwt()
    user_role = jwt_claims.get('role', 'user')

    # 管理员和保安可查看所有统计，普通用户只能查看自己的统计
    if is_admin() or user_role == 'security':
        # 管理员和保安可查看所有统计
        total_count = Bikes.query.count()
        available_count = Bikes.query.filter_by(status='可用').count()
        unavailable_count = Bikes.query.filter_by(status='废弃').count()

        # 按品牌统计
        brand_stats = db.session.query(
            Bikes.brand, db.func.count(Bikes.b_id)
        ).group_by(Bikes.brand).all()

        # 按类型统计
        type_stats = db.session.query(
            Bikes.b_type, db.func.count(Bikes.b_id)
        ).group_by(Bikes.b_type).all()

        # 按颜色统计
        color_stats = db.session.query(
            Bikes.color, db.func.count(Bikes.b_id)
        ).group_by(Bikes.color).all()

        # 转换为字典格式
        by_brand = {brand: count for brand, count in brand_stats}
        by_type = {type_: count for type_, count in type_stats}
        by_color = {color: count for color, count in color_stats}

        return api_response(
            data={
                'total': total_count,
                'available': available_count,
                'unavailable': unavailable_count,
                'by_brand': by_brand,
                'by_type': by_type,
                'by_color': by_color
            },
            message="获取车辆统计信息成功"
        )
    else:
        # 普通用户只能查看自己的车辆统计
        total_count = Bikes.query.filter_by(belong_to=user_id).count()
        available_count = Bikes.query.filter_by(belong_to=user_id, status='可用').count()
        unavailable_count = Bikes.query.filter_by(belong_to=user_id, status='废弃').count()

        # 按品牌统计
        brand_stats = db.session.query(
            Bikes.brand, db.func.count(Bikes.b_id)
        ).filter_by(belong_to=user_id).group_by(Bikes.brand).all()

        # 按类型统计
        type_stats = db.session.query(
            Bikes.b_type, db.func.count(Bikes.b_id)
        ).filter_by(belong_to=user_id).group_by(Bikes.b_type).all()

        # 按颜色统计
        color_stats = db.session.query(
            Bikes.color, db.func.count(Bikes.b_id)
        ).filter_by(belong_to=user_id).group_by(Bikes.color).all()

        # 转换为字典格式
        by_brand = {brand: count for brand, count in brand_stats}
        by_type = {type_: count for type_, count in type_stats}
        by_color = {color: count for color, count in color_stats}

        return api_response(
            data={
                'total': total_count,
                'available': available_count,
                'unavailable': unavailable_count,
                'by_brand': by_brand,
                'by_type': by_type,
                'by_color': by_color
            },
            message="获取车辆统计信息成功"
        )

@bikes_bp.route('/bikes/batch-update', methods=['POST'])
@jwt_required()
def batch_update_bike_status():
    """批量更新车辆状态

    普通用户只能更新自己的车辆状态，管理员可以更新任何车辆状态
    """
    # 权限检查
    user_id = get_current_user_id()
    if not user_id:
        return api_response(message="未登录或无法获取用户信息", status="error", code=401)

    # 获取请求数据
    data = request.get_json()
    if not data or 'bike_ids' not in data or 'status' not in data:
        return api_response(message="请求数据格式错误，需要提供bike_ids和status字段", status="error", code=400)

    # 验证状态值
    valid_status = ['可用', '废弃']
    if data['status'] not in valid_status:
        return api_response(message=f"状态值必须是以下之一: {', '.join(valid_status)}", status="error", code=400)

    # 验证ID列表
    bike_ids = data['bike_ids']
    if not isinstance(bike_ids, list) or not all(isinstance(id, int) for id in bike_ids):
        return api_response(message="bike_ids必须是整数ID列表", status="error", code=400)

    # 获取指定ID的电动车
    bikes = Bikes.query.filter(Bikes.b_id.in_(bike_ids)).all()

    # 检查权限：只能修改自己的车或管理员可以修改所有
    if not is_admin():
        for bike in bikes:
            if bike.belong_to != user_id:
                return api_response(message="没有权限修改某些车辆", status="error", code=403)

    # 更新状态
    updated_ids = []
    try:
        for bike in bikes:
            bike.status = data['status']
            bike.updated_at = datetime.now()
            updated_ids.append(bike.b_id)

        db.session.commit()

        return api_response(
            data={'updated_ids': updated_ids},
            message=f"成功更新{len(updated_ids)}辆车辆状态为{data['status']}"
        )
    except Exception as e:
        db.session.rollback()
        return api_response(message=f"批量更新状态失败: {str(e)}", status="error", code=500)

@bikes_bp.route('/bikes/export', methods=['GET'])
@filter_bikes_by_ownership
def export_bikes_csv():
    """导出车辆数据为CSV格式

    普通用户只能导出自己的车辆数据，管理员可以导出所有车辆数据
    """
    # 权限检查
    user_id = get_current_user_id()
    if not user_id:
        return api_response(message="未登录或无法获取用户信息", status="error", code=401)

    # 获取查询参数
    status = request.args.get('status')
    brand = request.args.get('brand')
    bike_number = request.args.get('bike_number')
    type_filter = request.args.get('type')

    # 构建查询
    query = Bikes.query

    # filter_bikes_by_ownership装饰器已经确保普通用户只能看到自己的车辆

    # 应用过滤条件
    if status:
        query = query.filter_by(status=status)
    if brand:
        query = query.filter_by(brand=brand)
    if type_filter:
        query = query.filter_by(b_type=type_filter)
    if bike_number:
        query = query.filter(Bikes.b_num.like(f'%{bike_number}%'))

    # 获取结果
    bikes = query.all()

    # 序列化数据
    schema = BikeSchema(many=True)
    bikes_data = schema.dump(bikes)

    # 准备CSV数据
    output = io.StringIO()
    fieldnames = ['车牌号', '所属用户ID', '品牌', '颜色', '类型', '状态', '注册时间', '更新时间']
    writer = csv.DictWriter(output, fieldnames=fieldnames)
    writer.writeheader()

    for bike in bikes_data:
        writer.writerow({
            '车牌号': bike.get('b_num', ''),
            '所属用户ID': bike.get('belong_to', ''),
            '品牌': bike.get('brand', ''),
            '颜色': bike.get('color', ''),
            '类型': bike.get('b_type', ''),
            '状态': bike.get('status', ''),
            '注册时间': bike.get('created_at', ''),
            '更新时间': bike.get('updated_at', '')
        })

    # 设置响应头，告诉浏览器这是一个CSV文件
    output_string = output.getvalue()
    output.close()

    filename = f"bikes-export-{datetime.now().strftime('%Y%m%d%H%M%S')}.csv"

    response = Response(
        output_string,
        mimetype="text/csv",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

    return response
