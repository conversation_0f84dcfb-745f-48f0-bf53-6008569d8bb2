"""empty message

Revision ID: 26f22139688a
Revises: 5792f68673ff
Create Date: 2025-05-15 13:44:09.375688

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '26f22139688a'
down_revision = '5792f68673ff'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # SQLite不能在一个execute中执行多条语句，需要分开执行

    # 关闭外键约束
    op.execute("PRAGMA foreign_keys=off")

    # 创建临时表
    op.execute("""
    CREATE TABLE charging_exceptions_temp (
        id INTEGER NOT NULL,
        charging_record_id INTEGER,
        fault_id INTEGER,
        space_id INTEGER,
        parking_lot_id INTEGER,
        exception_type VARCHAR(20) NOT NULL,
        description TEXT NOT NULL,
        time DATETIME NOT NULL,
        status INTEGER NOT NULL,
        processor VARCHAR(50),
        process_time DATETIME,
        process_result TEXT,
        created_at DATETIME,
        updated_at DATETIME,
        PRIMARY KEY (id),
        FOREIGN KEY(charging_record_id) REFERENCES charging_records (id) ON DELETE SET NULL,
        FOREIGN KEY(fault_id) REFERENCES charging_faults (id) ON DELETE SET NULL,
        FOREIGN KEY(space_id) REFERENCES parking_spaces (id) ON DELETE SET NULL,
        FOREIGN KEY(parking_lot_id) REFERENCES parking_lots (id) ON DELETE SET NULL
    )
    """)

    # 复制数据
    op.execute("""
    INSERT INTO charging_exceptions_temp
    SELECT id, charging_record_id, fault_id, space_id, parking_lot_id, exception_type,
           description, time, status, processor, process_time, process_result,
           created_at, updated_at
    FROM charging_exceptions
    """)

    # 删除原表
    op.execute("DROP TABLE charging_exceptions")

    # 重命名临时表
    op.execute("ALTER TABLE charging_exceptions_temp RENAME TO charging_exceptions")

    # 开启外键约束
    op.execute("PRAGMA foreign_keys=on")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # SQLite不能在一个execute中执行多条语句，需要分开执行

    # 关闭外键约束
    op.execute("PRAGMA foreign_keys=off")

    # 创建临时表（恢复原始结构）
    op.execute("""
    CREATE TABLE charging_exceptions_temp (
        id INTEGER NOT NULL,
        charging_record_id INTEGER NOT NULL,
        fault_id INTEGER,
        space_id INTEGER,
        parking_lot_id INTEGER,
        exception_type VARCHAR(20) NOT NULL,
        description TEXT NOT NULL,
        time DATETIME NOT NULL,
        status INTEGER NOT NULL,
        processor VARCHAR(50),
        process_time DATETIME,
        process_result TEXT,
        created_at DATETIME,
        updated_at DATETIME,
        PRIMARY KEY (id),
        FOREIGN KEY(charging_record_id) REFERENCES charging_records (id) ON DELETE CASCADE,
        FOREIGN KEY(fault_id) REFERENCES charging_faults (id),
        FOREIGN KEY(space_id) REFERENCES parking_spaces (id),
        FOREIGN KEY(parking_lot_id) REFERENCES parking_lots (id)
    )
    """)

    # 复制数据
    op.execute("""
    INSERT INTO charging_exceptions_temp
    SELECT id, charging_record_id, fault_id, space_id, parking_lot_id, exception_type,
           description, time, status, processor, process_time, process_result,
           created_at, updated_at
    FROM charging_exceptions
    """)

    # 删除原表
    op.execute("DROP TABLE charging_exceptions")

    # 重命名临时表
    op.execute("ALTER TABLE charging_exceptions_temp RENAME TO charging_exceptions")

    # 开启外键约束
    op.execute("PRAGMA foreign_keys=on")
    # ### end Alembic commands ###
