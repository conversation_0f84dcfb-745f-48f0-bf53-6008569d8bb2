<template>
  <div class="app-container">
    <el-card class="box-card violation-public-card">
      <div slot="header" class="clearfix">
        <span class="card-title">违规公示</span>
      </div>

      <violation-list
        :fetch-list-method="fetchAllViolations"
        :show-create-button="false"
        :show-date-range="true"
        :show-recorder="false"
        :show-handler="false"
        :can-appeal="false"
        :can-handle="false"
        :show-statistics="false"
        :default-sort="{ prop: 'id', order: 'ascending' }"
        @detail="handleDetail"
      />
    </el-card>
  </div>
</template>

<script>
import { getPublicViolations } from '@/api/violations'
import ViolationList from '../components/ViolationList'

export default {
  name: 'ViolationPublicRecords',
  components: {
    ViolationList
  },
  data() {
    return {}
  },
  methods: {
    fetchAllViolations(params) {
      // 确保按ID升序排序
      params.sort = 'id'
      params.order = 'asc'
      return getPublicViolations(params)
    },
    handleDetail(row) {
      // 跳转到详情页面
      this.$router.push({ name: 'PublicViolationDetail', params: { id: row.id }})
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.violation-public-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-title {
    font-size: 18px;
    font-weight: 600;
    color: #409EFF;
  }
}
</style>
