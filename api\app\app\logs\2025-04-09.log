2025/04/09 13:24:52 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/09 13:24:52 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/09 13:24:52 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/09 13:24:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:24:58] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/09 13:24:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:24:59] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/09 13:25:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:05] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/09 13:25:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:05] "POST /api/login HTTP/1.1" 200 -
2025/04/09 13:25:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:05] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:25:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:05] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:25:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:10] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:25:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:10] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:25:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:12] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:25:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:12] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:25:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:12] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:25:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:12] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:25:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:15] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:25:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:28] "OPTIONS /api/bikes/9 HTTP/1.1" 200 -
2025/04/09 13:25:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:28] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/09 13:25:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:28] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:25:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:28] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:25:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:33] "OPTIONS /api/bikes/9 HTTP/1.1" 200 -
2025/04/09 13:25:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:33] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/09 13:25:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:34] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:25:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:34] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:25:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:38] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/09 13:25:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:38] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:25:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:40] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:25:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:40] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:25:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:25:42] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:30:12 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/09 13:30:12 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/09 13:30:12 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/09 13:30:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:15] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:30:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:15] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:30:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:17] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:30:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:17] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:30:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:17] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:30:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:20] "OPTIONS /api/bikes/9 HTTP/1.1" 200 -
2025/04/09 13:30:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:20] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/09 13:30:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:21] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:30:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:21] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:30:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:27] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:30:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:27] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:44] "OPTIONS /api/bikes/9 HTTP/1.1" 200 -
2025/04/09 13:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:44] "DELETE /api/bikes/9 HTTP/1.1" 200 -
2025/04/09 13:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:44] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:44] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:30:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:55] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/09 13:30:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:30:55] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/09 13:31:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:31:02] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:31:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:31:02] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:31:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:31:02] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:31:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:31:02] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:32:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:32:46] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:32:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:32:46] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:32:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:32:48] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:32:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:32:48] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:32:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:32:49] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:32:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:32:51] "OPTIONS /api/bikes/12 HTTP/1.1" 200 -
2025/04/09 13:32:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:32:51] "DELETE /api/bikes/12 HTTP/1.1" 200 -
2025/04/09 13:32:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:32:51] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:32:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:32:51] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:33:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:33:03] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/09 13:33:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:33:03] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/09 13:33:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:33:10] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/09 13:33:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:33:10] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/09 13:33:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:33:14] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/09 13:33:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:33:30] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:33:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:33:30] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:33:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:33:30] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:33:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:33:30] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:38:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:00] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:38:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:00] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:38:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:02] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:38:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:02] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:38:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:03] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:38:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:16] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/09 13:38:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:16] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/09 13:38:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:22] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/09 13:38:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:22] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/09 13:38:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:22] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:38:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:22] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:38:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:31] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:38:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:31] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:38:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:31] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:38:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:38:31] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:39:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:39:25] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:39:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:39:25] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:39:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:39:25] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:39:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:39:25] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:39:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:39:27] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:40:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:40:54] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:40:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:40:54] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:40:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:40:58] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:40:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:40:58] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:40:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:40:59] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:41:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:41:07] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:41:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:41:07] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:41:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:41:07] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:41:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:41:07] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:41:18 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/09 13:41:18 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/09 13:41:18 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/09 13:41:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:41:26] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:41:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:41:26] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:41:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:41:26] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:41:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:41:26] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:41:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:41:30] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:41:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:41:30] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:41:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:41:31] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:42:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:42:48] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:42:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:42:48] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:42:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:42:52] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:42:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:42:52] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:42:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:42:52] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:42:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:42:59] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:42:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:42:59] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:43:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:43:00] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:43:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:43:01] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:43:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:43:06] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:43:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:43:06] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:43:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:43:06] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:43:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:43:06] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:45:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:05] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:45:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:05] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:45:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:08] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:45:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:08] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:45:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:09] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:45:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:13] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:45:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:13] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:45:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:14] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:45:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:18] "OPTIONS /api/bikes/11 HTTP/1.1" 200 -
2025/04/09 13:45:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:18] "PUT /api/bikes/11 HTTP/1.1" 200 -
2025/04/09 13:45:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:18] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:45:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:18] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:45:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:24] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/09 13:45:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:24] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/09 13:45:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:24] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:45:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:45:24] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:49:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:49:28] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:49:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:49:28] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:49:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:49:31] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:49:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:49:31] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:49:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:49:31] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:52:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:52:17] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:52:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:52:17] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:52:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:52:21] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:52:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:52:21] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:52:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:52:22] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:52:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:52:25] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:52:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:52:25] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:52:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:52:25] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:52:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:52:25] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:53:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:53:51] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:53:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:53:51] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:53:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:53:54] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:53:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:53:54] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:53:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:53:54] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:53:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:53:58] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:53:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:53:58] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:55:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:55:40] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:55:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:55:40] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:55:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:55:43] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/09 13:55:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:55:43] "GET /api/users/me HTTP/1.1" 200 -
2025/04/09 13:55:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:55:43] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:55:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:55:46] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:55:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:55:46] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/09 13:57:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:57:49] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/09 13:57:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Apr/2025 13:57:49] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
