<template>
  <div class="charging-notification">
    <!-- 通知列表 -->
    <div class="notification-list" v-if="notifications.length > 0">
      <div
        v-for="(notification, index) in notifications"
        :key="index"
        class="notification-item"
        :class="{ 'unread': !notification.read }"
        @click="handleNotificationClick(notification)"
      >
        <div class="notification-icon">
          <i :class="getNotificationIcon(notification.type)"></i>
        </div>
        <div class="notification-content">
          <div class="notification-title">{{ notification.title }}</div>
          <div class="notification-message">{{ notification.message }}</div>
          <div class="notification-time">{{ formatTime(notification.time) }}</div>
        </div>
        <div class="notification-actions">
          <el-button
            type="text"
            size="mini"
            @click.stop="markAsRead(notification)"
            v-if="!notification.read"
          >
            标为已读
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 无通知时显示 -->
    <div class="no-notification" v-else>
      <i class="el-icon-bell"></i>
      <span>暂无通知</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChargingNotification',
  props: {
    // 通知列表
    notifications: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 处理通知点击
    handleNotificationClick(notification) {
      // 标记为已读
      this.markAsRead(notification)
      
      // 触发点击事件
      this.$emit('click', notification)
    },
    
    // 标记为已读
    markAsRead(notification) {
      if (!notification.read) {
        // 修改通知状态
        notification.read = true
        
        // 触发已读事件
        this.$emit('read', notification)
      }
    },
    
    // 获取通知图标
    getNotificationIcon(type) {
      const iconMap = {
        'reservation': 'el-icon-date',
        'charging': 'el-icon-lightning',
        'complete': 'el-icon-check',
        'warning': 'el-icon-warning',
        'info': 'el-icon-info'
      }
      return iconMap[type] || 'el-icon-bell'
    },
    
    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      
      const date = new Date(time)
      const now = new Date()
      const diff = now - date
      
      // 一分钟内
      if (diff < 60 * 1000) {
        return '刚刚'
      }
      
      // 一小时内
      if (diff < 60 * 60 * 1000) {
        return `${Math.floor(diff / (60 * 1000))}分钟前`
      }
      
      // 一天内
      if (diff < 24 * 60 * 60 * 1000) {
        return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
      }
      
      // 一周内
      if (diff < 7 * 24 * 60 * 60 * 1000) {
        return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
      }
      
      // 超过一周
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-notification {
  .notification-list {
    .notification-item {
      display: flex;
      padding: 12px;
      border-bottom: 1px solid #ebeef5;
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      &.unread {
        background-color: #f0f9ff;
        
        &:hover {
          background-color: #ecf5ff;
        }
        
        .notification-title {
          font-weight: 600;
        }
      }
      
      .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f0f9ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        i {
          font-size: 20px;
          color: #409EFF;
        }
      }
      
      .notification-content {
        flex: 1;
        
        .notification-title {
          font-size: 14px;
          color: #303133;
          margin-bottom: 5px;
        }
        
        .notification-message {
          font-size: 13px;
          color: #606266;
          margin-bottom: 5px;
        }
        
        .notification-time {
          font-size: 12px;
          color: #909399;
        }
      }
      
      .notification-actions {
        display: flex;
        align-items: center;
      }
    }
  }
  
  .no-notification {
    padding: 30px 0;
    text-align: center;
    color: #909399;
    
    i {
      font-size: 30px;
      margin-bottom: 10px;
      display: block;
    }
  }
}
</style>
