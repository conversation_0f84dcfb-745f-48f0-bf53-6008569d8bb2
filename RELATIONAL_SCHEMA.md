# 校园电动车管理系统 - 关系模式

## 数据库关系模式定义

### 1. 用户表 (USERS)
```sql
USERS(
    u_id: INTEGER PRIMARY KEY AUTOINCREMENT,
    u_name: VARCHAR(50) NOT NULL UNIQUE,
    u_pwd: VARCHAR(255) NOT NULL,
    salt: VARCHAR(36) NOT NULL,
    u_role: VARCHAR(20) NOT NULL DEFAULT 'user',
    u_belong: VARCHAR(50),
    u_phone: VARCHAR(20),
    u_email: VARCHAR(100),
    avatar: VARCHAR(255),
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    version: INTEGER DEFAULT 1
)
```
**约束条件:**
- PRIMARY KEY: u_id
- UNIQUE: u_name
- NOT NULL: u_id, u_name, u_pwd, salt, u_role
- CHECK: u_role IN ('admin', 'security', 'user')

### 2. 车辆表 (BIKES)
```sql
BIKES(
    b_id: INTEGER PRIMARY KEY AUTOINCREMENT,
    belong_to: INTEGER NOT NULL,
    b_num: VARCHAR(20) NOT NULL UNIQUE,
    brand: VARCHAR(255) NOT NULL DEFAULT '未知品牌',
    color: VARCHAR(20) NOT NULL DEFAULT '未知颜色',
    b_type: VARCHAR(255) NOT NULL DEFAULT '普通型号',
    status: VARCHAR(20) NOT NULL DEFAULT '可用',
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (belong_to) REFERENCES USERS(u_id)
)
```
**约束条件:**
- PRIMARY KEY: b_id
- FOREIGN KEY: belong_to → USERS(u_id)
- UNIQUE: b_num
- CHECK: status IN ('可用', '废弃')

### 3. 停车场表 (PARKING_LOTS)
```sql
PARKING_LOTS(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    name: VARCHAR(100) NOT NULL UNIQUE,
    address: VARCHAR(255) NOT NULL,
    total_spaces: INTEGER NOT NULL DEFAULT 0,
    occupied_spaces: INTEGER NOT NULL DEFAULT 0,
    longitude: FLOAT,
    latitude: FLOAT,
    opening_hours: VARCHAR(100) DEFAULT '24小时',
    status: INTEGER NOT NULL DEFAULT 1,
    description: TEXT,
    campus: VARCHAR(50),
    area: VARCHAR(50),
    manager: VARCHAR(50),
    contact: VARCHAR(20),
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP
)
```
**约束条件:**
- PRIMARY KEY: id
- UNIQUE: name
- CHECK: status IN (0, 1)
- CHECK: total_spaces >= 0, occupied_spaces >= 0

### 4. 车位表 (PARKING_SPACES)
```sql
PARKING_SPACES(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    parking_lot_id: INTEGER NOT NULL,
    space_number: VARCHAR(20) NOT NULL,
    type: INTEGER NOT NULL DEFAULT 1,
    status: INTEGER NOT NULL DEFAULT 0,
    current_vehicle_id: INTEGER,
    power: FLOAT,
    remarks: TEXT,
    last_maintenance_time: DATETIME,
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parking_lot_id) REFERENCES PARKING_LOTS(id),
    FOREIGN KEY (current_vehicle_id) REFERENCES BIKES(b_id) ON DELETE SET NULL
)
```
**约束条件:**
- PRIMARY KEY: id
- FOREIGN KEY: parking_lot_id → PARKING_LOTS(id)
- FOREIGN KEY: current_vehicle_id → BIKES(b_id) ON DELETE SET NULL
- CHECK: type IN (1, 2, 3) -- 1普通, 2残疾人, 3充电
- CHECK: status IN (0, 1, 2, 3, 4) -- 0空闲, 1已占用, 2故障, 3维修中, 4禁用

### 5. 停车记录表 (PARKING_RECORDS)
```sql
PARKING_RECORDS(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    vehicle_id: INTEGER NOT NULL,
    user_id: INTEGER NOT NULL,
    parking_lot_id: INTEGER NOT NULL,
    parking_space_id: INTEGER NOT NULL,
    entry_time: DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    exit_time: DATETIME,
    status: INTEGER NOT NULL DEFAULT 0,
    remarks: TEXT,
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vehicle_id) REFERENCES BIKES(b_id),
    FOREIGN KEY (user_id) REFERENCES USERS(u_id),
    FOREIGN KEY (parking_lot_id) REFERENCES PARKING_LOTS(id),
    FOREIGN KEY (parking_space_id) REFERENCES PARKING_SPACES(id)
)
```
**约束条件:**
- PRIMARY KEY: id
- FOREIGN KEY: vehicle_id → BIKES(b_id)
- FOREIGN KEY: user_id → USERS(u_id)
- FOREIGN KEY: parking_lot_id → PARKING_LOTS(id)
- FOREIGN KEY: parking_space_id → PARKING_SPACES(id)
- CHECK: status IN (0, 1, 2) -- 0进行中, 1已完成, 2异常

### 6. 违规类型表 (VIOLATION_TYPES)
```sql
VIOLATION_TYPES(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    name: VARCHAR(100) NOT NULL UNIQUE,
    description: TEXT,
    needs_admin: BOOLEAN NOT NULL DEFAULT FALSE,
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP
)
```
**约束条件:**
- PRIMARY KEY: id
- UNIQUE: name
- NOT NULL: id, name, needs_admin

### 7. 违规记录表 (VIOLATION_RECORDS)
```sql
VIOLATION_RECORDS(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    bike_number: VARCHAR(50) NOT NULL,
    bike_id: INTEGER,
    user_id: INTEGER NOT NULL,
    violation_time: DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    location: VARCHAR(100) NOT NULL,
    violation_type: VARCHAR(50) NOT NULL,
    violation_type_id: INTEGER,
    description: TEXT,
    status: INTEGER NOT NULL DEFAULT 0,
    result: TEXT,
    recorder_id: INTEGER NOT NULL,
    handler_id: INTEGER,
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (bike_id) REFERENCES BIKES(b_id),
    FOREIGN KEY (user_id) REFERENCES USERS(u_id),
    FOREIGN KEY (violation_type_id) REFERENCES VIOLATION_TYPES(id),
    FOREIGN KEY (recorder_id) REFERENCES USERS(u_id),
    FOREIGN KEY (handler_id) REFERENCES USERS(u_id)
)
```
**约束条件:**
- PRIMARY KEY: id
- FOREIGN KEY: bike_id → BIKES(b_id)
- FOREIGN KEY: user_id → USERS(u_id)
- FOREIGN KEY: violation_type_id → VIOLATION_TYPES(id)
- FOREIGN KEY: recorder_id → USERS(u_id)
- FOREIGN KEY: handler_id → USERS(u_id)
- CHECK: status IN (0, 1, 2, 3) -- 0待审核, 1已处理, 2申诉中, 3已撤销

### 8. 申诉表 (APPEALS)
```sql
APPEALS(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    violation_id: INTEGER NOT NULL UNIQUE,
    user_id: INTEGER NOT NULL,
    reason: TEXT NOT NULL,
    status: INTEGER NOT NULL DEFAULT 0,
    comment: TEXT,
    handler_id: INTEGER,
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (violation_id) REFERENCES VIOLATION_RECORDS(id),
    FOREIGN KEY (user_id) REFERENCES USERS(u_id),
    FOREIGN KEY (handler_id) REFERENCES USERS(u_id)
)
```
**约束条件:**
- PRIMARY KEY: id
- FOREIGN KEY: violation_id → VIOLATION_RECORDS(id)
- FOREIGN KEY: user_id → USERS(u_id)
- FOREIGN KEY: handler_id → USERS(u_id)
- UNIQUE: violation_id (一对一关系)
- CHECK: status IN (0, 1, 2) -- 0待审核, 1已通过, 2未通过

### 9. 充电记录表 (CHARGING_RECORDS)
```sql
CHARGING_RECORDS(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    parking_record_id: INTEGER NOT NULL,
    vehicle_id: INTEGER NOT NULL,
    user_id: INTEGER NOT NULL,
    parking_lot_id: INTEGER NOT NULL,
    parking_space_id: INTEGER NOT NULL,
    start_time: DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_time: DATETIME,
    duration: INTEGER,
    status: INTEGER NOT NULL DEFAULT 0,
    power: FLOAT,
    charging_type: INTEGER,
    remarks: TEXT,
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parking_record_id) REFERENCES PARKING_RECORDS(id),
    FOREIGN KEY (vehicle_id) REFERENCES BIKES(b_id),
    FOREIGN KEY (user_id) REFERENCES USERS(u_id),
    FOREIGN KEY (parking_lot_id) REFERENCES PARKING_LOTS(id),
    FOREIGN KEY (parking_space_id) REFERENCES PARKING_SPACES(id)
)
```
**约束条件:**
- PRIMARY KEY: id
- FOREIGN KEY: parking_record_id → PARKING_RECORDS(id)
- FOREIGN KEY: vehicle_id → BIKES(b_id)
- FOREIGN KEY: user_id → USERS(u_id)
- FOREIGN KEY: parking_lot_id → PARKING_LOTS(id)
- FOREIGN KEY: parking_space_id → PARKING_SPACES(id)
- CHECK: status IN (0, 1, 2) -- 0进行中, 1已完成, 2异常

### 10. 公告表 (ANNOUNCEMENTS)
```sql
ANNOUNCEMENTS(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    title: VARCHAR(200) NOT NULL,
    content: TEXT NOT NULL,
    type: VARCHAR(20) NOT NULL DEFAULT 'info',
    priority: INTEGER NOT NULL DEFAULT 1,
    target_roles: VARCHAR(100),
    is_active: BOOLEAN NOT NULL DEFAULT TRUE,
    start_time: DATETIME,
    end_time: DATETIME,
    created_by: INTEGER NOT NULL,
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES USERS(u_id)
)
```
**约束条件:**
- PRIMARY KEY: id
- FOREIGN KEY: created_by → USERS(u_id)
- CHECK: type IN ('system', 'parking', 'violation')
- CHECK: priority IN (1, 2, 3) -- 1低, 2中, 3高

### 11. 证据表 (EVIDENCES)
```sql
EVIDENCES(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    related_id: INTEGER NOT NULL,
    related_type: VARCHAR(20) NOT NULL,
    evidence_type: VARCHAR(20) NOT NULL,
    file_path: VARCHAR(255) NOT NULL,
    uploader_id: INTEGER NOT NULL,
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploader_id) REFERENCES USERS(u_id)
)
```
**约束条件:**
- PRIMARY KEY: id
- FOREIGN KEY: uploader_id → USERS(u_id)
- CHECK: related_type IN ('violation', 'appeal')
- CHECK: evidence_type IN ('image', 'video')

### 12. 充电预约表 (CHARGING_RESERVATIONS)
```sql
CHARGING_RESERVATIONS(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id: INTEGER NOT NULL,
    vehicle_id: INTEGER NOT NULL,
    parking_lot_id: INTEGER NOT NULL,
    start_time: DATETIME NOT NULL,
    end_time: DATETIME NOT NULL,
    status: INTEGER NOT NULL DEFAULT 0,
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES USERS(u_id),
    FOREIGN KEY (vehicle_id) REFERENCES BIKES(b_id),
    FOREIGN KEY (parking_lot_id) REFERENCES PARKING_LOTS(id)
)
```
**约束条件:**
- PRIMARY KEY: id
- FOREIGN KEY: user_id → USERS(u_id)
- FOREIGN KEY: vehicle_id → BIKES(b_id)
- FOREIGN KEY: parking_lot_id → PARKING_LOTS(id)
- CHECK: status IN (0, 1, 2, 3) -- 0待使用, 1已使用, 2已取消, 3已过期

### 13. 充电故障表 (CHARGING_FAULTS)
```sql
CHARGING_FAULTS(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    parking_lot_id: INTEGER NOT NULL,
    space_id: INTEGER NOT NULL,
    fault_type: VARCHAR(20) NOT NULL,
    severity: VARCHAR(20) NOT NULL,
    reporter_name: VARCHAR(50) NOT NULL,
    reporter_phone: VARCHAR(20),
    description: TEXT NOT NULL,
    status: INTEGER NOT NULL DEFAULT 0,
    process_result: TEXT,
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parking_lot_id) REFERENCES PARKING_LOTS(id) ON DELETE CASCADE,
    FOREIGN KEY (space_id) REFERENCES PARKING_SPACES(id) ON DELETE CASCADE
)
```
**约束条件:**
- PRIMARY KEY: id
- FOREIGN KEY: parking_lot_id → PARKING_LOTS(id) ON DELETE CASCADE
- FOREIGN KEY: space_id → PARKING_SPACES(id) ON DELETE CASCADE
- CHECK: fault_type IN ('connector', 'startup', 'interruption', 'other')
- CHECK: severity IN ('low', 'medium', 'high', 'critical')
- CHECK: status IN (0, 1, 2) -- 0待处理, 1处理中, 2已完成

### 14. 充电异常表 (CHARGING_EXCEPTIONS)
```sql
CHARGING_EXCEPTIONS(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    charging_record_id: INTEGER,
    fault_id: INTEGER,
    space_id: INTEGER,
    parking_lot_id: INTEGER,
    exception_type: VARCHAR(20) NOT NULL,
    severity: VARCHAR(20) NOT NULL,
    description: TEXT NOT NULL,
    status: INTEGER NOT NULL DEFAULT 0,
    process_result: TEXT,
    created_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (charging_record_id) REFERENCES CHARGING_RECORDS(id) ON DELETE SET NULL,
    FOREIGN KEY (fault_id) REFERENCES CHARGING_FAULTS(id) ON DELETE SET NULL,
    FOREIGN KEY (space_id) REFERENCES PARKING_SPACES(id) ON DELETE SET NULL,
    FOREIGN KEY (parking_lot_id) REFERENCES PARKING_LOTS(id) ON DELETE SET NULL
)
```
**约束条件:**
- PRIMARY KEY: id
- FOREIGN KEY: charging_record_id → CHARGING_RECORDS(id) ON DELETE SET NULL
- FOREIGN KEY: fault_id → CHARGING_FAULTS(id) ON DELETE SET NULL
- FOREIGN KEY: space_id → PARKING_SPACES(id) ON DELETE SET NULL
- FOREIGN KEY: parking_lot_id → PARKING_LOTS(id) ON DELETE SET NULL
- CHECK: exception_type IN ('connector', 'startup', 'interruption', 'other')
- CHECK: severity IN ('low', 'medium', 'high', 'critical')
- CHECK: status IN (0, 1, 2) -- 0待处理, 1处理中, 2已完成

### 15. 玩家表 (PLAYERS) - 历史遗留
```sql
PLAYERS(
    id: INTEGER PRIMARY KEY AUTOINCREMENT,
    username: VARCHAR(120) NOT NULL UNIQUE,
    password: VARCHAR(120) NOT NULL,
    user_id: INTEGER,
    FOREIGN KEY (user_id) REFERENCES USERS(u_id)
)
```
**约束条件:**
- PRIMARY KEY: id
- FOREIGN KEY: user_id → USERS(u_id)
- UNIQUE: username

## 关系模式总结

### 主要实体关系
1. **USERS** ← 1:m → **BIKES** (用户拥有车辆)
2. **USERS** ← 1:1 → **PLAYERS** (用户关联游戏账户)
3. **PARKING_LOTS** ← 1:m → **PARKING_SPACES** (停车场包含车位)
4. **PARKING_SPACES** ← 1:m → **PARKING_RECORDS** (车位使用记录)
5. **VIOLATION_TYPES** ← 1:m → **VIOLATION_RECORDS** (违规类型分类)
6. **VIOLATION_RECORDS** ← 1:1 → **APPEALS** (违规申诉)
7. **PARKING_RECORDS** ← 1:m → **CHARGING_RECORDS** (停车关联充电)
8. **CHARGING_FAULTS** ← 1:m → **CHARGING_EXCEPTIONS** (故障产生异常)

### 数据完整性约束
1. **实体完整性**: 每个表都有主键约束
2. **参照完整性**: 外键约束保证数据一致性
3. **用户定义完整性**: CHECK约束限制字段值范围
4. **唯一性约束**: 防止重复数据 (用户名、车牌号等)

### 级联策略
- **CASCADE**: 删除停车场时级联删除相关故障记录
- **SET NULL**: 删除车辆时将车位的当前车辆ID置空
- **RESTRICT**: 默认策略，防止删除被引用的记录

### 索引建议
```sql
-- 性能优化索引
CREATE INDEX idx_bikes_belong_to ON BIKES(belong_to);
CREATE INDEX idx_parking_records_vehicle_id ON PARKING_RECORDS(vehicle_id);
CREATE INDEX idx_parking_records_user_id ON PARKING_RECORDS(user_id);
CREATE INDEX idx_parking_records_entry_time ON PARKING_RECORDS(entry_time);
CREATE INDEX idx_violation_records_user_id ON VIOLATION_RECORDS(user_id);
CREATE INDEX idx_violation_records_bike_id ON VIOLATION_RECORDS(bike_id);
CREATE INDEX idx_charging_records_vehicle_id ON CHARGING_RECORDS(vehicle_id);
CREATE INDEX idx_charging_records_start_time ON CHARGING_RECORDS(start_time);
```

## 关系模式简化表示

### 核心实体关系模式

| 表名 | 关系模式 | 主键 | 外键 |
|------|----------|------|------|
| **USERS** | USERS(u_id, u_name, u_pwd, salt, u_role, u_belong, u_phone, u_email, avatar, created_at, updated_at, version) | u_id | - |
| **BIKES** | BIKES(b_id, belong_to, b_num, brand, color, b_type, status, created_at, updated_at) | b_id | belong_to → USERS(u_id) |
| **PARKING_LOTS** | PARKING_LOTS(id, name, address, total_spaces, occupied_spaces, longitude, latitude, opening_hours, status, description, campus, area, manager, contact, created_at, updated_at) | id | - |
| **PARKING_SPACES** | PARKING_SPACES(id, parking_lot_id, space_number, type, status, current_vehicle_id, power, remarks, last_maintenance_time, created_at, updated_at) | id | parking_lot_id → PARKING_LOTS(id)<br>current_vehicle_id → BIKES(b_id) |
| **PARKING_RECORDS** | PARKING_RECORDS(id, vehicle_id, user_id, parking_lot_id, parking_space_id, entry_time, exit_time, status, remarks, created_at, updated_at) | id | vehicle_id → BIKES(b_id)<br>user_id → USERS(u_id)<br>parking_lot_id → PARKING_LOTS(id)<br>parking_space_id → PARKING_SPACES(id) |
| **VIOLATION_TYPES** | VIOLATION_TYPES(id, name, description, needs_admin, created_at, updated_at) | id | - |
| **VIOLATION_RECORDS** | VIOLATION_RECORDS(id, bike_number, bike_id, user_id, violation_time, location, violation_type, violation_type_id, description, status, result, recorder_id, handler_id, created_at, updated_at) | id | bike_id → BIKES(b_id)<br>user_id → USERS(u_id)<br>violation_type_id → VIOLATION_TYPES(id)<br>recorder_id → USERS(u_id)<br>handler_id → USERS(u_id) |
| **APPEALS** | APPEALS(id, violation_id, user_id, reason, status, comment, handler_id, created_at, updated_at) | id | violation_id → VIOLATION_RECORDS(id)<br>user_id → USERS(u_id)<br>handler_id → USERS(u_id) |
| **CHARGING_RECORDS** | CHARGING_RECORDS(id, parking_record_id, vehicle_id, user_id, parking_lot_id, parking_space_id, start_time, end_time, duration, status, power, charging_type, remarks, created_at, updated_at) | id | parking_record_id → PARKING_RECORDS(id)<br>vehicle_id → BIKES(b_id)<br>user_id → USERS(u_id)<br>parking_lot_id → PARKING_LOTS(id)<br>parking_space_id → PARKING_SPACES(id) |
| **ANNOUNCEMENTS** | ANNOUNCEMENTS(id, title, content, type, priority, target_roles, is_active, start_time, end_time, created_by, created_at, updated_at) | id | created_by → USERS(u_id) |
| **EVIDENCES** | EVIDENCES(id, related_id, related_type, evidence_type, file_path, uploader_id, created_at) | id | uploader_id → USERS(u_id) |
| **CHARGING_RESERVATIONS** | CHARGING_RESERVATIONS(id, user_id, vehicle_id, parking_lot_id, start_time, end_time, status, created_at, updated_at) | id | user_id → USERS(u_id)<br>vehicle_id → BIKES(b_id)<br>parking_lot_id → PARKING_LOTS(id) |
| **CHARGING_FAULTS** | CHARGING_FAULTS(id, parking_lot_id, space_id, fault_type, severity, reporter_name, reporter_phone, description, status, process_result, created_at, updated_at) | id | parking_lot_id → PARKING_LOTS(id)<br>space_id → PARKING_SPACES(id) |
| **CHARGING_EXCEPTIONS** | CHARGING_EXCEPTIONS(id, charging_record_id, fault_id, space_id, parking_lot_id, exception_type, severity, description, status, process_result, created_at, updated_at) | id | charging_record_id → CHARGING_RECORDS(id)<br>fault_id → CHARGING_FAULTS(id)<br>space_id → PARKING_SPACES(id)<br>parking_lot_id → PARKING_LOTS(id) |
| **PLAYERS** | PLAYERS(id, username, password, user_id) | id | user_id → USERS(u_id) |

### 函数依赖关系

#### 1. USERS表
- u_id → u_name, u_pwd, salt, u_role, u_belong, u_phone, u_email, avatar, created_at, updated_at, version
- u_name → u_id (候选键)

#### 2. BIKES表
- b_id → belong_to, b_num, brand, color, b_type, status, created_at, updated_at
- b_num → b_id (候选键)

#### 3. PARKING_LOTS表
- id → name, address, total_spaces, occupied_spaces, longitude, latitude, opening_hours, status, description, campus, area, manager, contact, created_at, updated_at
- name → id (候选键)

#### 4. PARKING_SPACES表
- id → parking_lot_id, space_number, type, status, current_vehicle_id, power, remarks, last_maintenance_time, created_at, updated_at
- (parking_lot_id, space_number) → id (复合候选键)

#### 5. PARKING_RECORDS表
- id → vehicle_id, user_id, parking_lot_id, parking_space_id, entry_time, exit_time, status, remarks, created_at, updated_at

#### 6. VIOLATION_RECORDS表
- id → bike_number, bike_id, user_id, violation_time, location, violation_type, violation_type_id, description, status, result, recorder_id, handler_id, created_at, updated_at

#### 7. APPEALS表
- id → violation_id, user_id, reason, status, comment, handler_id, created_at, updated_at
- violation_id → id (候选键，一对一关系)

### 范式分析

#### 第一范式 (1NF)
✅ **满足**: 所有表的属性都是原子性的，不可再分

#### 第二范式 (2NF)
✅ **满足**: 所有非主属性完全函数依赖于主键

#### 第三范式 (3NF)
✅ **满足**: 消除了传递依赖，非主属性不依赖于其他非主属性

#### BC范式 (BCNF)
✅ **基本满足**: 每个决定因素都是候选键

### 业务规则约束

1. **用户角色约束**: u_role ∈ {'admin', 'security', 'user'}
2. **车辆状态约束**: status ∈ {'可用', '废弃'}
3. **车位类型约束**: type ∈ {1, 2, 3} (普通、残疾人、充电)
4. **车位状态约束**: status ∈ {0, 1, 2, 3, 4} (空闲、已占用、故障、维修中、禁用)
5. **停车记录状态约束**: status ∈ {0, 1, 2} (进行中、已完成、异常)
6. **违规记录状态约束**: status ∈ {0, 1, 2, 3} (待审核、已处理、申诉中、已撤销)
7. **申诉状态约束**: status ∈ {0, 1, 2} (待审核、已通过、未通过)
8. **一对一约束**: 每个违规记录最多对应一个申诉
9. **时间约束**: exit_time ≥ entry_time, end_time ≥ start_time
