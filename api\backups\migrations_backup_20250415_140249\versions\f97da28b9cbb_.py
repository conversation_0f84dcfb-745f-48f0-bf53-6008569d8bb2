"""empty message

Revision ID: f97da28b9cbb
Revises:
Create Date: 2025-04-15 13:59:36.733768

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f97da28b9cbb'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('parking_lots',
    sa.<PERSON>umn('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('address', sa.String(length=255), nullable=False),
    sa.Column('total_spaces', sa.Integer(), nullable=False),
    sa.Column('occupied_spaces', sa.Integer(), nullable=False),
    sa.Column('longitude', sa.Float(), nullable=True),
    sa.Column('latitude', sa.Float(), nullable=True),
    sa.Column('opening_hours', sa.String(length=100), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('bikes',
    sa.Column('b_id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('belong_to', sa.Integer(), nullable=False),
    sa.Column('b_num', sa.String(length=20), nullable=False, comment='车牌号'),
    sa.Column('brand', sa.String(length=255), nullable=False),
    sa.Column('color', sa.String(length=20), nullable=False),
    sa.Column('b_type', sa.String(length=255), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['belong_to'], ['users.u_id'], ),
    sa.PrimaryKeyConstraint('b_id'),
    sa.UniqueConstraint('b_num')
    )
    with op.batch_alter_table('bikes', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_bikes_belong_to'), ['belong_to'], unique=False)

    op.create_table('parking_spaces',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('parking_lot_id', sa.Integer(), nullable=False),
    sa.Column('space_number', sa.String(length=20), nullable=False),
    sa.Column('type', sa.Integer(), nullable=False),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('current_vehicle_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['current_vehicle_id'], ['bikes.b_id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['parking_lot_id'], ['parking_lots.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('parking_records',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('vehicle_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('parking_lot_id', sa.Integer(), nullable=False),
    sa.Column('parking_space_id', sa.Integer(), nullable=False),
    sa.Column('entry_time', sa.DateTime(), nullable=False),
    sa.Column('exit_time', sa.DateTime(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['parking_lot_id'], ['parking_lots.id'], ),
    sa.ForeignKeyConstraint(['parking_space_id'], ['parking_spaces.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.u_id'], ),
    sa.ForeignKeyConstraint(['vehicle_id'], ['bikes.b_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('players', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.create_unique_constraint('uq_players_username', ['username'])

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('u_email', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('avatar', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('version', sa.Integer(), nullable=True))
        batch_op.alter_column('u_id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.create_unique_constraint('uq_users_u_name', ['u_name'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_constraint('uq_users_u_name', type_='unique')
        batch_op.alter_column('u_id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)
        batch_op.drop_column('version')
        batch_op.drop_column('avatar')
        batch_op.drop_column('u_email')

    with op.batch_alter_table('players', schema=None) as batch_op:
        batch_op.drop_constraint('uq_players_username', type_='unique')
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    op.drop_table('parking_records')
    op.drop_table('parking_spaces')
    with op.batch_alter_table('bikes', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_bikes_belong_to'))

    op.drop_table('bikes')
    op.drop_table('parking_lots')
    # ### end Alembic commands ###
