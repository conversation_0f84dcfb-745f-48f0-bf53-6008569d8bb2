from marshmallow_sqlalchemy import SQLAlchemyAutoSchema
from marshmallow import fields, post_load, validates, ValidationError
from app.bikes.models import Bikes
from app import db
import time
import re


# 序列接口数据为JSON格式
class BikeSchema(SQLAlchemyAutoSchema):
  class Meta:
    model = Bikes
    sqla_session = db.session

  b_id = fields.Integer(dump_only=True)
  belong_to = fields.Integer(required=True)
  b_num = fields.String(required=True)
  brand = fields.String(required=False, missing="未知品牌")
  color = fields.String(required=False, missing="未知颜色")
  b_type = fields.String(required=False, missing="普通型号")
  status = fields.String(required=False, missing="可用")  # 只能是"可用"或"废弃"
  created_at = fields.DateTime(dump_only=True)
  updated_at = fields.DateTime(dump_only=True)

  @validates('status')
  def validate_status(self, status):
    """验证status字段只能是'可用'或'废弃'"""
    valid_status = ['可用', '废弃']
    if status not in valid_status:
      raise ValidationError(f'状态必须是以下值之一: {", ".join(valid_status)}，而不是 "{status}"')

  @validates('belong_to')
  def validate_belong_to(self, belong_to):
    """验证belong_to必须是正整数"""
    if not isinstance(belong_to, int) or belong_to <= 0:
      raise ValidationError('所属用户ID必须是正整数')

  @validates('b_num')
  def validate_b_num(self, b_num):
    """验证车牌号不为空且格式正确"""
    if not b_num or b_num.strip() == '':
      raise ValidationError('车牌号不能为空')
    
    # 车牌号长度验证
    if len(b_num) > 20:
      raise ValidationError('车牌号长度不能超过20个字符')

    if len(b_num) < 2:
      raise ValidationError('车牌号长度至少需要2个字符')
      
    # 中国标准车牌号格式验证（可选）
    # 支持普通车牌、新能源车牌等
    if b_num.startswith('临时车牌-') or b_num.startswith('未知车牌'):
      # 临时车牌号直接通过验证
      return
      
    # 正则表达式匹配中国车牌号
    pattern = r'^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]{1}[A-Z]{1}[A-Z0-9]{4,5}[A-Z0-9挂学警港澳]{1}$'
    if not re.match(pattern, b_num):
      # 不是标准车牌格式，但我们仍然接受，只是给出警告
      print(f'警告: 车牌号 "{b_num}" 不符合标准格式')
      
  @validates('color')
  def validate_color(self, color):
    """验证颜色格式正确"""
    valid_colors = ['黑色', '白色', '红色', '蓝色', '黄色', '绿色', '灰色', '银色', '金色', '棕色', '紫色', '粉色', '橙色', '未知颜色']
    if color and color not in valid_colors and not color.endswith('色'):
      print(f'警告: 颜色 "{color}" 可能不是标准颜色名称')

  @post_load
  def ensure_defaults(self, data, **kwargs):
    """确保所有字段都有值并进行类型转换"""
    # 确保belong_to是整数
    if 'belong_to' in data:
      try:
        data['belong_to'] = int(data['belong_to'])
      except (ValueError, TypeError):
        # 如果无法转换为整数，使用默认值
        data['belong_to'] = 1
    else:
      data['belong_to'] = 1
      
    # 确保车牌号存在
    if not data.get('b_num'):
      data['b_num'] = f"临时车牌-{int(time.time())}"  # 使用时间戳创建唯一车牌号
      
    if not data.get('brand'):
      data['brand'] = "未知品牌"
      
    if not data.get('color'):
      data['color'] = "未知颜色"
      
    if not data.get('b_type'):
      data['b_type'] = "普通型号"

    # 确保status字段是有效值
    if not data.get('status') or data.get('status') not in ['可用', '废弃']:
      data['status'] = "可用"
      
    # 将类型转换为字符串类型并重命名
    if 'type' in data and not data.get('b_type'):
      data['b_type'] = str(data.pop('type'))

    return data
