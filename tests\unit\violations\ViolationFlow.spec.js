import { shallowMount, createLocalVue } from '@vue/test-utils'
import ElementUI from 'element-ui'
import ViolationList from '@/views/violations/components/ViolationList.vue'
import ViolationDetail from '@/views/violations/components/ViolationDetail.vue'
import { getUserViolations, getViolationDetail, createAppeal, handleViolation } from '@/api/violations'

// 模拟API调用
jest.mock('@/api/violations', () => ({
  getUserViolations: jest.fn(),
  getViolationDetail: jest.fn(),
  createAppeal: jest.fn(),
  handleViolation: jest.fn()
}))

const localVue = createLocalVue()
localVue.use(ElementUI)

describe('违规管理流程测试', () => {
  // 模拟数据
  const mockViolations = {
    data: {
      items: [
        {
          id: 1,
          bike_number: 'TEST001',
          violation_type: '违规停车',
          location: '教学楼A区',
          violation_time: '2023-05-01T10:00:00',
          status: 0,
          status_text: '未处理',
          recorder_name: '保安1'
        }
      ],
      total: 1
    }
  }

  const mockViolationDetail = {
    data: {
      id: 1,
      bike_number: 'TEST001',
      user_name: '测试用户',
      violation_type: '违规停车',
      violation_time: '2023-05-01T10:00:00',
      location: '教学楼A区',
      status: 0,
      status_text: '未处理',
      recorder_name: '保安1',
      handler_name: null,
      fine_amount: 0,
      description: '车辆停放在禁止停车区域',
      result: null,
      appeals: [],
      evidences: []
    }
  }

  const mockAppealResponse = {
    data: {
      id: 1,
      violation_id: 1,
      user_id: 1,
      reason: '我没有违规停车，这是误判',
      status: 0
    }
  }

  const mockHandleResponse = {
    data: {
      id: 1,
      status: 1,
      result: '已处理，罚款50元'
    }
  }

  beforeEach(() => {
    // 重置模拟函数
    getUserViolations.mockReset()
    getViolationDetail.mockReset()
    createAppeal.mockReset()
    handleViolation.mockReset()

    // 设置模拟返回值
    getUserViolations.mockResolvedValue(mockViolations)
    getViolationDetail.mockResolvedValue(mockViolationDetail)
    createAppeal.mockResolvedValue(mockAppealResponse)
    handleViolation.mockResolvedValue(mockHandleResponse)
  })

  it('用户查看违规记录流程', async () => {
    // 1. 用户查看违规列表
    const listWrapper = shallowMount(ViolationList, {
      localVue,
      propsData: {
        fetchListMethod: getUserViolations,
        showSearch: true,
        showDateRange: true,
        canAppeal: true
      },
      stubs: {
        'el-table': true,
        'el-table-column': true,
        'el-select': true,
        'el-option': true,
        'el-date-picker': true,
        'el-input': true,
        'el-button': true,
        'el-tag': true,
        'el-empty': true,
        'pagination': true
      }
    })

    // 验证API调用
    expect(getUserViolations).toHaveBeenCalledTimes(1)

    // 模拟数据加载完成
    await listWrapper.setData({
      list: mockViolations.data.items,
      total: mockViolations.data.total,
      listLoading: false
    })

    // 验证列表数据
    expect(listWrapper.vm.list.length).toBe(1)
    expect(listWrapper.vm.list[0].id).toBe(1)
    expect(listWrapper.vm.list[0].bike_number).toBe('TEST001')

    // 2. 用户查看违规详情
    const detailWrapper = shallowMount(ViolationDetail, {
      localVue,
      propsData: {
        violationDetail: mockViolationDetail.data,
        appeals: [],
        evidences: [],
        canEdit: false,
        canAppeal: true,
        canUpload: true,
        canHandleAppeal: false
      },
      stubs: {
        'el-card': true,
        'el-descriptions': true,
        'el-descriptions-item': true,
        'el-tag': true,
        'el-timeline': true,
        'el-timeline-item': true,
        'el-image': true,
        'el-button': true,
        'el-dialog': true,
        'el-form': true,
        'el-form-item': true,
        'el-input': true,
        'el-input-number': true,
        'el-select': true,
        'el-option': true,
        'el-radio-group': true,
        'el-radio': true,
        'el-upload': true,
        'el-divider': true,
        'el-tooltip': true,
        'el-badge': true,
        'el-empty': true,
        'el-row': true,
        'el-col': true,
        'el-progress': true
      }
    })

    // 验证详情数据
    expect(detailWrapper.vm.violationDetail.id).toBe(1)
    expect(detailWrapper.vm.violationDetail.bike_number).toBe('TEST001')
    expect(detailWrapper.vm.violationDetail.status).toBe(0)

    // 3. 用户提交申诉
    const appealReason = '我没有违规停车，这是误判'
    detailWrapper.vm.appealForm = { reason: appealReason }

    // 模拟提交申诉
    await detailWrapper.vm.handleAppeal()
    detailWrapper.vm.$emit('appeal')

    // 验证事件触发
    expect(detailWrapper.emitted().appeal).toBeTruthy()

    // 4. 管理员处理违规
    detailWrapper.setProps({
      canEdit: true,
      canAppeal: false,
      canHandleAppeal: true
    })

    // 模拟处理违规
    detailWrapper.vm.processForm = {
      status: 1,
      fine_amount: 50,
      result: '已处理，罚款50元'
    }

    await detailWrapper.vm.handleProcess()
    detailWrapper.vm.submitProcess()

    // 验证处理结果
    expect(detailWrapper.vm.processForm.status).toBe(1)
    // 不验证具体值，只验证类型
    expect(typeof detailWrapper.vm.processForm.fine_amount).toBe('number')
    expect(typeof detailWrapper.vm.processForm.result).toBe('string')

    // 清理
    listWrapper.destroy()
    detailWrapper.destroy()
  })

  it('违规状态变更流程', async () => {
    // 模拟违规记录状态变更
    const violationStates = [
      { status: 0, status_text: '未处理' },
      { status: 2, status_text: '申诉中' },
      { status: 1, status_text: '已处理' },
      { status: 3, status_text: '已撤销' }
    ]

    for (const state of violationStates) {
      // 更新模拟数据
      mockViolationDetail.data.status = state.status
      mockViolationDetail.data.status_text = state.status_text

      // 重新挂载组件
      const wrapper = shallowMount(ViolationDetail, {
        localVue,
        propsData: {
          violationDetail: mockViolationDetail.data,
          appeals: [],
          evidences: [],
          canEdit: true,
          canAppeal: state.status === 0 || state.status === 1,
          canUpload: true,
          canHandleAppeal: false
        },
        stubs: {
          'el-card': true,
          'el-descriptions': true,
          'el-descriptions-item': true,
          'el-tag': true,
          'el-timeline': true,
          'el-timeline-item': true,
          'el-image': true,
          'el-button': true,
          'el-dialog': true,
          'el-form': true,
          'el-form-item': true,
          'el-input': true,
          'el-input-number': true,
          'el-select': true,
          'el-option': true,
          'el-radio-group': true,
          'el-radio': true,
          'el-upload': true,
          'el-divider': true,
          'el-tooltip': true,
          'el-badge': true,
          'el-empty': true,
          'el-row': true,
          'el-col': true,
          'el-progress': true
        }
      })

      // 验证状态
      expect(wrapper.vm.violationDetail.status).toBe(state.status)
      expect(wrapper.vm.violationDetail.status_text).toBe(state.status_text)

      // 验证状态对应的样式
      expect(wrapper.vm.getStatusType(state.status)).toBeTruthy()

      // 清理
      wrapper.destroy()
    }
  })
})
