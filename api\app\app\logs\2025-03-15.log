2025/03/15 15:22:18 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 16:53:02 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 16:53:02 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/15 16:53:02 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/15 16:53:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 16:53:15] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/15 16:53:15 flask_api __init__.py[159] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/03/15 16:53:15 flask_api __init__.py[161] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 106, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/03/15 16:53:15 flask_api app.py[875] log_exception() ERROR: Exception on /api/login [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 106, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 809, in handle_user_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\try\try\vue-admin-template\api\app\__init__.py", line 162, in handle_exception
    return response_with(resp.SERVER_ERROR_500)
  File "D:\try\try\vue-admin-template\api\app\utils\responses.py", line 115, in response_with
    if response.get('message', None) is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'
2025/03/15 16:53:16 flask_api __init__.py[146] server_error() ERROR: 服务器错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/15 16:53:16 root __init__.py[147] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/15 16:53:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 16:53:16] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/03/15 16:53:16 werkzeug _internal.py[97] _log() ERROR: Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 106, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 809, in handle_user_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\try\try\vue-admin-template\api\app\__init__.py", line 162, in handle_exception
    return response_with(resp.SERVER_ERROR_500)
  File "D:\try\try\vue-admin-template\api\app\utils\responses.py", line 115, in response_with
    if response.get('message', None) is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
    ~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
  File "D:\try\try\vue-admin-template\api\app\__init__.py", line 148, in server_error
    return response_with(resp.SERVER_ERROR_500)
  File "D:\try\try\vue-admin-template\api\app\utils\responses.py", line 115, in response_with
    if response.get('message', None) is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'
2025/03/15 16:56:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 16:56:51] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/15 16:56:51 flask_api __init__.py[159] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/03/15 16:56:51 flask_api __init__.py[161] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 106, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/03/15 16:56:51 flask_api app.py[875] log_exception() ERROR: Exception on /api/login [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 106, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 809, in handle_user_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\try\try\vue-admin-template\api\app\__init__.py", line 162, in handle_exception
    return response_with(resp.SERVER_ERROR_500)
  File "D:\try\try\vue-admin-template\api\app\utils\responses.py", line 115, in response_with
    if response.get('message', None) is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'
2025/03/15 16:56:51 flask_api __init__.py[146] server_error() ERROR: 服务器错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/15 16:56:51 root __init__.py[147] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/15 16:56:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 16:56:51] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/03/15 16:56:51 werkzeug _internal.py[97] _log() ERROR: Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 106, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 809, in handle_user_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\try\try\vue-admin-template\api\app\__init__.py", line 162, in handle_exception
    return response_with(resp.SERVER_ERROR_500)
  File "D:\try\try\vue-admin-template\api\app\utils\responses.py", line 115, in response_with
    if response.get('message', None) is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
    ~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
  File "D:\try\try\vue-admin-template\api\app\__init__.py", line 148, in server_error
    return response_with(resp.SERVER_ERROR_500)
  File "D:\try\try\vue-admin-template\api\app\utils\responses.py", line 115, in response_with
    if response.get('message', None) is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'
2025/03/15 16:57:04 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 16:57:05 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/15 16:57:05 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/15 16:57:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 16:57:12] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/15 16:57:12 flask_api __init__.py[159] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/03/15 16:57:12 flask_api __init__.py[161] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 106, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/03/15 16:57:12 flask_api app.py[875] log_exception() ERROR: Exception on /api/login [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 106, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 809, in handle_user_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\try\try\vue-admin-template\api\app\__init__.py", line 162, in handle_exception
    return response_with(resp.SERVER_ERROR_500)
  File "D:\try\try\vue-admin-template\api\app\utils\responses.py", line 115, in response_with
    if response.get('message', None) is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'
2025/03/15 16:57:12 flask_api __init__.py[146] server_error() ERROR: 服务器错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/15 16:57:12 root __init__.py[147] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/15 16:57:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 16:57:12] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/03/15 16:57:12 werkzeug _internal.py[97] _log() ERROR: Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 106, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 809, in handle_user_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\try\try\vue-admin-template\api\app\__init__.py", line 162, in handle_exception
    return response_with(resp.SERVER_ERROR_500)
  File "D:\try\try\vue-admin-template\api\app\utils\responses.py", line 115, in response_with
    if response.get('message', None) is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
    ~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
  File "D:\try\try\vue-admin-template\api\app\__init__.py", line 148, in server_error
    return response_with(resp.SERVER_ERROR_500)
  File "D:\try\try\vue-admin-template\api\app\utils\responses.py", line 115, in response_with
    if response.get('message', None) is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'
2025/03/15 16:58:21 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 16:58:21 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/15 16:58:21 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/15 16:58:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 16:58:29] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/15 16:58:29 flask_api __init__.py[159] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/03/15 16:58:29 flask_api __init__.py[161] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 106, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/03/15 16:58:29 flask_api app.py[875] log_exception() ERROR: Exception on /api/login [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 106, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 809, in handle_user_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\try\try\vue-admin-template\api\app\__init__.py", line 162, in handle_exception
    return response_with(resp.SERVER_ERROR_500)
  File "D:\try\try\vue-admin-template\api\app\utils\responses.py", line 115, in response_with
    if response.get('message', None) is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'
2025/03/15 16:58:29 flask_api __init__.py[146] server_error() ERROR: 服务器错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/15 16:58:29 root __init__.py[147] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/15 16:58:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 16:58:29] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/03/15 16:58:29 werkzeug _internal.py[97] _log() ERROR: Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.created_at

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 106, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
    
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.created_at
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 809, in handle_user_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\try\try\vue-admin-template\api\app\__init__.py", line 162, in handle_exception
    return response_with(resp.SERVER_ERROR_500)
  File "D:\try\try\vue-admin-template\api\app\utils\responses.py", line 115, in response_with
    if response.get('message', None) is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
    ~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
  File "D:\try\try\vue-admin-template\api\app\__init__.py", line 148, in server_error
    return response_with(resp.SERVER_ERROR_500)
  File "D:\try\try\vue-admin-template\api\app\utils\responses.py", line 115, in response_with
    if response.get('message', None) is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'
2025/03/15 17:00:03 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 17:00:09 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 17:01:44 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 17:01:52 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 17:01:58 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 17:02:03 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 17:02:03 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/15 17:02:03 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/15 17:02:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:09] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/15 17:02:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:09] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/03/15 17:02:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:14] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/03/15 17:02:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:22] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/03/15 17:02:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:22] "OPTIONS /api/register HTTP/1.1" 200 -
2025/03/15 17:02:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:22] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/03/15 17:02:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:25] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/15 17:02:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:25] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:02:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:28] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:02:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:38] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/15 17:02:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:38] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:02:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:39] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:02:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:39] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:02:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:39] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:02:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:39] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:02:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:02:40] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:03:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:03:21] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/15 17:03:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:03:21] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:03:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:03:21] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:09:44 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 17:09:44 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/15 17:09:44 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/15 17:09:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:09:51] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/15 17:09:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:09:52] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:09:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:09:52] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/15 17:09:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:09:52] "[31m[1mGET /api/users HTTP/1.1[0m" 422 -
2025/03/15 17:10:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:10:15] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/15 17:10:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:10:15] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:10:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:10:15] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/15 17:10:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:10:15] "[31m[1mGET /api/users HTTP/1.1[0m" 422 -
2025/03/15 17:14:09 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 17:14:09 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/15 17:14:09 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/15 17:14:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:14:20] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/15 17:14:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:14:20] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:14:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:14:23] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/15 17:14:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:14:23] "[31m[1mGET /api/users HTTP/1.1[0m" 422 -
2025/03/15 17:14:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:14:32] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/15 17:14:32 flask_api __init__.py[140] not_found() ERROR: 路由未找到: /api/bikes, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0MjAzMDA2MCwianRpIjoiZTAzOWI5OTItNGRmMS00YjZiLWI0ZjYtN2Y3N2YzMDYyZTM2IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6MSwibmJmIjoxNzQyMDMwMDYwLCJjc3JmIjoiNmJkNTAzMDItY2EyOS00NjU5LTlmOGQtYzc2YmNhYzYxM2Y2IiwiZXhwIjoxNzQyMTE2NDYwfQ.4Jf25vvxbgrxSdgucSMc5P3eWXs-Tn8LklTt1a4RxFM

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/03/15 17:14:32 root __init__.py[141] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/15 17:14:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:14:32] "[33mGET /api/bikes HTTP/1.1[0m" 404 -
2025/03/15 17:19:59 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/15 17:19:59 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/15 17:19:59 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/15 17:20:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:20:36] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/03/15 17:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:20:43] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/03/15 17:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:20:43] "OPTIONS /api/register HTTP/1.1" 200 -
2025/03/15 17:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:20:43] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/03/15 17:20:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:20:46] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/15 17:20:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:20:46] "POST /api/login HTTP/1.1" 200 -
2025/03/15 17:20:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:20:48] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/03/15 17:20:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:20:48] "[31m[1mGET /api/users/me HTTP/1.1[0m" 422 -
2025/03/15 17:20:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:20:48] "OPTIONS /api/my-bikes HTTP/1.1" 200 -
2025/03/15 17:20:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:20:48] "[31m[1mGET /api/my-bikes HTTP/1.1[0m" 422 -
2025/03/15 17:20:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:20:55] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/15 17:20:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [15/Mar/2025 17:20:55] "GET /api/bikes HTTP/1.1" 200 -
