<template>
  <div class="app-container charging-reservation-container">
    <el-row :gutter="20">
      <!-- 快速预约卡片 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="reservation-card">
          <div slot="header" class="card-header">
            <span>快速预约充电</span>
          </div>
          <el-form ref="reservationForm" :model="reservationForm" :rules="reservationRules" label-position="top">
            <el-form-item label="选择车辆" prop="vehicle_id">
              <el-select v-model="reservationForm.vehicle_id" placeholder="请选择车辆" style="width: 100%">
                <el-option
                  v-for="vehicle in myVehicles"
                  :key="vehicle.id"
                  :label="`${vehicle.number} (${vehicle.brand})`"
                  :value="vehicle.id"
                >
                  <div class="vehicle-option">
                    <span>{{ vehicle.number }}</span>
                    <span class="vehicle-brand">{{ vehicle.brand }}</span>
                    <span class="vehicle-color" :style="{ backgroundColor: vehicle.color }"></span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="选择停车场" prop="parking_lot_id">
              <el-select 
                v-model="reservationForm.parking_lot_id" 
                placeholder="请选择停车场" 
                style="width: 100%"
                @change="handleParkingLotChange"
              >
                <el-option
                  v-for="lot in parkingLots"
                  :key="lot.id"
                  :label="lot.name"
                  :value="lot.id"
                >
                  <div class="lot-option">
                    <span>{{ lot.name }}</span>
                    <span class="lot-info">
                      可用充电车位: {{ getAvailableChargingSpaces(lot.id) }}
                    </span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="预约时间" prop="reservation_time">
              <el-date-picker
                v-model="reservationForm.reservation_time"
                type="datetime"
                placeholder="选择预约时间"
                :picker-options="pickerOptions"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
            
            <el-form-item label="预计充电时长" prop="duration">
              <el-select v-model="reservationForm.duration" placeholder="请选择充电时长" style="width: 100%">
                <el-option label="1小时" :value="1"></el-option>
                <el-option label="2小时" :value="2"></el-option>
                <el-option label="3小时" :value="3"></el-option>
                <el-option label="4小时" :value="4"></el-option>
                <el-option label="5小时" :value="5"></el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="reservationForm.remarks"
                type="textarea"
                :rows="2"
                placeholder="请输入备注信息（选填）"
              ></el-input>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" :loading="submitting" @click="submitReservation" style="width: 100%">
                提交预约
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <!-- 我的预约卡片 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="my-reservations-card">
          <div slot="header" class="card-header">
            <span>我的充电预约</span>
            <el-button type="text" @click="fetchMyReservations">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
          
          <div v-loading="loading" class="reservations-list">
            <div v-if="myReservations.length === 0" class="empty-data">
              <el-empty description="暂无充电预约记录"></el-empty>
            </div>
            
            <el-tabs v-else v-model="activeTab">
              <el-tab-pane label="待使用" name="pending">
                <div v-if="pendingReservations.length === 0" class="empty-tab-data">
                  暂无待使用的预约
                </div>
                <el-card 
                  v-for="item in pendingReservations" 
                  :key="item.id" 
                  class="reservation-item"
                  shadow="hover"
                >
                  <div class="reservation-header">
                    <span class="reservation-id">预约编号: {{ item.id }}</span>
                    <el-tag type="warning">待使用</el-tag>
                  </div>
                  <div class="reservation-info">
                    <div class="info-row">
                      <span class="label">车辆:</span>
                      <span class="value">{{ getVehicleInfo(item.vehicle_id) }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">停车场:</span>
                      <span class="value">{{ getParkingLotName(item.parking_lot_id) }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">预约时间:</span>
                      <span class="value">{{ formatDateTime(item.start_time) }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">结束时间:</span>
                      <span class="value">{{ formatDateTime(item.end_time) }}</span>
                    </div>
                  </div>
                  <div class="reservation-actions">
                    <el-button size="mini" type="danger" @click="cancelReservation(item)">取消预约</el-button>
                  </div>
                </el-card>
              </el-tab-pane>
              
              <el-tab-pane label="已完成/已取消" name="completed">
                <div v-if="completedReservations.length === 0" class="empty-tab-data">
                  暂无已完成或已取消的预约
                </div>
                <el-card 
                  v-for="item in completedReservations" 
                  :key="item.id" 
                  class="reservation-item"
                  shadow="hover"
                >
                  <div class="reservation-header">
                    <span class="reservation-id">预约编号: {{ item.id }}</span>
                    <el-tag :type="item.status === 1 ? 'success' : 'info'">
                      {{ item.status === 1 ? '已完成' : '已取消' }}
                    </el-tag>
                  </div>
                  <div class="reservation-info">
                    <div class="info-row">
                      <span class="label">车辆:</span>
                      <span class="value">{{ getVehicleInfo(item.vehicle_id) }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">停车场:</span>
                      <span class="value">{{ getParkingLotName(item.parking_lot_id) }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">预约时间:</span>
                      <span class="value">{{ formatDateTime(item.start_time) }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">结束时间:</span>
                      <span class="value">{{ formatDateTime(item.end_time) }}</span>
                    </div>
                  </div>
                </el-card>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getParkingLots } from '@/api/parkinglot'
import { getMyVehicles } from '@/api/vehicle'

// 模拟数据函数
function getMyReservations() {
  // 生成预约数据
  const result = []
  const total = 5
  
  for (let i = 0; i < total; i++) {
    const id = i + 1
    const parkingLotId = (i % 4) + 1
    const vehicleId = (i % 3) + 1
    const status = i < 3 ? 0 : (i === 3 ? 1 : 2)  // 0待使用，1已使用，2已取消
    
    // 生成开始时间（未来或过去）
    let startTime
    if (status === 0) {
      // 待使用的预约在未来
      const daysAhead = Math.floor(Math.random() * 7) + 1
      startTime = new Date(Date.now() + daysAhead * 24 * 60 * 60 * 1000)
    } else {
      // 已完成或已取消的预约在过去
      const daysAgo = Math.floor(Math.random() * 30) + 1
      startTime = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000)
    }
    
    // 结束时间（开始时间后1-3小时）
    const durationHours = Math.floor(Math.random() * 3) + 1
    const endTime = new Date(startTime.getTime() + durationHours * 60 * 60 * 1000)
    
    result.push({
      id: id,
      user_id: 1,  // 当前用户ID
      vehicle_id: vehicleId,
      parking_lot_id: parkingLotId,
      start_time: startTime.toISOString(),
      end_time: endTime.toISOString(),
      status: status,
      created_at: new Date(startTime.getTime() - 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(startTime.getTime() - 24 * 60 * 60 * 1000).toISOString(),
      remarks: `充电预约 ${id}`
    })
  }
  
  return Promise.resolve({
    data: {
      items: result,
      total: total
    }
  })
}

function createReservation(data) {
  return Promise.resolve({
    data: {
      ...data,
      id: Math.floor(Math.random() * 1000) + 100,
      status: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    message: '创建充电预约成功'
  })
}

function cancelReservation(id) {
  return Promise.resolve({
    data: {
      id: id,
      status: 2,
      updated_at: new Date().toISOString()
    },
    message: '取消充电预约成功'
  })
}

function getAvailableChargingSpacesCount(lotId) {
  // 模拟获取可用充电车位数量
  const counts = {
    1: 5,
    2: 3,
    3: 0,
    4: 8,
    5: 2
  }
  return Promise.resolve({
    data: counts[lotId] || 0
  })
}

export default {
  name: 'ChargingReservation',
  data() {
    return {
      loading: false,
      submitting: false,
      activeTab: 'pending',
      parkingLots: [],
      myVehicles: [],
      myReservations: [],
      availableSpaces: {},
      reservationForm: {
        vehicle_id: '',
        parking_lot_id: '',
        reservation_time: '',
        duration: 1,
        remarks: ''
      },
      reservationRules: {
        vehicle_id: [{ required: true, message: '请选择车辆', trigger: 'change' }],
        parking_lot_id: [{ required: true, message: '请选择停车场', trigger: 'change' }],
        reservation_time: [{ required: true, message: '请选择预约时间', trigger: 'change' }],
        duration: [{ required: true, message: '请选择充电时长', trigger: 'change' }]
      },
      pickerOptions: {
        disabledDate(time) {
          // 禁用过去的日期
          return time.getTime() < Date.now() - 8.64e7
        }
      }
    }
  },
  computed: {
    // 待使用的预约
    pendingReservations() {
      return this.myReservations.filter(item => item.status === 0)
    },
    // 已完成或已取消的预约
    completedReservations() {
      return this.myReservations.filter(item => item.status === 1 || item.status === 2)
    }
  },
  created() {
    this.fetchParkingLots()
    this.fetchMyVehicles()
    this.fetchMyReservations()
  },
  methods: {
    // 获取停车场列表
    async fetchParkingLots() {
      try {
        const response = await getParkingLots()
        this.parkingLots = response.data.items || []
        
        // 获取每个停车场的可用充电车位数量
        for (const lot of this.parkingLots) {
          this.fetchAvailableSpaces(lot.id)
        }
      } catch (error) {
        console.error('获取停车场列表失败:', error)
        this.$message.error('获取停车场列表失败')
      }
    },
    // 获取我的车辆列表
    async fetchMyVehicles() {
      try {
        const response = await getMyVehicles()
        this.myVehicles = response.data.items || []
        
        if (this.myVehicles.length > 0) {
          this.reservationForm.vehicle_id = this.myVehicles[0].id
        }
      } catch (error) {
        console.error('获取车辆列表失败:', error)
        this.$message.error('获取车辆列表失败')
      }
    },
    // 获取我的预约列表
    async fetchMyReservations() {
      this.loading = true
      try {
        const response = await getMyReservations()
        this.myReservations = response.data.items || []
      } catch (error) {
        console.error('获取充电预约列表失败:', error)
        this.$message.error('获取充电预约列表失败')
      } finally {
        this.loading = false
      }
    },
    // 获取停车场可用充电车位数量
    async fetchAvailableSpaces(lotId) {
      try {
        const response = await getAvailableChargingSpacesCount(lotId)
        this.$set(this.availableSpaces, lotId, response.data)
      } catch (error) {
        console.error(`获取停车场 ${lotId} 可用充电车位数量失败:`, error)
      }
    },
    // 获取可用充电车位数量
    getAvailableChargingSpaces(lotId) {
      return this.availableSpaces[lotId] || 0
    },
    // 处理停车场变更
    handleParkingLotChange(lotId) {
      // 检查所选停车场是否有可用充电车位
      if (this.getAvailableChargingSpaces(lotId) === 0) {
        this.$message.warning('该停车场暂无可用充电车位，请选择其他停车场')
      }
    },
    // 获取停车场名称
    getParkingLotName(id) {
      const lot = this.parkingLots.find(item => item.id === id)
      return lot ? lot.name : `停车场 ${id}`
    },
    // 获取车辆信息
    getVehicleInfo(id) {
      const vehicle = this.myVehicles.find(item => item.id === id)
      return vehicle ? `${vehicle.number} (${vehicle.brand})` : `车辆 ${id}`
    },
    // 格式化日期时间
    formatDateTime(dateStr) {
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    // 提交预约
    submitReservation() {
      this.$refs.reservationForm.validate(async valid => {
        if (valid) {
          // 检查所选停车场是否有可用充电车位
          if (this.getAvailableChargingSpaces(this.reservationForm.parking_lot_id) === 0) {
            this.$message.error('该停车场暂无可用充电车位，请选择其他停车场')
            return
          }
          
          this.submitting = true
          try {
            // 计算结束时间
            const startTime = new Date(this.reservationForm.reservation_time)
            const endTime = new Date(startTime.getTime() + this.reservationForm.duration * 60 * 60 * 1000)
            
            const data = {
              vehicle_id: this.reservationForm.vehicle_id,
              parking_lot_id: this.reservationForm.parking_lot_id,
              start_time: startTime.toISOString(),
              end_time: endTime.toISOString(),
              remarks: this.reservationForm.remarks
            }
            
            await createReservation(data)
            this.$message.success('充电预约创建成功')
            
            // 重置表单
            this.reservationForm.reservation_time = ''
            this.reservationForm.duration = 1
            this.reservationForm.remarks = ''
            
            // 刷新预约列表
            this.fetchMyReservations()
            
            // 刷新可用充电车位数量
            this.fetchAvailableSpaces(this.reservationForm.parking_lot_id)
          } catch (error) {
            console.error('创建充电预约失败:', error)
            this.$message.error('创建充电预约失败')
          } finally {
            this.submitting = false
          }
        }
      })
    },
    // 取消预约
    cancelReservation(reservation) {
      this.$confirm('确认取消该充电预约?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await cancelReservation(reservation.id)
          this.$message.success('充电预约取消成功')
          
          // 刷新预约列表
          this.fetchMyReservations()
          
          // 刷新可用充电车位数量
          this.fetchAvailableSpaces(reservation.parking_lot_id)
        } catch (error) {
          console.error('取消充电预约失败:', error)
          this.$message.error('取消充电预约失败')
        }
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-reservation-container {
  .reservation-card, .my-reservations-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      span {
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
  
  .vehicle-option, .lot-option {
    display: flex;
    align-items: center;
    
    .vehicle-brand, .lot-info {
      margin-left: 10px;
      color: #909399;
      font-size: 13px;
    }
    
    .vehicle-color {
      margin-left: 10px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 1px solid #DCDFE6;
    }
  }
  
  .reservations-list {
    min-height: 300px;
    
    .empty-data, .empty-tab-data {
      padding: 40px 0;
      text-align: center;
      color: #909399;
    }
    
    .reservation-item {
      margin-bottom: 15px;
      
      .reservation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        .reservation-id {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
        }
      }
      
      .reservation-info {
        margin-bottom: 15px;
        
        .info-row {
          display: flex;
          margin-bottom: 8px;
          font-size: 14px;
          
          .label {
            color: #909399;
            width: 70px;
          }
          
          .value {
            color: #606266;
            flex: 1;
          }
        }
      }
      
      .reservation-actions {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
</style>
