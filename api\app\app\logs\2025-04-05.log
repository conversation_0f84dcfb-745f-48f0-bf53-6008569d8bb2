2025/04/05 20:32:25 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/05 20:32:25 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/05 20:32:25 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/05 23:37:21 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/05 23:37:21 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/05 23:37:21 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/05 23:37:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:37:47] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/05 23:37:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:37:55] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/05 23:37:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:37:55] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/05 23:37:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:37:55] "[35m[1mPOST /api/register HTTP/1.1[0m" 500 -
2025/04/05 23:39:18 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/05 23:40:14 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/05 23:40:24 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/05 23:42:05 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/05 23:42:05 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/05 23:42:05 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/05 23:42:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:42:17] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/05 23:42:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:42:18] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/05 23:42:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:42:18] "[35m[1mPOST /api/register HTTP/1.1[0m" 500 -
2025/04/05 23:42:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:42:25] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/05 23:42:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:42:29] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/05 23:42:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:42:29] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/05 23:42:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:42:29] "[35m[1mPOST /api/register HTTP/1.1[0m" 500 -
2025/04/05 23:43:19 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/05 23:43:19 flask_migrate __init__.py[113] wrapped() ERROR: Error: Directory migrations already exists and is not empty
2025/04/05 23:43:38 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/05 23:43:46 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/05 23:43:55 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/05 23:43:59 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/05 23:43:59 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/05 23:43:59 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/05 23:44:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:44:07] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/05 23:44:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:44:10] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/05 23:44:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:44:10] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/05 23:44:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [05/Apr/2025 23:44:10] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
