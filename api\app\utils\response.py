"""
API 响应工具模块 - 提供统一的API响应格式
"""
from flask import jsonify

def api_response(data=None, message="操作成功", status="success", code=200, errors=None):
    """
    统一的API响应格式

    Args:
        data: 返回的数据
        message: 响应消息
        status: 状态标识 (success/error)
        code: HTTP状态码
        errors: 错误详情

    Returns:
        Flask响应对象
    """
    # 根据状态码设置响应码
    if 200 <= code < 300:
        response_code = 20000  # 成功响应码
    elif code == 400:
        response_code = 40000  # 错误请求
    elif code == 401:
        response_code = 40100  # 未授权
    elif code == 403:
        response_code = 40300  # 禁止访问
    elif code == 404:
        response_code = 40400  # 资源不存在
    elif code == 422:
        response_code = 42200  # 无效输入
    else:
        response_code = 50000  # 服务器错误

    # 构建响应
    response = {
        'code': response_code
    }

    # 处理数据字段
    if data is not None:
        # 将数据包装在data字段中
        response_data = {
            'status': status,
            'message': message
        }

        if isinstance(data, dict):
            response_data.update(data)
        else:
            response_data['data'] = data

        if errors is not None:
            response_data['errors'] = errors

        response['data'] = response_data
    else:
        # 如果没有数据，直接返回消息
        response['message'] = message
        if status != "success":
            response['status'] = status

    return jsonify(response), code
