/**
 * 前端表单提交测试脚本
 * 
 * 使用方法：
 * 1. 登录系统，进入违规录入页面
 * 2. 打开浏览器控制台 (F12)
 * 3. 复制粘贴此脚本到控制台
 * 4. 运行脚本，观察结果
 */

// 测试数据
const testData = {
  bike_number: "TEST-123",
  user_id: 1,
  violation_type: "违规停车",
  violation_time: new Date().toISOString().slice(0, 19).replace('T', ' '),
  location: "校园南门",
  description: "这是一条测试违规记录"
};

// 测试函数
async function testViolationForm() {
  console.log("开始测试违规录入表单...");
  console.log("测试数据:", testData);
  
  try {
    // 直接调用API
    const token = localStorage.getItem('token');
    if (!token) {
      console.error("未找到登录令牌，请先登录");
      return;
    }
    
    console.log("发送请求...");
    const response = await fetch('/api/violations/records', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(testData)
    });
    
    const result = await response.json();
    console.log("响应状态:", response.status);
    console.log("响应数据:", result);
    
    if (response.ok && result.code === 20000) {
      console.log("测试成功: 违规记录创建成功");
      console.log("创建的记录ID:", result.data.id);
    } else {
      console.error("测试失败:", result.message);
    }
  } catch (error) {
    console.error("测试出错:", error);
  }
}

// 执行测试
testViolationForm();
