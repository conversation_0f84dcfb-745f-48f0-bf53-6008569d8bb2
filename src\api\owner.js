import request from '@/utils/request'
// 关于用户的所有接口

// 添加用户信息
export function create_user(data) {
  return request({
    url: '/api/users',
    method: 'post',
    data,
    transformResponse: [
      (data) => {
        try {
          // 尝试解析JSON数据
          const parsedData = JSON.parse(data)
          console.log('创建用户API原始响应:', parsedData)

          // 处理标准API响应格式 (code/data格式)
          if (parsedData.code === 20000 && parsedData.data) {
            return parsedData.data
          }
          return parsedData
        } catch (error) {
          console.error('解析创建用户响应数据失败:', error)
          return data
        }
      }
    ]
  })
}

// 获取所有用户信息
export function get_users(params) {
  return request({
    url: '/api/users',
    method: 'get',
    params,
    transformResponse: [
      (data) => {
        try {
          // 尝试解析JSON数据
          const parsedData = JSON.parse(data)
          console.log('用户列表API原始响应:', parsedData)

          // 处理多种可能的响应格式
          if (parsedData.code === 20000 && parsedData.data) {
            return parsedData.data // 返回data部分
          } else if (parsedData.message && parsedData.status === 'success') {
            // 兼容处理标准API格式
            return parsedData.data || parsedData
          } else if (parsedData.users) {
            // 直接包含users数组的格式
            return parsedData
          }
          return parsedData
        } catch (error) {
          console.error('解析用户列表数据失败:', error)
          return data
        }
      }
    ]
  })
}

// 通过作者ID获取作者信息
export function get_user_by_id(u_id) {
  return request({
    url: '/api/users/' + u_id,
    method: 'get'
  })
}

// 更新作者信息
export function update_user_by_id(u_id, data) {
  return request({
    url: '/api/users/' + u_id,
    method: 'put',
    data
  })
}

// 删除作者信息接口
export function delete_user_by_id(u_id) {
  return request({
    url: '/api/users/' + u_id,
    method: 'delete'
  })
}

// 使用公共API端点获取基本用户信息
export function getPublicUsers(params) {
  return request({
    url: '/api/users/public',
    method: 'get',
    params
  })
}

// 认证后的API调用
export function getAuthenticatedUsers(params) {
  return request({
    url: '/api/users',
    method: 'get',
    params
  })
}
