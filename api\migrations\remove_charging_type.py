"""
移除充电类型字段的迁移脚本
"""
import sqlite3
import os

def run_migration():
    """执行迁移：从 charging_records 和 parking_spaces 表中移除 charging_type 列"""
    conn = None
    cursor = None
    try:
        # 获取数据库文件路径
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'sys.db')
        print(f"数据库路径: {db_path}")

        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 检查 charging_records 表中是否存在 charging_type 列
        cursor.execute("PRAGMA table_info(charging_records)")
        columns = cursor.fetchall()
        columns_dict = {column[1]: column for column in columns}
        has_charging_type_in_records = 'charging_type' in columns_dict

        # 检查 parking_spaces 表中是否存在 charging_type 列
        cursor.execute("PRAGMA table_info(parking_spaces)")
        columns = cursor.fetchall()
        columns_dict_spaces = {column[1]: column for column in columns}
        has_charging_type_in_spaces = 'charging_type' in columns_dict_spaces

        # 如果 charging_records 表中存在 charging_type 列，则移除它
        if has_charging_type_in_records:
            print("从 charging_records 表中移除 charging_type 列...")

            # 获取所有列名，除了 charging_type
            column_names = [col for col in columns_dict.keys() if col != 'charging_type']

            # 检查并删除可能存在的临时表
            cursor.execute("DROP TABLE IF EXISTS charging_records_temp")

            # 创建临时表，不包含 charging_type 列
            create_table_sql = f"""
            CREATE TABLE charging_records_temp (
                {', '.join([f'{col} {columns_dict[col][2]}' for col in column_names])}
            )
            """
            cursor.execute(create_table_sql)

            # 复制数据，不包含 charging_type 列
            columns_str = ', '.join(column_names)
            cursor.execute(f"""
            INSERT INTO charging_records_temp ({columns_str})
            SELECT {columns_str} FROM charging_records
            """)

            # 删除原表
            cursor.execute("DROP TABLE charging_records")

            # 重命名临时表
            cursor.execute("ALTER TABLE charging_records_temp RENAME TO charging_records")

            print("成功从 charging_records 表中移除 charging_type 列")
        else:
            print("charging_records 表中不存在 charging_type 列，无需移除")

        # 如果 parking_spaces 表中存在 charging_type 列，则移除它
        if has_charging_type_in_spaces:
            print("从 parking_spaces 表中移除 charging_type 列...")

            # 获取所有列名，除了 charging_type
            column_names = [col for col in columns_dict_spaces.keys() if col != 'charging_type']

            # 检查并删除可能存在的临时表
            cursor.execute("DROP TABLE IF EXISTS parking_spaces_temp")

            # 创建临时表，不包含 charging_type 列
            create_table_sql = f"""
            CREATE TABLE parking_spaces_temp (
                {', '.join([f'{col} {columns_dict_spaces[col][2]}' for col in column_names])}
            )
            """
            cursor.execute(create_table_sql)

            # 复制数据，不包含 charging_type 列
            columns_str = ', '.join(column_names)
            cursor.execute(f"""
            INSERT INTO parking_spaces_temp ({columns_str})
            SELECT {columns_str} FROM parking_spaces
            """)

            # 删除原表
            cursor.execute("DROP TABLE parking_spaces")

            # 重命名临时表
            cursor.execute("ALTER TABLE parking_spaces_temp RENAME TO parking_spaces")

            print("成功从 parking_spaces 表中移除 charging_type 列")
        else:
            print("parking_spaces 表中不存在 charging_type 列，无需移除")

        # 提交更改
        conn.commit()
        print("迁移成功完成")

    except Exception as e:
        print(f"迁移失败: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    run_migration()
