"""
车辆权限控制模块
提供车辆相关操作的权限控制装饰器
"""
from functools import wraps
from flask import request
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.utils.auth_helpers import get_current_user_id, is_admin
from app.utils.response import api_response
from app.bikes.models import Bikes

def bike_owner_or_admin_required(bike_id_param='bike_id'):
    """
    装饰器：确保用户是车辆所有者或管理员

    Args:
        bike_id_param: URL参数中车辆ID的参数名
    """
    def decorator(fn):
        @wraps(fn)
        @jwt_required()
        def wrapper(*args, **kwargs):
            # 获取当前用户ID
            user_id = get_current_user_id()
            if not user_id:
                return api_response(message="未登录或无法获取用户信息", status="error", code=401)

            # 检查是否为管理员
            if is_admin():
                # 管理员可以操作所有车辆
                return fn(*args, **kwargs)

            # 获取车辆ID
            bike_id = kwargs.get(bike_id_param)
            if not bike_id:
                return api_response(message="缺少车辆ID参数", status="error", code=400)

            # 查找车辆
            bike = Bikes.query.get(bike_id)
            if not bike:
                return api_response(message="车辆不存在", status="error", code=404)

            # 检查是否为车辆所有者
            if bike.belong_to != user_id:
                return api_response(message="您没有权限操作此车辆", status="error", code=403)

            # 权限验证通过，执行原函数
            return fn(*args, **kwargs)
        return wrapper
    return decorator

def ensure_bike_ownership(fn):
    """
    装饰器：确保用户只能为自己创建车辆，管理员可以为任何用户创建车辆
    用于POST请求创建车辆时
    """
    @wraps(fn)
    @jwt_required()
    def wrapper(*args, **kwargs):
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取请求数据
        data = request.get_json()
        if not data:
            data = {}

        # 检查是否为管理员
        if is_admin():
            # 管理员可以为任何用户创建车辆
            # 如果没有指定用户ID，则默认为当前用户
            if 'user_id' not in data and 'belong_to' not in data:
                data['belong_to'] = user_id
            elif 'user_id' in data and 'belong_to' not in data:
                data['belong_to'] = data['user_id']
        else:
            # 普通用户只能为自己创建车辆
            # 强制设置所属用户为当前用户
            data['belong_to'] = user_id
            # 移除可能存在的user_id字段
            if 'user_id' in data:
                del data['user_id']

        # 修改请求数据
        request._cached_json = (data, request._cached_json[1] if hasattr(request, '_cached_json') and isinstance(request._cached_json, tuple) and len(request._cached_json) > 1 else None)

        # 继续执行原函数
        return fn(*args, **kwargs)
    return wrapper

def filter_bikes_by_ownership(fn):
    """
    装饰器：过滤车辆列表，普通用户只能看到自己的车辆，管理员可以看到所有车辆
    用于GET请求获取车辆列表时
    """
    @wraps(fn)
    @jwt_required()
    def wrapper(*args, **kwargs):
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 检查是否为管理员
        print(f"当前用户ID: {user_id}, 是否为管理员: {is_admin()}")

        # 获取请求参数
        request_args = dict(request.args)
        print(f"filter_bikes_by_ownership 装饰器接收到的查询参数: {request_args}")

        # 如果是管理员且请求中明确指定了 user_id，则不修改请求参数
        if is_admin() and ('user_id' in request_args or 'belong_to' in request_args):
            print("管理员请求且指定了用户ID，不修改请求参数")
            pass
        elif not is_admin():
            # 普通用户只能看到自己的车辆
            # 修改请求参数，强制按用户ID过滤
            if request.method == 'GET':
                # 对于GET请求，修改查询参数
                request.args = request.args.copy()
                request.args['user_id'] = user_id
            elif request.json:
                # 对于带有JSON数据的请求，修改JSON数据
                request.json['user_id'] = user_id

        # 继续执行原函数
        return fn(*args, **kwargs)
    return wrapper
