<template>
  <div class="violation-list">
    <!-- 数据统计 -->
    <div v-if="list.length > 0 && showStatistics" class="statistics-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-card-title">总违规数</div>
            <div class="stat-card-value">{{ total }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-card-title">待审核</div>
            <div class="stat-card-value">{{ getStatusCount(0) }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-card-title">申诉中</div>
            <div class="stat-card-value">{{ getStatusCount(2) }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-card-title">已处理</div>
            <div class="stat-card-value">{{ getStatusCount(1) + getStatusCount(3) }}</div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-container">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="6" :lg="4">
          <el-select
            v-model="listQuery.status"
            placeholder="处理状态"
            clearable
            class="filter-item"
            style="width: 100%"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>

        <el-col v-if="showDateRange" :xs="24" :sm="12" :md="10" :lg="8">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            class="filter-item"
            style="width: 100%"
            @change="handleDateRangeChange"
          />
        </el-col>

        <el-col :xs="24" :sm="8" :md="6" :lg="4">
          <el-select
            v-model="listQuery.violation_type"
            placeholder="违规类型"
            clearable
            class="filter-item"
            style="width: 100%"
          >
            <el-option
              v-for="item in violationTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>

        <el-col v-if="showHandler" :xs="24" :sm="8" :md="6" :lg="4">
          <el-select
            v-model="listQuery.handler_id"
            placeholder="处理人"
            clearable
            class="filter-item"
            style="width: 100%"
          >
            <el-option
              v-for="item in handlerOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>

        <el-col :xs="24" :sm="8" :md="6" :lg="4">
          <el-input
            v-model="listQuery.id"
            placeholder="违规记录ID"
            class="filter-item"
            style="width: 100%"
            clearable
            @keyup.enter.native="handleFilter"
          />
        </el-col>

        <el-col v-if="showSearch" :xs="24" :sm="8" :md="6" :lg="4">
          <el-input
            v-model="listQuery.search"
            placeholder="搜索车牌号/地点"
            class="filter-item"
            style="width: 100%"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter.native="handleFilter"
          />
        </el-col>

        <el-col :xs="24" :sm="8" :md="6" :lg="4" class="btn-col">
          <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
            搜索
          </el-button>

          <el-button
            v-if="showCreateButton"
            class="filter-item"
            type="success"
            icon="el-icon-plus"
            @click="handleCreate"
          >
            新增违规
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{backgroundColor: '#f5f7fa', color: '#606266'}"
      :row-class-name="tableRowClassName"
    >
      <el-table-column label="ID" align="center" width="80">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="车牌号" align="center" min-width="110">
        <template slot-scope="{row}">
          <el-tooltip effect="dark" :content="row.bike_number" placement="top">
            <span class="highlight-text">{{ row.bike_number }}</span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="车主" align="center" min-width="100">
        <template slot-scope="{row}">
          <el-tooltip effect="dark" :content="row.user_name || '未知'" placement="top">
            <span>{{ row.user_name || '未知' }}</span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="违规类型" align="center" min-width="120">
        <template slot-scope="{row}">
          <el-tag
            :type="getViolationTypeTag(row.violation_type)"
            effect="plain"
            size="medium"
          >
            {{ row.violation_type }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="违规地点" align="center" min-width="150">
        <template slot-scope="{row}">
          <el-tooltip effect="dark" :content="row.location" placement="top">
            <span>{{ row.location }}</span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="违规时间" align="center" min-width="160">
        <template slot-scope="{row}">
          <span>{{ formatDateTime(row.violation_time) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="处理状态" align="center" min-width="100">
        <template slot-scope="{row}">
          <el-tag
            :type="getStatusType(row.status)"
            effect="dark"
            size="medium"
          >
            {{ row.status_text }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column v-if="showRecorder" label="记录人" align="center" min-width="100">
        <template slot-scope="{row}">
          <span>{{ row.recorder_name }}</span>
        </template>
      </el-table-column>

      <el-table-column v-if="showHandler" label="处理人" align="center" min-width="100">
        <template slot-scope="{row}">
          <span>{{ row.handler_name || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" min-width="180" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-tooltip content="查看详情" placement="top">
            <el-button type="primary" size="mini" icon="el-icon-view" circle @click="handleDetail(row)" />
          </el-tooltip>

          <el-tooltip v-if="canAppeal && row.status === 1" content="提交申诉" placement="top">
            <el-button type="warning" size="mini" icon="el-icon-chat-dot-square" circle @click="handleAppeal(row)" />
          </el-tooltip>

          <el-tooltip v-if="canHandle && row.status !== 3" content="处理违规" placement="top">
            <el-button type="success" size="mini" icon="el-icon-check" circle @click="handleProcess(row)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 空数据提示 -->
    <el-empty
      v-if="list.length === 0 && !listLoading"
      description="暂无违规记录"
      :image-size="200"
    >
      <el-button v-if="showCreateButton" type="primary" @click="handleCreate">新增违规</el-button>
    </el-empty>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.per_page"
      @pagination="handlePagination"
      class="pagination-container"
    />


  </div>
</template>

<script>
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'ViolationList',
  components: { Pagination },
  props: {
    // 是否显示创建按钮
    showCreateButton: {
      type: Boolean,
      default: false
    },
    // 是否显示日期范围筛选
    showDateRange: {
      type: Boolean,
      default: false
    },
    // 是否显示搜索框
    showSearch: {
      type: Boolean,
      default: true
    },
    // 是否显示记录人
    showRecorder: {
      type: Boolean,
      default: false
    },
    // 是否显示处理人
    showHandler: {
      type: Boolean,
      default: false
    },
    // 是否可以申诉
    canAppeal: {
      type: Boolean,
      default: false
    },
    // 是否可以处理
    canHandle: {
      type: Boolean,
      default: false
    },
    // 是否显示统计卡片
    showStatistics: {
      type: Boolean,
      default: true
    },
    // 默认排序方式
    defaultSort: {
      type: Object,
      default: () => ({ prop: 'id', order: 'ascending' })
    },
    // 获取数据的API方法
    fetchListMethod: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        per_page: 15,
        status: undefined,
        search: '',
        start_date: undefined,
        end_date: undefined,
        sort: 'id',
        order: 'asc',
        violation_type: undefined,
        id: undefined,
        handler_id: undefined
      },
      dateRange: [],
      statusOptions: [
        { label: '待审核', value: 0 },
        { label: '已处理', value: 1 },
        { label: '申诉中', value: 2 },
        { label: '已撤销', value: 3 }
      ],
      violationTypeOptions: [
        { label: '违规停车', value: '违规停车' },
        { label: '占用消防通道', value: '占用消防通道' },
        { label: '占用无障碍通道', value: '占用无障碍通道' },
        { label: '超时停车', value: '超时停车' },
        { label: '车辆损坏公物', value: '车辆损坏公物' },
        { label: '无证驾驶', value: '无证驾驶' },
        { label: '其他违规', value: '其他违规' }
      ],
      handlerOptions: [
        { label: '管理员', value: 1 },
        { label: '保安一号', value: 2 },
        { label: '保安二号', value: 3 },
        { label: '保安三号', value: 4 }
      ]
    }
  },
  created() {
    // 应用默认排序
    if (this.defaultSort) {
      if (this.defaultSort.prop === 'id' && this.defaultSort.order === 'ascending') {
        this.listQuery.sort = 'id'
        this.listQuery.order = 'asc'
      } else if (this.defaultSort.prop === 'id' && this.defaultSort.order === 'descending') {
        this.listQuery.sort = 'id'
        this.listQuery.order = 'desc'
      }
    }
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true

      // 创建一个新的查询对象，只包含有效的筛选参数
      const queryParams = { ...this.listQuery }

      // 处理ID筛选
      if (queryParams.id) {
        queryParams.id = parseInt(queryParams.id)
        if (isNaN(queryParams.id)) {
          delete queryParams.id
        }
      } else {
        delete queryParams.id
      }

      // 处理状态筛选
      if (queryParams.status === undefined || queryParams.status === '') {
        delete queryParams.status
      }

      // 处理违规类型筛选
      if (!queryParams.violation_type) {
        delete queryParams.violation_type
      }

      // 处理搜索条件
      if (!queryParams.search) {
        delete queryParams.search
      }

      // 处理处理人筛选
      if (!queryParams.handler_id) {
        delete queryParams.handler_id
      }

      // 处理日期范围
      if (!queryParams.start_date) {
        delete queryParams.start_date
      }

      if (!queryParams.end_date) {
        delete queryParams.end_date
      }

      // 打印最终的查询参数
      console.log('发送的查询参数:', queryParams)

      this.fetchListMethod(queryParams).then(response => {
        // 打印响应数据，便于调试
        console.log('违规记录响应数据:', response)

        // 确保 response.data 存在且包含 items 属性
        if (response && response.data) {
          // 检查是否超出页码范围
          const maxPage = Math.ceil(response.data.total / response.data.per_page)
          if (response.data.total > 0 && response.data.page > maxPage) {
            // 如果超出范围，跳转到最后一页
            console.warn(`请求的页码 ${response.data.page} 超出范围，最大页码为 ${maxPage}，自动跳转到最后一页`)
            this.listQuery.page = maxPage
            this.getList()
            return
          }

          // 正常处理数据
          if (Array.isArray(response.data.items)) {
            this.list = response.data.items
            this.total = response.data.total || 0

            // 数据加载完成后显示成功提示
            this.$notify({
              title: '成功',
              message: `已加载 ${this.list.length} 条违规记录`,
              type: 'success',
              duration: 2000
            })
          } else {
            // 如果 items 不是数组，初始化为空数组
            console.warn('items 不是数组:', response.data)
            this.list = []
            this.total = response.data.total || 0
          }
        } else {
          // 如果数据格式不正确，初始化为空数组
          console.warn('响应数据格式不正确:', response)
          this.list = []
          this.total = 0
        }

        this.listLoading = false
      }).catch((error) => {
        this.listLoading = false
        this.list = [] // 出错时确保列表为空数组
        this.total = 0

        console.error('加载违规记录失败:', error)

        // 显示错误提示，但不影响用户体验
        this.$message({
          message: '加载违规记录失败，请稍后再试',
          type: 'warning',
          duration: 2000
        })
      })
    },
    handleFilter() {
      // 重置页码
      this.listQuery.page = 1

      // 清理空的筛选条件
      const cleanQuery = { ...this.listQuery }

      // 处理ID筛选
      if (cleanQuery.id) {
        cleanQuery.id = parseInt(cleanQuery.id)
        if (isNaN(cleanQuery.id)) {
          delete cleanQuery.id
        }
      } else {
        delete cleanQuery.id
      }

      // 处理状态筛选
      if (cleanQuery.status === undefined || cleanQuery.status === '') {
        delete cleanQuery.status
      }

      // 处理违规类型筛选
      if (!cleanQuery.violation_type) {
        delete cleanQuery.violation_type
      }

      // 处理搜索条件
      if (!cleanQuery.search) {
        delete cleanQuery.search
      }

      // 处理处理人筛选
      if (!cleanQuery.handler_id) {
        delete cleanQuery.handler_id
      }

      // 更新查询参数
      this.listQuery = cleanQuery

      // 打印筛选条件
      console.log('筛选条件:', this.listQuery)

      // 获取数据
      this.getList()
    },
    handleDateRangeChange(val) {
      if (val) {
        this.listQuery.start_date = val[0]
        this.listQuery.end_date = val[1]
        console.log('日期范围已更新:', this.listQuery.start_date, this.listQuery.end_date)
      } else {
        this.listQuery.start_date = undefined
        this.listQuery.end_date = undefined
        console.log('日期范围已清除')
      }

      // 自动触发筛选
      this.handleFilter()
    },
    handlePagination({ page, limit }) {
      // 计算最大页数
      const maxPage = Math.ceil(this.total / limit)

      // 确保页码不超出范围
      if (page > maxPage) {
        this.listQuery.page = maxPage > 0 ? maxPage : 1
      } else {
        this.listQuery.page = page
      }

      this.listQuery.per_page = limit
      this.getList()
    },
    handleCreate() {
      this.$emit('create')
    },
    handleDetail(row) {
      this.$emit('detail', row)
    },
    handleAppeal(row) {
      this.$emit('appeal', row)
    },
    handleProcess(row) {
      this.$emit('process', row)
    },
    formatDateTime(time) {
      if (!time) return ''
      return parseTime(new Date(time), '{y}-{m}-{d} {h}:{i}')
    },
    getStatusType(status) {
      const statusMap = {
        0: 'info',    // 待审核
        1: 'success', // 已处理
        2: 'warning', // 申诉中
        3: 'danger'   // 已撤销
      }
      return statusMap[status] || 'info'
    },
    getViolationTypeTag(type) {
      const typeMap = {
        '违规停车': 'danger',
        '占用消防通道': 'danger',
        '占用无障碍通道': 'warning',
        '超时停车': 'warning',
        '车辆损坏公物': 'danger',
        '无证驾驶': 'danger',
        '其他违规': 'info'
      }
      return typeMap[type] || 'info'
    },
    // 表格行类名
    tableRowClassName({ row, rowIndex }) {
      if (row.status === 2) {
        return 'warning-row'
      } else if (row.status === 3) {
        return 'success-row'
      }
      return ''
    },
    // 获取指定状态的记录数量
    getStatusCount(status) {
      return this.list.filter(item => item.status === status).length
    }
  }
}
</script>

<style lang="scss" scoped>
.violation-list {
  padding: 10px;

  .filter-container {
    padding-bottom: 20px;

    .filter-item {
      margin-right: 10px;
      margin-bottom: 10px;
    }

    .btn-col {
      display: flex;
      align-items: flex-start;

      @media (max-width: 768px) {
        margin-top: 10px;
      }
    }
  }

  .highlight-text {
    font-weight: bold;
    color: #409EFF;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }

  .statistics-container {
    margin-bottom: 20px;

    .stat-card {
      text-align: center;
      transition: all 0.3s;
      border-radius: 8px;
      overflow: hidden;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      }

      .stat-card-title {
        font-size: 16px;
        color: #606266;
        margin-bottom: 10px;
      }

      .stat-card-value {
        font-size: 28px;
        font-weight: bold;
        color: #409EFF;
        padding: 5px 0;
      }
    }
  }
}

// 表格行样式
::v-deep .el-table .warning-row {
  background-color: rgba(230, 162, 60, 0.1);
}

::v-deep .el-table .success-row {
  background-color: rgba(103, 194, 58, 0.1);
}

// 美化表格样式
::v-deep .el-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

::v-deep .el-table th {
  background-color: #f0f7ff !important;
  color: #409EFF !important;
  font-weight: bold;
}

::v-deep .el-table--border th, ::v-deep .el-table--border td {
  border-right: 1px solid #ebeef5;
}

// 响应式调整
@media (max-width: 768px) {
  .violation-list {
    .statistics-container {
      .el-col {
        margin-bottom: 15px;
      }
    }
  }
}
</style>
