<template>
  <div class="appeal-form">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="违规记录">
        <el-card class="violation-info-card" shadow="never">
          <div class="violation-info-item">
            <span class="info-label">车牌号:</span>
            <span class="info-value">{{ violationDetail.bike_number }}</span>
          </div>
          <div class="violation-info-item">
            <span class="info-label">违规类型:</span>
            <span class="info-value">{{ violationDetail.violation_type }}</span>
          </div>
          <div class="violation-info-item">
            <span class="info-label">违规时间:</span>
            <span class="info-value">{{ formatDateTime(violationDetail.violation_time) }}</span>
          </div>
          <div class="violation-info-item">
            <span class="info-label">违规地点:</span>
            <span class="info-value">{{ violationDetail.location }}</span>
          </div>
        </el-card>
      </el-form-item>

      <el-form-item label="申诉理由" prop="reason">
        <el-input
          v-model="form.reason"
          type="textarea"
          :rows="6"
          placeholder="请详细描述申诉理由，例如：违规认定有误、特殊情况等"
        />
      </el-form-item>

      <el-form-item label="上传证据">
        <el-upload
          :action="uploadUrl"
          :headers="uploadHeaders"
          :data="uploadData"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :file-list="fileList"
          multiple
          list-type="picture-card"
        >
          <i class="el-icon-plus"></i>
          <div slot="tip" class="el-upload__tip">
            支持上传图片(jpg/png)和视频(mp4)作为申诉证据，单个文件不超过10MB
          </div>
        </el-upload>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm" :loading="submitting" :disabled="submitting">
          {{ submitting ? '提交中...' : '提交申诉' }}
        </el-button>
        <el-button @click="resetForm" :disabled="submitting">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
import { getToken } from '@/utils/auth'

export default {
  name: 'AppealForm',
  props: {
    // 违规记录详情
    violationDetail: {
      type: Object,
      required: true
    },
    // 提交申诉的方法
    submitMethod: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      form: {
        violation_id: null,
        reason: ''
      },
      rules: {
        reason: [
          { required: true, message: '请输入申诉理由', trigger: 'blur' },
          { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }
        ]
      },
      fileList: [],
      uploadEvidenceType: 'image',
      submitting: false
    }
  },
  computed: {
    // 上传URL
    uploadUrl() {
      return process.env.VUE_APP_BASE_API + '/violations/evidences'
    },
    // 上传请求头
    uploadHeaders() {
      return {
        Authorization: 'Bearer ' + getToken()
      }
    },
    // 上传数据
    uploadData() {
      return {
        related_type: 'appeal',
        related_id: this.form.appeal_id
      }
    }
  },
  created() {
    if (this.violationDetail && this.violationDetail.id) {
      this.form.violation_id = this.violationDetail.id
      console.log('申诉表单 - 设置违规ID:', this.form.violation_id)
    } else {
      console.error('申诉表单 - 无法获取违规ID，violationDetail:', this.violationDetail)
    }
  },
  methods: {
    formatDateTime(time) {
      if (!time) return ''
      return parseTime(new Date(time), '{y}-{m}-{d} {h}:{i}')
    },
    beforeUpload(file) {
      // 判断文件类型
      const isImage = file.type.indexOf('image/') !== -1
      const isVideo = file.type.indexOf('video/') !== -1
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isImage && !isVideo) {
        this.$message.error('只能上传图片或视频文件!')
        return false
      }

      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!')
        return false
      }

      // 设置上传的证据类型
      this.uploadEvidenceType = isImage ? 'image' : 'video'

      return true
    },
    handleUploadSuccess(response, file, fileList) {
      if (response.status === 'success') {
        this.$message.success('证据上传成功')
        this.fileList = fileList
      } else {
        this.$message.error(response.message || '上传失败')
      }
    },
    handleUploadError(err) {
      console.error(err)
      this.$message.error('上传失败')
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 提交前再次检查违规ID是否存在
          if (!this.form.violation_id) {
            console.error('申诉提交失败: 缺少违规记录ID');
            this.$message.error('申诉提交失败: 缺少违规记录ID');
            return false;
          }

          // 显示提交中状态
          this.submitting = true;

          // 打印提交的数据，用于调试
          console.log('提交申诉数据:', JSON.stringify(this.form));

          this.submitMethod(this.form)
            .then(response => {
              console.log('申诉提交响应:', response);
              if (response.status === 'success') {
                this.$message.success('申诉提交成功');

                // 如果有上传的证据，需要更新关联ID
                if (this.fileList.length > 0 && response.data && response.data.id) {
                  this.form.appeal_id = response.data.id;

                  // 更新上传组件的数据，使其包含正确的appeal_id
                  this.$nextTick(() => {
                    // 这里可以添加更新证据关联ID的逻辑
                    this.$message.info('请注意：您上传的证据将与此申诉关联');
                  });
                }

                this.$emit('success', response.data);
              } else {
                this.$message.error(response.message || '申诉提交失败');
              }
            })
            .catch(error => {
              console.error('申诉提交失败:', error);
              // 显示更详细的错误信息
              let errorMsg = '申诉提交失败，请稍后重试';
              if (error.message) {
                errorMsg = error.message;
              } else if (error.data && error.data.message) {
                errorMsg = error.data.message;
              } else if (typeof error === 'string') {
                errorMsg = error;
              }
              this.$message.error(errorMsg);
            })
            .finally(() => {
              this.submitting = false;
            });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.form.resetFields()
      this.fileList = []
    }
  }
}
</script>

<style lang="scss" scoped>
.appeal-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;

  .violation-info-card {
    background-color: #f8f9fa;
    margin-bottom: 15px;

    .violation-info-item {
      display: flex;
      margin-bottom: 10px;
      line-height: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        width: 80px;
        color: #606266;
        font-weight: bold;
      }

      .info-value {
        flex: 1;
        color: #303133;
      }
    }
  }

  .el-form-item {
    margin-bottom: 22px;
  }

  .el-textarea__inner {
    font-family: Arial, sans-serif;
  }

  .el-upload {
    &.el-upload--picture-card {
      width: 120px;
      height: 120px;
      line-height: 120px;
    }
  }

  .el-upload__tip {
    line-height: 1.5;
    color: #909399;
    margin-top: 8px;
  }

  .el-button {
    padding: 10px 20px;
    min-width: 100px;
  }
}
</style>
