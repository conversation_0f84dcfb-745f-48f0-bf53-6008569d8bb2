import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

// 打印环境变量
console.log('环境变量:', process.env)
console.log('VUE_APP_BASE_API:', process.env.VUE_APP_BASE_API)

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/zh-CN' // 使用中文语言包
import Empty from '@/components/Empty' // 导入自定义的Empty组件

// 导入 Descriptions 组件
import { Descriptions, DescriptionsItem } from 'element-ui'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import socketService from '@/utils/socket' // WebSocket服务

import '@/icons' // icon
import '@/permission' // permission control

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (process.env.NODE_ENV === 'production') {
  const { mockXHR } = require('../mock')
  mockXHR()
}

// 使用中文版 element-ui
Vue.use(ElementUI, { locale })

// 全局注册自定义的Empty组件
Vue.component('el-empty', Empty)

// 全局注册 Descriptions 组件
Vue.component('el-descriptions', Descriptions)
Vue.component('el-descriptions-item', DescriptionsItem)

// 在Vue原型上添加socketService，方便组件中使用
Vue.prototype.$socket = socketService

Vue.config.productionTip = false

// 全局错误处理
Vue.config.errorHandler = function(err, vm, info) {
  console.error('Vue错误:', err)
  console.log('错误组件:', vm)
  console.log('错误信息:', info)

  // 检查SQLite唯一性约束错误
  if (err.message && err.message.includes('UNIQUE constraint failed')) {
    console.warn('全局捕获到SQLite唯一性约束错误')
    Vue.prototype.$notify({
      title: '操作失败',
      message: '数据已存在，请检查输入信息',
      type: 'error',
      duration: 5000
    })
  }
}

// 捕获未处理的Promise rejection
window.addEventListener('unhandledrejection', function(event) {
  console.error('未处理的Promise错误:', event.reason)

  // 检查SQLite错误
  if (event.reason && event.reason.message && event.reason.message.includes('UNIQUE constraint failed')) {
    console.warn('全局捕获到未处理的SQLite错误')
    Vue.prototype.$notify({
      title: '数据操作失败',
      message: '数据已存在，请检查输入信息',
      type: 'error',
      duration: 5000
    })

    // 阻止默认处理
    event.preventDefault()
  }
})

// 在用户登录后连接WebSocket
router.beforeEach((to, from, next) => {
  // 如果用户已登录且WebSocket未连接，且不在登出过程中，则连接WebSocket
  if (store.getters.token && !socketService.connected && !socketService.isLoggingOut) {
    console.log('路由变化，检测到用户已登录但WebSocket未连接，尝试连接')
    socketService.connect()
  }
  next()
})

// 在用户登出时断开WebSocket，在用户登录时连接WebSocket
store.watch(
  (state) => state.user.token,
  (newToken, oldToken) => {
    console.log(`Token状态变化: ${oldToken ? '有token' : '无token'} -> ${newToken ? '有token' : '无token'}`)

    // 如果token被清除，断开WebSocket连接
    if (!newToken && socketService.connected) {
      console.log('检测到token被清除，断开WebSocket连接')
      socketService.disconnect()
    }
    // 如果新token被设置，且不在登出过程中，连接WebSocket
    else if (newToken && !socketService.connected && !socketService.isLoggingOut) {
      console.log('检测到新token被设置，尝试连接WebSocket')
      socketService.connect()
    }
  }
)

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
