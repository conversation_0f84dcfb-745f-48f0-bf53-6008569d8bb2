<template>
  <div class="violation-form">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <!-- 基本信息部分 -->
      <div class="form-card">
        <div class="form-section-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="车牌号" prop="bike_number">
              <el-input v-model="form.bike_number" placeholder="请输入车牌号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车主ID" prop="user_id">
              <el-input v-model.number="form.user_id" placeholder="请输入车主ID" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="违规类型" prop="violation_type">
              <el-select v-model="form.violation_type" placeholder="请选择违规类型" style="width: 100%">
                <el-option
                  v-for="item in violationTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="违规时间" prop="violation_time">
              <el-date-picker
                v-model="form.violation_time"
                type="datetime"
                placeholder="选择日期时间"
                style="width: 100%"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="违规地点" prop="location">
              <el-input v-model="form.location" placeholder="请输入违规地点" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="填写人" prop="recorder_name">
              <el-input v-model="form.recorder_name" placeholder="请输入填写人姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="违规描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入违规描述"
          />
        </el-form-item>

        <el-form-item label="违规证据" v-if="!isEdit">
          <el-upload
            :action="uploadUrl"
            :headers="uploadHeaders"
            :data="uploadData"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :file-list="fileList"
            multiple
            list-type="picture-card"
          >
            <i class="el-icon-plus"></i>
            <div slot="tip" class="el-upload__tip">
              支持上传图片(jpg/png)和视频(mp4)作为违规证据，单个文件不超过10MB
            </div>
          </el-upload>
        </el-form-item>
      </div>

      <!-- 违规处置部分，仅在编辑模式下显示 -->
      <div class="form-card" v-if="isEdit">
        <div class="form-section-title">违规处置</div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="处理状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择处理状态" style="width: 100%">
                <el-option :label="'待审核'" :value="0" />
                <el-option :label="'已处理'" :value="1" />
                <el-option :label="'申诉中'" :value="2" disabled />
                <el-option :label="'已撤销'" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="罚款金额" prop="fine_amount">
              <el-input-number
                v-model="form.fine_amount"
                :min="0"
                :precision="2"
                :step="10"
                style="width: 100%"
                :disabled="form.status === 3"
                placeholder="请输入罚款金额（元）"
              />
              <div class="form-tip" v-if="form.status === 3">已撤销的违规记录不能设置罚款</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="禁用车辆" prop="disable_vehicle">
              <el-switch
                v-model="form.disable_vehicle"
                :disabled="form.status === 3"
                active-text="禁用该车辆"
                inactive-text="不禁用"
              />
              <div class="form-tip" v-if="form.disable_vehicle">
                禁用后该车辆将无法使用，直到管理员手动解除禁用
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="处理结果" prop="result">
          <el-input
            v-model="form.result"
            type="textarea"
            :rows="3"
            placeholder="请输入处理结果"
          />
        </el-form-item>
      </div>

      <div class="form-actions">
        <el-form-item>
          <el-button type="primary" @click="confirmSubmit">{{ isEdit ? '保存' : '提交' }}</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { getViolationTypeOptions, getViolationTypes } from '@/api/violations'

export default {
  name: 'ViolationForm',
  props: {
    // 是否为编辑模式
    isEdit: {
      type: Boolean,
      default: false
    },
    // 编辑时的初始数据
    initialData: {
      type: Object,
      default: () => ({})
    },
    // 提交表单的方法
    submitMethod: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      form: {
        bike_number: '',
        user_id: '',
        violation_type: '',
        violation_time: new Date().toISOString().slice(0, 19).replace('T', ' '),
        location: '',
        description: '',
        status: 0,
        result: '',
        fine_amount: 0,
        disable_vehicle: false,
        disable_days: 7,
        recorder_name: this.$store.getters.name || '' // 默认使用当前登录用户的名称
      },
      fileList: [],
      createdViolationId: null,
      rules: {
        bike_number: [
          { required: true, message: '请输入车牌号', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        user_id: [
          { required: true, message: '请输入车主ID', trigger: 'blur' },
          { type: 'number', message: '车主ID必须为数字', trigger: 'blur' }
        ],
        violation_type: [
          { required: true, message: '请选择违规类型', trigger: 'change' }
        ],
        violation_time: [
          { required: true, message: '请选择违规时间', trigger: 'change' }
        ],
        location: [
          { required: true, message: '请输入违规地点', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ]
      },
      violationTypes: [],
      violationTypeOptions: []
    }
  },
  computed: {
    // 上传URL
    uploadUrl() {
      return process.env.VUE_APP_BASE_API + '/violations/evidences'
    },
    // 上传请求头
    uploadHeaders() {
      return {
        Authorization: 'Bearer ' + getToken()
      }
    },
    // 上传数据
    uploadData() {
      return {
        related_type: 'violation',
        related_id: this.createdViolationId
      }
    }
  },
  created() {
    // 如果是编辑模式，初始化表单数据
    if (this.isEdit && this.initialData) {
      Object.keys(this.form).forEach(key => {
        if (this.initialData[key] !== undefined) {
          this.form[key] = this.initialData[key]
        }
      })
    }

    // 获取违规类型选项
    this.fetchViolationTypeOptions()

    // 获取违规类型列表
    this.fetchViolationTypes()
  },
  methods: {
    // 确认提交前显示确认对话框
    confirmSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 在提交前检查必填字段
          if (!this.form.user_id) {
            this.$message.error('请输入车主ID')
            return false
          }

          // 确保 user_id 是数字类型
          if (typeof this.form.user_id === 'string') {
            this.form.user_id = parseInt(this.form.user_id, 10)
          }

          // 构建确认信息
          let confirmMessage = `
            <div style="text-align: left; padding: 10px;">
              <p><strong>车牌号：</strong>${this.form.bike_number}</p>
              <p><strong>车主ID：</strong>${this.form.user_id}</p>
              <p><strong>违规类型：</strong>${this.form.violation_type}</p>
              <p><strong>违规时间：</strong>${this.form.violation_time}</p>
              <p><strong>违规地点：</strong>${this.form.location}</p>
              <p><strong>填写人：</strong>${this.form.recorder_name}</p>
            `

          // 如果是编辑模式，添加处置信息
          if (this.isEdit) {
            const statusMap = {
              0: '待审核',
              1: '已处理',
              2: '申诉中',
              3: '已撤销'
            }

            confirmMessage += `
              <div style="margin-top: 15px; border-top: 1px dashed #ccc; padding-top: 10px;">
                <p><strong>处理状态：</strong>${statusMap[this.form.status]}</p>
                <p><strong>处理结果：</strong>${this.form.result || '无'}</p>
            `

            // 如果状态是已处理，显示罚款和禁用信息
            if (this.form.status === 1) {
              if (this.form.fine_amount > 0) {
                confirmMessage += `<p><strong>罚款金额：</strong>${this.form.fine_amount} 元</p>`
              }

              if (this.form.disable_vehicle) {
                confirmMessage += `<p><strong>禁用车辆：</strong>是</p>`
              }
            }

            confirmMessage += `</div>`
          }

          confirmMessage += `</div>`

          // 显示确认对话框
          this.$confirm(confirmMessage, '请确认违规信息', {
            confirmButtonText: '确认提交',
            cancelButtonText: '取消',
            dangerouslyUseHTMLString: true,
            type: 'warning'
          }).then(() => {
            this.submitForm()
          }).catch(() => {
            this.$message.info('已取消提交')
          })
        } else {
          console.warn('表单验证失败')
          return false
        }
      })
    },

    // 提交表单
    submitForm() {
      // 打印表单数据，便于调试
      console.log('提交的表单数据:', this.form)

      // 确保表单数据中包含 user_id 字段
      const formData = { ...this.form }
      if (!formData.user_id && formData.userId) {
        formData.user_id = formData.userId
      }

      // 确保 user_id 是数字类型
      if (formData.user_id && typeof formData.user_id === 'string') {
        formData.user_id = parseInt(formData.user_id, 10)
      }

      // 确保 recorder_id 字段存在
      if (!formData.recorder_id) {
        formData.recorder_id = this.$store.getters.userId || null
      }

      // 处理违规处置相关字段
      if (this.isEdit) {
        // 如果是已撤销状态，清空罚款金额和禁用车辆设置
        if (formData.status === 3) {
          formData.fine_amount = 0
          formData.disable_vehicle = false
          formData.disable_days = 0
        }

        // 如果不禁用车辆，清空禁用天数
        if (!formData.disable_vehicle) {
          formData.disable_days = 0
        } else {
          // 如果需要禁用车辆，添加标志以便在禁用失败时返回错误
          formData.fail_on_disable_error = true
        }

        // 添加处理人ID
        formData.handler_id = this.$store.getters.userId || null

        // 添加处理时间
        formData.handling_time = new Date().toISOString().slice(0, 19).replace('T', ' ')

        // 确保违规时间格式正确
        if (formData.violation_time && typeof formData.violation_time === 'string') {
          // 确保格式为 'YYYY-MM-DD HH:MM:SS'
          if (!formData.violation_time.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
            try {
              // 尝试转换为正确的格式
              const date = new Date(formData.violation_time)
              formData.violation_time = date.toISOString().slice(0, 19).replace('T', ' ')
            } catch (e) {
              console.error('违规时间格式转换失败:', e)
              // 如果转换失败，使用当前时间
              formData.violation_time = new Date().toISOString().slice(0, 19).replace('T', ' ')
            }
          }
        }

        // 构建处理结果文本
        if (formData.status === 1) { // 已处理
          let resultText = formData.result || '已确认违规'

          // 添加罚款信息
          if (formData.fine_amount > 0) {
            resultText += `，罚款${formData.fine_amount}元`
          }

          // 添加禁用车辆信息
          if (formData.disable_vehicle) {
            resultText += '，车辆已禁用'
          }

          formData.result = resultText
        } else if (formData.status === 3) { // 已撤销
          formData.result = formData.result || '违规记录无效'
        }
      }

      console.log('处理后的表单数据:', formData)
      this.submitMethod(formData)
        .then(response => {
          console.log('违规记录创建响应:', response)

          // 检查禁用结果
          if (formData.disable_vehicle && response.data && response.data.disable_result) {
            const disableResult = response.data.disable_result
            if (!disableResult.success && !disableResult.already_disabled) {
              this.$message.warning(`违规记录已保存，但禁用车辆失败: ${disableResult.message}`)
            } else {
              this.$message.success(this.isEdit ? '保存成功' : '提交成功')
            }
          } else {
            this.$message.success(this.isEdit ? '保存成功' : '提交成功')
          }

          // 检查响应中是否包含有效的ID
          if (response.data && response.data.id) {
            // 如果是创建模式且有上传文件，则先保存违规ID再上传证据
            if (!this.isEdit && this.fileList.length > 0) {
              this.createdViolationId = response.data.id
              this.uploadEvidences()
            } else {
              this.$emit('success', response.data)
            }
          } else {
            console.error('响应中缺少ID:', response)
            this.$message.warning('记录创建成功，但无法获取记录ID')
          }
        })
        .catch(error => {
          console.error('提交表单错误:', error)
          if (error.response && error.response.data) {
            // 检查是否包含禁用结果信息
            if (error.response.data.data && error.response.data.data.disable_result) {
              const disableResult = error.response.data.data.disable_result
              this.$message.error(`提交失败: ${error.response.data.message}，禁用车辆失败: ${disableResult.message}`)
            } else {
              this.$message.error(`提交失败: ${error.response.data.message || '未知错误'}`)
            }
          } else {
            this.$message.error(this.isEdit ? '保存失败' : '提交失败')
          }
        })
    },

    // 上传证据
    uploadEvidences() {
      if (this.fileList.length === 0 || !this.createdViolationId) {
        this.$emit('success', { id: this.createdViolationId })
        return
      }

      // 模拟上传完成
      setTimeout(() => {
        this.$message.success('证据上传成功')
        this.$emit('success', { id: this.createdViolationId })
      }, 1000)
    },

    // 上传前检查
    beforeUpload(file) {
      // 判断文件类型
      const isImage = file.type.indexOf('image/') !== -1
      const isVideo = file.type.indexOf('video/') !== -1
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isImage && !isVideo) {
        this.$message.error('只能上传图片或视频文件!')
        return false
      }

      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!')
        return false
      }

      // 如果还没有创建违规记录，则先保存到文件列表
      if (!this.createdViolationId) {
        this.fileList.push(file)
        return false // 阻止自动上传
      }

      return true
    },

    // 上传成功
    handleUploadSuccess(response, file, fileList) {
      if (response.status === 'success') {
        this.$message.success('证据上传成功')
      } else {
        this.$message.error(response.message || '上传失败')
      }
    },

    // 上传失败
    handleUploadError(err) {
      console.error(err)
      this.$message.error('上传失败')
    },
    resetForm() {
      this.$refs.form.resetFields()
      if (this.isEdit && this.initialData) {
        Object.keys(this.form).forEach(key => {
          if (this.initialData[key] !== undefined) {
            this.form[key] = this.initialData[key]
          }
        })
      }
    },

    // 设置表单数据，用于从外部填充表单
    setFormData(data) {
      if (!data) return

      // 保存初始数据，用于重置表单
      this.initialData = { ...data }

      // 处理日期时间字段
      const processedData = { ...data }

      // 确保违规时间格式正确
      if (processedData.violation_time) {
        // 如果是Date对象，转换为字符串
        if (processedData.violation_time instanceof Date) {
          processedData.violation_time = processedData.violation_time.toISOString().slice(0, 19).replace('T', ' ')
        }
        // 如果是字符串但格式不正确，尝试转换
        else if (typeof processedData.violation_time === 'string' &&
                !processedData.violation_time.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
          try {
            const date = new Date(processedData.violation_time)
            processedData.violation_time = date.toISOString().slice(0, 19).replace('T', ' ')
          } catch (e) {
            console.error('违规时间格式转换失败:', e)
          }
        }
      }

      // 将处理后的数据合并到表单中
      Object.keys(processedData).forEach(key => {
        if (this.form.hasOwnProperty(key)) {
          this.form[key] = processedData[key]
        }
      })

      // 确保填写人字段有值
      if (!this.form.recorder_name) {
        this.form.recorder_name = this.$store.getters.name || ''
      }

      console.log('设置表单数据:', this.form)

      // 触发表单验证
      this.$nextTick(() => {
        this.$refs.form.validateField(['bike_number', 'user_id'])
      })
    },

    // 获取违规类型选项
    fetchViolationTypeOptions() {
      getViolationTypeOptions().then(response => {
        if (response.data && Array.isArray(response.data)) {
          this.violationTypeOptions = response.data
        } else {
          // 如果获取失败，使用默认选项
          this.violationTypeOptions = [
            { value: '违规停车', label: '违规停车' },
            { value: '占用消防通道', label: '占用消防通道' },
            { value: '占用无障碍通道', label: '占用无障碍通道' },
            { value: '超时停车', label: '超时停车' },
            { value: '车辆损坏公物', label: '车辆损坏公物' },
            { value: '无证驾驶', label: '无证驾驶' },
            { value: '其他违规', label: '其他违规' }
          ]
        }
        // 将选项设置到违规类型列表中
        this.violationTypes = this.violationTypeOptions
      }).catch(() => {
        // 如果获取失败，使用默认选项
        this.violationTypeOptions = [
          { value: '违规停车', label: '违规停车' },
          { value: '占用消防通道', label: '占用消防通道' },
          { value: '占用无障碍通道', label: '占用无障碍通道' },
          { value: '超时停车', label: '超时停车' },
          { value: '车辆损坏公物', label: '车辆损坏公物' },
          { value: '无证驾驶', label: '无证驾驶' },
          { value: '其他违规', label: '其他违规' }
        ]
        this.violationTypes = this.violationTypeOptions
      })
    },

    // 获取违规类型列表
    fetchViolationTypes() {
      getViolationTypes().then(response => {
        if (response.data && Array.isArray(response.data)) {
          // 将数据库中的违规类型转换为选项格式
          const dbTypes = response.data.map(item => ({
            value: item.name,
            label: item.name,
            needs_admin: item.needs_admin,
            description: item.description,
            id: item.id
          }))

          // 如果有数据库中的类型，则使用数据库中的类型
          if (dbTypes.length > 0) {
            this.violationTypes = dbTypes
          }
        }
      }).catch(error => {
        console.error('获取违规类型列表失败:', error)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.violation-form {
  width: 100%;
  margin: 0 auto;
  padding: 0;

  .form-tip {
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
    margin-top: 5px;
  }

  .el-form-item {
    margin-bottom: 18px;
  }

  .el-input-number {
    width: 100%;
  }

  // 表单卡片样式
  .form-card {
    background-color: #f9f9f9;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }

  // 表单分组标题样式
  .form-section-title {
    font-size: 16px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #EBEEF5;
    position: relative;

    &:after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      width: 60px;
      height: 2px;
      background-color: #409EFF;
    }
  }

  // 表单操作按钮区域
  .form-actions {
    text-align: center;
    padding: 10px 0;
    margin-top: 10px;

    .el-button {
      padding: 10px 20px;
      min-width: 100px;
    }
  }

  // 在对话框中显示时的特殊样式
  .el-dialog & {
    padding: 0;
  }

  // 分隔线样式
  .el-divider {
    margin: 24px 0 20px;

    .el-divider__text {
      font-size: 16px;
      font-weight: bold;
      color: #409EFF;
      background-color: #fff;
    }
  }

  // 处理状态选择器样式
  .el-select {
    width: 100%;
  }

  // 禁用车辆开关样式
  .el-switch {
    margin-right: 10px;
  }

  // 处理结果文本域样式
  textarea {
    font-family: Arial, sans-serif;
    resize: vertical;
  }

  // 罚款金额输入框样式
  .el-input-number.is-disabled {
    .el-input-number__decrease,
    .el-input-number__increase {
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }

  // 行间距
  .el-row {
    margin-bottom: 8px;
  }

  // 上传组件样式
  .el-upload {
    &.el-upload--picture-card {
      width: 120px;
      height: 120px;
      line-height: 120px;
    }
  }
}
</style>
