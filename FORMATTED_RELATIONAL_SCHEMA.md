# 校园电动车管理系统 - 关系模式 (标准格式)

## 数据库关系模式定义

### （1）用户信息表（用户ID，用户名，密码，盐值，用户角色，所属学院，手机号码，电子邮箱，头像URL，创建时间，更新时间，版本号）

**主键：** 用户ID
**外键：** 无
**唯一约束：** 用户名
**说明：** 存储系统用户基本信息，包括管理员、保安、普通用户三种角色

### （2）车辆信息表（车辆ID，所属用户ID，车牌号，品牌，颜色，车辆类型，车辆状态，创建时间，更新时间）

**主键：** 车辆ID
**外键：** 所属用户ID → 用户信息表（用户ID）
**唯一约束：** 车牌号
**说明：** 存储电动车基本信息，每辆车归属于一个用户

### （3）停车场信息表（停车场ID，停车场名称，地址，总车位数，已占用车位数，经度，纬度，开放时间，状态，描述，校区，区域，管理员，联系电话，创建时间，更新时间）

**主键：** 停车场ID
**外键：** 无
**唯一约束：** 停车场名称
**说明：** 存储停车场基本信息和地理位置信息

### （4）车位信息表（车位ID，停车场ID，车位编号，车位类型，车位状态，当前车辆ID，充电功率，备注，最近维护时间，创建时间，更新时间）

**主键：** 车位ID
**外键：** 停车场ID → 停车场信息表（停车场ID），当前车辆ID → 车辆信息表（车辆ID）
**唯一约束：** 无
**说明：** 存储具体车位信息，包括普通车位、残疾人车位、充电车位三种类型

### （5）停车记录表（记录ID，车辆ID，用户ID，停车场ID，车位ID，入场时间，出场时间，记录状态，备注，创建时间，更新时间）

**主键：** 记录ID
**外键：** 车辆ID → 车辆信息表（车辆ID），用户ID → 用户信息表（用户ID），停车场ID → 停车场信息表（停车场ID），车位ID → 车位信息表（车位ID）
**唯一约束：** 无
**说明：** 记录车辆停车的完整过程，包括入场、出场时间和状态

### （6）违规类型表（类型ID，类型名称，类型描述，是否需要管理员处理，创建时间，更新时间）

**主键：** 类型ID
**外键：** 无
**唯一约束：** 类型名称
**说明：** 定义违规行为的分类标准

### （7）违规记录表（违规ID，车牌号，车辆ID，车主ID，违规时间，违规地点，违规类型，违规类型ID，违规描述，处理状态，处理结果，录入人ID，处理人ID，创建时间，更新时间）

**主键：** 违规ID
**外键：** 车辆ID → 车辆信息表（车辆ID），车主ID → 用户信息表（用户ID），违规类型ID → 违规类型表（类型ID），录入人ID → 用户信息表（用户ID），处理人ID → 用户信息表（用户ID）
**唯一约束：** 无
**说明：** 记录车辆违规行为和处理过程

### （8）申诉记录表（申诉ID，违规记录ID，申诉用户ID，申诉理由，申诉状态，处理意见，处理人ID，创建时间，更新时间）

**主键：** 申诉ID
**外键：** 违规记录ID → 违规记录表（违规ID），申诉用户ID → 用户信息表（用户ID），处理人ID → 用户信息表（用户ID）
**唯一约束：** 违规记录ID
**说明：** 处理用户对违规记录的申诉，一条违规记录最多对应一条申诉

### （9）充电记录表（充电ID，停车记录ID，车辆ID，用户ID，停车场ID，车位ID，开始时间，结束时间，充电时长，充电状态，充电功率，充电类型，备注，创建时间，更新时间）

**主键：** 充电ID
**外键：** 停车记录ID → 停车记录表（记录ID），车辆ID → 车辆信息表（车辆ID），用户ID → 用户信息表（用户ID），停车场ID → 停车场信息表（停车场ID），车位ID → 车位信息表（车位ID）
**唯一约束：** 无
**说明：** 记录车辆充电过程，必须基于有效的停车记录

### （10）公告信息表（公告ID，公告标题，公告内容，公告类型，优先级，目标角色，是否启用，生效开始时间，生效结束时间，发布人ID，创建时间，更新时间）

**主键：** 公告ID
**外键：** 发布人ID → 用户信息表（用户ID）
**唯一约束：** 无
**说明：** 系统公告管理，支持按角色定向发布

### （11）证据信息表（证据ID，关联记录ID，关联类型，证据类型，文件路径，上传人ID，创建时间）

**主键：** 证据ID
**外键：** 上传人ID → 用户信息表（用户ID）
**唯一约束：** 无
**说明：** 存储违规记录和申诉的证据文件，支持图片和视频

### （12）充电预约表（预约ID，用户ID，车辆ID，停车场ID，预约开始时间，预约结束时间，预约状态，创建时间，更新时间）

**主键：** 预约ID
**外键：** 用户ID → 用户信息表（用户ID），车辆ID → 车辆信息表（车辆ID），停车场ID → 停车场信息表（停车场ID）
**唯一约束：** 无
**说明：** 充电服务预约管理

### （13）充电故障表（故障ID，停车场ID，车位ID，故障类型，严重程度，报修人姓名，报修人电话，故障描述，处理状态，处理结果，创建时间，更新时间）

**主键：** 故障ID
**外键：** 停车场ID → 停车场信息表（停车场ID），车位ID → 车位信息表（车位ID）
**唯一约束：** 无
**说明：** 充电设备故障管理

### （14）充电异常表（异常ID，充电记录ID，故障ID，车位ID，停车场ID，异常类型，严重程度，异常描述，处理状态，处理结果，创建时间，更新时间）

**主键：** 异常ID
**外键：** 充电记录ID → 充电记录表（充电ID），故障ID → 充电故障表（故障ID），车位ID → 车位信息表（车位ID），停车场ID → 停车场信息表（停车场ID）
**唯一约束：** 无
**说明：** 充电过程中的异常情况记录

### （15）玩家账户表（玩家ID，用户名，密码，关联用户ID）

**主键：** 玩家ID
**外键：** 关联用户ID → 用户信息表（用户ID）
**唯一约束：** 用户名
**说明：** 历史遗留的游戏账户关联表

## 关系模式补充说明

### 数据类型定义

#### 基本数据类型
- **ID类型：** INTEGER，自增主键
- **字符串类型：** VARCHAR(长度) 或 TEXT
- **数值类型：** INTEGER、FLOAT
- **时间类型：** DATETIME
- **布尔类型：** BOOLEAN

#### 字段长度规范
- **用户名：** VARCHAR(50)
- **密码：** VARCHAR(255) （加密后存储）
- **车牌号：** VARCHAR(20)
- **手机号：** VARCHAR(20)
- **邮箱：** VARCHAR(100)
- **地址：** VARCHAR(255)
- **描述文本：** TEXT

### 约束条件说明

#### 主键约束
- 所有表都有自增整数主键
- 保证实体完整性

#### 外键约束
- 维护参照完整性
- 支持级联删除和置空策略

#### 唯一约束
- 用户名全局唯一
- 车牌号全局唯一
- 停车场名称全局唯一
- 违规类型名称唯一
- 违规记录与申诉一对一关系

#### 检查约束
- **用户角色：** 'admin', 'security', 'user'
- **车辆状态：** '可用', '废弃'
- **车位类型：** 1(普通), 2(残疾人), 3(充电)
- **车位状态：** 0(空闲), 1(已占用), 2(故障), 3(维修中), 4(禁用)
- **记录状态：** 0(进行中), 1(已完成), 2(异常)
- **违规状态：** 0(待审核), 1(已处理), 2(申诉中), 3(已撤销)
- **申诉状态：** 0(待审核), 1(已通过), 2(未通过)

### 业务规则

#### 核心业务流程
1. **用户注册 → 车辆登记 → 停车使用**
2. **违规录入 → 违规处理 → 申诉流程**
3. **停车记录 → 充电服务 → 异常处理**

#### 权限控制
- **管理员：** 全部功能权限
- **保安：** 违规录入、停车管理、充电管理
- **普通用户：** 个人信息、车辆管理、申诉提交

#### 数据一致性
- 车位占用状态与停车记录状态保持一致
- 充电记录必须关联有效的停车记录
- 申诉记录与违规记录一对一关系
- 时间逻辑约束（出场时间≥入场时间）

### 索引优化建议

#### 查询性能索引
```sql
-- 用户相关查询
CREATE INDEX idx_users_name ON 用户信息表(用户名);
CREATE INDEX idx_users_role ON 用户信息表(用户角色);

-- 车辆相关查询
CREATE INDEX idx_bikes_owner ON 车辆信息表(所属用户ID);
CREATE INDEX idx_bikes_number ON 车辆信息表(车牌号);

-- 停车记录查询
CREATE INDEX idx_parking_vehicle ON 停车记录表(车辆ID);
CREATE INDEX idx_parking_user ON 停车记录表(用户ID);
CREATE INDEX idx_parking_time ON 停车记录表(入场时间);

-- 违规记录查询
CREATE INDEX idx_violation_user ON 违规记录表(车主ID);
CREATE INDEX idx_violation_bike ON 违规记录表(车辆ID);
CREATE INDEX idx_violation_time ON 违规记录表(违规时间);

-- 充电记录查询
CREATE INDEX idx_charging_vehicle ON 充电记录表(车辆ID);
CREATE INDEX idx_charging_time ON 充电记录表(开始时间);
```

### 数据完整性保证

#### 事务处理
- 停车入场：更新车位状态 + 创建停车记录
- 停车出场：更新车位状态 + 更新停车记录
- 违规处理：更新违规状态 + 记录处理结果
- 申诉处理：更新申诉状态 + 可能更新违规状态

#### 并发控制
- 车位分配采用乐观锁机制
- 关键业务操作使用事务保证原子性
- 防止重复提交和数据冲突

#### 数据备份
- 定期备份重要业务数据
- 保留操作日志用于审计
- 支持数据恢复和回滚操作
