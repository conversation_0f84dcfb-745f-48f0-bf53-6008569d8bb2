<template>
  <div class="app-container">
    <!-- 管理员操作栏和筛选栏 -->
    <div class="mb-4">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="typeFilter" placeholder="选择公告类型" clearable @change="handleTypeChange">
            <el-option label="全部" value="" />
            <el-option label="系统通知" value="system" />
            <el-option label="停车快讯" value="parking" />
            <el-option label="违规警告" value="violation" />
          </el-select>
        </el-col>
        <el-col :span="18" class="text-right">
          <el-button v-if="canManage" type="primary" @click="handleCreate">发布公告</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 公告列表 -->
    <el-card v-for="item in announcements" :key="item.id" class="mb-4">
      <div slot="header" class="clearfix">
        <span class="title">{{ item.title }}</span>
        <el-tag :type="getTypeTag(item.type)" class="ml-2">
          {{ getTypeName(item.type) }}
        </el-tag>
        <el-button
          v-if="canManage"
          style="float: right; padding: 3px 0"
          type="text"
          @click="handleEdit(item)"
        >
          编辑
        </el-button>
        <el-button
          v-if="canManage"
          style="float: right; padding: 3px 0; margin-right: 10px"
          type="text"
          @click="handleDelete(item)"
        >
          删除
        </el-button>
      </div>
      <div class="content">{{ item.content }}</div>
      <div class="footer">
        <span class="time">发布时间：{{ formatDate(item.created_at) }}</span>
      </div>
    </el-card>

    <!-- 分页 -->
    <el-pagination
      :current-page="page"
      :page-size="perPage"
      :total="total"
      @current-change="handlePageChange"
    />

    <!-- 编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" />
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type">
            <el-option label="系统通知" value="system" />
            <el-option label="停车快讯" value="parking" />
            <el-option label="违规警告" value="violation" />
          </el-select>
        </el-form-item>

        <el-form-item label="内容" prop="content">
          <el-input type="textarea" v-model="form.content" rows="6" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getAnnouncements, createAnnouncement, updateAnnouncement, deleteAnnouncement } from '@/api/announcements'
import { formatDate } from '@/utils/date'

export default {
  name: 'Announcements',
  data() {
    return {
      announcements: [],
      page: 1,
      perPage: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '',
      typeFilter: '',
      form: {
        id: null,
        title: '',
        content: '',
        type: 'system'
      },
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['isAdmin']),
    canManage() {
      return this.isAdmin || this.$store.getters.roles.includes('admin')
    }
  },
  methods: {
    formatDate,
    async fetchData() {
      try {
        console.log('开始获取公告列表...')
        const res = await getAnnouncements({
          page: this.page,
          per_page: this.perPage,
          type: this.typeFilter
        })

        console.log('获取公告列表响应:', res)

        // 处理不同的响应格式
        if (res.data && res.data.items) {
          this.announcements = res.data.items
          this.total = res.data.total || res.data.items.length
        } else if (Array.isArray(res.data)) {
          this.announcements = res.data
          this.total = res.data.length
        } else if (res.data && res.data.announcements) {
          this.announcements = res.data.announcements
          this.total = res.data.total || res.data.announcements.length
        } else {
          // 如果没有数据，设置为空数组
          this.announcements = []
          this.total = 0
          console.warn('公告列表响应格式不符合预期:', res)
        }

        console.log('处理后的公告列表:', this.announcements)
      } catch (error) {
        console.error('获取公告列表失败:', error)
        this.$message.error('获取公告列表失败')
        this.announcements = []
        this.total = 0
      }
    },
    getTypeTag(type) {
      const typeMap = {
        system: 'info',
        parking: 'success',
        violation: 'danger'
      }
      return typeMap[type] || 'info'
    },
    getTypeName(type) {
      const typeMap = {
        system: '系统通知',
        parking: '停车快讯',
        violation: '违规警告'
      }
      return typeMap[type] || '未知类型'
    },
    handleTypeChange() {
      this.page = 1
      this.fetchData()
    },
    handleCreate() {
      this.dialogTitle = '发布公告'
      this.form = {
        id: null,
        title: '',
        content: '',
        type: 'system'
      }
      this.dialogVisible = true
    },
    handleEdit(item) {
      this.dialogTitle = '编辑公告'
      this.form = { ...item }
      this.dialogVisible = true
    },
    async handleDelete(item) {
      try {
        await this.$confirm('确认删除该公告吗？', '提示', {
          type: 'warning'
        })
        await deleteAnnouncement(item.id)
        this.$message.success('删除成功')
        this.fetchData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除公告失败:', error)
          this.$message.error('删除公告失败')
        }
      }
    },
    handlePageChange(page) {
      this.page = page
      this.fetchData()
    },
    async submitForm() {
      try {
        await this.$refs.form.validate()
        if (this.form.id) {
          await updateAnnouncement(this.form.id, this.form)
          this.$message.success('更新成功')
        } else {
          await createAnnouncement(this.form)
          this.$message.success('发布成功')
        }
        this.dialogVisible = false
        this.fetchData()
      } catch (error) {
        console.error('提交表单失败:', error)
        this.$message.error('提交失败')
      }
    }
  },
  created() {
    this.fetchData()
  }
}
</script>

<style scoped>
.title {
  font-size: 16px;
  font-weight: bold;
}
.content {
  margin: 10px 0;
  line-height: 1.6;
}
.footer {
  color: #999;
  font-size: 12px;
}
.text-right {
  text-align: right;
}
</style>