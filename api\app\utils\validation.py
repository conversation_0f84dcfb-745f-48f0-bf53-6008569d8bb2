from flask import request
from app.utils.response import api_response
from flask_jwt_extended import get_jwt, verify_jwt_in_request
from functools import wraps
import logging
import json

def admin_required(fn):
    """装饰器：要求用户必须具有管理员权限"""
    @wraps(fn)
    def wrapper(*args, **kwargs):
        verify_jwt_in_request()
        claims = get_jwt()

        # 详细记录JWT声明内容，用于调试
        print(f"[DEBUG] JWT声明内容: {json.dumps(claims, indent=2)}")
        print(f"[DEBUG] 角色信息: {claims.get('role')}")
        print(f"[DEBUG] 是否管理员: {claims.get('role') == 'admin'}")

        # 检查角色是否包含'admin'
        if claims.get("role") != "admin":
            print(f"[DEBUG] 权限验证失败: 用户角色 '{claims.get('role')}' 不是管理员")
            return api_response(message="只有管理员才能访问此资源", status="error", code=403)

        print(f"[DEBUG] 权限验证成功: 用户是管理员")
        return fn(*args, **kwargs)
    return wrapper

def admin_or_security_required(fn):
    """装饰器：要求用户必须具有管理员或保安权限"""
    @wraps(fn)
    def wrapper(*args, **kwargs):
        verify_jwt_in_request()
        claims = get_jwt()

        # 详细记录JWT声明内容，用于调试
        print(f"[DEBUG] JWT声明内容: {json.dumps(claims, indent=2)}")
        print(f"[DEBUG] 角色信息: {claims.get('role')}")
        print(f"[DEBUG] 是否管理员或保安: {claims.get('role') in ['admin', 'security']}")

        # 检查角色是否为'admin'或'security'
        if claims.get("role") not in ["admin", "security"]:
            print(f"[DEBUG] 权限验证失败: 用户角色 '{claims.get('role')}' 不是管理员或保安")
            return api_response(message="只有管理员或保安才能访问此资源", status="error", code=403)

        print(f"[DEBUG] 权限验证成功: 用户是{claims.get('role')}")
        return fn(*args, **kwargs)
    return wrapper

def validate_request(data, required_fields, field_types=None):
    """
    验证请求中必填字段是否存在，并检查字段类型

    参数:
        data: 请求中的JSON数据
        required_fields: 必填字段列表
        field_types: 字段类型字典，格式为 {字段名: 期望类型}

    返回:
        None: 验证通过
        JSON响应: 验证失败时的错误响应
    """
    if not data:
        return api_response(message='请求体不能为空或非JSON格式', status='error', code=400)

    # 验证必填字段
    missing_fields = []
    for field in required_fields:
        if field not in data or data[field] in (None, ''):
            missing_fields.append(field)

    if missing_fields:
        return api_response(
            message='缺少必填字段',
            status='error',
            code=400,
            errors={'missing_fields': missing_fields}
        )

    # 验证字段类型
    if field_types:
        type_errors = []
        for field, expected_type in field_types.items():
            if field in data and data[field] is not None:
                # 检查字段的数据类型是否符合预期
                if not isinstance(data[field], expected_type):
                    actual_type = type(data[field]).__name__
                    expected_type_name = expected_type.__name__
                    type_errors.append(f"字段 '{field}' 的类型应为 {expected_type_name}，但收到了 {actual_type}")

        if type_errors:
            return api_response(
                message='字段类型验证失败',
                status='error',
                code=400,
                errors={'type_errors': type_errors}
            )

    # 验证通过
    return None