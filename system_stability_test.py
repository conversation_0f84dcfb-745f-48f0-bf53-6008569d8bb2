#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统稳定性测试脚本
测试内容：
1. 长时间运行稳定性测试
2. 数据一致性测试
"""

import os
import sys
import time
import json
import random
import requests
import threading
from datetime import datetime, timedelta

# 测试配置
BASE_URL = "http://127.0.0.1:5000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"
USER_USERNAME = "user"
USER_PASSWORD = "user123"
SECURITY_USERNAME = "security"
SECURITY_PASSWORD = "security123"

# 测试结果存储
results = {
    "stability": {
        "long_running": {},
        "data_consistency": {}
    }
}

def get_token(username, password):
    """获取用户令牌"""
    login_url = f"{BASE_URL}/api/login"
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            return response.json().get("access_token")
        else:
            print(f"登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"登录请求异常: {str(e)}")
        return None

def make_request(url, token=None, params=None, method="GET", data=None):
    """发送API请求"""
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, params=params)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method.upper() == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        return {
            "status_code": response.status_code,
            "success": 200 <= response.status_code < 300,
            "data": response.json() if response.status_code == 200 else None
        }
    except Exception as e:
        return {
            "status_code": 0,
            "success": False,
            "error": str(e),
            "data": None
        }

def run_long_running_test(duration_minutes=10, interval_seconds=30):
    """运行长时间稳定性测试"""
    print(f"\n=== 长时间运行稳定性测试 (持续{duration_minutes}分钟) ===\n")
    
    # 获取管理员令牌
    admin_token = get_token(ADMIN_USERNAME, ADMIN_PASSWORD)
    if not admin_token:
        print("获取管理员令牌失败，无法继续测试")
        return
    
    # 测试的API列表
    apis = [
        {"name": "获取停车场列表", "url": f"{BASE_URL}/api/parkinglots", "method": "GET", "params": {"page": 1, "limit": 10}},
        {"name": "获取停车记录", "url": f"{BASE_URL}/api/parking-records", "method": "GET", "params": {"page": 1, "per_page": 10}},
        {"name": "获取公告列表", "url": f"{BASE_URL}/api/announcements", "method": "GET", "params": {"page": 1, "per_page": 10}}
    ]
    
    # 初始化结果统计
    for api in apis:
        results["stability"]["long_running"][api["name"]] = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "response_times": []
        }
    
    start_time = datetime.now()
    end_time = start_time + timedelta(minutes=duration_minutes)
    
    print(f"测试开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"预计结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    while datetime.now() < end_time:
        current_time = datetime.now()
        elapsed_minutes = (current_time - start_time).total_seconds() / 60
        
        print(f"\n当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"已运行: {elapsed_minutes:.2f}分钟 / {duration_minutes}分钟 ({elapsed_minutes/duration_minutes*100:.2f}%)")
        
        for api in apis:
            api_name = api["name"]
            print(f"测试API: {api_name}")
            
            start_request_time = time.time()
            result = make_request(api["url"], admin_token, api.get("params"), api["method"], api.get("data"))
            end_request_time = time.time()
            response_time = (end_request_time - start_request_time) * 1000  # 转换为毫秒
            
            # 更新统计数据
            results["stability"]["long_running"][api_name]["total_requests"] += 1
            if result["success"]:
                results["stability"]["long_running"][api_name]["successful_requests"] += 1
                results["stability"]["long_running"][api_name]["response_times"].append(response_time)
                print(f"  请求成功，响应时间: {response_time:.2f}ms")
            else:
                results["stability"]["long_running"][api_name]["failed_requests"] += 1
                print(f"  请求失败: {result.get('error', '未知错误')}")
        
        # 等待下一轮测试
        if datetime.now() < end_time:
            print(f"等待{interval_seconds}秒后进行下一轮测试...")
            time.sleep(interval_seconds)
    
    print(f"\n测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("长时间运行稳定性测试完成")

def test_data_consistency():
    """测试数据一致性"""
    print("\n=== 数据一致性测试 ===\n")
    
    # 获取管理员令牌
    admin_token = get_token(ADMIN_USERNAME, ADMIN_PASSWORD)
    if not admin_token:
        print("获取管理员令牌失败，无法继续测试")
        return
    
    # 测试停车场数据一致性
    test_parking_lot_consistency(admin_token)
    
    # 测试停车记录数据一致性
    test_parking_record_consistency(admin_token)

def test_parking_lot_consistency(token):
    """测试停车场数据一致性"""
    print("测试停车场数据一致性")
    
    # 获取所有停车场
    parking_lots_result = make_request(f"{BASE_URL}/api/parkinglots", token)
    if not parking_lots_result["success"]:
        print("  获取停车场列表失败")
        return
    
    parking_lots = parking_lots_result["data"].get("items", [])
    if not parking_lots:
        print("  没有找到停车场数据")
        return
    
    print(f"  找到{len(parking_lots)}个停车场")
    
    # 检查每个停车场的车位数量与实际占用数是否一致
    consistency_results = []
    
    for lot in parking_lots:
        lot_id = lot.get("id")
        lot_name = lot.get("name")
        total_spaces = lot.get("total_spaces", 0)
        occupied_spaces = lot.get("occupied_spaces", 0)
        
        print(f"  检查停车场: {lot_name} (ID: {lot_id})")
        print(f"    报告的总车位数: {total_spaces}")
        print(f"    报告的已占用车位数: {occupied_spaces}")
        
        # 获取该停车场的所有车位
        spaces_result = make_request(f"{BASE_URL}/api/parking-lots/{lot_id}/spaces", token)
        if not spaces_result["success"]:
            print(f"    获取车位列表失败")
            continue
        
        spaces = spaces_result["data"].get("items", [])
        actual_total = len(spaces)
        actual_occupied = sum(1 for space in spaces if space.get("status") == 1)
        
        print(f"    实际总车位数: {actual_total}")
        print(f"    实际已占用车位数: {actual_occupied}")
        
        # 检查一致性
        total_consistent = total_spaces == actual_total
        occupied_consistent = occupied_spaces == actual_occupied
        
        consistency_results.append({
            "lot_id": lot_id,
            "lot_name": lot_name,
            "reported_total": total_spaces,
            "actual_total": actual_total,
            "total_consistent": total_consistent,
            "reported_occupied": occupied_spaces,
            "actual_occupied": actual_occupied,
            "occupied_consistent": occupied_consistent
        })
        
        print(f"    总车位数一致性: {'一致' if total_consistent else '不一致'}")
        print(f"    已占用车位数一致性: {'一致' if occupied_consistent else '不一致'}")
    
    # 保存结果
    results["stability"]["data_consistency"]["parking_lots"] = consistency_results
    
    # 计算一致性百分比
    total_lots = len(consistency_results)
    total_consistent_count = sum(1 for r in consistency_results if r["total_consistent"])
    occupied_consistent_count = sum(1 for r in consistency_results if r["occupied_consistent"])
    
    total_consistency_percentage = (total_consistent_count / total_lots * 100) if total_lots > 0 else 0
    occupied_consistency_percentage = (occupied_consistent_count / total_lots * 100) if total_lots > 0 else 0
    
    print(f"  总车位数一致性百分比: {total_consistency_percentage:.2f}%")
    print(f"  已占用车位数一致性百分比: {occupied_consistency_percentage:.2f}%")

def test_parking_record_consistency(token):
    """测试停车记录数据一致性"""
    print("测试停车记录数据一致性")
    
    # 获取进行中的停车记录
    active_records_result = make_request(f"{BASE_URL}/api/parking-records", token, {"status": 0})
    if not active_records_result["success"]:
        print("  获取进行中的停车记录失败")
        return
    
    active_records = active_records_result["data"].get("items", [])
    print(f"  找到{len(active_records)}条进行中的停车记录")
    
    # 检查每条进行中的停车记录对应的车位状态是否为已占用
    consistency_results = []
    
    for record in active_records:
        record_id = record.get("id")
        parking_space_id = record.get("parking_space_id")
        vehicle_id = record.get("vehicle_id")
        
        print(f"  检查停车记录: ID {record_id}, 车位ID {parking_space_id}, 车辆ID {vehicle_id}")
        
        # 获取对应车位信息
        space_result = make_request(f"{BASE_URL}/api/parking-spaces/{parking_space_id}", token)
        if not space_result["success"]:
            print(f"    获取车位信息失败")
            continue
        
        space = space_result["data"]
        space_status = space.get("status")
        space_vehicle_id = space.get("current_vehicle_id")
        
        print(f"    车位状态: {space_status} (0=空闲, 1=已占用)")
        print(f"    车位当前车辆ID: {space_vehicle_id}")
        
        # 检查一致性
        status_consistent = space_status == 1  # 1表示已占用
        vehicle_consistent = space_vehicle_id == vehicle_id
        
        consistency_results.append({
            "record_id": record_id,
            "parking_space_id": parking_space_id,
            "vehicle_id": vehicle_id,
            "space_status": space_status,
            "space_vehicle_id": space_vehicle_id,
            "status_consistent": status_consistent,
            "vehicle_consistent": vehicle_consistent
        })
        
        print(f"    车位状态一致性: {'一致' if status_consistent else '不一致'}")
        print(f"    车辆ID一致性: {'一致' if vehicle_consistent else '不一致'}")
    
    # 保存结果
    results["stability"]["data_consistency"]["parking_records"] = consistency_results
    
    # 计算一致性百分比
    total_records = len(consistency_results)
    status_consistent_count = sum(1 for r in consistency_results if r["status_consistent"])
    vehicle_consistent_count = sum(1 for r in consistency_results if r["vehicle_consistent"])
    
    status_consistency_percentage = (status_consistent_count / total_records * 100) if total_records > 0 else 0
    vehicle_consistency_percentage = (vehicle_consistent_count / total_records * 100) if total_records > 0 else 0
    
    print(f"  车位状态一致性百分比: {status_consistency_percentage:.2f}%")
    print(f"  车辆ID一致性百分比: {vehicle_consistency_percentage:.2f}%")

def export_results_to_excel_format():
    """将测试结果导出为Excel友好的格式"""
    print("\n=== 稳定性测试结果（Excel友好格式）===\n")
    
    # 长时间运行测试结果
    print("长时间运行测试结果")
    print("API名称\t总请求数\t成功请求数\t失败请求数\t成功率(%)\t平均响应时间(ms)")
    for name, data in results["stability"]["long_running"].items():
        total = data["total_requests"]
        success = data["successful_requests"]
        failed = data["failed_requests"]
        success_rate = (success / total * 100) if total > 0 else 0
        avg_response_time = sum(data["response_times"]) / len(data["response_times"]) if data["response_times"] else 0
        
        print(f"{name}\t{total}\t{success}\t{failed}\t{success_rate:.2f}\t{avg_response_time:.2f}")
    
    # 数据一致性测试结果 - 停车场
    if "parking_lots" in results["stability"]["data_consistency"]:
        print("\n停车场数据一致性测试结果")
        print("停车场ID\t停车场名称\t报告总车位数\t实际总车位数\t总车位一致性\t报告占用车位数\t实际占用车位数\t占用车位一致性")
        
        for lot in results["stability"]["data_consistency"]["parking_lots"]:
            total_consistent = "一致" if lot["total_consistent"] else "不一致"
            occupied_consistent = "一致" if lot["occupied_consistent"] else "不一致"
            
            print(f"{lot['lot_id']}\t{lot['lot_name']}\t{lot['reported_total']}\t{lot['actual_total']}\t{total_consistent}\t{lot['reported_occupied']}\t{lot['actual_occupied']}\t{occupied_consistent}")
    
    # 数据一致性测试结果 - 停车记录
    if "parking_records" in results["stability"]["data_consistency"]:
        print("\n停车记录数据一致性测试结果")
        print("记录ID\t车位ID\t车辆ID\t车位状态\t车位当前车辆ID\t状态一致性\t车辆ID一致性")
        
        for record in results["stability"]["data_consistency"]["parking_records"]:
            status_consistent = "一致" if record["status_consistent"] else "不一致"
            vehicle_consistent = "一致" if record["vehicle_consistent"] else "不一致"
            
            print(f"{record['record_id']}\t{record['parking_space_id']}\t{record['vehicle_id']}\t{record['space_status']}\t{record['space_vehicle_id']}\t{status_consistent}\t{vehicle_consistent}")

if __name__ == "__main__":
    # 运行长时间稳定性测试（默认10分钟，可以根据需要调整）
    run_long_running_test(duration_minutes=10, interval_seconds=30)
    
    # 运行数据一致性测试
    test_data_consistency()
    
    # 导出结果为Excel友好格式
    export_results_to_excel_format()
