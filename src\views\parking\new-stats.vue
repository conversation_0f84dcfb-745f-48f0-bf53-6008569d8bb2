<template>
  <div class="app-container">
    <div class="dashboard-header">
      <h2>停车场统计数据</h2>
      <div>
        <el-button type="primary" icon="el-icon-refresh" @click="fetchData">刷新数据</el-button>
        <el-button type="success" icon="el-icon-download" @click="exportData">导出数据</el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card shadow="hover" class="filter-card">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="停车场">
          <el-select v-model="filterForm.parkingLotId" placeholder="选择停车场" clearable>
            <el-option
              v-for="item in parkingLots"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="applyFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览卡片 -->
    <el-row v-loading="loading" :gutter="20" class="stat-cards">
      <el-col :xs="12" :sm="8" :md="6" :lg="4" v-for="(item, index) in overviewItems" :key="index">
        <el-card shadow="hover" class="stat-card" :body-style="{ padding: '15px' }">
          <div class="card-content">
            <div class="card-icon" :style="{ backgroundColor: item.color }">
              <i :class="item.icon"></i>
            </div>
            <div class="card-data">
              <div class="card-value">{{ item.value }}</div>
              <div class="card-label">{{ item.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 每日停车记录表格 -->
    <el-card v-loading="loading" shadow="hover" class="data-card">
      <div slot="header" class="clearfix">
        <span>近7天停车记录</span>
      </div>
      <el-table :data="stats.daily_stats || []" border style="width: 100%">
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column label="总记录数" width="120" align="center">
          <template slot-scope="scope">
            <el-tag type="primary">{{ scope.row.count || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="已完成" width="120" align="center">
          <template slot-scope="scope">
            <el-tag type="success">{{ scope.row.completed || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="进行中" width="120" align="center">
          <template slot-scope="scope">
            <el-tag type="warning">{{ scope.row.active || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="完成率" min-width="180">
          <template slot-scope="scope">
            <el-progress
              :percentage="getCompletionRate(scope.row)"
              :status="getCompletionStatus(scope.row)"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 停车场使用率 -->
    <el-card v-loading="loading" shadow="hover" class="data-card">
      <div slot="header" class="clearfix">
        <span>停车场使用率</span>
      </div>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" v-for="lot in stats.parking_lots" :key="lot.id">
          <div class="parking-lot-card">
            <h3>{{ lot.name }}</h3>
            <div class="parking-lot-info">
              <div class="info-item">
                <span class="label">总车位:</span>
                <span class="value">{{ lot.total_spaces }}</span>
              </div>
              <div class="info-item">
                <span class="label">已占用:</span>
                <span class="value">{{ lot.occupied_spaces }}</span>
              </div>
              <div class="info-item">
                <span class="label">空闲:</span>
                <span class="value">{{ lot.total_spaces - lot.occupied_spaces }}</span>
              </div>
            </div>
            <el-progress
              :percentage="lot.utilization_rate"
              :status="getUtilizationStatus(lot.utilization_rate)"
              :format="percentFormat"
              :stroke-width="18"
            />
            <div class="action-buttons">
              <el-button type="primary" size="mini" @click="viewParkingLot(lot.id)">查看详情</el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 车辆类型分布 -->
    <el-card v-loading="loading" shadow="hover" class="data-card">
      <div slot="header" class="clearfix">
        <span>车辆类型分布</span>
      </div>
      <el-table :data="stats.vehicle_types || []" border style="width: 100%">
        <el-table-column prop="type" label="车辆类型" width="150">
          <template slot-scope="scope">
            {{ getVehicleTypeName(scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="count" label="数量" width="120" align="center" />
        <el-table-column label="占比" min-width="180">
          <template slot-scope="scope">
            <el-progress
              :percentage="getVehicleTypePercentage(scope.row)"
              :color="getRandomColor(scope.row.type)"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import request from '@/utils/request'
import { getParkingLots } from '@/api/parkinglots'

export default {
  name: 'ParkingStats',
  data() {
    return {
      loading: true,
      filterForm: {
        parkingLotId: '',
        dateRange: []
      },
      parkingLots: [],
      stats: {
        overview: {
          total_records: 0,
          active_records: 0,
          completed_records: 0,
          abnormal_records: 0,
          today_records: 0,
          avg_duration_hours: 0
        },
        parking_lots: [],
        vehicle_types: [],
        daily_stats: []
      },
      colorMap: {} // 用于存储车辆类型的颜色映射
    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ]),
    isAdmin() {
      return this.roles.includes('admin')
    },
    overviewItems() {
      const overview = this.stats.overview || {}

      // 格式化平均停车时长
      const formatDuration = (hours) => {
        if (!hours || isNaN(parseFloat(hours))) return '0小时'

        const h = Math.floor(hours)
        const m = Math.round((hours - h) * 60)

        if (h > 0) {
          return `${h}小时${m > 0 ? ` ${m}分钟` : ''}`
        } else {
          return `${m}分钟`
        }
      }

      return [
        {
          label: '总停车记录',
          value: overview.total_records || 0,
          icon: 'el-icon-s-data',
          color: '#409EFF'
        },
        {
          label: '进行中记录',
          value: overview.active_records || 0,
          icon: 'el-icon-time',
          color: '#67C23A'
        },
        {
          label: '已完成记录',
          value: overview.completed_records || 0,
          icon: 'el-icon-check',
          color: '#E6A23C'
        },
        {
          label: '今日停车',
          value: overview.today_records || 0,
          icon: 'el-icon-date',
          color: '#F56C6C'
        },
        {
          label: '平均停车时长',
          value: formatDuration(overview.avg_duration_hours),
          icon: 'el-icon-stopwatch',
          color: '#909399'
        },
        {
          label: '异常记录',
          value: overview.abnormal_records || 0,
          icon: 'el-icon-warning',
          color: '#9B59B6'
        }
      ]
    }
  },
  created() {
    if (!this.isAdmin) {
      this.$message.error('您没有权限访问此页面')
      this.$router.push('/')
      return
    }
    this.fetchParkingLots()
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      console.log('开始获取停车统计数据')

      // 构建查询参数
      const params = {
        date_range: 'week', // 默认获取一周的数据
        _t: new Date().getTime() // 添加时间戳防止缓存
      }

      // 添加停车场筛选
      if (this.filterForm.parkingLotId) {
        params.parking_lot_id = this.filterForm.parkingLotId
      }

      // 添加日期范围筛选
      if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
        params.start_date = this.filterForm.dateRange[0]
        params.end_date = this.filterForm.dateRange[1]
      }

      request({
        url: '/api/parking-records/stats',
        method: 'get',
        params: params
      }).then(response => {
        console.log('获取停车统计数据成功:', response)

        let responseData = response
        if (response.data) {
          responseData = response.data
        }

        // 检查数据完整性并设置默认值
        this.ensureDataIntegrity(responseData)

        // 更新统计数据
        this.stats = responseData
        this.loading = false
      }).catch(error => {
        console.error('获取停车统计数据失败', error)
        this.$message.error('获取停车统计数据失败')
        this.loading = false
      })
    },

    // 确保数据完整性
    ensureDataIntegrity(data) {
      // 确保 overview 数据存在
      if (!data.overview) {
        data.overview = {
          total_records: 0,
          active_records: 0,
          completed_records: 0,
          abnormal_records: 0,
          today_records: 0,
          avg_duration_hours: 0
        }
      }

      // 确保 daily_stats 数据存在
      if (!data.daily_stats || !Array.isArray(data.daily_stats)) {
        data.daily_stats = []
      }

      // 确保 vehicle_types 数据存在
      if (!data.vehicle_types || !Array.isArray(data.vehicle_types)) {
        data.vehicle_types = []
      }

      // 确保 parking_lots 数据存在
      if (!data.parking_lots || !Array.isArray(data.parking_lots)) {
        data.parking_lots = []
      }

      // 补充每日停车记录数据，确保有7天的数据
      this.ensureDailyStatsData(data)
    },

    // 确保每日停车记录数据完整
    ensureDailyStatsData(data) {
      if (data.daily_stats.length < 7) {
        // 获取最近7天的日期
        const dates = []
        for (let i = 6; i >= 0; i--) {
          const date = new Date()
          date.setDate(date.getDate() - i)
          dates.push(date.toISOString().split('T')[0]) // 格式: YYYY-MM-DD
        }

        // 创建默认数据
        const existingDates = data.daily_stats.map(item => item.date)

        // 添加缺失的日期
        dates.forEach(date => {
          if (!existingDates.includes(date)) {
            data.daily_stats.push({
              date: date,
              count: 0,
              completed: 0,
              active: 0
            })
          }
        })

        // 按日期排序
        data.daily_stats.sort((a, b) => new Date(a.date) - new Date(b.date))
      }
    },

    // 获取停车场列表
    fetchParkingLots() {
      getParkingLots().then(response => {
        if (response.data && response.data.items) {
          this.parkingLots = response.data.items
        }
      }).catch(error => {
        console.error('获取停车场列表失败', error)
      })
    },

    // 应用筛选条件
    applyFilter() {
      this.fetchData()
    },

    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        parkingLotId: '',
        dateRange: []
      }
      this.fetchData()
    },

    // 获取完成率
    getCompletionRate(row) {
      if (!row.count || row.count === 0) return 0
      return Math.round((row.completed / row.count) * 100)
    },

    // 获取完成状态
    getCompletionStatus(row) {
      const rate = this.getCompletionRate(row)
      if (rate >= 80) return 'success'
      if (rate >= 50) return 'warning'
      return 'exception'
    },

    // 获取使用率状态
    getUtilizationStatus(rate) {
      if (rate >= 90) return 'exception'
      if (rate >= 70) return 'warning'
      return 'success'
    },

    // 百分比格式化
    percentFormat(percentage) {
      return `${percentage}%`
    },

    // 获取车辆类型名称
    getVehicleTypeName(type) {
      const typeMap = {
        'car': '小汽车',
        'suv': 'SUV',
        'truck': '卡车',
        'motorcycle': '摩托车',
        'bicycle': '自行车',
        'electric': '电动车'
      }
      return typeMap[type] || type
    },

    // 获取车辆类型占比
    getVehicleTypePercentage(row) {
      const totalVehicles = this.stats.vehicle_types.reduce((sum, item) => sum + item.count, 0)
      if (totalVehicles === 0) return 0
      return Math.round((row.count / totalVehicles) * 100)
    },

    // 获取随机颜色（但对同一类型保持一致）
    getRandomColor(type) {
      if (!this.colorMap[type]) {
        const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#9B59B6', '#3498DB', '#2ECC71']
        this.colorMap[type] = colors[Object.keys(this.colorMap).length % colors.length]
      }
      return this.colorMap[type]
    },

    // 查看停车场详情
    viewParkingLot(id) {
      this.$router.push(`/parking/details/${id}`)
    },

    // 导出数据
    exportData() {
      this.$message({
        message: '数据导出功能开发中',
        type: 'info'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    font-size: 20px;
  }
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.stat-cards {
  margin-bottom: 20px;
}

.stat-card {
  margin-bottom: 15px;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .card-content {
    display: flex;
    align-items: center;

    .card-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        color: white;
        font-size: 20px;
      }
    }

    .card-data {
      flex: 1;

      .card-value {
        font-size: 24px;
        font-weight: bold;
        line-height: 1.2;
      }

      .card-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.data-card {
  margin-bottom: 20px;
}

.parking-lot-card {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  transition: all 0.3s;

  &:hover {
    background-color: #eef1f6;
  }

  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: #303133;
  }

  .parking-lot-info {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;

    .info-item {
      margin-right: 20px;
      margin-bottom: 10px;

      .label {
        color: #909399;
        margin-right: 5px;
      }

      .value {
        font-weight: bold;
        color: #303133;
      }
    }
  }

  .action-buttons {
    margin-top: 15px;
    text-align: right;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .stat-card {
    .card-content {
      .card-data {
        .card-value {
          font-size: 20px;
        }
      }
    }
  }

  .parking-lot-card {
    .parking-lot-info {
      flex-direction: column;

      .info-item {
        margin-right: 0;
      }
    }
  }
}
</style>
