2025/04/08 14:05:21 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/08 14:05:21 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 14:05:21 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 14:07:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:07:59] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 14:07:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:07:59] "POST /api/login HTTP/1.1" 200 -
2025/04/08 14:08:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:00] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:00] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:03] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:03] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:08:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:03] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:08:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:05] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:05] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:06] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:06] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:08:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:06] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:08:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:08] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:08] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:08:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:10] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:10] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:11] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:08:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:11] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:11] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:08:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:11] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:08:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:13] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:15] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:15] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:08:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:15] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:15] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:08:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:17] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:17] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:08:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:18] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:08:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:18] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:08:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:36] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 14:08:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:36] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 14:08:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:40] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 14:08:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:41] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 14:08:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:41] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 14:08:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:41] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 14:08:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:41] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 14:08:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:41] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 14:08:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:41] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 14:08:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:57] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 14:08:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:08:57] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 14:11:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:11:30] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:11:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:11:30] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:11:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:11:30] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:11:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:11:30] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:11:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:11:53] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:11:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:11:53] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:11:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:11:54] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:11:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:11:54] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:12:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:12:55] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:12:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:12:55] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:19:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:19:44] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:19:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:19:44] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:19:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:19:45] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:19:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:19:45] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:19:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:19:45] "OPTIONS /api/bikes/bike HTTP/1.1" 200 -
2025/04/08 14:19:45 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/bikes/bike, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDA5MjQ3OSwianRpIjoiNTk4MTVkYjUtYzAxMy00Y2NhLTg2NWQtZTk4NTMwOWYxMmZkIiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQwOTI0NzksImNzcmYiOiI3NTRmZWEyNS01NjQ5LTQzYzctOTVhZi02NTQ2OTllNDhjM2UiLCJleHAiOjE3NDQxNzg4Nzl9.GKaULzVEwRKXDJr_AKxfVq5IYfzonH3KCaDyN_w8fBk

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 14:19:45 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 14:19:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:19:45] "[33mGET /api/bikes/bike HTTP/1.1[0m" 404 -
2025/04/08 14:22:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:13] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/08 14:22:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:13] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/08 14:22:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:13] "OPTIONS /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/04/08 14:22:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:13] "GET /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/04/08 14:22:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:24] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 14:22:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:24] "POST /api/login HTTP/1.1" 200 -
2025/04/08 14:22:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:25] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:22:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:25] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:22:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:28] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/08 14:22:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:28] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/08 14:22:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:28] "OPTIONS /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/04/08 14:22:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:28] "GET /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/04/08 14:22:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:33] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:22:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:33] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:22:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:33] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:22:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:33] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:22:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:37] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:22:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:37] "OPTIONS /api/bikes/profile HTTP/1.1" 200 -
2025/04/08 14:22:37 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/bikes/profile, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDA5MzM0NCwianRpIjoiNjYwYWJlYzEtNmQ3OC00YzNkLWEwMjEtMzc1NWQ4OTQzYzM1IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQwOTMzNDQsImNzcmYiOiI5ZWQxMmI0ZC1iYzliLTQwZjgtODk4ZS1jMDBmNjk4ZDJiNWEiLCJleHAiOjE3NDQxNzk3NDR9.2OM1ehOI_ADIWkj6RxUCMrlxfp-wujAM3d0MwUXqdE8

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9529

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9529/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 14:22:37 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 14:22:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:37] "[33mGET /api/bikes/profile HTTP/1.1[0m" 404 -
2025/04/08 14:22:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:39] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:22:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:39] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:22:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:44] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/08 14:22:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:22:44] "POST /api/logout HTTP/1.1" 200 -
2025/04/08 14:22:55 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/08 14:22:55 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 14:22:55 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 14:23:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:23:08] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 14:23:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:23:09] "POST /api/login HTTP/1.1" 200 -
2025/04/08 14:23:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:23:09] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:23:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:23:09] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:23:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:23:14] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:23:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:23:14] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:23:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:23:15] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:23:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:23:15] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:23:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:23:16] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:23:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:23:17] "OPTIONS /api/bikes/profile HTTP/1.1" 200 -
2025/04/08 14:23:17 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/bikes/profile, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDA5MzM4OSwianRpIjoiYjIxMGQ5ZmItYTgxNy00YWMzLThhMWEtNzMzNDUwMjJiNWU1IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQwOTMzODksImNzcmYiOiJjOGU1ZTgyOS01NzM1LTRhODctODkyZC04YjMyNGQ1ZTEwZjAiLCJleHAiOjE3NDQxNzk3ODl9.D3IMb7BRUHqa4cZkUOb5s_MLJ-LY0kq5Tw9f-WZ0euY

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9529

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9529/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 14:23:17 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 14:23:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:23:17] "[33mGET /api/bikes/profile HTTP/1.1[0m" 404 -
2025/04/08 14:25:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:21] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:25:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:21] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:25:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:21] "OPTIONS /api/bikes/profile HTTP/1.1" 200 -
2025/04/08 14:25:21 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/bikes/profile, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDA5MzM4OSwianRpIjoiYjIxMGQ5ZmItYTgxNy00YWMzLThhMWEtNzMzNDUwMjJiNWU1IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQwOTMzODksImNzcmYiOiJjOGU1ZTgyOS01NzM1LTRhODctODkyZC04YjMyNGQ1ZTEwZjAiLCJleHAiOjE3NDQxNzk3ODl9.D3IMb7BRUHqa4cZkUOb5s_MLJ-LY0kq5Tw9f-WZ0euY

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9529

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9529/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 14:25:21 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 14:25:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:21] "[33mGET /api/bikes/profile HTTP/1.1[0m" 404 -
2025/04/08 14:25:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:22] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:25:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:22] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:25:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:22] "OPTIONS /api/bikes/bike HTTP/1.1" 200 -
2025/04/08 14:25:22 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/bikes/bike, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDA5MzM4OSwianRpIjoiYjIxMGQ5ZmItYTgxNy00YWMzLThhMWEtNzMzNDUwMjJiNWU1IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQwOTMzODksImNzcmYiOiJjOGU1ZTgyOS01NzM1LTRhODctODkyZC04YjMyNGQ1ZTEwZjAiLCJleHAiOjE3NDQxNzk3ODl9.D3IMb7BRUHqa4cZkUOb5s_MLJ-LY0kq5Tw9f-WZ0euY

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 14:25:22 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 14:25:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:22] "[33mGET /api/bikes/bike HTTP/1.1[0m" 404 -
2025/04/08 14:25:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:36] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:25:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:25:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:37] "OPTIONS /api/bikes/profile HTTP/1.1" 200 -
2025/04/08 14:25:37 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/bikes/profile, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDA5MzM4OSwianRpIjoiYjIxMGQ5ZmItYTgxNy00YWMzLThhMWEtNzMzNDUwMjJiNWU1IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQwOTMzODksImNzcmYiOiJjOGU1ZTgyOS01NzM1LTRhODctODkyZC04YjMyNGQ1ZTEwZjAiLCJleHAiOjE3NDQxNzk3ODl9.D3IMb7BRUHqa4cZkUOb5s_MLJ-LY0kq5Tw9f-WZ0euY

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9529

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9529/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 14:25:37 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 14:25:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:37] "[33mGET /api/bikes/profile HTTP/1.1[0m" 404 -
2025/04/08 14:25:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:38] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:25:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:38] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:25:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:38] "OPTIONS /api/bikes/bike HTTP/1.1" 200 -
2025/04/08 14:25:38 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/bikes/bike, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDA5MzM4OSwianRpIjoiYjIxMGQ5ZmItYTgxNy00YWMzLThhMWEtNzMzNDUwMjJiNWU1IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQwOTMzODksImNzcmYiOiJjOGU1ZTgyOS01NzM1LTRhODctODkyZC04YjMyNGQ1ZTEwZjAiLCJleHAiOjE3NDQxNzk3ODl9.D3IMb7BRUHqa4cZkUOb5s_MLJ-LY0kq5Tw9f-WZ0euY

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 14:25:38 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 14:25:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:38] "[33mGET /api/bikes/bike HTTP/1.1[0m" 404 -
2025/04/08 14:25:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:49] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:25:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:49] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:25:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:49] "OPTIONS /api/bikes/profile HTTP/1.1" 200 -
2025/04/08 14:25:49 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/bikes/profile, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDA5MzM4OSwianRpIjoiYjIxMGQ5ZmItYTgxNy00YWMzLThhMWEtNzMzNDUwMjJiNWU1IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQwOTMzODksImNzcmYiOiJjOGU1ZTgyOS01NzM1LTRhODctODkyZC04YjMyNGQ1ZTEwZjAiLCJleHAiOjE3NDQxNzk3ODl9.D3IMb7BRUHqa4cZkUOb5s_MLJ-LY0kq5Tw9f-WZ0euY

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9529

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9529/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 14:25:49 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 14:25:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:49] "[33mGET /api/bikes/profile HTTP/1.1[0m" 404 -
2025/04/08 14:25:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:50] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:25:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:50] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:25:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:51] "OPTIONS /api/bikes/bike HTTP/1.1" 200 -
2025/04/08 14:25:51 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/bikes/bike, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDA5MzM4OSwianRpIjoiYjIxMGQ5ZmItYTgxNy00YWMzLThhMWEtNzMzNDUwMjJiNWU1IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQwOTMzODksImNzcmYiOiJjOGU1ZTgyOS01NzM1LTRhODctODkyZC04YjMyNGQ1ZTEwZjAiLCJleHAiOjE3NDQxNzk3ODl9.D3IMb7BRUHqa4cZkUOb5s_MLJ-LY0kq5Tw9f-WZ0euY

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 14:25:51 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 14:25:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:25:51] "[33mGET /api/bikes/bike HTTP/1.1[0m" 404 -
2025/04/08 14:26:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:04] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/08 14:26:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:04] "POST /api/logout HTTP/1.1" 200 -
2025/04/08 14:26:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:08] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 14:26:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:08] "POST /api/login HTTP/1.1" 200 -
2025/04/08 14:26:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:08] "OPTIONS /api/bikes/profile HTTP/1.1" 200 -
2025/04/08 14:26:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:08] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:26:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:08] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:26:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:12] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:26:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:13] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:26:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:14] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:26:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:14] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:26:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:16] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:26:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:16] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:26:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:16] "OPTIONS /api/bikes/profile HTTP/1.1" 200 -
2025/04/08 14:26:16 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/bikes/profile, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDA5MzU2OCwianRpIjoiYTExNmViODMtYmU4MS00YTk3LWE3ZmYtZWJmYjhmY2YxZDViIiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQwOTM1NjgsImNzcmYiOiJmNTUxZTc3Yi0wZGZhLTRlMzMtODY2ZC1mNDVjYzZkZTg0ZjgiLCJleHAiOjE3NDQxNzk5Njh9.BwMmtT299gHlxQXZHNsjrIt_rSibwIcKXhime47McCU

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9529

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9529/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 14:26:16 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 14:26:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:16] "[33mGET /api/bikes/profile HTTP/1.1[0m" 404 -
2025/04/08 14:26:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:38] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:26:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:38] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:26:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:38] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:26:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:38] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:26:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:39] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:26:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:39] "OPTIONS /api/bikes/profile HTTP/1.1" 200 -
2025/04/08 14:26:39 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/bikes/profile, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDA5MzU2OCwianRpIjoiYTExNmViODMtYmU4MS00YTk3LWE3ZmYtZWJmYjhmY2YxZDViIiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQwOTM1NjgsImNzcmYiOiJmNTUxZTc3Yi0wZGZhLTRlMzMtODY2ZC1mNDVjYzZkZTg0ZjgiLCJleHAiOjE3NDQxNzk5Njh9.BwMmtT299gHlxQXZHNsjrIt_rSibwIcKXhime47McCU

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9529

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9529/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 14:26:39 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 14:26:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:39] "[33mGET /api/bikes/profile HTTP/1.1[0m" 404 -
2025/04/08 14:26:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:51] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:26:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:51] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:26:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:51] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:26:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:26:51] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:27:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:27:50] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:27:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:27:50] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/08 14:27:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:27:53] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:27:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:27:53] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:27:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:27:56] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/08 14:27:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:27:56] "POST /api/logout HTTP/1.1" 200 -
2025/04/08 14:28:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:28:01] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 14:28:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:28:01] "POST /api/login HTTP/1.1" 200 -
2025/04/08 14:28:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:28:02] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:28:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:28:02] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:28:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:28:04] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/08 14:28:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:28:04] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/08 14:28:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:28:04] "OPTIONS /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/04/08 14:28:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:28:04] "GET /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/04/08 14:29:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:29:08] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:29:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:29:08] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:33:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:33:03] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:33:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:33:03] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:33:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:33:08] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/08 14:33:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:33:08] "POST /api/logout HTTP/1.1" 200 -
2025/04/08 14:33:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:33:13] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 14:33:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:33:13] "POST /api/login HTTP/1.1" 200 -
2025/04/08 14:33:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:33:14] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:33:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:33:14] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:36:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:36:00] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 14:36:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:36:00] "POST /api/login HTTP/1.1" 200 -
2025/04/08 14:36:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:36:01] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:36:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:36:01] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:37:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:37:26] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 14:37:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:37:26] "POST /api/login HTTP/1.1" 200 -
2025/04/08 14:37:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:37:27] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:37:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:37:27] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:37:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:37:35] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:37:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:37:35] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:38:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:38:29] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:38:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:38:29] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:41:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:41:09] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:41:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:41:09] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:42:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:42:35] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:42:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:42:35] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:42:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:42:57] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:42:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:42:57] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:42:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:42:59] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:42:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:42:59] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:43:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:43:34] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:43:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:43:34] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:44:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:44:01] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:44:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:44:01] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:44:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:44:13] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:44:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:44:13] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:44:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:44:21] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:44:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:44:21] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:44:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:44:28] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 14:44:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:44:28] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 14:44:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:44:38] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:44:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:44:38] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:44:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:44:53] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 14:44:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:44:53] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 14:45:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:45:41] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 14:45:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:45:41] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 14:46:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:46:27] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:46:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:46:27] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:46:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:46:40] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 14:46:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:46:40] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 14:48:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:48:08] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:48:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:48:08] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:48:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:48:26] "OPTIONS /bikes HTTP/1.1" 200 -
2025/04/08 14:48:26 flask_api __init__.py[234] handle_exception() ERROR: 未处理的异常: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025/04/08 14:48:26 flask_api __init__.py[236] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.

2025/04/08 14:48:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:48:26] "[35m[1mPOST /bikes HTTP/1.1[0m" 500 -
2025/04/08 14:50:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:50:02] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:50:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:50:02] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:50:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:50:12] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 14:50:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:50:12] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 14:51:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:51:06] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:51:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:51:06] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 14:51:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:51:22] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 14:51:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 14:51:22] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 16:55:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:55:25] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 16:55:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:55:25] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 16:58:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:58:12] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 16:58:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:58:12] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 16:58:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:58:19] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 16:58:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:58:19] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 16:58:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:58:54] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 16:58:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:58:54] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 16:59:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:59:52] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 16:59:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:59:52] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 16:59:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:59:58] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 16:59:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:59:58] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 16:59:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:59:58] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 16:59:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 16:59:59] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:00:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:00:02] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:00:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:00:03] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:00:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:00:15] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 17:00:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:00:15] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 17:01:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:01:52] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 17:01:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:01:52] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 17:02:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:02:10] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:02:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:02:10] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:02:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:02:10] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:02:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:02:13] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:02:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:02:13] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:02:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:02:13] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:02:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:02:13] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:02:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:02:17] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:02:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:02:17] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:03:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:03:53] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:03:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:03:53] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:03:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:03:53] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:03:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:03:53] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:04:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:04:03] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 17:04:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:04:03] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 17:04:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:04:03] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:04:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:04:03] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:04:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:04:11] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:04:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:04:11] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:04:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:04:19] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:04:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:04:19] "[31m[1mPUT /api/users/9 HTTP/1.1[0m" 403 -
2025/04/08 17:05:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:05:22] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:05:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:05:22] "[31m[1mPUT /api/users/9 HTTP/1.1[0m" 403 -
2025/04/08 17:06:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:06:50] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:06:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:06:50] "[31m[1mPUT /api/users/9 HTTP/1.1[0m" 403 -
2025/04/08 17:06:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:06:53] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:06:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:06:53] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:06:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:06:53] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:06:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:06:59] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:06:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:06:59] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:07:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:07:08] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:07:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:07:08] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:07:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:07:09] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:07:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:07:19] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:07:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:07:19] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:07:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:07:25] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:07:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:07:25] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:10:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:10:11] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:10:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:10:11] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:10:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:10:12] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:10:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:10:12] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:10:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:10:26] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:10:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:10:26] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:10:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:10:36] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:10:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:10:36] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:10:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:10:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:14:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:14:01] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:14:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:14:01] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:14:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:14:01] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:14:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:14:01] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:14:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:14:02] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:14:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:14:05] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:14:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:14:05] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:14:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:14:05] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:14:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:14:05] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:14:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:14:11] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:14:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:14:11] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:17:15 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/08 17:17:23 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/08 17:18:00 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/08 17:18:00 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:18:00 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:18:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:18:04] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:18:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:18:04] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:18:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:18:04] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:18:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:18:04] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:18:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:18:06] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:18:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:18:10] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:18:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:18:10] "[31m[1mPUT /api/users/me HTTP/1.1[0m" 400 -
2025/04/08 17:18:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:18:18] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:18:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:18:18] "[31m[1mPUT /api/users/me HTTP/1.1[0m" 400 -
2025/04/08 17:19:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:19:27] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:19:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:19:27] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:19:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:19:27] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:20:15 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/08 17:20:16 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:20:16 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:20:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:20:20] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:20:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:20:20] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:20:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:20:20] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:20:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:20:23] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:20:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:20:23] "[31m[1mPUT /api/users/me HTTP/1.1[0m" 400 -
2025/04/08 17:21:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:20] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:21:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:20] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:21:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:21] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:21:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:33] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:21:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:33] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:21:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:33] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:21:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:33] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:21:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:21:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:37] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:21:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:39] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:21:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:39] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:21:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:39] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:21:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:21:39] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:26 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/08 17:22:26 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:22:26 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:22:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:29] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:29] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:30] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:32] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/08 17:22:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:32] "POST /api/logout HTTP/1.1" 200 -
2025/04/08 17:22:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:37] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:22:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:37] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:22:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:37] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:38] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:40] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:44] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:44] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:44] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:44] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:58] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:58] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:59] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:22:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:22:59] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:16] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:16] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:16] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:30] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:30] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:30] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:33] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:33] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:33] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:33] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:36] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/08 17:24:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:36] "POST /api/logout HTTP/1.1" 200 -
2025/04/08 17:24:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:40] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:24:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:40] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:24:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:40] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:41] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:43] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:45] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:45] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:45] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:24:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:24:45] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:25:03 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/08 17:25:03 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:25:03 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:25:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:25:09] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:25:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:25:09] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:25:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:25:09] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:25:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:25:09] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:27:23] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:27:23] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:27:27] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:27:27] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:27:27] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:27:27] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:27:31] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:27:31] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:27:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:27:34] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:27:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:27:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:27:56 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/08 17:27:56 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:27:56 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:28:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:28:24] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:28:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:28:24] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:28:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:28:26] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:28:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:28:26] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:28:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:28:26] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:28:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:28:26] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:28:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:28:32] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:28:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:28:32] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:30:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:30:15] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:30:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:30:15] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:30:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:30:15] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:30:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:30:51] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:30:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:30:51] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:30:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:30:51] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:30:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:30:55] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:30:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:30:55] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:30:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:30:55] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:30:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:30:55] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:31:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:31:05] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:31:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:31:05] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:32:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:32:48] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:32:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:32:48] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:32:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:32:49] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:33:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:17] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:33:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:17] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:33:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:21] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:33:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:21] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:33:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:31] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/08 17:33:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:31] "POST /api/logout HTTP/1.1" 200 -
2025/04/08 17:33:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:35] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:33:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:35] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:33:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:35] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:33:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:36] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:33:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:33:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:39] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:33:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:48] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:33:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:48] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:33:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:53] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:33:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:53] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:33:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:53] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:33:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:53] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:33:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:56] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:33:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:33:57] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:34:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:34:02] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:34:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:34:02] "[31m[1mPUT /api/users/me HTTP/1.1[0m" 400 -
2025/04/08 17:34:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:34:04] "[31m[1mPUT /api/users/me HTTP/1.1[0m" 400 -
2025/04/08 17:36:24 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/08 17:36:24 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:36:24 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:36:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:36:27] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/08 17:36:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:36:27] "POST /api/logout HTTP/1.1" 200 -
2025/04/08 17:36:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:36:33] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:36:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:36:33] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:36:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:36:33] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:36:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:36:35] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:36:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:36:37] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:36:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:36:44] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:36:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:36:44] "[31m[1mPUT /api/users/me HTTP/1.1[0m" 400 -
2025/04/08 17:37:25 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/08 17:37:25 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:37:25 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:37:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:31] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:37:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:31] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:37:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:31] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:37:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:35] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:37:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:35] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:37:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:35] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:37:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:35] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:37:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:39] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:37:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:39] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:37:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:39] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:37:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:39] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:37:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:43] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:37:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:43] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:37:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:47] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:37:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:47] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:37:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:47] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:37:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:37:47] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:39:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:39:21] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:39:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:39:21] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:39:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:39:22] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:42:15 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:42:15 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:42:15 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:42:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:42:41] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:42:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:42:41] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:42:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:42:43] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:42:43 flask_api auth_helpers.py[80] get_current_user() ERROR: 获取当前用户时出错: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_name = ?
 LIMIT ? OFFSET ?]
[parameters: (9, 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/08 17:42:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:42:43] "[31m[1mGET /api/users/me HTTP/1.1[0m" 401 -
2025/04/08 17:42:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:42:51] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:42:51 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/08 17:42:51 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.avatar

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 117, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/04/08 17:42:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:42:51] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/04/08 17:43:46 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:43:46 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:43:46 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:43:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:43:48] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:43:48 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/08 17:43:48 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.avatar

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 117, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/04/08 17:43:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:43:48] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/04/08 17:43:49 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/08 17:43:49 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.avatar

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 117, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/04/08 17:43:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:43:49] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/04/08 17:43:50 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/08 17:43:50 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.avatar

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 117, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/04/08 17:43:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:43:50] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/04/08 17:43:51 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/08 17:43:51 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.avatar

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 117, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/04/08 17:43:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:43:51] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/04/08 17:44:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:44:41] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:44:41 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/08 17:44:41 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.avatar

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 117, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.avatar
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/04/08 17:44:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:44:41] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/04/08 17:45:46 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:45:54 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:46:01 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:46:01 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:46:01 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:46:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:10] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:46:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:10] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:46:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:11] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:11] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:12] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:25] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:25] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:25] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:25] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:36] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:46:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:36] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:46:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:36] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:46:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:36] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:46:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:39] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:39] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:45] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:45] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:45] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:45] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:46:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:47] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:46:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:47] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:46:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:47] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:46:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:46:47] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:47:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:47:48] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:47:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:47:48] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:48:10 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:49:25 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:49:25 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:49:25 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:49:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:30] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:49:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:30] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:49:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:35] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:49:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:35] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:49:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:36] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:49:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:49:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:37] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:49:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:42] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:49:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:42] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 17:49:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:42] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:49:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:42] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:49:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:46] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:49:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:46] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 17:49:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:46] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:49:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:46] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 17:49:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:50] "OPTIONS /vue-admin-template/table/list HTTP/1.1" 200 -
2025/04/08 17:49:50 flask_api __init__.py[227] not_found() ERROR: 路由未找到: /vue-admin-template/table/list, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDEwNTc3NSwianRpIjoiYWNlMTVjN2YtOWFmZC00OWRhLWFmOTEtNWU4OWU1NmE2N2I5IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQxMDU3NzUsImNzcmYiOiIxMjkxYTNiNC0wNTFlLTRiMDQtODVlNC1jY2U5MjJlYTRmNDgiLCJleHAiOjE3NDQxOTIxNzV9.dAD2oFg31UOM4XhNshc7KiuxEctBlY38YxBNqtAOyyY

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 17:49:50 root __init__.py[228] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 17:49:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:50] "[33mGET /vue-admin-template/table/list HTTP/1.1[0m" 404 -
2025/04/08 17:49:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:54] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:49:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:49:54] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:50:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:50:08] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 17:50:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:50:08] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 17:50:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:50:08] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:50:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:50:08] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 17:50:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:50:30] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:50:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:50:30] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:52:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:52:43] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/08 17:53:07 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:53:07 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:53:07 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:53:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:53:12] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:53:12 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such column: users.version
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/08 17:53:12 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: users.version

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 117, in login
    user = Users.query.get(player.user_id)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: users.version
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?]
[parameters: (9,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/04/08 17:53:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:53:12] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/04/08 17:54:02 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:54:09 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:54:18 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:54:18 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:54:18 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:54:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:20] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:54:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:21] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:54:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:21] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:54:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:21] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 17:54:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:21] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:54:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:21] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 17:54:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:21] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/08 17:54:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:28] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:54:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:28] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:54:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:28] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/08 17:54:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:55] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:54:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:56] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:54:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:54:56] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/08 17:55:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:55:20] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:55:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:55:20] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:55:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:55:20] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/08 17:56:01 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:58:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:58:17] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:58:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:58:17] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:58:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:58:17] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/08 17:59:16 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 17:59:16 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 17:59:16 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 17:59:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:59:20] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:59:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:59:20] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:59:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:59:20] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/08 17:59:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:59:50] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 17:59:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:59:50] "POST /api/login HTTP/1.1" 200 -
2025/04/08 17:59:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 17:59:51] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/08 18:00:47 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 18:00:48 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 18:00:48 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 18:01:44 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 18:01:44 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 18:01:44 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 18:01:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:01:47] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 18:01:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:01:47] "POST /api/login HTTP/1.1" 200 -
2025/04/08 18:01:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:01:49] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/08 18:01:49 flask_api __init__.py[227] not_found() ERROR: 路由未找到: /users/me, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDEwNjUwNywianRpIjoiMGRlMzdlYzItY2ZmYy00ZTFjLWI4NGYtOWZiOTMwYmRmMDBiIiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQxMDY1MDcsImNzcmYiOiI2NzAxZDJhOC05YzFjLTQzYzUtODVjNS00NzMwZjNjOTU1ZmQiLCJleHAiOjE3NDQxOTI5MDd9.VgVyCMpoUmiADixFGwedKMyacOyurSYmxjEXZgx7Lqg

Cache-Control: no-cache

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 18:01:49 root __init__.py[228] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 18:01:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:01:49] "[33mGET /users/me HTTP/1.1[0m" 404 -
2025/04/08 18:02:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:02:08] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 18:02:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:02:08] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/08 18:02:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:02:15] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 18:02:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:02:15] "POST /api/login HTTP/1.1" 200 -
2025/04/08 18:02:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:02:17] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/08 18:02:17 flask_api __init__.py[227] not_found() ERROR: 路由未找到: /users/me, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDEwNjUzNSwianRpIjoiMjk2MWQ0N2MtNTQyZi00YzFkLWI1ZjctZmYyOTdjMzMyZTZiIiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQxMDY1MzUsImNzcmYiOiIxYWY2M2I0NC1iMzNjLTQ1NGYtODdjYi01ZmM0NjYxNTgyNWQiLCJleHAiOjE3NDQxOTI5MzV9.nqpDCtjv3_uvbH8erXTbH4ZJiEQ28NjXtw1kRPa5I2g

Cache-Control: no-cache

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/08 18:02:17 root __init__.py[228] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/08 18:02:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:02:17] "[33mGET /users/me HTTP/1.1[0m" 404 -
2025/04/08 18:03:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:40] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 18:03:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:40] "POST /api/login HTTP/1.1" 200 -
2025/04/08 18:03:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:41] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 18:03:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:41] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:03:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:44] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:03:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:44] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:03:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:51] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 18:03:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:51] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 18:03:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:51] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:03:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:51] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:03:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:54] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 18:03:54 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: 'Response' object has no attribute 'u_id'
2025/04/08 18:03:54 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_jwt_extended\view_decorators.py", line 170, in decorator
    return current_app.ensure_sync(fn)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 223, in get_user
    if current_user.u_id != user_id and not is_admin():
       ^^^^^^^^^^^^^^^^^
AttributeError: 'Response' object has no attribute 'u_id'

2025/04/08 18:03:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:54] "[35m[1mGET /api/users/9 HTTP/1.1[0m" 500 -
2025/04/08 18:03:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:55] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 18:03:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:03:55] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 18:04:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:04:02] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 18:04:02 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: 'Response' object has no attribute 'u_id'
2025/04/08 18:04:02 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_jwt_extended\view_decorators.py", line 170, in decorator
    return current_app.ensure_sync(fn)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 223, in get_user
    if current_user.u_id != user_id and not is_admin():
       ^^^^^^^^^^^^^^^^^
AttributeError: 'Response' object has no attribute 'u_id'

2025/04/08 18:04:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:04:02] "[35m[1mGET /api/users/9 HTTP/1.1[0m" 500 -
2025/04/08 18:04:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:04:02] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 18:04:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:04:02] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 18:05:05 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 18:05:05 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 18:05:05 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 18:05:24 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 18:05:24 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 18:05:24 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 18:05:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:28] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/08 18:05:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:28] "POST /api/logout HTTP/1.1" 200 -
2025/04/08 18:05:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:31] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/08 18:05:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:31] "POST /api/login HTTP/1.1" 200 -
2025/04/08 18:05:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:32] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 18:05:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:32] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:05:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:34] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 18:05:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:34] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 18:05:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:34] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 18:05:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:34] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 18:05:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:37] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 18:05:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:37] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 18:05:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:37] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:05:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:37] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:44] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 18:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:44] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/08 18:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:44] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:44] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:05:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:48] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 18:05:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:48] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 18:05:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:48] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 18:05:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:48] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 18:05:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:52] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:05:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:05:52] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:08:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:08:30] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:08:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:08:30] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:08:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:08:34] "OPTIONS /api/bikes/1 HTTP/1.1" 200 -
2025/04/08 18:08:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:08:34] "PUT /api/bikes/1 HTTP/1.1" 200 -
2025/04/08 18:08:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:08:35] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:08:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:08:37] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:08:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:08:37] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:08:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:08:40] "OPTIONS /api/bikes/2 HTTP/1.1" 200 -
2025/04/08 18:08:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:08:40] "PUT /api/bikes/2 HTTP/1.1" 200 -
2025/04/08 18:08:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:08:40] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:08:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:08:54] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 18:08:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:08:54] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 18:09:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:09:00] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 18:09:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:09:00] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 18:09:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:09:01] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:09:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:09:01] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:09:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:09:05] "OPTIONS /api/bikes/3 HTTP/1.1" 200 -
2025/04/08 18:09:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:09:05] "PUT /api/bikes/3 HTTP/1.1" 200 -
2025/04/08 18:09:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:09:06] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:12:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:12:50] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:12:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:12:50] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:12:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:12:56] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 18:12:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:12:56] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:12:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:12:56] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:12:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:12:56] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:13:05 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 18:13:05 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 18:13:05 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 18:13:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:13:39] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 18:13:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:13:39] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 18:13:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:13:39] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:13:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:13:39] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:13:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:13:42] "OPTIONS /api/bikes/4 HTTP/1.1" 200 -
2025/04/08 18:13:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:13:42] "PUT /api/bikes/4 HTTP/1.1" 200 -
2025/04/08 18:13:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:13:42] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:14:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:14:41] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 18:14:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:14:41] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 18:14:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:14:49] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 18:14:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:14:49] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 18:14:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:14:49] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:14:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:14:49] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:17:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:17:15] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:17:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:17:15] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:17:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:17:18] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:17:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:17:19] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:17:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:17:19] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:17:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:17:20] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:17:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:17:59] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 18:17:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:17:59] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:18:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:00] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:18:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:00] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:18:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:09] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 18:18:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:09] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 18:18:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:09] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:18:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:09] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:18:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:19] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 18:18:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:19] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 18:18:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:19] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:18:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:19] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:18:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:23] "OPTIONS /api/bikes/2 HTTP/1.1" 200 -
2025/04/08 18:18:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:23] "PUT /api/bikes/2 HTTP/1.1" 200 -
2025/04/08 18:18:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:18:23] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:11] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:11] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:26] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 18:20:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:26] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 18:20:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:26] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:26] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:31] "OPTIONS /api/bikes/3 HTTP/1.1" 200 -
2025/04/08 18:20:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:31] "PUT /api/bikes/3 HTTP/1.1" 200 -
2025/04/08 18:20:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:31] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:31] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:38] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:38] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:38] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:39] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:41] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:41] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:20:43] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:21 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 18:27:21 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 18:27:21 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 18:27:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:22] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:22] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:26] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 18:27:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:27] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:27:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:27] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:29] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:27:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:29] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:27:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:32] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:32] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:40] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 18:27:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:40] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 18:27:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:40] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:40] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:43] "OPTIONS /api/bikes/4 HTTP/1.1" 200 -
2025/04/08 18:27:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:43] "PUT /api/bikes/4 HTTP/1.1" 200 -
2025/04/08 18:27:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:43] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:48] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:48] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:48] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:49] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:49] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:49] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:49] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:49] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:50] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:27:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:27:50] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 18:28:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:28:06] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 18:28:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:28:06] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 18:28:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:28:06] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:28:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:28:06] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 18:28:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:28:08] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 18:28:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:28:08] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 18:28:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:28:08] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 18:28:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 18:28:08] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 19:45:13 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 19:45:13 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 19:45:13 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 19:45:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:25] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 19:45:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:25] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 19:45:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:26] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 19:45:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:26] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 19:45:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:26] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 19:45:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:26] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 19:45:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:30] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:45:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:30] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:45:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:43] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 19:45:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:43] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 19:45:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:49] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 19:45:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:50] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 19:45:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:50] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:45:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:50] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:45:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:55] "OPTIONS /api/bikes/5 HTTP/1.1" 200 -
2025/04/08 19:45:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:55] "PUT /api/bikes/5 HTTP/1.1" 200 -
2025/04/08 19:45:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:55] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:45:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:45:55] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:19] "OPTIONS /api/bikes/1 HTTP/1.1" 200 -
2025/04/08 19:46:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:19] "PUT /api/bikes/1 HTTP/1.1" 200 -
2025/04/08 19:46:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:19] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:19] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:21] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:22] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:22] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:23] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:23] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:24] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:25] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:25] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:27] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 19:46:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:27] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 19:46:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:27] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 19:46:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:27] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 19:46:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:30] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:30] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:34] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:46:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:46:35] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:53:18 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 19:53:18 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 19:53:18 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 19:53:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:53:22] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:53:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:53:22] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:53:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:53:25] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 19:53:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:53:25] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 19:53:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:53:25] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:53:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:53:35] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 19:53:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:53:35] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 19:54:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:54:57] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:54:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:54:57] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:55:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:55:06] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 19:55:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:55:06] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 19:55:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:55:13] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 19:55:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:55:13] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 19:55:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:55:13] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:55:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:55:13] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:55:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:55:28] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 19:55:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:55:28] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 19:56:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:56:46] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:56:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:56:46] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:56:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:56:52] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 19:56:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:56:52] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 19:56:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:56:56] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 19:56:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:56:56] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 19:56:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:56:56] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:56:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:56:56] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:57:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:57:02] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 19:57:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:57:02] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 19:57:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:57:08] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 19:57:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:57:08] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 19:57:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:57:14] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 19:57:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:57:14] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 19:57:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:57:20] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 19:57:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:57:20] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 19:57:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:57:23] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 19:59:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:59:07] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:59:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:59:07] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 19:59:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:59:21] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 19:59:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:59:21] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 19:59:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:59:33] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 19:59:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 19:59:33] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 20:01:14 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 20:01:14 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 20:01:14 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 20:02:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:02:26] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 20:02:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:02:26] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:02:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:02:29] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:02:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:02:29] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:02:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:02:33] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:02:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:02:33] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:02:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:02:35] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:03:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:03:10] "OPTIONS /api/bikes/6 HTTP/1.1" 200 -
2025/04/08 20:03:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:03:10] "PUT /api/bikes/6 HTTP/1.1" 200 -
2025/04/08 20:03:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:03:13] "[31m[1mPUT /api/bikes/6 HTTP/1.1[0m" 403 -
2025/04/08 20:03:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:03:13] "[31m[1mPUT /api/bikes/6 HTTP/1.1[0m" 403 -
2025/04/08 20:06:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:08] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:06:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:08] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:06:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:14] "OPTIONS /api/bikes/7 HTTP/1.1" 200 -
2025/04/08 20:06:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:14] "PUT /api/bikes/7 HTTP/1.1" 200 -
2025/04/08 20:06:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:19] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 20:06:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:19] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:06:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:19] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:06:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:19] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:06:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:24] "OPTIONS /api/bikes/8 HTTP/1.1" 200 -
2025/04/08 20:06:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:24] "PUT /api/bikes/8 HTTP/1.1" 200 -
2025/04/08 20:06:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:27] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 20:06:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:27] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:06:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:27] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:06:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:27] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:06:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:34] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:06:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:34] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:06:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:34] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:06:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:39] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 20:06:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:39] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 20:06:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:39] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:06:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:39] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:06:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:42] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 20:06:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:42] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 20:06:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:42] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 20:06:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:06:42] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 20:07:04 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 20:07:04 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 20:07:04 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 20:07:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:08] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/08 20:07:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:08] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 20:07:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:08] "OPTIONS /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 20:07:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:08] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 20:07:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:10] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 20:07:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:10] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:07:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:12] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/08 20:07:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:12] "GET /api/bikes?belong_to=9 HTTP/1.1" 200 -
2025/04/08 20:07:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:15] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:07:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:15] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:07:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:21] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 20:07:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:21] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 20:07:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:27] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 20:07:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:27] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 20:07:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:07:30] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 500 -
2025/04/08 20:37:30 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/08 20:37:30 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/08 20:37:30 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/08 20:38:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:38:55] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:38:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:38:55] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:39:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:00] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:39:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:06] "OPTIONS /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:39:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:06] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:39:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:07] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:39:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:07] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:39:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:07] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:39:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:08] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:39:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:15] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 20:39:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:15] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:39:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:15] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:39:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:15] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:39:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:20] "OPTIONS /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:39:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:20] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:39:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:29] "OPTIONS /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:39:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:29] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:39:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:33] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 20:39:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:33] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:39:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:33] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:39:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:33] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:39:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:59] "OPTIONS /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:39:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:39:59] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:42:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:34] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:42:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:34] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:42:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:38] "OPTIONS /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:42:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:39] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:42:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:44] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 20:42:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:44] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:42:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:46] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:42:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:46] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:42:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:51] "OPTIONS /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:42:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:51] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:42:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:58] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 20:42:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:58] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:42:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:58] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:42:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:42:58] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:44:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:44:03] "OPTIONS /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:44:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:44:03] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:46:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:46:50] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:46:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:46:50] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:46:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:46:55] "OPTIONS /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:46:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:46:55] "PUT /api/bikes/9 HTTP/1.1" 200 -
2025/04/08 20:46:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:46:55] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:46:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:46:55] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:46:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:46:59] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/08 20:46:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:46:59] "GET /api/users/me HTTP/1.1" 200 -
2025/04/08 20:46:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:46:59] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:47:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:47:08] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 20:47:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:47:08] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 20:47:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:47:13] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 20:47:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:47:13] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 20:47:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:47:19] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/08 20:47:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:47:19] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/08 20:47:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:47:20] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 400 -
2025/04/08 20:47:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:47:24] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:47:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:47:24] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:47:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:47:25] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/08 20:47:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Apr/2025 20:47:26] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
