from marshmallow import Schema, fields, validate, validate, validates, ValidationError
from app.violations.models import ViolationRecord, Appeal, Evidence, ViolationType
from datetime import datetime

# 违规类型Schema
class ViolationTypeSchema(Schema):
    """违规类型的Schema"""
    # 定义字段

    id = fields.Integer(dump_only=True)
    name = fields.String(required=True, validate=validate.Length(min=2, max=50))
    description = fields.String(allow_none=True)
    needs_admin = fields.Integer(validate=validate.OneOf([0, 1]), default=0)
    created_at = fields.String(dump_only=True)
    updated_at = fields.String(dump_only=True)

# 违规记录Schema
class ViolationRecordSchema(Schema):
    """ViolationRecord 的简单 Schema"""
    # 定义字段

    id = fields.Integer(dump_only=True)
    bike_number = fields.String(required=True, validate=validate.Length(min=2, max=50))
    bike_id = fields.Integer(allow_none=True)
    user_id = fields.Integer(required=False)
    violation_time = fields.String(required=True)
    location = fields.String(required=True, validate=validate.Length(min=2, max=100))
    violation_type = fields.String(required=True, validate=validate.Length(min=2, max=50))
    violation_type_id = fields.Integer(allow_none=True)
    description = fields.String(allow_none=True)
    status = fields.Integer(validate=validate.OneOf([0, 1, 2, 3]), default=0)
    result = fields.String(allow_none=True)
    fine_amount = fields.Float(allow_none=True, default=0)
    recorder_id = fields.Integer(required=False)
    handler_id = fields.Integer(allow_none=True)
    created_at = fields.String(dump_only=True)
    updated_at = fields.String(dump_only=True)

    # 额外字段
    status_text = fields.String(dump_only=True)
    user_name = fields.String(dump_only=True, required=False)
    recorder_name = fields.String(dump_only=True, required=False)
    handler_name = fields.String(dump_only=True, required=False)
    type_needs_admin = fields.Integer(dump_only=True)
    type_description = fields.String(dump_only=True)

    # 移除关联字段

    # 移除 post_load 方法

# 申诉记录Schema
class AppealSchema(Schema):
    """Appeal 的简单 Schema"""
    # 定义字段

    id = fields.Integer(dump_only=True)
    violation_id = fields.Integer(required=True)
    user_id = fields.Integer(required=True)
    reason = fields.String(required=True)
    status = fields.Integer(validate=validate.OneOf([0, 1, 2]), default=0)
    comment = fields.String(allow_none=True)
    handler_id = fields.Integer(allow_none=True)
    created_at = fields.String(dump_only=True)
    updated_at = fields.String(dump_only=True)

    # 额外字段
    status_text = fields.String(dump_only=True)
    user_name = fields.String(dump_only=True)
    handler_name = fields.String(dump_only=True)

    # 移除 post_load 方法

# 证据Schema
class EvidenceSchema(Schema):
    """Evidence 的简单 Schema"""
    # 定义字段

    id = fields.Integer(dump_only=True)
    related_id = fields.Integer(required=True)
    related_type = fields.String(required=True, validate=validate.OneOf(['violation', 'appeal']))
    evidence_type = fields.String(required=True, validate=validate.OneOf(['image', 'video']))
    file_path = fields.String(required=True)
    uploader_id = fields.Integer(required=True)
    created_at = fields.String(dump_only=True)

    # 额外字段
    uploader_name = fields.String(dump_only=True)

    # 移除 post_load 方法
