# 校园电动车管理系统 - 数据库E-R图

## 系统总体E-R图

```mermaid
erDiagram
    %% 用户相关实体
    USERS {
        int u_id PK
        string u_name
        string u_pwd
        string salt
        string u_role
        string u_belong
        string u_phone
        string u_email
        string avatar
        datetime created_at
        datetime updated_at
        int version
    }

    PLAYERS {
        int id PK
        string username
        string password
        int user_id FK
    }

    %% 车辆相关实体
    BIKES {
        int b_id PK
        int belong_to FK
        string b_num
        string brand
        string color
        string b_type
        string status
        datetime created_at
        datetime updated_at
    }

    %% 停车场相关实体
    PARKING_LOTS {
        int id PK
        string name
        string address
        int total_spaces
        int occupied_spaces
        float longitude
        float latitude
        string opening_hours
        int status
        string description
        string campus
        string area
        string manager
        string contact
        datetime created_at
        datetime updated_at
    }

    PARKING_SPACES {
        int id PK
        int parking_lot_id FK
        string space_number
        int type
        int status
        int current_vehicle_id FK
        float power
        text remarks
        datetime last_maintenance_time
        datetime created_at
        datetime updated_at
    }

    %% 停车记录实体
    PARKING_RECORDS {
        int id PK
        int vehicle_id FK
        int user_id FK
        int parking_lot_id FK
        int parking_space_id FK
        datetime entry_time
        datetime exit_time
        int status
        text remarks
        datetime created_at
        datetime updated_at
    }

    %% 充电相关实体
    CHARGING_RECORDS {
        int id PK
        int parking_record_id FK
        int vehicle_id FK
        int user_id FK
        int parking_lot_id FK
        int parking_space_id FK
        datetime start_time
        datetime end_time
        int duration
        int status
        text remarks
        datetime created_at
        datetime updated_at
    }

    CHARGING_RESERVATIONS {
        int id PK
        int user_id FK
        int vehicle_id FK
        int parking_lot_id FK
        datetime start_time
        datetime end_time
        int status
        datetime created_at
        datetime updated_at
    }

    CHARGING_FAULTS {
        int id PK
        int parking_lot_id FK
        int space_id FK
        string fault_type
        string severity
        string reporter_name
        string reporter_phone
        text description
        int status
        text process_result
        datetime created_at
        datetime updated_at
    }

    CHARGING_EXCEPTIONS {
        int id PK
        int charging_record_id FK
        int fault_id FK
        int space_id FK
        int parking_lot_id FK
        string exception_type
        string severity
        text description
        int status
        text process_result
        datetime created_at
        datetime updated_at
    }

    %% 违规相关实体
    VIOLATION_TYPES {
        int id PK
        string name
        text description
        boolean needs_admin
        datetime created_at
        datetime updated_at
    }

    VIOLATION_RECORDS {
        int id PK
        string bike_number
        int bike_id FK
        int user_id FK
        datetime violation_time
        string location
        string violation_type
        int violation_type_id FK
        text description
        int status
        text result
        int recorder_id FK
        int handler_id FK
        datetime created_at
        datetime updated_at
    }

    APPEALS {
        int id PK
        int violation_id FK
        int user_id FK
        text reason
        int status
        text comment
        int handler_id FK
        datetime created_at
        datetime updated_at
    }

    EVIDENCES {
        int id PK
        int related_id
        string related_type
        string evidence_type
        string file_path
        int uploader_id FK
        datetime created_at
    }

    %% 公告实体
    ANNOUNCEMENTS {
        int id PK
        string title
        text content
        string type
        int priority
        string target_roles
        boolean is_active
        datetime start_time
        datetime end_time
        int created_by FK
        datetime created_at
        datetime updated_at
    }

    %% 关系定义
    USERS ||--o{ BIKES : "拥有"
    USERS ||--o| PLAYERS : "关联"
    USERS ||--o{ PARKING_RECORDS : "停车"
    USERS ||--o{ CHARGING_RECORDS : "充电"
    USERS ||--o{ CHARGING_RESERVATIONS : "预约"
    USERS ||--o{ VIOLATION_RECORDS : "违规"
    USERS ||--o{ APPEALS : "申诉"
    USERS ||--o{ EVIDENCES : "上传"
    USERS ||--o{ ANNOUNCEMENTS : "发布"

    PARKING_LOTS ||--o{ PARKING_SPACES : "包含"
    PARKING_LOTS ||--o{ PARKING_RECORDS : "记录"
    PARKING_LOTS ||--o{ CHARGING_RECORDS : "充电记录"
    PARKING_LOTS ||--o{ CHARGING_RESERVATIONS : "预约"
    PARKING_LOTS ||--o{ CHARGING_FAULTS : "故障"
    PARKING_LOTS ||--o{ CHARGING_EXCEPTIONS : "异常"

    PARKING_SPACES ||--o{ PARKING_RECORDS : "使用"
    PARKING_SPACES ||--o{ CHARGING_RECORDS : "充电"
    PARKING_SPACES ||--o{ CHARGING_FAULTS : "故障"
    PARKING_SPACES ||--o{ CHARGING_EXCEPTIONS : "异常"
    PARKING_SPACES ||--o| BIKES : "当前停放"

    BIKES ||--o{ PARKING_RECORDS : "停车记录"
    BIKES ||--o{ CHARGING_RECORDS : "充电记录"
    BIKES ||--o{ CHARGING_RESERVATIONS : "预约记录"
    BIKES ||--o{ VIOLATION_RECORDS : "违规记录"

    PARKING_RECORDS ||--o{ CHARGING_RECORDS : "关联充电"

    VIOLATION_TYPES ||--o{ VIOLATION_RECORDS : "分类"
    VIOLATION_RECORDS ||--o| APPEALS : "申诉"

    CHARGING_FAULTS ||--o{ CHARGING_EXCEPTIONS : "关联"
```

## 实体详细说明

### 核心实体

#### 1. USERS (用户表)
- **主键**: u_id
- **功能**: 存储系统用户信息，包括管理员、保安、普通用户
- **关键字段**:
  - u_role: 用户角色 (admin/security/user)
  - u_name: 用户名 (唯一)
  - u_pwd: 加密密码
  - salt: 密码盐值

#### 2. BIKES (车辆表)
- **主键**: b_id
- **功能**: 存储电动车信息
- **关键字段**:
  - belong_to: 所属用户ID (外键)
  - b_num: 车牌号 (唯一)
  - status: 车辆状态 (可用/废弃)

#### 3. PARKING_LOTS (停车场表)
- **主键**: id
- **功能**: 存储停车场基本信息
- **关键字段**:
  - name: 停车场名称 (唯一)
  - total_spaces: 总车位数
  - occupied_spaces: 已占用车位数
  - campus: 校区 (南湖校区/文昌校区)
  - area: 区域 (教学楼区/宿舍区/图书馆区/食堂区/运动场区)

#### 4. PARKING_SPACES (车位表)
- **主键**: id
- **功能**: 存储车位详细信息
- **关键字段**:
  - parking_lot_id: 所属停车场ID (外键)
  - space_number: 车位编号 (格式: {类型前缀}-{停车场ID}-{序号})
  - type: 车位类型 (1普通/2残疾人/3充电)
  - status: 车位状态 (0空闲/1已占用/2故障/3维修中/4禁用)
  - current_vehicle_id: 当前停放车辆ID (外键)

### 业务记录实体

#### 5. PARKING_RECORDS (停车记录表)
- **主键**: id
- **功能**: 记录车辆停车的完整过程
- **关键字段**:
  - vehicle_id: 车辆ID (外键)
  - user_id: 用户ID (外键)
  - parking_lot_id: 停车场ID (外键)
  - parking_space_id: 车位ID (外键)
  - entry_time: 入场时间
  - exit_time: 出场时间
  - status: 记录状态 (0进行中/1已完成/2异常)

#### 6. CHARGING_RECORDS (充电记录表)
- **主键**: id
- **功能**: 记录车辆充电过程
- **关键字段**:
  - parking_record_id: 关联停车记录ID (外键)
  - vehicle_id: 车辆ID (外键)
  - user_id: 用户ID (外键)
  - start_time: 开始充电时间
  - end_time: 结束充电时间
  - duration: 充电时长 (分钟)

#### 7. VIOLATION_RECORDS (违规记录表)
- **主键**: id
- **功能**: 记录车辆违规信息
- **关键字段**:
  - bike_number: 车牌号
  - bike_id: 车辆ID (外键)
  - user_id: 车主ID (外键)
  - violation_type_id: 违规类型ID (外键)
  - recorder_id: 录入人ID (外键)
  - handler_id: 处理人ID (外键)
  - status: 处理状态 (0待审核/1已处理/2申诉中/3已撤销)

#### 8. APPEALS (申诉表)
- **主键**: id
- **功能**: 处理违规申诉
- **关键字段**:
  - violation_id: 违规记录ID (外键, 唯一)
  - user_id: 申诉用户ID (外键)
  - handler_id: 处理人ID (外键)
  - status: 申诉状态 (0待审核/1已通过/2未通过)

### 辅助实体

#### 9. VIOLATION_TYPES (违规类型表)
- **主键**: id
- **功能**: 定义违规类型
- **关键字段**:
  - name: 违规类型名称
  - needs_admin: 是否需要管理员处理

#### 10. EVIDENCES (证据表)
- **主键**: id
- **功能**: 存储违规和申诉的证据文件
- **关键字段**:
  - related_id: 关联ID (违规记录ID或申诉ID)
  - related_type: 关联类型 (violation/appeal)
  - evidence_type: 证据类型 (image/video)
  - file_path: 文件路径

#### 11. ANNOUNCEMENTS (公告表)
- **主键**: id
- **功能**: 系统公告管理
- **关键字段**:
  - title: 公告标题
  - type: 公告类型 (system/parking/violation)
  - target_roles: 目标角色 (逗号分隔)
  - is_active: 是否启用

### 充电管理实体

#### 12. CHARGING_RESERVATIONS (充电预约表)
- **主键**: id
- **功能**: 充电预约管理
- **关键字段**:
  - user_id: 用户ID (外键)
  - vehicle_id: 车辆ID (外键)
  - parking_lot_id: 停车场ID (外键)
  - status: 预约状态 (0待使用/1已使用/2已取消/3已过期)

#### 13. CHARGING_FAULTS (充电故障表)
- **主键**: id
- **功能**: 充电设备故障管理
- **关键字段**:
  - parking_lot_id: 停车场ID (外键)
  - space_id: 车位ID (外键)
  - fault_type: 故障类型 (connector/startup/interruption/other)
  - severity: 严重程度 (low/medium/high/critical)

#### 14. CHARGING_EXCEPTIONS (充电异常表)
- **主键**: id
- **功能**: 充电异常记录
- **关键字段**:
  - charging_record_id: 充电记录ID (外键)
  - fault_id: 故障ID (外键)
  - exception_type: 异常类型
  - severity: 严重程度

#### 15. PLAYERS (玩家表)
- **主键**: id
- **功能**: 游戏账户关联 (历史遗留)
- **关键字段**:
  - user_id: 关联用户ID (外键)
  - username: 游戏用户名

## 主要关系说明

### 一对多关系
1. **USERS → BIKES**: 一个用户可以拥有多辆车
2. **PARKING_LOTS → PARKING_SPACES**: 一个停车场包含多个车位
3. **PARKING_LOTS → PARKING_RECORDS**: 一个停车场有多条停车记录
4. **USERS → VIOLATION_RECORDS**: 一个用户可能有多条违规记录
5. **VIOLATION_TYPES → VIOLATION_RECORDS**: 一种违规类型对应多条违规记录

### 一对一关系
1. **USERS ↔ PLAYERS**: 用户与游戏账户一对一关联
2. **VIOLATION_RECORDS ↔ APPEALS**: 一条违规记录最多对应一条申诉
3. **PARKING_SPACES ↔ BIKES**: 一个车位当前最多停放一辆车

### 多对多关系 (通过中间表实现)
1. **USERS ↔ PARKING_LOTS**: 通过PARKING_RECORDS实现用户与停车场的多对多关系
2. **BIKES ↔ PARKING_SPACES**: 通过PARKING_RECORDS实现车辆与车位的多对多关系

### 特殊关系
1. **PARKING_RECORDS → CHARGING_RECORDS**: 停车记录可以关联充电记录
2. **CHARGING_FAULTS → CHARGING_EXCEPTIONS**: 充电故障可以产生多个异常记录
3. **EVIDENCES**: 多态关联，可以关联违规记录或申诉记录

## 数据完整性约束

### 外键约束
- 所有外键字段都设置了适当的级联删除或置空策略
- 关键业务数据采用级联删除 (CASCADE)
- 可选关联数据采用置空策略 (SET NULL)

### 唯一性约束
- 用户名 (u_name) 全局唯一
- 车牌号 (b_num) 全局唯一
- 停车场名称 (name) 全局唯一
- 违规记录与申诉一对一关系 (violation_id 唯一)

### 业务逻辑约束
- 车位状态与停车记录状态保持一致
- 充电记录必须关联有效的停车记录
- 申诉只能针对特定状态的违规记录
- 车辆禁用状态影响所有业务操作
