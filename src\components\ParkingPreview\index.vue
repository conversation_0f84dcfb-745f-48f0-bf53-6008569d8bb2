<template>
  <div class="parking-preview">
    <div class="preview-header">
      <h4>车位分布预览</h4>
      <el-button size="mini" icon="el-icon-refresh" @click="refreshData" circle></el-button>
    </div>

    <div class="preview-content" v-loading="loading">
      <div v-if="!loading && !hasData" class="no-data">
        <el-empty description="暂无车位数据" :image-size="60"></el-empty>
      </div>

      <div v-else class="space-types">
        <div
          v-for="(type, index) in spaceTypes"
          :key="index"
          class="space-type-card"
          :class="{ 'clickable': type.available > 0 }"
          @click="handleTypeClick(type.code)"
        >
          <div class="type-icon" :class="'type-' + type.code">{{ type.icon }}</div>
          <div class="type-info">
            <div class="type-name">{{ type.name }}</div>
            <div class="type-stats">
              <span class="available">{{ type.available }}</span> / {{ type.total }}
            </div>
          </div>
          <div class="usage-bar">
            <div
              class="usage-progress"
              :style="{ width: type.usagePercent + '%', backgroundColor: getUsageColor(type.usagePercent) }"
            ></div>
          </div>
        </div>
      </div>

      <div class="preview-grid">
        <div
          v-for="(row, rowIndex) in previewGrid"
          :key="'row-' + rowIndex"
          class="preview-row"
        >
          <div
            v-for="(space, colIndex) in row"
            :key="'space-' + rowIndex + '-' + colIndex"
            class="preview-space"
            :class="getSpaceClass(space)"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getParkingSpaces } from '@/api/parkinglot'

export default {
  name: 'ParkingPreview',
  props: {
    parkingLotId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      spaces: [],
      previewGrid: [],
      spaceTypes: [
        {
          code: 1,
          name: '普通车位',
          icon: '🚕',
          total: 0,
          available: 0,
          usagePercent: 0
        },
        {
          code: 2,
          name: '残疾人车位',
          icon: '♿',
          total: 0,
          available: 0,
          usagePercent: 0
        },
        {
          code: 3,
          name: '充电车位',
          icon: '⚡',
          total: 0,
          available: 0,
          usagePercent: 0
        }
      ]
    }
  },
  computed: {
    hasData() {
      return this.spaces && this.spaces.length > 0
    }
  },
  watch: {
    parkingLotId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.fetchSpaces()
        }
      }
    }
  },
  methods: {
    fetchSpaces() {
      if (!this.parkingLotId) return

      this.loading = true
      getParkingSpaces(this.parkingLotId, { limit: 200 })
        .then(response => {
          let data = response
          if (response.data) {
            data = response.data
          }

          if (Array.isArray(data)) {
            this.spaces = data
          } else if (data.items) {
            this.spaces = data.items
          } else if (data.data && Array.isArray(data.data)) {
            this.spaces = data.data
          } else {
            console.error('无法识别的车位数据格式:', data)
            this.spaces = []
          }

          this.processSpaceData()
        })
        .catch(error => {
          console.error('获取车位数据失败:', error)
          this.$message.error('获取车位数据失败')
        })
        .finally(() => {
          this.loading = false
        })
    },

    processSpaceData() {
      if (!this.spaces || this.spaces.length === 0) return

      // 重置统计数据
      this.spaceTypes.forEach(type => {
        type.total = 0
        type.available = 0
        type.usagePercent = 0
      })

      // 统计各类型车位数量
      this.spaces.forEach(space => {
        const typeIndex = this.spaceTypes.findIndex(t => t.code === space.type)
        if (typeIndex !== -1) {
          this.spaceTypes[typeIndex].total++
          if (space.status === 0) {
            this.spaceTypes[typeIndex].available++
          }
        }
      })

      // 计算使用率
      this.spaceTypes.forEach(type => {
        if (type.total > 0) {
          type.usagePercent = Math.round(((type.total - type.available) / type.total) * 100)
        }
      })

      // 生成预览网格
      this.generatePreviewGrid()
    },

    generatePreviewGrid() {
      // 简化的网格表示，每种类型的车位用不同颜色表示
      // 这里我们创建一个5x5的网格作为示例
      const grid = []
      const rows = 5
      const cols = 5

      // 根据实际车位数据生成网格
      for (let i = 0; i < rows; i++) {
        const row = []
        for (let j = 0; j < cols; j++) {
          // 随机选择一个车位类型和状态
          const index = Math.floor(Math.random() * this.spaces.length)
          const space = this.spaces[index]
          row.push({
            type: space.type,
            status: space.status
          })
        }
        grid.push(row)
      }

      this.previewGrid = grid
    },

    getSpaceClass(space) {
      return {
        [`type-${space.type}`]: true,
        'available': space.status === 0,
        'occupied': space.status === 1,
        'maintenance': space.status === 2
      }
    },

    getUsageColor(percent) {
      if (percent < 50) return '#67C23A' // 绿色
      if (percent < 80) return '#E6A23C' // 黄色
      return '#F56C6C' // 红色
    },

    handleTypeClick(typeCode) {
      // 检查该类型是否有可用车位
      const type = this.spaceTypes.find(t => t.code === typeCode)
      if (!type || type.available <= 0) {
        this.$message.warning(`没有可用的${type.name}`)
        return
      }

      // 触发事件，通知父组件用户选择了车位类型
      this.$emit('type-selected', typeCode)
    },

    refreshData() {
      this.fetchSpaces()
    }
  }
}
</script>

<style lang="scss" scoped>
.parking-preview {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
  margin-bottom: 15px;

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h4 {
      margin: 0;
      font-size: 16px;
      color: #303133;
    }
  }

  .preview-content {
    min-height: 200px;

    .no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 150px;
    }

    .space-types {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin-bottom: 15px;

      .space-type-card {
        display: flex;
        align-items: center;
        padding: 10px;
        border-radius: 6px;
        background-color: #f5f7fa;
        position: relative;
        overflow: hidden;

        &.clickable {
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            background-color: #ecf5ff;
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }

        .type-icon {
          font-size: 20px;
          margin-right: 10px;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;

          &.type-0 {
            background-color: #e1f3d8;
          }

          &.type-1 {
            background-color: #d9ecff;
          }

          &.type-2 {
            background-color: #fdf6ec;
          }
        }

        .type-info {
          flex: 1;

          .type-name {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
          }

          .type-stats {
            font-size: 12px;
            color: #909399;

            .available {
              color: #67C23A;
              font-weight: bold;
            }
          }
        }

        .usage-bar {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 3px;
          background-color: #ebeef5;

          .usage-progress {
            height: 100%;
            transition: width 0.3s ease;
          }
        }
      }
    }

    .preview-grid {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .preview-row {
        display: flex;
        gap: 2px;

        .preview-space {
          width: 20px;
          height: 20px;
          border-radius: 2px;

          &.type-0 {
            background-color: #67C23A;

            &.occupied {
              background-color: #85ce61;
              opacity: 0.5;
            }
          }

          &.type-1 {
            background-color: #409EFF;

            &.occupied {
              background-color: #79bbff;
              opacity: 0.5;
            }
          }

          &.type-2 {
            background-color: #E6A23C;

            &.occupied {
              background-color: #ebb563;
              opacity: 0.5;
            }
          }

          &.maintenance {
            background-color: #909399;
            opacity: 0.5;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .parking-preview {
    .preview-content {
      .space-types {
        .space-type-card {
          padding: 8px;

          .type-icon {
            font-size: 16px;
            width: 24px;
            height: 24px;
          }

          .type-info {
            .type-name {
              font-size: 13px;
            }

            .type-stats {
              font-size: 11px;
            }
          }
        }
      }

      .preview-grid {
        .preview-row {
          .preview-space {
            width: 15px;
            height: 15px;
          }
        }
      }
    }
  }
}
</style>
