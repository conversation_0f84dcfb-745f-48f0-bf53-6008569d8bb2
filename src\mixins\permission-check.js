/**
 * 权限检查混入
 * 用于在组件中检查用户是否有权限访问特定页面
 */
import { mapGetters } from 'vuex'

export default {
  computed: {
    ...mapGetters([
      'role',
      'roles',
      'isAdmin'
    ]),

    /**
     * 检查用户是否有管理员权限
     * @returns {boolean} 是否有管理员权限
     */
    hasAdminPermission() {
      // 检查用户是否为管理员
      if (this.isAdmin) return true

      // 检查用户角色
      const userRoles = this.roles || []
      return userRoles.includes('admin')
    },

    /**
     * 检查用户是否有保安权限
     * @returns {boolean} 是否有保安权限
     */
    hasSecurityPermission() {
      // 管理员拥有所有权限
      if (this.hasAdminPermission) return true

      // 检查用户角色
      const userRoles = this.roles || []
      const userRole = this.role || ''

      return userRoles.includes('security') || userRole === 'security'
    },

    /**
     * 检查用户是否有特定权限
     * @param {string} requiredRole - 需要的角色
     * @returns {boolean} 是否有特定权限
     */
    hasPermission() {
      return (requiredRole) => {
        // 管理员拥有所有权限
        if (this.hasAdminPermission) return true

        // 检查用户角色
        const userRoles = this.roles || []
        const userRole = this.role || ''

        return userRoles.includes(requiredRole) || userRole === requiredRole
      }
    }
  },

  methods: {
    /**
     * 检查用户是否有权限访问当前页面
     * @param {Array} allowedRoles - 允许访问的角色列表
     * @returns {boolean} 是否有权限访问
     */
    checkPagePermission(allowedRoles = ['admin']) {
      console.log('组件权限检查 - 允许角色:', allowedRoles, '用户角色:', this.roles, '单一角色:', this.role)

      // 管理员拥有所有权限
      if (this.hasAdminPermission) {
        console.log('用户是管理员，允许访问')
        return true
      }

      // 检查用户角色是否在允许的角色列表中
      const userRoles = this.roles || []
      const userRole = this.role || ''

      // 如果用户角色是数组，检查是否有交集
      if (Array.isArray(userRoles) && userRoles.length > 0) {
        const hasPermission = userRoles.some(role => allowedRoles.includes(role))
        console.log('检查角色数组是否有交集:', hasPermission)
        return hasPermission
      }

      // 如果用户角色是字符串，直接检查
      const hasPermission = allowedRoles.includes(userRole)
      console.log('检查单一角色是否匹配:', hasPermission)
      return hasPermission
    }
  }
}
