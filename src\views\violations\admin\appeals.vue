<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>申诉管理</span>
      </div>

      <div class="filter-container">
        <el-select v-model="listQuery.status" placeholder="申诉状态" clearable class="filter-item" style="width: 130px">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

        <el-input
          v-model="listQuery.violation_id"
          placeholder="违规记录ID"
          clearable
          class="filter-item"
          style="width: 150px"
        />

        <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
          搜索
        </el-button>

        <el-button v-if="listQuery.violation_id" class="filter-item" type="info" icon="el-icon-close" @click="clearViolationIdFilter">
          清除违规ID筛选
        </el-button>
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="加载中..."
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="ID" align="center" width="80">
          <template slot-scope="{row}">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column>

        <el-table-column label="违规记录ID" align="center" width="120">
          <template slot-scope="{row}">
            <el-button type="text" @click="viewViolation(row.violation_id)">{{ row.violation_id }}</el-button>
          </template>
        </el-table-column>

        <el-table-column label="申诉用户" align="center" width="120">
          <template slot-scope="{row}">
            <span>{{ row.user_name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="申诉理由" align="center" min-width="200">
          <template slot-scope="{row}">
            <el-tooltip :content="row.reason" placement="top" :disabled="row.reason.length < 20">
              <span>{{ row.reason.length > 20 ? row.reason.slice(0, 20) + '...' : row.reason }}</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="申诉状态" align="center" width="100">
          <template slot-scope="{row}">
            <el-tag :type="getStatusType(row.status)">{{ row.status_text }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="申诉时间" align="center" width="160">
          <template slot-scope="{row}">
            <span>{{ formatDateTime(row.created_at) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="处理人" align="center" width="120">
          <template slot-scope="{row}">
            <span>{{ row.handler_name || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template slot-scope="{row}">
            <el-button
              v-if="row.status === 0"
              type="primary"
              size="mini"
              @click="handleProcess(row)"
            >
              处理
            </el-button>

            <el-button
              type="success"
              size="mini"
              @click="viewViolation(row.violation_id)"
            >
              查看违规
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.per_page"
        @pagination="getList"
      />

      <!-- 处理申诉对话框 -->
      <el-dialog title="处理申诉" :visible.sync="processDialogVisible" width="600px">
        <div class="appeal-summary" v-if="currentAppeal">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="summary-item">
                <span class="summary-label">申诉ID:</span>
                <span class="summary-value">{{ currentAppeal.id }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="summary-item">
                <span class="summary-label">违规记录ID:</span>
                <span class="summary-value highlight">
                  <el-button type="text" @click="viewViolation(currentAppeal.violation_id)">
                    {{ currentAppeal.violation_id }}
                  </el-button>
                </span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="summary-item">
                <span class="summary-label">申诉用户:</span>
                <span class="summary-value">{{ currentAppeal.user_name }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="summary-item">
                <span class="summary-label">申诉时间:</span>
                <span class="summary-value">{{ formatDateTime(currentAppeal.created_at) }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="summary-item">
                <span class="summary-label">申诉理由:</span>
                <div class="summary-value description">
                  {{ currentAppeal.reason }}
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="currentAppeal.violation_info">
            <el-col :span="24">
              <div class="summary-item">
                <span class="summary-label">违规信息:</span>
                <div class="violation-info">
                  <p><strong>车牌号:</strong> {{ currentAppeal.violation_info.bike_number }}</p>
                  <p><strong>违规类型:</strong> {{ currentAppeal.violation_info.violation_type }}</p>
                  <p><strong>违规地点:</strong> {{ currentAppeal.violation_info.location }}</p>
                  <p><strong>违规时间:</strong> {{ formatDateTime(currentAppeal.violation_info.violation_time) }}</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <el-divider content-position="center">处理信息</el-divider>

        <el-form :model="processForm" label-width="100px">
          <el-form-item label="处理结果" required>
            <el-radio-group v-model="processForm.status">
              <el-radio :label="1">
                <span class="radio-label">已通过</span>
                <span class="radio-desc">撤销违规记录</span>
              </el-radio>
              <el-radio :label="2">
                <span class="radio-label">未通过</span>
                <span class="radio-desc">维持违规记录</span>
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="处理意见" required>
            <el-input
              v-model="processForm.comment"
              type="textarea"
              :rows="4"
              :placeholder="processForm.status === 1 ? '请输入通过申诉的原因' : '请输入未通过申诉的原因'"
            />
          </el-form-item>


        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button @click="processDialogVisible = false" icon="el-icon-close">取消</el-button>
          <el-button type="primary" @click="submitProcess" icon="el-icon-check" :loading="submitting">确定</el-button>
        </span>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { getAllAppeals, handleAppeal } from '@/api/violations'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'AdminViolationAppeals',
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        per_page: 10,
        status: undefined,
        violation_id: undefined
      },
      statusOptions: [
        { label: '待审核', value: 0 },
        { label: '已通过', value: 1 },
        { label: '未通过', value: 2 }
      ],
      processDialogVisible: false,
      currentAppeal: null,
      submitting: false,
      processForm: {
        status: 1,
        comment: ''
      }
    }
  },
  created() {
    // 检查URL中是否有违规记录ID参数
    const violationId = this.$route.query.violation_id
    if (violationId) {
      this.listQuery.violation_id = violationId
    }
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getAllAppeals(this.listQuery).then(response => {
        this.list = response.data.items
        this.total = response.data.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    clearViolationIdFilter() {
      this.listQuery.violation_id = undefined
      this.listQuery.page = 1
      this.getList()
      // 清除URL中的参数
      this.$router.push({
        name: 'AdminViolationAppeals',
        query: { ...this.$route.query, violation_id: undefined }
      })
    },
    formatDateTime(time) {
      if (!time) return ''
      return parseTime(new Date(time), '{y}-{m}-{d} {h}:{i}')
    },
    getStatusType(status) {
      const statusMap = {
        0: 'warning', // 待审核
        1: 'success', // 已通过
        2: 'danger'   // 未通过
      }
      return statusMap[status] || 'info'
    },
    viewViolation(id) {
      this.$router.push({ name: 'AdminViolationDetail', params: { id }})
    },
    handleProcess(row) {
      this.currentAppeal = row
      this.processForm = {
        status: 1,
        comment: ''
      }
      this.submitting = false
      this.processDialogVisible = true
    },
    submitProcess() {
      if (!this.processForm.comment) {
        this.$message.warning('请输入处理意见')
        return
      }

      // 确认对话框
      const confirmMessage = this.processForm.status === 1
        ? '确认通过该申诉吗？该操作将撤销相关违规记录。'
        : '确认设置申诉为未通过吗？该操作将维持相关违规记录。'

      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: this.processForm.status === 1 ? 'warning' : 'info'
      }).then(() => {
        this.submitting = true

        handleAppeal(this.currentAppeal.id, this.processForm)
          .then(response => {
            this.$notify({
              title: '成功',
              message: this.processForm.status === 1 ? '申诉已通过，相关违规记录已撤销' : '申诉未通过，相关违规记录已维持',
              type: 'success',
              duration: 2000
            })
            this.processDialogVisible = false
            this.getList()
          })
          .catch(error => {
            console.error('处理申诉失败:', error)
            this.$message.error(error.message || '处理失败，请重试')
          })
          .finally(() => {
            this.submitting = false
          })
      }).catch(() => {
        // 用户取消操作
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .filter-container {
    padding-bottom: 10px;

    .filter-item {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }

  // 处理对话框样式
  .appeal-summary {
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;

    .summary-item {
      margin-bottom: 10px;

      .summary-label {
        display: inline-block;
        width: 80px;
        color: #909399;
        font-weight: bold;
      }

      .summary-value {
        color: #303133;

        &.highlight {
          color: #409EFF;
          font-weight: bold;
        }

        &.description {
          margin-top: 5px;
          padding: 10px;
          background-color: #fff;
          border-radius: 4px;
          border: 1px solid #EBEEF5;
          line-height: 1.5;
          min-height: 40px;
        }
      }
    }

    .violation-info {
      margin-top: 5px;
      padding: 10px;
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid #EBEEF5;

      p {
        margin: 5px 0;
      }
    }
  }

  .radio-label {
    font-weight: bold;
    margin-right: 5px;
  }

  .radio-desc {
    color: #909399;
    font-size: 12px;
  }
}
</style>
