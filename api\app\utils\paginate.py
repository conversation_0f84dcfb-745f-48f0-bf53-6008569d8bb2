from flask import url_for, request

def paginate_data(query, page=1, per_page=20, endpoint=None, **kwargs):
    """
    通用分页函数，将查询分页并返回适合JSON响应的格式化结果

    参数:
        query: SQLAlchemy查询对象
        page: 当前页码，默认为1
        per_page: 每页记录数，默认为20
        endpoint: 分页链接使用的路由端点，默认为None（不生成链接）
        **kwargs: 传递给url_for的额外参数

    返回:
        包含分页数据和分页信息的字典
    """
    # 执行分页
    pagination = query.paginate(page=page, per_page=per_page)
    items = pagination.items

    # 准备JSON响应
    result = {
        'items': [],
        'total': pagination.total,
        'pages': pagination.pages,
        'page': page,
        'per_page': per_page
    }

    # 处理items，确保可序列化
    for item in items:
        if hasattr(item, 'to_dict'):
            # 如果对象有to_dict方法，使用它
            result['items'].append(item.to_dict())
        elif hasattr(item, 'get_details'):
            # 如果对象有get_details方法，使用它
            result['items'].append(item.get_details())
        else:
            # 否则尝试手动构建安全的字典
            item_dict = {}
            for key, value in vars(item).items():
                # 跳过SQLAlchemy内部属性和不可序列化对象
                if not key.startswith('_') and key != '_sa_instance_state':
                    item_dict[key] = value
            result['items'].append(item_dict)

    # 如果提供了端点，生成分页链接
    if endpoint:
        links = {}
        # 保留所有原始查询参数
        args = request.args.copy()

        # 生成下一页链接
        if pagination.has_next:
            args['page'] = page + 1
            links['next'] = url_for(endpoint, **args, **kwargs, _external=True)

        # 生成上一页链接
        if pagination.has_prev:
            args['page'] = page - 1
            links['prev'] = url_for(endpoint, **args, **kwargs, _external=True)

        # 生成首页链接
        args['page'] = 1
        links['first'] = url_for(endpoint, **args, **kwargs, _external=True)

        # 生成末页链接
        args['page'] = pagination.pages
        links['last'] = url_for(endpoint, **args, **kwargs, _external=True)

        # 添加链接到结果
        result['links'] = links

    return result