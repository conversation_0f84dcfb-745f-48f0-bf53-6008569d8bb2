<template>
  <el-card class="charging-card">
    <div class="card-header">
      <div class="vehicle-info">
        <span class="license-plate">{{ record.vehicle.number }}</span>
        <span class="vehicle-details">{{ record.vehicle.brand }} {{ record.vehicle.color }}</span>
      </div>
      <el-tag :type="getStatusTagType(record.status)" size="mini">
        {{ record.status_text }}
      </el-tag>
    </div>

    <div class="card-content">
      <div class="info-item">
        <i class="el-icon-location"></i>
        <span>{{ record.parking_lot.name }}</span>
      </div>
      <div class="info-item">
        <i class="el-icon-office-building"></i>
        <span>车位: {{ record.parking_space.space_number }}</span>
      </div>
      <div class="info-item">
        <i class="el-icon-lightning"></i>
        <span>充电功率: {{ record.power }}kW</span>
      </div>
      <div class="info-item">
        <i class="el-icon-time"></i>
        <span>开始时间: {{ formatDateTime(record.start_time, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
      </div>
      <div class="info-item">
        <i class="el-icon-timer"></i>
        <span>已充电: {{ calculateDuration(record.start_time) }}</span>
      </div>
      <div class="info-item" style="color: #409EFF;">
        <i class="el-icon-info"></i>
        <span>原始时间: {{ record.start_time }}</span>
      </div>

    </div>

    <div class="card-footer">
      <slot name="footer">
        <el-button
          type="danger"
          size="mini"
          :loading="loading"
          @click="$emit('end-charging', record)"
        >
          结束充电
        </el-button>
      </slot>
    </div>
  </el-card>
</template>

<script>
import { formatDateTime } from '@/utils/index'

export default {
  name: 'ChargingCard',
  props: {
    record: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentTime: new Date(),
      timer: null
    }
  },
  mounted() {
    // 启动定时器，每秒更新当前时间
    this.timer = setInterval(() => {
      this.currentTime = new Date()
    }, 1000)
  },
  beforeDestroy() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    formatDateTime,
    getStatusTagType(status) {
      const statusMap = {
        0: 'info',    // 待开始
        1: 'success', // 充电中
        2: 'danger',  // 已结束
        3: 'warning'  // 异常
      }
      return statusMap[status] || 'info'
    },

    // 计算充电时长（实时）
    calculateDuration(startTime) {
      if (!startTime) return '0分钟'

      try {
        // 标准化ISO日期格式
        let timeStr = startTime
        // 直接使用原始时间字符串，不添加Z后缀
        // 这样可以确保时间被正确解析为本地时间

        const start = new Date(timeStr)

        // 检查日期是否有效
        if (isNaN(start.getTime())) {
          console.warn('无效的开始时间:', startTime)
          return '0分钟'
        }

        const now = this.currentTime

        // 计算时间差（毫秒）
        const diffMs = now - start

        // 如果时间差为负数，返回0
        if (diffMs < 0) return '0分钟'

        // 计算小时、分钟和秒
        const hours = Math.floor(diffMs / (1000 * 60 * 60))
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
        const seconds = Math.floor((diffMs % (1000 * 60)) / 1000)

        // 格式化输出
        if (hours > 0) {
          return `${hours}小时${minutes}分钟${seconds}秒`
        } else if (minutes > 0) {
          return `${minutes}分钟${seconds}秒`
        } else {
          return `${seconds}秒`
        }
      } catch (error) {
        console.error('计算充电时长出错:', error, startTime)
        return '0分钟'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .vehicle-info {
      display: flex;
      flex-direction: column;

      .license-plate {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .vehicle-details {
        font-size: 13px;
        color: #606266;
      }
    }
  }

  .card-content {
    margin-bottom: 15px;

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;

      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }
  }

  .card-footer {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
