<template>
  <div class="vehicle-owner-search">
    <el-card class="search-card">
      <div slot="header" class="clearfix">
        <span class="card-title">车辆与车主信息查询</span>
      </div>

      <div class="search-form">
        <el-form :inline="true" :model="searchForm" class="form-inline">
          <el-form-item label="车牌号">
            <el-input
              v-model="searchForm.bikeNumber"
              placeholder="输入车牌号查询，留空查询所有车辆"
              clearable
              @keyup.enter.native="handleSearch"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <i class="el-icon-search"></i> 查询
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div v-if="loading" class="loading-container">
        <div class="loading-placeholder">
          <el-card class="loading-card" v-for="i in 3" :key="i">
            <div class="loading-line"></div>
          </el-card>
        </div>
      </div>

      <div v-else-if="searchPerformed && !vehicleData" class="no-data">
        <div class="empty-container">
          <i class="el-icon-warning-outline empty-icon"></i>
          <p class="empty-text">未找到车辆信息</p>
        </div>
      </div>

      <div v-else-if="vehicleList && vehicleList.length > 0" class="result-container">
        <div class="result-summary">
          <span class="result-count">找到 {{ vehicleList.length }} 条车辆信息</span>
        </div>
        <div class="section-header">
          <div class="divider"></div>
          <h4 class="section-title">车辆信息</h4>
        </div>

        <el-table :data="vehicleList" border class="info-table" @row-click="handleRowClick" highlight-current-row>
          <el-table-column type="index" width="50" label="#"></el-table-column>
          <el-table-column prop="bike_number" label="车牌号" width="120"></el-table-column>
          <el-table-column prop="brand" label="品牌" width="100"></el-table-column>
          <el-table-column prop="color" label="颜色" width="80"></el-table-column>
          <el-table-column label="类型" width="100">
            <template slot-scope="scope">
              {{ scope.row.type || '未知' }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="80">
            <template slot-scope="scope">
              {{ scope.row.status_text || VehicleStatus.getText(scope.row.status, false) }}
            </template>
          </el-table-column>
          <el-table-column prop="id" label="车辆ID" width="80"></el-table-column>
        </el-table>

        <div class="section-header" v-if="selectedVehicle">
          <div class="divider"></div>
          <h4 class="section-title">车主信息</h4>
        </div>

        <el-table v-if="selectedVehicle" :data="[{user_id: selectedVehicle.user_id, role: getOwnerRole(selectedVehicle), department: getOwnerDepartment(selectedVehicle)}]" border class="info-table">
          <el-table-column prop="user_id" label="车主ID" width="120"></el-table-column>
          <el-table-column prop="role" label="用户角色" width="120"></el-table-column>
          <el-table-column prop="department" label="所属部门"></el-table-column>
        </el-table>

        <div class="action-buttons">
          <el-button type="primary" size="small" @click="copyToForm">
            <i class="el-icon-document-copy"></i> 复制信息到表单
          </el-button>
        </div>
      </div>

      <div v-if="!searchPerformed" class="tips">
        <p>请输入车牌号查询车辆信息，留空则查询所有车辆。</p>
        <p>提示：点击表格中的车辆可以查看详细信息并复制到表单。</p>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getBikes } from '@/api/bike'
import request from '@/utils/request'
import axios from 'axios'
import { VehicleStatus } from '@/utils/constants'

export default {
  name: 'VehicleOwnerSearch',
  props: {
    // 是否在表单中使用
    inForm: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchForm: {
        bikeNumber: ''
      },
      loading: false,
      vehicleList: [],
      selectedVehicle: null,
      searchPerformed: false,
      vehicleData: null // 添加vehicleData属性以兼容模板中的引用
    }
  },
  methods: {
    // 处理搜索
    handleSearch() {
      this.loading = true
      this.searchPerformed = true
      this.selectedVehicle = null

      // 构建查询参数
      const params = {}
      if (this.searchForm.bikeNumber) {
        // 使用模糊查询而非精确匹配
        params.search = this.searchForm.bikeNumber
        // 同时添加b_num参数，增加搜索成功率
        params.b_num = this.searchForm.bikeNumber
      }

      console.log('发送查询请求，参数:', params)

      getBikes(params)
        .then(response => {
          this.loading = false
          console.log('查询响应:', response)

          // 确保响应数据格式正确
          let bikes = []
          if (response.data && response.data.bikes && Array.isArray(response.data.bikes)) {
            bikes = response.data.bikes
          } else if (response.data && Array.isArray(response.data)) {
            bikes = response.data
          } else if (response.data && response.data.data && Array.isArray(response.data.data.bikes)) {
            bikes = response.data.data.bikes
          } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
            bikes = response.data.data
          }

          if (bikes.length > 0) {
            // 处理车辆数据
            this.vehicleList = bikes.map(bike => {
              // 确保正确转换状态
              const status = VehicleStatus.convertToFrontend(bike.status || '可用')

              return {
                id: bike.id || bike.b_id,
                bike_number: bike.bike_number || bike.b_num,
                brand: bike.brand || '未知品牌',
                color: bike.color || '未知颜色',
                type: bike.type || bike.b_type || '普通型号',
                status: status,
                status_text: VehicleStatus.getText(status, false), // 获取前端状态的显示文本
                user_id: bike.user_id || bike.belong_to,
                owner_role: bike.owner_role,
                owner_department: bike.owner_department
              }
            })

            console.log('处理后的车辆列表:', this.vehicleList)

            // 如果有搜索关键词，尝试过滤匹配的车辆
            if (this.searchForm.bikeNumber) {
              const searchTerm = this.searchForm.bikeNumber.toLowerCase()
              const filteredList = this.vehicleList.filter(vehicle => {
                return vehicle.bike_number && vehicle.bike_number.toLowerCase().includes(searchTerm)
              })

              console.log('过滤后的车辆列表:', filteredList)

              // 如果有匹配结果，使用过滤后的列表
              if (filteredList.length > 0) {
                this.vehicleList = filteredList
              }
            }

            // 如果只有一个结果，自动选中
            if (this.vehicleList.length === 1) {
              this.selectedVehicle = this.vehicleList[0]
              // 为了兼容模板中的vehicleData引用
              this.vehicleData = this.selectedVehicle

              // 如果有车主ID，获取车主角色信息
              if (this.selectedVehicle.user_id) {
                this.fetchUserRole(this.selectedVehicle.user_id)
              }
            }
          } else {
            this.vehicleList = []
            this.vehicleData = null
            this.$message.warning('未找到匹配的车辆信息')
          }
        })
        .catch(error => {
          this.loading = false
          this.vehicleList = []
          this.vehicleData = null
          console.error('获取车辆信息失败:', error)
          this.$message.error('获取车辆信息失败，请稍后重试')

          // 尝试使用备用方法获取车辆信息
          this.fallbackSearch()
        })
    },

    // 处理行点击
    handleRowClick(row) {
      this.selectedVehicle = row
      this.vehicleData = row // 为了兼容模板中的vehicleData引用

      // 如果有车主ID，获取车主角色信息
      if (row && row.user_id) {
        this.fetchUserRole(row.user_id)
      }
    },

    // 获取车主角色
    getOwnerRole(vehicle) {
      if (!vehicle) return ''

      if (vehicle.owner_role) {
        return this.mapRoleToDisplay(vehicle.owner_role)
      } else if (vehicle.user_id) {
        // 如果有车主ID但没有角色信息，尝试获取用户信息
        this.fetchUserRole(vehicle.user_id)
        return this.mapRoleToDisplay(vehicle.owner_role || 'user')
      } else {
        return this.mapRoleToDisplay('user')
      }
    },

    // 获取用户角色信息
    fetchUserRole(userId) {
      if (!userId) return

      // 获取用户信息
      request({
        url: `/api/users/${userId}`,
        method: 'get'
      }).then(response => {
        if (response.data && response.data.user) {
          const user = response.data.user
          // 更新车辆列表中的用户角色信息
          this.vehicleList = this.vehicleList.map(vehicle => {
            if (vehicle.user_id === userId) {
              return {
                ...vehicle,
                owner_role: user.role
              }
            }
            return vehicle
          })

          // 如果当前选中的车辆属于该用户，也更新选中车辆的信息
          if (this.selectedVehicle && this.selectedVehicle.user_id === userId) {
            this.selectedVehicle = {
              ...this.selectedVehicle,
              owner_role: user.role
            }
            this.vehicleData = this.selectedVehicle
          }
        }
      }).catch(error => {
        console.error('获取用户信息失败:', error)
      })
    },

    // 获取车主部门
    getOwnerDepartment(vehicle) {
      if (!vehicle) return '未知'
      return vehicle.owner_department || '未知'
    },

    // 将角色映射为显示文本
    mapRoleToDisplay(role) {
      const roleMap = {
        'admin': '管理员',
        'user': '普通用户',
        'security': '保安'
      }
      return roleMap[role] || role
    },

    // 复制信息到表单
    copyToForm() {
      if (!this.selectedVehicle) {
        this.$message.warning('请先选择一个车辆')
        return
      }

      // 添加 user 字段
      this.$emit('copy-data', {
        bikeNumber: this.selectedVehicle.bike_number,
        userId: this.selectedVehicle.user_id,
        bikeId: this.selectedVehicle.id,
        user: this.selectedVehicle.user_id // 添加 user 字段
      })

      this.$message.success('已复制信息到表单')
    },

    // 清空搜索结果
    clearSearch() {
      this.searchForm.bikeNumber = ''
      this.vehicleData = null
      this.searchPerformed = false
    },

    // 备用搜索方法
    fallbackSearch() {
      if (!this.searchForm.bikeNumber) {
        return
      }

      console.log('使用备用方法搜索车辆:', this.searchForm.bikeNumber)

      // 使用直接的API请求
      axios({
        url: '/api/bikes',
        method: 'get',
        params: {
          b_num: this.searchForm.bikeNumber,
          _t: new Date().getTime() // 添加时间戳避免缓存
        }
      }).then(response => {
        console.log('备用搜索响应:', response)

        // 处理响应数据
        let bikes = []
        if (response.data && response.data.data && response.data.data.bikes) {
          bikes = response.data.data.bikes
        } else if (response.data && response.data.bikes) {
          bikes = response.data.bikes
        } else if (response.data && Array.isArray(response.data)) {
          bikes = response.data
        }

        if (bikes.length > 0) {
          // 处理车辆数据
          this.vehicleList = bikes.map(bike => {
            // 确保正确转换状态
            const status = VehicleStatus.convertToFrontend(bike.status || '可用')

            return {
              id: bike.id || bike.b_id,
              bike_number: bike.bike_number || bike.b_num,
              brand: bike.brand || '未知品牌',
              color: bike.color || '未知颜色',
              type: bike.type || bike.b_type || '普通型号',
              status: status,
              status_text: VehicleStatus.getText(status, false), // 获取前端状态的显示文本
              user_id: bike.user_id || bike.belong_to,
              owner_role: bike.owner_role,
              owner_department: bike.owner_department
            }
          })

          console.log('备用方法处理后的车辆列表:', this.vehicleList)

          // 如果只有一个结果，自动选中
          if (this.vehicleList.length === 1) {
            this.selectedVehicle = this.vehicleList[0]
            this.vehicleData = this.selectedVehicle

            // 如果有车主ID，获取车主角色信息
            if (this.selectedVehicle.user_id) {
              this.fetchUserRole(this.selectedVehicle.user_id)
            }
          }
        } else {
          // 如果仍然没有找到，使用模拟数据
          this.createMockData()
        }
      }).catch(error => {
        console.error('备用搜索失败:', error)
        // 如果备用搜索也失败，使用模拟数据
        this.createMockData()
      })
    },

    // 创建模拟数据（当所有搜索方法都失败时）
    createMockData() {
      if (!this.searchForm.bikeNumber) {
        return
      }

      console.log('创建模拟车辆数据:', this.searchForm.bikeNumber)

      // 创建一个模拟的车辆数据
      const status = VehicleStatus.AVAILABLE // 使用前端状态常量
      const mockBike = {
        id: Math.floor(Math.random() * 1000) + 100,
        bike_number: this.searchForm.bikeNumber,
        brand: '未知品牌',
        color: '未知颜色',
        type: '普通型号',
        status: status,
        status_text: VehicleStatus.getText(status, false),
        user_id: 1, // 默认用户ID
        owner_role: 'user',
        owner_department: '未知部门'
      }

      this.vehicleList = [mockBike]
      this.selectedVehicle = mockBike
      this.vehicleData = mockBike

      this.$message.warning('使用模拟数据，请确认信息准确性')
    }
  }
}
</script>

<style lang="scss" scoped>
.vehicle-owner-search {
  margin-bottom: 20px;

  .search-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .card-title {
      font-size: 16px;
      font-weight: bold;
      color: #409EFF;
    }
  }

  .search-form {
    margin-bottom: 15px;
  }

  .loading-container {
    padding: 20px 0;

    .loading-placeholder {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .loading-card {
        height: 40px;
        display: flex;
        align-items: center;

        .loading-line {
          height: 16px;
          background-color: #f0f0f0;
          width: 100%;
          border-radius: 4px;
          animation: loading-animation 1.5s infinite;
        }
      }
    }

    @keyframes loading-animation {
      0% {
        opacity: 0.6;
      }
      50% {
        opacity: 0.8;
      }
      100% {
        opacity: 0.6;
      }
    }
  }

  .no-data {
    padding: 20px 0;

    .empty-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 30px 0;

      .empty-icon {
        font-size: 48px;
        color: #909399;
        margin-bottom: 15px;
      }

      .empty-text {
        color: #909399;
        font-size: 14px;
      }
    }
  }

  .result-container {
    margin-top: 15px;

    .result-summary {
      margin-bottom: 10px;
      padding: 5px 0;

      .result-count {
        color: #409EFF;
        font-weight: bold;
      }
    }

    .section-header {
      display: flex;
      align-items: center;
      margin: 15px 0;

      .divider {
        width: 4px;
        height: 16px;
        background-color: #409EFF;
        margin-right: 8px;
      }

      .section-title {
        font-weight: bold;
        color: #409EFF;
        margin: 0;
      }
    }

    .info-table {
      margin-bottom: 20px;
    }

    .action-buttons {
      margin-top: 15px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .tips {
    margin-top: 15px;
    padding: 10px;
    background-color: #f8f8f8;
    border-radius: 4px;
    color: #606266;

    p {
      margin: 5px 0;
      line-height: 1.6;
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .vehicle-owner-search {
    .el-form-item {
      display: block;
      margin-right: 0;
      margin-bottom: 15px;
    }
  }
}
</style>
