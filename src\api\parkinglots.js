import request from '@/utils/request'

// 获取所有停车场
export function getParkingLots(params) {
  // 添加调试日志
  console.log('调用 parkinglots.js 中的 getParkingLots API，参数:', params)

  return request({
    url: '/api/parkinglots',  // 使用 /api/parkinglots 路径，与后端路由匹配
    method: 'get',
    params
  }).catch(error => {
    console.error('获取停车场列表失败 (/api/parkinglots):', error)

    // 如果第一个API失败，尝试备用API
    console.log('尝试备用API路径 /api/parking-lots')
    return request({
      url: '/api/parking-lots',
      method: 'get',
      params
    })
  })
}

// 获取单个停车场详情
export function getParkingLot(id) {
  return request({
    url: `/api/parking-lots/${id}`,
    method: 'get'
  })
}

// 获取停车场的车位
export function getParkingSpaces(lotId, params) {
  return request({
    url: `/api/parking-lots/${lotId}/spaces`,
    method: 'get',
    params
  })
}

// 创建停车场（仅管理员）
export function createParkingLot(data) {
  return request({
    url: '/api/parking-lots',
    method: 'post',
    data
  })
}

// 更新停车场（仅管理员）
export function updateParkingLot(id, data) {
  return request({
    url: `/api/parking-lots/${id}`,
    method: 'put',
    data
  })
}

// 删除停车场（仅管理员）
export function deleteParkingLot(id) {
  return request({
    url: `/api/parking-lots/${id}`,
    method: 'delete'
  })
}

// 更新车位状态（仅管理员）
export function updateSpaceStatus(spaceId, status) {
  return request({
    url: `/api/spaces/${spaceId}/status`,
    method: 'put',
    data: { status }
  })
}

// 批量更新车位状态（仅管理员）
export function batchUpdateSpaces(spaceIds, status) {
  return request({
    url: '/api/spaces/batch-update',
    method: 'post',
    data: {
      space_ids: spaceIds,
      status
    }
  })
}

// 更新车位类型（仅管理员）
export function updateSpaceType(spaceId, type, options = {}) {
  const data = { type, ...options }
  return request({
    url: `/api/spaces/${spaceId}/type`,
    method: 'put',
    data
  })
}

// 批量更新车位类型（仅管理员）
export function batchUpdateSpaceTypes(spaceIds, type, options = {}) {
  const data = {
    space_ids: spaceIds,
    type,
    ...options
  }
  return request({
    url: '/api/spaces/batch-update-type',
    method: 'post',
    data
  })
}

// 获取停车场统计信息
export function getParkingLotStats(lotId) {
  return request({
    url: `/api/parking-lots/${lotId}/stats`,
    method: 'get'
  })
}

// 获取所有停车场统计信息
export function getAllParkingLotStats() {
  return request({
    url: '/api/parking-lots/stats',
    method: 'get'
  })
}

// 获取停车场详细信息（包括车位状态）
export function getParkingLotDetails(lotId) {
  return request({
    url: `/api/parking-lots/${lotId}/details`,
    method: 'get'
  })
}
