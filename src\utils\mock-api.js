/**
 * 前端模拟API功能
 * 仅在开发环境且未配置后端API时使用
 */
import MockAdapter from 'axios-mock-adapter'
import store from '@/store'

// 定义模拟数据
const mockUsersCache = {
  1: { id: 1, name: 'Admin', username: 'admin', role: 'admin' },
  2: { id: 2, name: 'Aba User', username: 'aba', role: 'user' },
  3: { id: 3, name: 'AAA管理员', username: 'aaa', role: 'admin' }
}

const mockBikesCache = {}

// 获取用户信息
function getUserFromCache(userId) {
  return mockUsersCache[userId] || null
}

// 标准化字段
function standardizeFields(data, isUpdate = false) {
  return data
}

// 更新用户信息
function updateUserInCache(userId, data) {
  if (mockUsersCache[userId]) {
    mockUsersCache[userId] = { ...mockUsersCache[userId], ...data }
  }
  return mockUsersCache[userId] || null
}

// 通用函数：从URL解析token
function extractTokenFromUrl(url) {
  if (!url) return null
  const tokenMatch = url.match(/token=([^&]+)/)
  return tokenMatch ? tokenMatch[1] : null
}

// 通用函数：从token解析用户ID
function extractUserIdFromToken(token) {
  if (!token) return null

  // 处理不同格式的token
  if (token.startsWith('admin-token')) {
    return 1
  } else if (token.startsWith('aba-token')) {
    return 3 // aaa用户的ID
  } else if (token.startsWith('user-token-')) {
    const parts = token.split('-')
    if (parts.length >= 3) {
      const userId = parseInt(parts[2], 10)
      return !isNaN(userId) && userId > 0 ? userId : null
    }
  }
  return null
}

// 通用函数：生成用户信息响应
function generateUserInfoResponse(token) {
  // 先尝试从token解析用户ID
  const userId = extractUserIdFromToken(token)
  console.log('模拟API - 解析token:', token, '获取到用户ID:', userId)

  if (userId === null) {
    console.log('模拟API - 无法从token解析出用户ID')
    return [404, {
      code: 50008,
      message: 'Login failed, unable to get user details.'
    }]
  }

  // 管理员用户
  if (userId === 1) {
    return [200, {
      code: 20000,
      data: {
        roles: ['admin'],
        introduction: 'I am a super administrator',
        avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
        name: 'Admin',
        userId: 1
      }
    }]
  }

  // aaa用户 - 管理员权限
  if (userId === 3) {
    return [200, {
      code: 20000,
      data: {
        roles: ['admin'],
        introduction: 'I am an administrator',
        avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
        name: 'AAA管理员',
        userId: 3
      }
    }]
  }

  // aba用户
  if (userId === 2) {
    return [200, {
      code: 20000,
      data: {
        roles: ['user'],
        introduction: 'I am a normal user',
        avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
        name: 'Aba User',
        userId: 2
      }
    }]
  }

  // 其他用户 - 从缓存中查找
  const userFound = mockUsersCache[userId]
  if (userFound) {
    console.log('模拟API - 从缓存中找到用户:', userFound)
    return [200, {
      code: 20000,
      data: {
        roles: [userFound.role || 'user'],
        introduction: `I am a ${userFound.role || 'normal'} user`,
        avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
        name: userFound.name || userFound.username,
        userId: userId
      }
    }]
  }

  // 找不到用户信息
  console.log('模拟API - 在缓存中未找到用户ID:', userId)
  return [404, {
    code: 50008,
    message: 'Login failed, unable to get user details.'
  }]
}

/**
 * 初始化模拟API
 * @param {Object} service - axios实例
 * @returns {Object|null} - 模拟适配器实例或null
 */
export function setupMockApi(service) {
  // 如果后端不可用，使用模拟数据
  const useMockAPI = process.env.NODE_ENV === 'development' && !process.env.VUE_APP_BASE_API
  
  if (!useMockAPI) {
    console.log('使用真实API服务:', process.env.VUE_APP_BASE_API)
    return null
  }
  
  console.log('使用模拟API服务...')
  // 设置更长的延迟响应时间，以模拟实际网络环境
  const mock = new MockAdapter(service, {
    delayResponse: 500,
    onNoMatch: 'passthrough'
  })

  // 同时拦截两种不同形式的用户信息API，确保兼容性
  mock.onGet(/\/api\/users\/info/).reply(config => {
    console.log('模拟API - 拦截到用户信息请求:', config.url)

    // 从请求头中获取token
    const token = config.headers['Authorization']
    if (!token) {
      // 尝试从URL中获取token
      const urlToken = extractTokenFromUrl(config.url)
      if (urlToken) {
        console.log('模拟API - 从URL中提取到token:', urlToken)
        return generateUserInfoResponse(urlToken)
      }

      return [401, { message: '请先登录' }]
    }

    return generateUserInfoResponse(token)
  })

  // 拦截用户信息API的另一种形式
  mock.onGet('/api/user/info').reply(config => {
    console.log('模拟API - 拦截到用户信息请求(旧格式):', config.url)

    // 从请求头中获取token
    const token = config.headers['Authorization']
    if (!token) {
      // 尝试从URL中获取token
      const urlToken = extractTokenFromUrl(config.url)
      if (urlToken) {
        console.log('模拟API - 从URL中提取到token:', urlToken)
        return generateUserInfoResponse(urlToken)
      }

      return [401, { message: '请先登录' }]
    }

    return generateUserInfoResponse(token)
  })

  // 模拟个人信息API
  mock.onGet(new RegExp('/api/users/\\d+')).reply(function(config) {
    const token = config.headers['Authorization']
    if (!token) {
      return [401, { message: '请先登录' }]
    }

    // 从URL中提取用户ID
    const urlParts = config.url.split('/')
    const userId = parseInt(urlParts[urlParts.length - 1], 10)

    // 确保用户ID是当前登录用户或管理员
    const currentUserId = store.getters.userId
    const isAdmin = store.getters.roles && store.getters.roles.includes('admin')

    console.log('模拟API - 个人信息请求:', {
      请求的用户ID: userId,
      当前用户ID: currentUserId,
      是否管理员: isAdmin
    })

    if (userId !== currentUserId && !isAdmin) {
      console.log('模拟API - 权限检查失败，非管理员用户尝试查看其他用户信息')
      return [403, {
        code: 40300,
        message: '您没有权限查看此用户信息',
        status: 'error'
      }]
    }

    // 从缓存中获取用户信息
    const userInfo = getUserFromCache(userId)
    console.log(`获取用户ID=${userId}的信息:`, userInfo)

    // 如果找不到用户信息
    if (!userInfo || !userInfo.id) {
      console.log('模拟API - 用户不存在:', userId)
      return [404, {
        code: 40400,
        message: '用户不存在',
        status: 'error'
      }]
    }

    return [200, {
      status: 'success',
      code: 20000,
      data: {
        user: userInfo
      },
      message: '获取用户信息成功'
    }]
  })

  // 模拟更新用户信息API
  mock.onPut(new RegExp('/api/users/\\d+')).reply(function(config) {
    const token = config.headers['Authorization']
    if (!token) {
      return [401, { message: '请先登录' }]
    }

    // 从URL中提取用户ID
    const urlParts = config.url.split('/')
    const userId = parseInt(urlParts[urlParts.length - 1], 10)

    // 获取当前登录用户ID和角色
    const currentUserId = store.getters.userId
    const isAdmin = store.getters.roles && store.getters.roles.includes('admin')

    console.log(`更新用户信息请求 - 目标用户ID: ${userId}, 当前用户ID: ${currentUserId}, 是否管理员: ${isAdmin}`)

    // 验证权限
    if (userId !== currentUserId && !isAdmin) {
      return [403, { message: '您没有权限修改此用户信息' }]
    }

    // 解析请求数据
    let updateData = {}
    try {
      updateData = JSON.parse(config.data)
      console.log('更新用户信息数据:', updateData)
    } catch (e) {
      console.error('解析用户更新数据失败:', e)
      return [400, { message: '无效的请求数据' }]
    }

    // 标准化更新数据
    const standardData = standardizeFields(updateData, true)

    // 验证必填字段
    if (standardData.name && standardData.name.trim() === '') {
      return [422, {
        message: '参数验证失败',
        errors: { 'name': ['姓名不能为空'] }
      }]
    }

    if (standardData.phone && !/^1[3-9]\d{9}$/.test(standardData.phone)) {
      return [422, {
        message: '参数验证失败',
        errors: { 'phone': ['请输入正确的手机号码'] }
      }]
    }

    if (standardData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(standardData.email)) {
      return [422, {
        message: '参数验证失败',
        errors: { 'email': ['请输入正确的邮箱地址'] }
      }]
    }

    // 更新用户信息到缓存
    const updatedUser = updateUserInCache(userId, standardData)

    console.log('用户信息更新成功:', updatedUser)

    return [200, {
      status: 'success',
      code: 20000,
      data: {
        user: updatedUser
      },
      message: '用户信息更新成功'
    }]
  })

  // 模拟个人信息页面API
  mock.onGet('/api/profile').reply(function(config) {
    const token = config.headers['Authorization']
    if (!token) {
      return [401, { message: '请先登录' }]
    }

    const userId = parseInt(store.getters.userId || 1, 10)

    // 从缓存中获取用户信息
    const userInfo = getUserFromCache(userId)

    return [200, {
      status: 'success',
      code: 20000,
      data: {
        profile: {
          ...userInfo,
          bikes_count: (mockBikesCache[userId] || []).length
        }
      },
      message: '获取个人信息成功'
    }]
  })

  // 模拟用户登录接口
  mock.onPost('/api/user/login').reply(function(config) {
    console.log('模拟API - 收到登录请求', config.data)

    // 解析POST数据
    const postData = JSON.parse(config.data)
    const { username, password } = postData

    // 检查用户名和密码 (模拟验证)
    if (username === 'admin' && password === '111111') {
      // 管理员用户登录
      return [200, {
        code: 20000,
        status: 'success',
        data: {
          token: 'admin-token',
          user: {
            id: 1,
            username: 'admin',
            name: 'Admin',
            role: 'admin',
            roles: ['admin']
          }
        },
        message: '登录成功'
      }]
    } else if (username === 'aaa' && password === '123123') {
      // aaa用户登录 - 设置为管理员
      return [200, {
        code: 20000,
        status: 'success',
        data: {
          token: 'aba-token',
          user: {
            id: 3,
            username: 'aaa',
            name: 'AAA管理员',
            role: 'admin',
            roles: ['admin']
          }
        },
        message: '登录成功'
      }]
    } else if (username && password) {
      // 普通用户登录，生成简单的用户ID
      const userId = parseInt(username.replace(/\D/g, '')) || Math.floor(Math.random() * 1000) + 10
      return [200, {
        code: 20000,
        status: 'success',
        data: {
          token: 'user-token-' + userId,
          user: {
            id: userId,
            username: username,
            name: 'User ' + userId,
            role: 'user',
            roles: ['user']
          }
        },
        message: '登录成功'
      }]
    } else {
      // 用户名或密码为空
      return [400, {
        code: 40000,
        status: 'error',
        message: '用户名和密码不能为空'
      }]
    }
  })

  return mock
}
