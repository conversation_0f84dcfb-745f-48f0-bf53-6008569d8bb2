from marshmallow_sqlalchemy import SQLAlchemyAutoSchema
from marshmallow import fields, validates, ValidationError, post_load, Schema, validate
from app.users.models import Users
from app.bikes.schema import BikeSchema
from app import db
import re

# 将后端数据序列化为json格式，方便与前端接口进行数据交互

# 序列接口数据为JSON格式
class UserSchema(SQLAlchemyAutoSchema):
    class Meta:
        model = Users
        sqla_session = db.session
        include_relationships = True  # 包含关联关系
        load_instance = False  # 支持将数据加载为模型实例 or 字典（json）

    # 序列化中要求required=True的字段，在post过程中必须提供！！！即初始化时必须赋值！！！
    u_id = fields.Integer(dump_only=True)
    u_name = fields.String(required=True, validate=validate.Length(min=3, max=50))
    # load_only=True,当进行序列化操作(加密),这个字段不会被包含在输出结果中(适用于处理敏感数据)
    u_pwd = fields.String(load_only=True)
    u_role = fields.String(validate=validate.OneOf(['admin', 'user']), default='user')
    u_belong = fields.String(validate=validate.Length(max=50))  # 是哪个学院的
    u_phone = fields.String(validate=validate.Length(max=20))
    u_email = fields.String(validate=validate.Email())
    avatar = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    version = fields.Integer(dump_only=True)  # 版本控制字段

    bikes = fields.Nested(BikeSchema, many=True, required=False, dump_only=True)  # Users 模型有一个 bikes 关联字段:b.belong_to -> u_id

    @validates('u_phone')
    def validate_phone(self, value):
        """验证手机号格式"""
        if value and not re.match(r'^\d{11}$', value):
            raise ValidationError('手机号格式不正确，应为11位数字')

    @post_load
    def ensure_defaults(self, data, **kwargs):
        """确保所有字段都有默认值"""
        if 'u_role' not in data:
            data['u_role'] = 'user'
            
        return data

# 用户更新Schema（不包含敏感字段和只读字段）
class UserUpdateSchema(Schema):
    """用户信息更新模式"""
    u_name = fields.Str(allow_none=True, allow_empty=True, validate=validate.Length(max=50))
    u_belong = fields.Str(allow_none=True, allow_empty=True, validate=validate.Length(max=50))
    u_phone = fields.Str(allow_none=True, allow_empty=True, validate=validate.Length(max=20))
    u_email = fields.Str(allow_none=True, allow_empty=True)  # 使用Str而不是Email，允许为空
    u_role = fields.Str(allow_none=True, allow_empty=True, validate=validate.OneOf(['admin', 'user']))
    avatar = fields.Str(allow_none=True)
    
    @validates('u_phone')
    def validate_phone(self, value):
        """验证手机号格式"""
        if value and not re.match(r'^\d{11}$', value):
            raise ValidationError('手机号格式不正确，应为11位数字')
            
    @validates('u_email')
    def validate_email(self, value):
        """验证邮箱格式，但允许为空"""
        if value and not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', value):
            raise ValidationError('邮箱格式不正确')

# 管理员使用的用户更新Schema
class AdminUserUpdateSchema(UserUpdateSchema):
    u_role = fields.String(validate=validate.OneOf(['admin', 'user']))

