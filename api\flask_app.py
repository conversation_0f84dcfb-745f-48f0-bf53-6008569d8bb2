#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Flask应用入口文件
专门为flask run命令设计
"""

# 在导入任何其他模块之前执行eventlet的monkey patching
import eventlet
eventlet.monkey_patch()

import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from flask import Flask, request, jsonify, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_marshmallow import Marshmallow
from flask_cors import CORS
from flask_migrate import Migrate
from flask_jwt_extended import JWTManager
from flask_socketio import SocketIO
from datetime import timedelta

# 将项目根目录加入系统路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 导入配置
from app.config import Config, log_dir
from app.utils.log import DayRotatingHandler
from app.utils.response import api_response

# 初始化扩展
db = SQLAlchemy()
jwt = JWTManager()
cors = CORS()
migrate = Migrate()
marshmallow = Marshmallow()
socketio = SocketIO()

# 创建应用实例
app = Flask(__name__, static_folder='../dist', template_folder="../dist", static_url_path='/')
app.config.from_object(Config)

# 配置文件上传
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 限制上传文件大小为16MB
app.config['UPLOAD_FOLDER'] = os.path.join(app.root_path, 'static', 'uploads')

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 注册静态文件路由
@app.route('/uploads/<path:filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

# 在插件注册前初始化数据库
db.init_app(app)
migrate.init_app(app, db)

# 设置JWT配置
app.config["JWT_SECRET_KEY"] = "f8s9df89s7df9s87df98s7df98sd7f9s8df"  # 在生产环境中应使用环境变量
app.config["JWT_IDENTITY_CLAIM"] = "identity"  # 使用"identity"作为用户标识声明
app.config["JWT_ACCESS_TOKEN_EXPIRES"] = timedelta(hours=24)  # 设置令牌24小时过期
app.config["JWT_TOKEN_LOCATION"] = ["headers", "query_string"]  # 允许从请求头和查询参数获取令牌
app.config["JWT_HEADER_NAME"] = "Authorization"  # 请求头名称
app.config["JWT_HEADER_TYPE"] = "Bearer"  # 令牌类型前缀
app.config["JWT_QUERY_STRING_NAME"] = "token"  # 查询参数名称
app.config["JWT_ERROR_MESSAGE_KEY"] = "message"  # 错误消息的键名
app.config["JWT_BLACKLIST_ENABLED"] = False  # 暂不启用黑名单功能

# 注册插件
cors.init_app(
    app,
    supports_credentials=True,
    resources={
        r"/*": {
            "origins": "*",
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization", "Accept", "X-Requested-With", "Cache-Control"]
        }
    }
)

# 添加CORS响应头
@app.after_request
def add_cors_headers(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-Requested-With,Cache-Control')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

marshmallow.init_app(app)
jwt.init_app(app)

# 初始化SocketIO，并设置完整配置
socketio.init_app(app, cors_allowed_origins="*", async_mode='eventlet', logger=True, engineio_logger=True, ping_timeout=60, ping_interval=25)

# 注册蓝图
from app.users import users_bp, users
app.register_blueprint(users_bp, url_prefix='/api')
app.register_blueprint(users)  # 这个蓝图已经包含了url_prefix

from app.bikes import bikes_bp
app.register_blueprint(bikes_bp, url_prefix='/api')

from app.players import players_bp
app.register_blueprint(players_bp, url_prefix='/api')

# 注册停车场管理相关蓝图
from app.parkinglots import parkinglots_bp
app.register_blueprint(parkinglots_bp)

from app.parking_records import parking_records_bp
app.register_blueprint(parking_records_bp, url_prefix='/api/parking-records')

# 注册WebSocket蓝图
try:
    from app.websocket import websocket_bp
    app.register_blueprint(websocket_bp, url_prefix='/api/ws')
    app.logger.info('WebSocket蓝图注册成功')
except ImportError as e:
    app.logger.error(f'WebSocket蓝图注册失败: {str(e)}')

# 注册违规管理相关蓝图
try:
    from app.violations import violations_bp
    app.register_blueprint(violations_bp)  # 不添加url_prefix，因为蓝图已经定义了前缀
    app.logger.info('违规管理蓝图注册成功')
except ImportError as e:
    app.logger.error(f'违规管理蓝图注册失败: {str(e)}')

# 注册充电管理相关蓝图
try:
    from app.charging import charging_bp
    app.register_blueprint(charging_bp, url_prefix='/api')
    app.logger.info('充电管理蓝图注册成功')
except ImportError as e:
    app.logger.error(f'充电管理蓝图注册失败: {str(e)}')

# 注册公告蓝图
from app.announcements.routes import announcements as announcements_blueprint
app.register_blueprint(announcements_blueprint, url_prefix='/api/announcements')

# 这个文件不会直接运行，而是通过flask run命令运行
# 所以不需要app.run()调用
