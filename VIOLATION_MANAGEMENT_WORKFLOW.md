# 校园电动车管理系统 - 违规管理完整流程

## 一、违规管理总体流程架构

### 1.1 角色权限分工
- **保安**：违规录入、查看自己录入的记录
- **管理员**：处理违规、管理申诉、车辆禁用、统计分析
- **用户**：查看自己的违规记录、提交申诉、查看公示信息

### 1.2 流程概览
```
违规发现 → 违规录入 → 违规审核 → 违规处理 → 申诉提交 → 申诉处理 → 结果执行
```

## 二、保安违规录入流程

### 2.1 违规录入完整流程
```
保安登录 → 进入违规中心 → 点击违规录入 → 查询车辆信息 → 填写违规表单 → 确认违规信息 → 提交违规记录 → 录入成功 → 查看录入记录
```

### 2.2 详细操作步骤

#### **步骤1：保安登录系统**
- 使用保安账号登录系统
- 验证身份和权限

#### **步骤2：进入违规录入页面**
- 导航路径：`违规中心 → 违规录入`
- 页面路由：`/violation/security/create`
- 权限验证：仅保安和管理员可访问

#### **步骤3：查询车辆和车主信息**
- 输入车牌号查询车辆信息
- 获取车主用户ID和基本信息
- 验证车辆是否存在于系统中

#### **步骤4：填写违规记录表单**
- **必填信息**：
  - 车牌号（自动填充或手动输入）
  - 车主ID（查询获得）
  - 违规类型（下拉选择）
  - 违规时间（默认当前时间，可修改）
  - 违规地点（手动输入）
- **可选信息**：
  - 违规描述（详细说明）
  - 录入人姓名（默认当前用户）

#### **步骤5：确认违规信息**
- 系统显示确认对话框
- 核对所有填写信息
- 确认无误后提交

#### **步骤6：提交违规记录**
- 调用API：`POST /api/violations/records`
- 系统自动设置录入人ID
- 根据违规类型设置初始状态：
  - 需要管理员审核：状态为"待审核"(0)
  - 无需审核：状态为"已处理"(1)

#### **步骤7：查看录入结果**
- 显示录入成功提示
- 可选择继续录入或查看记录列表
- 导航到：`/violation/security/records`

## 三、管理员违规处理流程

### 3.1 违规处理完整流程
```
管理员登录 → 进入违规管理 → 查看待处理违规 → 选择违规记录 → 查看违规详情 → 处理违规记录 → 设置处理结果 → 可选车辆禁用 → 保存处理结果 → 通知相关用户
```

### 3.2 详细操作步骤

#### **步骤1：管理员登录系统**
- 使用管理员账号登录
- 验证管理员权限

#### **步骤2：进入违规管理页面**
- 导航路径：`违规中心 → 违规管理`
- 页面路由：`/violation/admin/management`
- 显示所有违规记录列表

#### **步骤3：筛选待处理违规**
- 按状态筛选：待审核(0)、已处理(1)、申诉中(2)、已撤销(3)
- 按时间范围筛选
- 按违规类型筛选
- 按录入人筛选

#### **步骤4：查看违规详情**
- 点击违规记录查看详情
- 页面路由：`/violation/admin/detail/{id}`
- 显示完整违规信息和相关证据

#### **步骤5：处理违规记录**
- 点击"处理违规"按钮
- 填写处理表单：
  - **处理状态**：已处理(1) / 已撤销(3)
  - **处理结果**：详细处理说明
  - **是否禁用车辆**：可选
  - **禁用天数**：如果选择禁用（默认7天）

#### **步骤6：车辆禁用处理（可选）**
- 如果选择禁用车辆：
  - 创建车辆禁用记录
  - 更新车辆状态为"废弃"
  - 设置禁用期限
  - 影响车辆的所有业务操作

#### **步骤7：保存处理结果**
- 调用API：`PUT /api/violations/admin/records/{id}`
- 更新违规记录状态和处理信息
- 记录处理人ID和处理时间

#### **步骤8：系统通知**
- 自动通知车主处理结果
- 如有车辆禁用，发送禁用通知

## 四、用户申诉提交流程

### 4.1 申诉提交完整流程
```
用户登录 → 查看我的违规 → 选择可申诉记录 → 点击申诉按钮 → 填写申诉表单 → 上传申诉证据 → 确认申诉信息 → 提交申诉 → 等待处理结果
```

### 4.2 详细操作步骤

#### **步骤1：用户登录系统**
- 使用普通用户账号登录
- 验证用户身份

#### **步骤2：查看个人违规记录**
- 导航路径：`个人中心 → 我的违规`
- 页面路由：`/profile/violations`
- 显示当前用户的所有违规记录

#### **步骤3：选择可申诉的违规记录**
- 申诉条件：
  - 违规状态为"已处理"(1)
  - 尚未提交过申诉
  - 在申诉期限内

#### **步骤4：进入申诉页面**
- 点击"申诉"按钮
- 页面路由：`/violation/user/appeal/{violation_id}`
- 显示违规详情和申诉表单

#### **步骤5：填写申诉表单**
- **必填信息**：
  - 申诉理由（10-500字符）
- **可选信息**：
  - 上传申诉证据（图片/视频）
  - 补充说明

#### **步骤6：上传申诉证据**
- 支持图片格式：jpg, jpeg, png, gif
- 支持视频格式：mp4, avi, mov
- 文件大小限制：图片≤5MB，视频≤50MB
- 最多上传5个文件

#### **步骤7：确认并提交申诉**
- 检查申诉信息完整性
- 确认提交申诉
- 调用API：`POST /api/violations/appeals`

#### **步骤8：申诉提交成功**
- 显示提交成功提示
- 违规记录状态更新为"申诉中"(2)
- 返回违规详情页面查看申诉状态

## 五、管理员申诉处理流程

### 5.1 申诉处理完整流程
```
管理员登录 → 进入违规管理 → 切换申诉标签 → 查看待处理申诉 → 选择申诉记录 → 查看申诉详情 → 审核申诉材料 → 做出处理决定 → 填写处理意见 → 保存处理结果 → 同步更新违规状态
```

### 5.2 详细操作步骤

#### **步骤1：进入申诉管理**
- 在违规管理页面切换到"申诉管理"标签
- 显示所有申诉记录列表

#### **步骤2：筛选待处理申诉**
- 按申诉状态筛选：待审核(0)、已通过(1)、未通过(2)
- 按提交时间筛选
- 按关联违规记录筛选

#### **步骤3：查看申诉详情**
- 点击申诉记录查看详情
- 显示申诉理由和上传的证据
- 显示原违规记录信息

#### **步骤4：审核申诉材料**
- 查看申诉理由是否合理
- 检查上传的证据文件
- 对比原违规记录信息

#### **步骤5：做出处理决定**
- **申诉通过**：认为申诉理由成立
- **申诉未通过**：认为申诉理由不成立

#### **步骤6：填写处理意见**
- 详细说明处理理由
- 处理意见将通知给申诉用户

#### **步骤7：保存处理结果**
- 调用API：`PUT /api/violations/admin/appeals/{id}`
- 更新申诉状态和处理信息
- 记录处理人ID和处理时间

#### **步骤8：同步更新违规状态**
- **申诉通过**：违规记录状态更新为"已撤销"(3)
- **申诉未通过**：违规记录状态恢复为"已处理"(1)

## 六、公开违规查询流程

### 6.1 公开查询完整流程
```
任意用户访问 → 进入违规公示 → 查看公开违规列表 → 搜索特定记录 → 查看违规详情 → 了解处理结果
```

### 6.2 详细操作步骤

#### **步骤1：访问违规公示页面**
- 导航路径：`违规中心 → 违规公示`
- 页面路由：`/violation/public/records`
- 无需登录即可访问

#### **步骤2：浏览公开违规记录**
- 显示已处理的违规记录
- 按时间倒序排列
- 支持分页浏览

#### **步骤3：搜索特定记录**
- 按车牌号搜索
- 按违规类型筛选
- 按时间范围筛选

#### **步骤4：查看违规详情**
- 点击记录查看详情
- 页面路由：`/violation/public/detail/{id}`
- 显示违规信息和处理结果

## 七、状态流转机制

### 7.1 违规记录状态流转
```
录入 → 待审核(0) → 已处理(1) → 申诉中(2) → 已通过(撤销3) / 未通过(恢复1)
      ↓
   直接处理(1)
```

### 7.2 申诉记录状态流转
```
提交 → 待审核(0) → 已通过(1) / 未通过(2)
```

### 7.3 车辆状态影响
```
违规处理 → 车辆禁用 → 影响停车/充电操作 → 禁用期满自动恢复
```

## 八、系统集成点

### 8.1 与其他模块的关联
- **用户管理**：验证车主身份和权限
- **车辆管理**：关联车辆信息和状态
- **停车管理**：禁用车辆影响停车操作
- **充电管理**：禁用车辆影响充电操作

### 8.2 数据同步机制
- 违规处理结果实时同步到车辆状态
- 申诉处理结果自动更新违规记录状态
- 车辆禁用状态影响所有业务模块

这个完整的违规管理流程涵盖了从违规发现到最终处理的全过程，确保了系统的规范性和用户体验的完整性。
