<template>
  <div class="app-container charging-records-container">
    <permission-wrapper :allowed-roles="['admin']" message="您没有权限访问充电记录管理数据，此功能仅对管理员开放">
      <el-card class="filter-container">
        <div class="filter-item">
          <el-form :inline="true" :model="listQuery" class="demo-form-inline">
            <el-form-item label="停车场">
              <el-select v-model="listQuery.parking_lot_id" placeholder="选择停车场" clearable @change="handleFilter">
                <el-option
                  v-for="item in parkingLots"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="listQuery.status" placeholder="选择状态" clearable @change="handleFilter">
                <el-option label="进行中" :value="0" />
                <el-option label="已完成" :value="1" />
                <el-option label="异常" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="车牌号">
              <el-input v-model="listQuery.vehicle_number" placeholder="输入车牌号" clearable @keyup.enter.native="handleFilter" />
            </el-form-item>
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleDateRangeChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleFilter">查询</el-button>
              <el-button type="default" @click="resetFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

    <el-card class="list-container">
      <div class="list-header">
        <div class="header-title">
          <i class="el-icon-tickets"></i>
          <span>充电记录列表</span>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="handleExport">导出数据</el-button>
          <el-button type="text" @click="fetchData">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="加载中..."
        border
        fit
        highlight-current-row
        :header-cell-style="{background:'#f5f7fa'}"
        @sort-change="handleSortChange"
      >
        <el-table-column
          align="center"
          label="ID"
          width="80"
          prop="id"
          sortable="custom"
        >
          <template slot-scope="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="车辆信息"
          width="150"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.vehicle">
              {{ scope.row.vehicle.number }}<br>
              <small>{{ scope.row.vehicle.brand }} {{ scope.row.vehicle.color }}</small>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="用户信息"
          width="150"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.user">
              {{ scope.row.user.name }}
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="停车场及车位"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.parking_lot">
              {{ scope.row.parking_lot.name }}<br>
              <small>车位: {{ scope.row.parking_space ? scope.row.parking_space.space_number : '-' }}</small>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="开始时间"
          width="180"
          prop="start_time"
          sortable="custom"
        >
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.start_time) }}
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="结束时间"
          width="180"
          prop="end_time"
          sortable="custom"
        >
          <template slot-scope="scope">
            {{ scope.row.end_time ? formatDateTime(scope.row.end_time) : '-' }}
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="充电时长"
          width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.duration_formatted || '-' }}
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="状态"
          width="100"
        >
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status_text }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="操作"
          width="150"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="listQuery.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="listQuery.per_page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </el-card>

    <!-- 充电记录详情对话框 -->
    <el-dialog title="充电记录详情" :visible.sync="detailDialogVisible" width="600px">
      <div v-if="detailData" class="record-detail">
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="detail-item">
            <span class="label">记录ID：</span>
            <span class="value">{{ detailData.id }}</span>
          </div>
          <div class="detail-item">
            <span class="label">状态：</span>
            <span class="value">
              <el-tag :type="getStatusType(detailData.status)">
                {{ detailData.status_text }}
              </el-tag>
            </span>
          </div>
          <div class="detail-item">
            <span class="label">开始时间：</span>
            <span class="value">{{ formatDateTime(detailData.start_time) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">结束时间：</span>
            <span class="value">{{ detailData.end_time ? formatDateTime(detailData.end_time) : '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">充电时长：</span>
            <span class="value">{{ detailData.duration_formatted || '-' }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h3>车辆信息</h3>
          <div v-if="detailData.vehicle" class="detail-item">
            <span class="label">车牌号：</span>
            <span class="value">{{ detailData.vehicle.number }}</span>
          </div>
          <div v-if="detailData.vehicle" class="detail-item">
            <span class="label">品牌：</span>
            <span class="value">{{ detailData.vehicle.brand }}</span>
          </div>
          <div v-if="detailData.vehicle" class="detail-item">
            <span class="label">颜色：</span>
            <span class="value">{{ detailData.vehicle.color }}</span>
          </div>
          <div v-if="detailData.vehicle" class="detail-item">
            <span class="label">车辆ID：</span>
            <span class="value">{{ detailData.vehicle.b_id }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h3>用户信息</h3>
          <div v-if="detailData.user" class="detail-item">
            <span class="label">用户名：</span>
            <span class="value">{{ detailData.user.name }}</span>
          </div>
          <div v-if="detailData.user" class="detail-item">
            <span class="label">联系电话：</span>
            <span class="value">{{ detailData.user.phone || '-' }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h3>停车场信息</h3>
          <div v-if="detailData.parking_lot" class="detail-item">
            <span class="label">停车场：</span>
            <span class="value">{{ detailData.parking_lot.name }}</span>
          </div>
          <div v-if="detailData.parking_space" class="detail-item">
            <span class="label">车位编号：</span>
            <span class="value">{{ detailData.parking_space.space_number }}</span>
          </div>
        </div>

        <div v-if="detailData.exception_info" class="detail-section">
          <h3>异常信息</h3>
          <div class="detail-item">
            <span class="label">异常类型：</span>
            <span class="value">{{ getExceptionTypeName(detailData.exception_info.type) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">异常时间：</span>
            <span class="value">{{ formatDateTime(detailData.exception_info.time) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">异常描述：</span>
            <span class="value">{{ detailData.exception_info.description }}</span>
          </div>
          <div class="detail-item">
            <span class="label">处理状态：</span>
            <span class="value">{{ detailData.exception_info.handled ? '已处理' : '未处理' }}</span>
          </div>
          <div v-if="detailData.exception_info.handled" class="detail-item">
            <span class="label">处理时间：</span>
            <span class="value">{{ formatDateTime(detailData.exception_info.handle_time) }}</span>
          </div>
          <div v-if="detailData.exception_info.handled" class="detail-item">
            <span class="label">处理人：</span>
            <span class="value">{{ detailData.exception_info.handler }}</span>
          </div>
          <div v-if="detailData.exception_info.handled" class="detail-item">
            <span class="label">处理结果：</span>
            <span class="value">{{ detailData.exception_info.handle_result }}</span>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button v-if="detailData && detailData.status === 0" type="warning" @click="handleEndCharging(detailData)">结束充电</el-button>
        <el-button v-if="detailData && detailData.status === 0" type="danger" @click="handleMarkException(detailData)">标记异常</el-button>
      </div>
    </el-dialog>

    <!-- 编辑充电记录对话框 -->
    <el-dialog title="编辑充电记录" :visible.sync="editDialogVisible" width="600px">
      <el-form ref="editForm" :rules="editRules" :model="editTemp" label-position="right" label-width="120px">
        <el-form-item label="记录ID">
          <span>{{ editTemp.id }}</span>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select v-model="editTemp.status" class="filter-item" placeholder="请选择状态">
            <el-option label="进行中" :value="0" />
            <el-option label="已完成" :value="1" />
            <el-option label="异常" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="开始时间" prop="start_time">
          <el-date-picker
            v-model="editTemp.start_time"
            type="datetime"
            placeholder="选择开始时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="结束时间" prop="end_time">
          <el-date-picker
            v-model="editTemp.end_time"
            type="datetime"
            placeholder="选择结束时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="editTemp.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <div v-if="editTemp.status === 2">
          <el-divider content-position="left">异常信息</el-divider>

          <el-form-item label="异常类型" prop="exception_type">
            <el-select v-model="editTemp.exception_type" class="filter-item" placeholder="请选择异常类型">
              <el-option label="充电接口故障" value="connector" />
              <el-option label="设备无法启动" value="startup" />
              <el-option label="充电中断" value="interruption" />
              <el-option label="其他故障" value="other" />
            </el-select>
          </el-form-item>

          <el-form-item label="异常描述" prop="exception_description">
            <el-input
              v-model="editTemp.exception_description"
              type="textarea"
              :rows="3"
              placeholder="请输入异常描述"
            />
          </el-form-item>

          <el-form-item label="是否已处理" prop="exception_handled">
            <el-switch v-model="editTemp.exception_handled" />
          </el-form-item>

          <el-form-item v-if="editTemp.exception_handled" label="处理人" prop="exception_handler">
            <el-input v-model="editTemp.exception_handler" placeholder="请输入处理人姓名" />
          </el-form-item>

          <el-form-item v-if="editTemp.exception_handled" label="处理结果" prop="exception_handle_result">
            <el-input
              v-model="editTemp.exception_handle_result"
              type="textarea"
              :rows="3"
              placeholder="请输入处理结果"
            />
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEdit">保存</el-button>
      </div>
    </el-dialog>

    <!-- 删除充电记录确认对话框 -->
    <el-dialog title="删除充电记录" :visible.sync="deleteDialogVisible" width="400px">
      <div class="delete-confirm">
        <p>确定要删除该充电记录吗？</p>
        <p class="warning">此操作不可逆，请谨慎操作！</p>
        <div v-if="deleteTemp">
          <p><strong>记录ID：</strong>{{ deleteTemp.id }}</p>
          <p><strong>车辆信息：</strong>{{ deleteTemp.vehicle ? deleteTemp.vehicle.number : '-' }}</p>
          <p><strong>用户信息：</strong>{{ deleteTemp.user ? deleteTemp.user.name : '-' }}</p>
          <p><strong>开始时间：</strong>{{ formatDateTime(deleteTemp.start_time) }}</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete">确认删除</el-button>
      </div>
    </el-dialog>

    <!-- 标记异常对话框 -->
    <el-dialog title="标记充电异常" :visible.sync="exceptionDialogVisible" width="500px">
      <el-form ref="exceptionForm" :rules="exceptionRules" :model="exceptionTemp" label-position="right" label-width="110px">
        <el-form-item label="异常类型" prop="type">
          <el-select v-model="exceptionTemp.type" class="filter-item" placeholder="请选择异常类型">
            <el-option label="充电接口故障" value="connector" />
            <el-option label="设备无法启动" value="startup" />
            <el-option label="充电中断" value="interruption" />
            <el-option label="其他故障" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="异常描述" prop="description">
          <el-input
            v-model="exceptionTemp.description"
            type="textarea"
            :rows="3"
            placeholder="请输入异常描述"
          />
        </el-form-item>
        <el-form-item label="处理方式" prop="handle_immediately">
          <el-radio-group v-model="exceptionTemp.handle_immediately">
            <el-radio :label="true">立即处理</el-radio>
            <el-radio :label="false">稍后处理</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="exceptionTemp.handle_immediately" label="处理人" prop="handler">
          <el-input v-model="exceptionTemp.handler" placeholder="请输入处理人姓名" />
        </el-form-item>
        <el-form-item v-if="exceptionTemp.handle_immediately" label="处理结果" prop="handle_result">
          <el-input
            v-model="exceptionTemp.handle_result"
            type="textarea"
            :rows="3"
            placeholder="请输入处理结果"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exceptionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitException">确认</el-button>
      </div>
    </el-dialog>
    </permission-wrapper>
  </div>
</template>

<script>
import { getParkingLots } from '@/api/parkinglot'
import {
  getChargingRecords,
  getChargingRecordDetail,
  endChargingRecord,
  markChargingException,
  exportChargingRecords,
  updateChargingRecord,
  deleteChargingRecord
} from '@/api/charging'
import PermissionWrapper from '@/components/PermissionWrapper'

export default {
  name: 'ChargingRecords',
  components: {
    PermissionWrapper
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        per_page: 10,
        parking_lot_id: undefined,
        status: undefined,
        vehicle_number: undefined,
        start_date: undefined,
        end_date: undefined,
        sort_field: 'id',
        sort_order: 'asc'
      },
      dateRange: null,
      parkingLots: [],
      detailDialogVisible: false,
      detailData: null,

      // 编辑对话框相关数据
      editDialogVisible: false,
      editTemp: {
        id: undefined,
        status: 0,
        start_time: '',
        end_time: '',
        remarks: '',
        exception_type: '',
        exception_description: '',
        exception_handled: false,
        exception_handler: '',
        exception_handle_result: ''
      },
      editRules: {
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        start_time: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
        exception_type: [{ required: true, message: '请选择异常类型', trigger: 'change' }],
        exception_description: [{ required: true, message: '请输入异常描述', trigger: 'blur' }],
        exception_handler: [{ required: true, message: '请输入处理人姓名', trigger: 'blur' }],
        exception_handle_result: [{ required: true, message: '请输入处理结果', trigger: 'blur' }]
      },

      // 删除对话框相关数据
      deleteDialogVisible: false,
      deleteTemp: null,

      // 异常对话框相关数据
      exceptionDialogVisible: false,
      exceptionTemp: {
        record_id: undefined,
        type: '',
        description: '',
        handle_immediately: false,
        handler: '',
        handle_result: ''
      },
      exceptionRules: {
        type: [{ required: true, message: '请选择异常类型', trigger: 'change' }],
        description: [{ required: true, message: '请输入异常描述', trigger: 'blur' }],
        handler: [{ required: true, message: '请输入处理人姓名', trigger: 'blur' }],
        handle_result: [{ required: true, message: '请输入处理结果', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.fetchParkingLots()
    this.fetchData()
  },
  methods: {
    // 获取停车场列表
    async fetchParkingLots() {
      try {
        const response = await getParkingLots()
        this.parkingLots = response.data.items || []
      } catch (error) {
        console.error('获取停车场列表失败:', error)
        this.$message.error('获取停车场列表失败')
      }
    },
    // 获取充电记录列表
    async fetchData() {
      this.listLoading = true
      try {
        const params = { ...this.listQuery }
        console.log('获取充电记录列表，参数:', params)
        const response = await getChargingRecords(params)
        console.log('充电记录列表响应:', response)

        // 确保获取到的数据是正确的
        if (response.data && response.data.items) {
          this.list = response.data.items
          this.total = response.data.total || 0

          // 打印每条记录的关键信息，用于调试
          this.list.forEach((record, index) => {
            console.log(`记录 ${index+1}:`, {
              id: record.id,
              status: record.status,
              status_text: record.status_text,
              start_time: record.start_time,
              end_time: record.end_time,
              duration: record.duration,
              duration_formatted: record.duration_formatted
            })
          })
        } else {
          console.error('充电记录数据格式不正确:', response)
          this.list = []
          this.total = 0
          this.$message.warning('获取充电记录数据格式不正确')
        }
      } catch (error) {
        console.error('获取充电记录列表失败:', error)
        this.$message.error('获取充电记录列表失败')
      } finally {
        this.listLoading = false
      }
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'primary',  // 进行中
        1: 'success',  // 已完成
        2: 'danger'    // 异常
      }
      return statusMap[status] || 'info'
    },
    // 获取异常类型名称
    getExceptionTypeName(type) {
      const typeMap = {
        'connector': '充电接口故障',
        'startup': '设备无法启动',
        'interruption': '充电中断',
        'other': '其他故障',
        // 兼容旧数据
        'charging_gun': '充电接口故障',
        'communication': '设备无法启动',
        'power': '充电中断',
        'software': '其他故障',
        'user_operation': '其他故障'
      }
      return typeMap[type] || type
    },
    // 格式化日期时间
    formatDateTime(dateStr) {
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    // 处理日期范围变化
    handleDateRangeChange(val) {
      if (val) {
        this.listQuery.start_date = val[0]
        this.listQuery.end_date = val[1]
      } else {
        this.listQuery.start_date = undefined
        this.listQuery.end_date = undefined
      }
    },
    // 处理筛选
    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    },
    // 重置筛选条件
    resetFilter() {
      this.listQuery = {
        page: 1,
        per_page: 10,
        parking_lot_id: undefined,
        status: undefined,
        vehicle_number: undefined,
        start_date: undefined,
        end_date: undefined,
        sort_field: 'id',
        sort_order: 'asc'
      }
      this.dateRange = null
      this.fetchData()
    },
    // 处理页面大小变化
    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchData()
    },
    // 处理页码变化
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.fetchData()
    },

    // 处理表格排序变化
    handleSortChange({ column, prop, order }) {
      if (order === 'ascending') {
        this.listQuery.sort_field = prop
        this.listQuery.sort_order = 'asc'
      } else if (order === 'descending') {
        this.listQuery.sort_field = prop
        this.listQuery.sort_order = 'desc'
      } else {
        // 如果取消排序，恢复默认排序
        this.listQuery.sort_field = 'id'
        this.listQuery.sort_order = 'asc'
      }
      this.fetchData()
    },
    // 处理查看详情
    async handleView(row) {
      try {
        console.log('获取充电记录详情，ID:', row.id)
        const response = await getChargingRecordDetail(row.id)
        console.log('充电记录详情响应:', response)

        if (response.data) {
          this.detailData = response.data

          // 打印详情关键信息，用于调试
          console.log('充电记录详情:', {
            id: this.detailData.id,
            status: this.detailData.status,
            status_text: this.detailData.status_text,
            start_time: this.detailData.start_time,
            end_time: this.detailData.end_time,
            duration: this.detailData.duration,
            duration_formatted: this.detailData.duration_formatted
          })

          this.detailDialogVisible = true
        } else {
          console.error('充电记录详情数据格式不正确:', response)
          this.$message.warning('获取充电记录详情数据格式不正确')
        }
      } catch (error) {
        console.error('获取充电记录详情失败:', error)
        this.$message.error('获取充电记录详情失败')
      }
    },
    // 处理结束充电
    handleEndCharging(row) {
      this.$confirm('确认结束该充电记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          console.log('结束充电记录，ID:', row.id)
          const response = await endChargingRecord(row.id)
          console.log('结束充电响应:', response)

          this.$message({
            type: 'success',
            message: '结束充电成功!'
          })

          // 关闭详情对话框
          this.detailDialogVisible = false

          // 重新获取数据
          this.fetchData()

          // 如果有详情数据，也更新详情
          if (this.detailData && this.detailData.id === row.id) {
            try {
              const detailResponse = await getChargingRecordDetail(row.id)
              if (detailResponse.data) {
                this.detailData = detailResponse.data
              }
            } catch (detailError) {
              console.error('更新充电记录详情失败:', detailError)
            }
          }
        } catch (error) {
          console.error('结束充电失败:', error)
          this.$message.error('结束充电失败')
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        })
      })
    },
    // 处理标记异常
    handleMarkException(row) {
      this.exceptionTemp = {
        record_id: row.id,
        type: '',
        description: '',
        handle_immediately: false,
        handler: '',
        handle_result: ''
      }
      this.detailDialogVisible = false
      this.exceptionDialogVisible = true
      this.$nextTick(() => {
        this.$refs['exceptionForm'].clearValidate()
      })
    },
    // 提交异常
    submitException() {
      this.$refs['exceptionForm'].validate(async (valid) => {
        if (valid) {
          // 如果选择立即处理，需要验证处理人和处理结果
          if (this.exceptionTemp.handle_immediately && (!this.exceptionTemp.handler || !this.exceptionTemp.handle_result)) {
            this.$message.warning('请填写处理人和处理结果')
            return
          }

          try {
            await markChargingException(this.exceptionTemp)
            this.exceptionDialogVisible = false
            this.$message({
              type: 'success',
              message: '标记异常成功!'
            })
            this.fetchData()
          } catch (error) {
            console.error('标记异常失败:', error)
            this.$message.error('标记异常失败')
          }
        }
      })
    },
    // 处理导出数据
    async handleExport() {
      try {
        const params = { ...this.listQuery }
        const response = await exportChargingRecords(params)

        // 创建Blob对象
        const blob = new Blob([response], { type: 'application/vnd.ms-excel' })

        // 创建下载链接
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = `充电记录_${new Date().toISOString().split('T')[0]}.xlsx`
        link.click()

        this.$message({
          type: 'success',
          message: '导出成功!'
        })
      } catch (error) {
        console.error('导出数据失败:', error)
        this.$message.error('导出数据失败')
      }
    },

    // 处理编辑充电记录
    async handleEdit(row) {
      try {
        console.log('获取充电记录详情，ID:', row.id)
        const response = await getChargingRecordDetail(row.id)
        console.log('充电记录详情响应:', response)

        if (response.data) {
          const record = response.data

          // 初始化编辑表单数据
          this.editTemp = {
            id: record.id,
            status: record.status,
            start_time: record.start_time,
            end_time: record.end_time || '',
            remarks: record.remarks || '',
            exception_type: record.exception_info ? record.exception_info.type : '',
            exception_description: record.exception_info ? record.exception_info.description : '',
            exception_handled: record.exception_info ? record.exception_info.handled : false,
            exception_handler: record.exception_info ? record.exception_info.handler : '',
            exception_handle_result: record.exception_info ? record.exception_info.handle_result : ''
          }

          // 显示编辑对话框
          this.editDialogVisible = true

          // 在下一个DOM更新周期清除表单验证
          this.$nextTick(() => {
            this.$refs['editForm'] && this.$refs['editForm'].clearValidate()
          })
        } else {
          console.error('充电记录详情数据格式不正确:', response)
          this.$message.warning('获取充电记录详情数据格式不正确')
        }
      } catch (error) {
        console.error('获取充电记录详情失败:', error)
        this.$message.error('获取充电记录详情失败')
      }
    },

    // 提交编辑充电记录
    submitEdit() {
      this.$refs['editForm'].validate(async (valid) => {
        if (valid) {
          try {
            // 构建请求数据
            const data = {
              status: this.editTemp.status,
              start_time: this.editTemp.start_time,
              end_time: this.editTemp.end_time,
              remarks: this.editTemp.remarks
            }

            // 如果状态为异常，添加异常信息
            if (this.editTemp.status === 2) {
              data.exception_info = {
                type: this.editTemp.exception_type,
                description: this.editTemp.exception_description,
                handled: this.editTemp.exception_handled
              }

              // 如果异常已处理，添加处理信息
              if (this.editTemp.exception_handled) {
                data.exception_info.handler = this.editTemp.exception_handler
                data.exception_info.handle_result = this.editTemp.exception_handle_result
              }
            }

            // 发送更新请求
            await updateChargingRecord(this.editTemp.id, data)

            // 关闭对话框
            this.editDialogVisible = false

            // 显示成功消息
            this.$message({
              type: 'success',
              message: '更新充电记录成功!'
            })

            // 刷新数据
            this.fetchData()
          } catch (error) {
            console.error('更新充电记录失败:', error)
            this.$message.error('更新充电记录失败')
          }
        }
      })
    },

    // 处理删除充电记录
    handleDelete(row) {
      this.deleteTemp = row
      this.deleteDialogVisible = true
    },

    // 确认删除充电记录
    async confirmDelete() {
      if (!this.deleteTemp || !this.deleteTemp.id) {
        this.$message.error('删除失败：记录ID无效')
        return
      }

      try {
        // 发送删除请求
        await deleteChargingRecord(this.deleteTemp.id)

        // 关闭对话框
        this.deleteDialogVisible = false

        // 显示成功消息
        this.$message({
          type: 'success',
          message: '删除充电记录成功!'
        })

        // 刷新数据
        this.fetchData()
      } catch (error) {
        console.error('删除充电记录失败:', error)
        this.$message.error('删除充电记录失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-records-container {
  .filter-container {
    margin-bottom: 20px;
  }

  .list-container {
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .header-title {
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;

        i {
          margin-right: 10px;
          color: #409EFF;
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }

  .record-detail {
    .detail-section {
      margin-bottom: 20px;

      h3 {
        font-size: 16px;
        margin-bottom: 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #ebeef5;
      }

      .detail-item {
        display: flex;
        margin-bottom: 8px;

        .label {
          width: 100px;
          color: #606266;
          font-weight: 500;
        }

        .value {
          flex: 1;
        }
      }
    }
  }

  .delete-confirm {
    text-align: center;
    padding: 10px 0;

    p {
      margin: 10px 0;
    }

    .warning {
      color: #f56c6c;
      font-weight: bold;
      margin: 15px 0;
    }

    div {
      text-align: left;
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      background-color: #f9f9f9;

      p {
        margin: 5px 0;
      }
    }
  }
}
</style>
