// 主题颜色
$primaryColor: #4A9BFF; // 主色调 - 浅蓝色
$secondaryColor: #6BBFFF; // 次要色调 - 更浅的蓝色
$accentColor: #2D7DD2; // 强调色 - 深蓝色
$successColor: #67C23A; // 成功色 - 绿色
$warningColor: #E6A23C; // 警告色 - 橙色
$dangerColor: #F56C6C; // 危险色 - 红色
$infoColor: #909399; // 信息色 - 灰色

// 背景色
$pageBgColor: #F5F7FA; // 页面背景色 - 浅灰色
$cardBgColor: #FFFFFF; // 卡片背景色 - 白色
$headerBgColor: #FFFFFF; // 头部背景色 - 白色

// 文字颜色
$primaryTextColor: #303133; // 主要文字颜色 - 深灰色
$regularTextColor: #606266; // 常规文字颜色 - 灰色
$secondaryTextColor: #909399; // 次要文字颜色 - 浅灰色
$placeholderTextColor: #C0C4CC; // 占位文字颜色 - 更浅的灰色

// 边框颜色
$borderColor: #DCDFE6; // 边框颜色 - 浅灰色
$lightBorderColor: #E4E7ED; // 浅色边框 - 更浅的灰色

// 阴影
$boxShadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 基础阴影
$hoverShadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15); // 悬停阴影

// sidebar
$menuText: #bfcbd9;
$menuActiveText: $primaryColor;
$subMenuActiveText: #f4f4f5; //https://github.com/ElemeFE/element/issues/12951

$menuBg: #304156;
$menuHover: #263445;

$subMenuBg: #1f2d3d;
$subMenuHover: #001528;

$sideBarWidth: 210px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  primaryColor: $primaryColor;
  secondaryColor: $secondaryColor;
  accentColor: $accentColor;
  successColor: $successColor;
  warningColor: $warningColor;
  dangerColor: $dangerColor;
  infoColor: $infoColor;
}
