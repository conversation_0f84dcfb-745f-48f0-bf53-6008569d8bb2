<template>
  <div class="parking-lot-visualizer">
    <div class="visualizer-header">
      <h3>{{ parkingLotName }} - 车位状态</h3>
      <div class="legend">
        <div class="legend-item">
          <div class="space-indicator available"></div>
          <span>空闲</span>
        </div>
        <div class="legend-item">
          <div class="space-indicator occupied"></div>
          <span>已占用</span>
        </div>
        <div class="legend-item">
          <div class="space-indicator maintenance"></div>
          <span>维护中</span>
        </div>
      </div>
      <div class="controls">
        <el-button size="mini" icon="el-icon-refresh" @click="fetchSpaces">刷新</el-button>
        <el-button-group>
          <el-button size="mini" icon="el-icon-zoom-in" @click="zoomIn"></el-button>
          <el-button size="mini" icon="el-icon-zoom-out" @click="zoomOut"></el-button>
          <el-button size="mini" icon="el-icon-refresh-right" @click="resetZoom"></el-button>
        </el-button-group>
      </div>
    </div>

    <div class="visualizer-container" ref="container">
      <div
        class="parking-lot-grid"
        :style="{ transform: `scale(${zoomLevel})` }"
      >
        <div
          v-for="space in spaces"
          :key="space.id"
          class="parking-space"
          :class="getSpaceClasses(space)"
          @click="handleSpaceClick(space)"
        >
          <div class="space-number">{{ space.space_number }}</div>
          <div v-if="space.status === 1" class="vehicle-info">
            {{ getVehicleInfo(space) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 车位详情弹窗 -->
    <el-dialog
      title="车位详情"
      :visible.sync="spaceDialogVisible"
      width="400px"
    >
      <div v-if="selectedSpace" class="space-details">
        <p><strong>车位编号:</strong> {{ selectedSpace.space_number }}</p>
        <p><strong>车位类型:</strong> {{ getSpaceTypeName(selectedSpace.type) }}</p>
        <p><strong>状态:</strong> {{ getSpaceStatusName(selectedSpace.status) }}</p>

        <template v-if="selectedSpace.status === 1 && selectedSpace.vehicle">
          <h4>当前车辆信息</h4>
          <p><strong>车牌号:</strong> {{ selectedSpace.vehicle.bike_number }}</p>
          <p><strong>品牌:</strong> {{ selectedSpace.vehicle.brand }}</p>
          <p><strong>颜色:</strong> {{ selectedSpace.vehicle.color }}</p>
          <p><strong>类型:</strong> {{ selectedSpace.vehicle.type }}</p>

          <div v-if="selectedSpace.parkingRecord">
            <h4>停车记录</h4>
            <p><strong>开始时间:</strong> {{ formatDateTime(selectedSpace.parkingRecord.entry_time) }}</p>
            <p><strong>停车时长:</strong> {{ calculateDuration(selectedSpace.parkingRecord.entry_time) }}</p>
          </div>
        </template>

        <div class="space-actions">
          <el-button
            v-if="selectedSpace.status === 0"
            type="primary"
            size="small"
            @click="startParking(selectedSpace)"
          >
            开始停车
          </el-button>
          <el-button
            v-if="selectedSpace.status === 1"
            type="danger"
            size="small"
            @click="endParking(selectedSpace)"
          >
            结束停车
          </el-button>
          <el-button
            v-if="isAdmin && selectedSpace.status !== 2"
            type="warning"
            size="small"
            @click="setMaintenance(selectedSpace)"
          >
            设为维护
          </el-button>
          <el-button
            v-if="isAdmin && selectedSpace.status === 2"
            type="success"
            size="small"
            @click="setAvailable(selectedSpace)"
          >
            恢复可用
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 开始停车组件 -->
    <start-parking
      v-if="showStartParking"
      :parking-lot-id="parkingLotId"
      :parking-lot-name="parkingLotName"
      :parking-space-id="selectedSpaceId"
      @success="handleParkingSuccess"
      @cancel="showStartParking = false"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import StartParking from '@/components/StartParking'
import socketService from '@/utils/socket'
import { getParkingSpaces, updateParkingSpace } from '@/api/parkinglot'
import { getBikeById } from '@/api/bike'
import { getParkingRecordByVehicle, endParkingRecord } from '@/api/parking'

export default {
  name: 'ParkingLotVisualizer',
  components: {
    StartParking
  },
  props: {
    parkingLotId: {
      type: [Number, String],
      required: true
    },
    parkingLotName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      spaces: [],
      loading: false,
      zoomLevel: 1,
      spaceDialogVisible: false,
      selectedSpace: null,
      showStartParking: false,
      selectedSpaceId: null,
      vehicleCache: {}, // 缓存车辆信息
      parkingRecordCache: {} // 缓存停车记录
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'isAdmin'
    ])
  },
  created() {
    this.fetchSpaces()
  },
  mounted() {
    // 订阅停车场更新
    this.subscribeToUpdates()
  },
  beforeDestroy() {
    // 取消订阅停车场更新
    this.unsubscribeFromUpdates()
  },
  methods: {
    // 获取车位数据
    async fetchSpaces() {
      try {
        this.loading = true
        const response = await getParkingSpaces(this.parkingLotId)

        if (response && response.data && response.data.spaces) {
          this.spaces = response.data.spaces

          // 获取已占用车位的车辆信息
          this.fetchVehicleInfo()
        }
      } catch (error) {
        console.error('获取车位数据失败:', error)
        this.$message.error('获取车位数据失败')
      } finally {
        this.loading = false
      }
    },

    // 获取已占用车位的车辆信息
    async fetchVehicleInfo() {
      const occupiedSpaces = this.spaces.filter(space =>
        space.status === 1 && space.current_vehicle_id
      )

      for (const space of occupiedSpaces) {
        try {
          // 检查缓存
          if (this.vehicleCache[space.current_vehicle_id]) {
            space.vehicle = this.vehicleCache[space.current_vehicle_id]
          } else {
            // 获取车辆信息
            const response = await getBikeById(space.current_vehicle_id)
            if (response && response.data && response.data.bike) {
              space.vehicle = response.data.bike
              // 缓存车辆信息
              this.vehicleCache[space.current_vehicle_id] = response.data.bike
            }
          }

          // 获取停车记录
          if (this.parkingRecordCache[space.current_vehicle_id]) {
            space.parkingRecord = this.parkingRecordCache[space.current_vehicle_id]
          } else {
            const recordResponse = await getParkingRecordByVehicle(space.current_vehicle_id, 0) // 0表示进行中
            if (recordResponse && recordResponse.data && recordResponse.data.records && recordResponse.data.records.length > 0) {
              space.parkingRecord = recordResponse.data.records[0]
              // 缓存停车记录
              this.parkingRecordCache[space.current_vehicle_id] = recordResponse.data.records[0]
            }
          }
        } catch (error) {
          console.error(`获取车位 ${space.id} 的车辆信息失败:`, error)
        }
      }
    },

    // 获取车位类名
    getSpaceClasses(space) {
      return {
        'available': space.status === 0,
        'occupied': space.status === 1,
        'maintenance': space.status === 2,
        [`type-${space.type}`]: true
      }
    },

    // 获取车辆信息
    getVehicleInfo(space) {
      if (!space.vehicle) {
        return '加载中...'
      }
      return space.vehicle.bike_number
    },

    // 处理车位点击
    handleSpaceClick(space) {
      this.selectedSpace = space
      this.spaceDialogVisible = true
    },

    // 开始停车
    startParking(space) {
      this.selectedSpaceId = space.id
      this.showStartParking = true
      this.spaceDialogVisible = false
    },

    // 结束停车
    async endParking(space) {
      if (!space.parkingRecord) {
        this.$message.warning('找不到相关停车记录')
        return
      }

      try {
        this.$confirm('确定要结束停车吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const loading = this.$loading({
            lock: true,
            text: '正在结束停车...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          try {
            await endParkingRecord(space.parkingRecord.id)

            this.$message.success('停车已结束')
            this.spaceDialogVisible = false

            // 手动更新车位状态
            const index = this.spaces.findIndex(s => s.id === space.id)
            if (index !== -1) {
              this.spaces[index].status = 0
              this.spaces[index].current_vehicle_id = null
              delete this.spaces[index].vehicle
              delete this.spaces[index].parkingRecord
            }

            // 清除缓存
            if (space.current_vehicle_id) {
              delete this.parkingRecordCache[space.current_vehicle_id]
            }

            loading.close()
          } catch (error) {
            loading.close()
            console.error('结束停车失败:', error)
            this.$message.error('结束停车失败: ' + (error.message || '未知错误'))
          }
        }).catch(() => {
          // 取消操作
        })
      } catch (error) {
        console.error('结束停车操作失败:', error)
        this.$message.error('操作失败: ' + (error.message || '未知错误'))
      }
    },

    // 设置车位为维护状态
    async setMaintenance(space) {
      if (!this.isAdmin) {
        this.$message.warning('只有管理员可以执行此操作')
        return
      }

      if (space.status === 1) {
        this.$message.warning('车位已被占用，无法设为维护状态')
        return
      }

      try {
        this.$confirm('确定要将此车位设为维护状态吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const loading = this.$loading({
            lock: true,
            text: '正在更新车位状态...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          try {
            await updateParkingSpace(space.id, { status: 2 }) // 2表示维护中

            this.$message.success('车位已设为维护状态')
            this.spaceDialogVisible = false

            // 手动更新车位状态
            const index = this.spaces.findIndex(s => s.id === space.id)
            if (index !== -1) {
              this.spaces[index].status = 2
            }

            loading.close()
          } catch (error) {
            loading.close()
            console.error('更新车位状态失败:', error)
            this.$message.error('更新车位状态失败: ' + (error.message || '未知错误'))
          }
        }).catch(() => {
          // 取消操作
        })
      } catch (error) {
        console.error('设置维护状态操作失败:', error)
        this.$message.error('操作失败: ' + (error.message || '未知错误'))
      }
    },

    // 恢复车位为可用状态
    async setAvailable(space) {
      if (!this.isAdmin) {
        this.$message.warning('只有管理员可以执行此操作')
        return
      }

      try {
        this.$confirm('确定要将此车位恢复为可用状态吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const loading = this.$loading({
            lock: true,
            text: '正在更新车位状态...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          try {
            await updateParkingSpace(space.id, { status: 0 }) // 0表示空闲

            this.$message.success('车位已恢复为可用状态')
            this.spaceDialogVisible = false

            // 手动更新车位状态
            const index = this.spaces.findIndex(s => s.id === space.id)
            if (index !== -1) {
              this.spaces[index].status = 0
            }

            loading.close()
          } catch (error) {
            loading.close()
            console.error('更新车位状态失败:', error)
            this.$message.error('更新车位状态失败: ' + (error.message || '未知错误'))
          }
        }).catch(() => {
          // 取消操作
        })
      } catch (error) {
        console.error('恢复可用状态操作失败:', error)
        this.$message.error('操作失败: ' + (error.message || '未知错误'))
      }
    },

    // 处理停车成功
    handleParkingSuccess() {
      this.showStartParking = false
      this.fetchSpaces() // 刷新车位数据
      this.$message.success('停车成功')
    },

    // 获取车位类型名称
    getSpaceTypeName(type) {
      const typeMap = {
        1: '普通车位',
        2: '残疾人车位',
        3: '充电车位'
      }
      return typeMap[type] || `未知类型(${type})`
    },

    // 获取车位状态名称
    getSpaceStatusName(status) {
      const statusMap = {
        0: '空闲',
        1: '已占用',
        2: '维护中'
      }
      return statusMap[status] || `未知状态(${status})`
    },

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '未知时间'

      const date = new Date(dateTimeStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
    },

    // 计算时间间隔
    calculateDuration(startTimeStr) {
      if (!startTimeStr) return '未知'

      const startTime = new Date(startTimeStr)
      const now = new Date()

      const diffMs = now - startTime
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))

      return `${diffHours}小时${diffMinutes}分钟`
    },

    // 缩放控制
    zoomIn() {
      if (this.zoomLevel < 2) {
        this.zoomLevel += 0.1
      }
    },

    zoomOut() {
      if (this.zoomLevel > 0.5) {
        this.zoomLevel -= 0.1
      }
    },

    resetZoom() {
      this.zoomLevel = 1
    },

    // 订阅停车场更新
    subscribeToUpdates() {
      // 订阅停车场
      socketService.subscribeParkingLot(this.parkingLotId)

      // 监听车位更新事件
      socketService.on('parking_space_updated', this.handleSpaceUpdate)
    },

    // 取消订阅停车场更新
    unsubscribeFromUpdates() {
      // 取消订阅停车场
      socketService.unsubscribeParkingLot(this.parkingLotId)

      // 移除事件监听
      socketService.off('parking_space_updated', this.handleSpaceUpdate)
    },

    // 处理车位更新事件
    handleSpaceUpdate(data) {
      console.log('收到车位更新:', data)

      // 确保是当前停车场的更新
      if (data.parking_lot_id != this.parkingLotId) {
        return
      }

      // 查找并更新车位
      const index = this.spaces.findIndex(space => space.id == data.parking_space_id)
      if (index !== -1) {
        // 更新状态
        this.$set(this.spaces[index], 'status', data.status)

        // 更新车辆ID
        this.$set(this.spaces[index], 'current_vehicle_id', data.vehicle_id)

        // 如果车位变为空闲，清除车辆信息
        if (data.status === 0) {
          if (this.spaces[index].vehicle) {
            this.$delete(this.spaces[index], 'vehicle')
          }
          if (this.spaces[index].parkingRecord) {
            this.$delete(this.spaces[index], 'parkingRecord')
          }
        }
        // 如果车位被占用且有车辆ID，获取车辆信息
        else if (data.status === 1 && data.vehicle_id) {
          this.fetchVehicleForSpace(this.spaces[index])
        }

        // 如果当前选中的是这个车位，更新选中的车位信息
        if (this.selectedSpace && this.selectedSpace.id === data.parking_space_id) {
          this.selectedSpace = { ...this.spaces[index] }
        }

        // 显示通知
        this.$notify({
          title: '车位状态更新',
          message: `车位 ${this.spaces[index].space_number} 状态已更新`,
          type: 'info',
          duration: 3000
        })
      }
    },

    // 为单个车位获取车辆信息
    async fetchVehicleForSpace(space) {
      if (!space.current_vehicle_id) return

      try {
        // 检查缓存
        if (this.vehicleCache[space.current_vehicle_id]) {
          space.vehicle = this.vehicleCache[space.current_vehicle_id]
        } else {
          // 获取车辆信息
          const response = await getBikeById(space.current_vehicle_id)
          if (response && response.data && response.data.bike) {
            space.vehicle = response.data.bike
            // 缓存车辆信息
            this.vehicleCache[space.current_vehicle_id] = response.data.bike
          }
        }

        // 获取停车记录
        if (this.parkingRecordCache[space.current_vehicle_id]) {
          space.parkingRecord = this.parkingRecordCache[space.current_vehicle_id]
        } else {
          const recordResponse = await getParkingRecordByVehicle(space.current_vehicle_id, 0) // 0表示进行中
          if (recordResponse && recordResponse.data && recordResponse.data.records && recordResponse.data.records.length > 0) {
            space.parkingRecord = recordResponse.data.records[0]
            // 缓存停车记录
            this.parkingRecordCache[space.current_vehicle_id] = recordResponse.data.records[0]
          }
        }
      } catch (error) {
        console.error(`获取车位 ${space.id} 的车辆信息失败:`, error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.parking-lot-visualizer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .visualizer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;

    h3 {
      margin: 0;
      font-size: 18px;
    }

    .legend {
      display: flex;
      align-items: center;

      .legend-item {
        display: flex;
        align-items: center;
        margin-right: 15px;

        .space-indicator {
          width: 16px;
          height: 16px;
          border-radius: 3px;
          margin-right: 5px;

          &.available {
            background-color: #67C23A;
          }

          &.occupied {
            background-color: #F56C6C;
          }

          &.maintenance {
            background-color: #E6A23C;
          }
        }
      }
    }
  }

  .visualizer-container {
    flex: 1;
    overflow: auto;
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    padding: 15px;
    background-color: #F5F7FA;

    .parking-lot-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      transform-origin: top left;
      transition: transform 0.3s ease;
    }
  }

  .parking-space {
    width: 100px;
    height: 150px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &.available {
      background-color: #67C23A;
      color: white;
    }

    &.occupied {
      background-color: #F56C6C;
      color: white;
    }

    &.maintenance {
      background-color: #E6A23C;
      color: white;

      &::after {
        content: '维护中';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-30deg);
        font-size: 18px;
        font-weight: bold;
        white-space: nowrap;
      }
    }

    // 不同类型车位的样式
    &.type-1 {
      border: 2px solid #409EFF;
    }

    &.type-2 {
      border: 2px solid #67C23A;

      &::before {
        content: '⚡';
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 16px;
      }
    }

    &.type-3 {
      border: 2px solid #E6A23C;

      &::before {
        content: '♿';
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 16px;
      }
    }

    &.type-4 {
      border: 2px solid #F56C6C;

      &::before {
        content: 'VIP';
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 12px;
        font-weight: bold;
      }
    }

    .space-number {
      font-weight: bold;
      font-size: 16px;
    }

    .vehicle-info {
      font-size: 14px;
      word-break: break-all;
      text-align: center;
      background-color: rgba(0, 0, 0, 0.2);
      padding: 3px;
      border-radius: 3px;
    }
  }

  .space-details {
    p {
      margin: 8px 0;
    }

    h4 {
      margin: 15px 0 8px;
      border-bottom: 1px solid #EBEEF5;
      padding-bottom: 5px;
    }

    .space-actions {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }
  }
}
</style>
