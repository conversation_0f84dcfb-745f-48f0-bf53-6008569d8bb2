<template>
  <div class="app-container">
    <el-card class="box-card" shadow="hover">
      <div slot="header" class="clearfix">
        <span class="card-title">我的车辆</span>
        <el-button type="primary" icon="el-icon-plus" size="small" style="float: right;" @click="handleAdd">添加车辆</el-button>
      </div>

      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form" size="small">
        <el-form-item label="车牌号">
          <el-input v-model="searchForm.bike_number" placeholder="请输入车牌号" clearable @keyup.enter.native="handleSearch" />
        </el-form-item>
        <el-form-item label="品牌">
          <el-select v-model="searchForm.brand" placeholder="请选择品牌" clearable>
            <el-option v-for="item in brandOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="正常" value="available" />
            <el-option label="停用" value="unavailable" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作提示 -->
      <el-alert
        v-if="showAlert"
        :title="alertMessage"
        :type="alertType"
        show-icon
        :closable="true"
        style="margin-bottom: 15px;"
        @close="showAlert = false"
      />

      <!-- 车辆列表 -->
      <el-table
        v-loading="loading"
        :data="bikeList"
        border
        style="width: 100%"
        :row-class-name="tableRowClassName"
        empty-text="暂无车辆信息"
      >
        <el-table-column prop="bike_number" label="车牌号" min-width="130" align="center">
          <template slot-scope="scope">
            <div class="vehicle-info-cell">
              <i class="el-icon-bicycle" style="color: #409EFF; margin-right: 5px;"></i>
              <span>{{ scope.row.bike_number }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="brand" label="品牌" min-width="120" align="center">
          <template slot-scope="scope">
            <el-tag size="medium" effect="plain" type="primary">{{ scope.row.brand }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" min-width="120" align="center">
          <template slot-scope="scope">
            <el-tag size="medium" effect="plain" type="success">{{ scope.row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="color" label="颜色" min-width="100" align="center">
          <template slot-scope="scope">
            <div class="color-preview">
              <span
                class="color-dot"
                :style="{
                  backgroundColor: getColorValue(scope.row.color),
                  borderColor: getColorBorder(scope.row.color)
                }"
              />
              {{ scope.row.color.startsWith('#') ? getColorName(scope.row.color) : scope.row.color }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 'available' ? 'success' : 'info'">
              {{ scope.row.status === 'available' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="注册时间" min-width="150" align="center">
          <template slot-scope="scope">
            <i class="el-icon-time" style="color: #909399; margin-right: 5px;"></i>
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center" fixed="right" class-name="operation-column">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" trigger="click">
                <el-button type="primary" size="mini">
                  操作<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="edit">
                    <i class="el-icon-edit" style="color: #409EFF;"></i> 编辑车辆
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <i class="el-icon-delete" style="color: #F56C6C;"></i> 删除车辆
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page.sync="currentPage"
          :page-sizes="[5, 10, 20, 50]"
          :page-size.sync="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '添加车辆' : '编辑车辆'"
      :visible.sync="dialogVisible"
      width="500px"
      @closed="resetForm"
    >
      <el-form ref="bikeForm" :model="bikeForm" :rules="rules" label-width="100px">
        <el-form-item label="车牌号" prop="bike_number">
          <el-input v-model="bikeForm.bike_number" placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="品牌" prop="brand">
          <el-select
            v-model="bikeForm.brand"
            placeholder="请选择品牌"
            style="width: 100%"
            filterable
          >
            <el-option v-for="item in brandOptions" :key="item" :label="item" :value="item" />
          </el-select>
          <div class="form-tip">选择电动车品牌</div>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select
            v-model="bikeForm.type"
            placeholder="请选择类型"
            style="width: 100%"
            filterable
            allow-create
            default-first-option
          >
            <el-option label="电动自行车" value="电动自行车" />
            <el-option label="电动车" value="电动车" />
            <el-option label="电动滑板车" value="电动滑板车" />
            <el-option label="山地电动车" value="山地电动车" />
            <el-option label="折叠电动车" value="折叠电动车" />
            <el-option label="其他电动车" value="其他电动车" />
          </el-select>
          <div class="form-tip">可选择或输入自定义类型</div>
        </el-form-item>
        <el-form-item label="颜色" prop="color">
          <el-select
            v-model="bikeForm.color"
            placeholder="请选择颜色"
            style="width: 100%;"
            popper-class="color-select-dropdown"
          >
            <el-option v-for="item in colorOptions" :key="item" :label="item" :value="item">
              <div class="color-option">
                <span
                  class="color-block"
                  :style="{
                    backgroundColor: getColorValue(item),
                    borderColor: getColorBorder(item)
                  }"
                />
                <span>{{ item }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">选择车辆的颜色</div>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="bikeForm.status">
            <el-radio label="available">正常</el-radio>
            <el-radio label="unavailable">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="删除确认"
      :visible.sync="deleteDialogVisible"
      width="400px"
    >
      <div class="delete-confirm">
        <i class="el-icon-warning warning-icon" />
        <p>确定要删除车牌号为 <span class="delete-item">{{ currentBike.bike_number }}</span> 的车辆吗？</p>
        <p class="delete-warning">此操作不可恢复，请谨慎操作！</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" :loading="deleteLoading" @click="confirmDelete">确认删除</el-button>
      </div>
    </el-dialog>

    <!-- 停车对话框 -->
    <el-dialog
      title="开始停车"
      :visible.sync="parkingDialogVisible"
      width="500px"
      @closed="resetParkingForm"
    >
      <div class="dialog-header">
        <div class="dialog-title">
          <i class="el-icon-s-order"></i>
          <span>创建停车记录</span>
        </div>
        <div class="dialog-subtitle">
          车辆：{{ currentBike.bike_number }} ({{ currentBike.brand }})
        </div>
      </div>

      <el-form ref="parkingForm" :model="parkingForm" :rules="parkingRules" label-width="100px">
        <el-form-item label="停车场" prop="parkingLotId">
          <el-select
            v-model="parkingForm.parkingLotId"
            placeholder="请选择停车场"
            style="width: 100%"
            :loading="parkingLotsLoading"
            @change="handleParkingLotChange"
          >
            <el-option
              v-for="lot in parkingLots"
              :key="lot.id"
              :label="lot.name"
              :value="lot.id"
            >
              <div class="parking-lot-option">
                <span class="lot-name">{{ lot.name }}</span>
                <span class="lot-info">
                  <el-tag size="mini" type="success">空闲: {{ lot.available_spaces }}</el-tag>
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="车位" prop="parkingSpaceId">
          <el-select
            v-model="parkingForm.parkingSpaceId"
            placeholder="请选择车位"
            style="width: 100%"
            :loading="parkingSpacesLoading"
            :disabled="!parkingForm.parkingLotId"
          >
            <el-option
              v-for="space in parkingSpaces"
              :key="space.id"
              :label="space.space_number"
              :value="space.id"
            >
              <div class="space-option">
                <div class="space-tag" :class="'type-' + space.type">
                  {{ getSpaceTypeIcon(space.type) }}
                </div>
                <span class="space-number">{{ space.space_number }}</span>
                <span class="space-type">{{ getSpaceTypeText(space.type) }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip" v-if="parkingSpaces.length === 0 && parkingForm.parkingLotId">
            没有可用的车位，请选择其他停车场
          </div>
        </el-form-item>

        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="parkingForm.notes"
            type="textarea"
            :rows="2"
            placeholder="可选填写停车备注信息"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="parkingDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="parkingSubmitLoading" @click="submitParkingForm">确认停车</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBikes, createBike, updateBike, deleteBike } from '@/api/bike'
// getBikesByUser 未使用
import { mapGetters } from 'vuex'
import request from '@/utils/request'

export default {
  name: 'MyBikes',
  data() {
    return {
      // 车辆列表
      bikeList: [],
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 加载状态
      loading: false,
      submitLoading: false,
      deleteLoading: false,
      // 对话框状态
      dialogVisible: false,
      dialogType: 'add', // 'add' 或 'edit'
      deleteDialogVisible: false,
      // 停车对话框
      parkingDialogVisible: false,
      parkingLotsLoading: false,
      parkingSpacesLoading: false,
      parkingSubmitLoading: false,
      // 停车场和车位数据
      parkingLots: [],
      parkingSpaces: [],
      // 停车表单
      parkingForm: {
        parkingLotId: '',
        parkingSpaceId: '',
        notes: ''
      },
      // 停车表单验证规则
      parkingRules: {
        parkingLotId: [
          { required: true, message: '请选择停车场', trigger: 'change' }
        ],
        parkingSpaceId: [
          { required: true, message: '请选择车位', trigger: 'change' }
        ]
      },
      // 当前操作的车辆
      currentBike: {},
      // 表单数据
      bikeForm: {
        id: null,
        bike_number: '',
        brand: '',
        type: '',
        color: '黑色',
        status: 'available'
      },
      // 表单验证规则
      rules: {
        bike_number: [
          { required: true, message: '请输入车牌号', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        brand: [
          { required: true, message: '请输入品牌', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        color: [
          { required: true, message: '请选择颜色', trigger: 'change' }
        ]
      },
      // 搜索表单
      searchForm: {
        bike_number: '',
        brand: '',
        status: ''
      },
      // 品牌选项
      brandOptions: ['雅迪', '爱玛', '台铃', '小刀', '立马', '绿源', '新日', '飞鸽', '美利达', '捷安特', '阿米尼', '未知品牌'],
      // 提示信息
      showAlert: false,
      alertMessage: '',
      alertType: 'success',
      // 预定义颜色
      predefineColors: [
        '#000000', '#ffffff', '#ff0000', '#0000ff', '#ffff00',
        '#00ff00', '#808080', '#c0c0c0', '#ffd700', '#a52a2a',
        '#800080', '#ffc0cb', '#ffa500', '#d3d3d3'
      ],
      // 常见颜色
      commonColors: {
        '黑色': '#000000',
        '白色': '#ffffff',
        '红色': '#ff0000',
        '蓝色': '#0000ff',
        '黄色': '#ffff00',
        '绿色': '#00ff00',
        '灰色': '#808080',
        '银色': '#c0c0c0',
        '金色': '#ffd700',
        '棕色': '#a52a2a',
        '紫色': '#800080',
        '粉色': '#ffc0cb',
        '橙色': '#ffa500',
        '浅灰色': '#d3d3d3'
      },
      // 颜色选项
      colorOptions: ['黑色', '白色', '红色', '蓝色', '黄色', '绿色', '灰色', '银色', '金色', '棕色', '紫色', '粉色', '橙色', '浅灰色']
    }
  },
  computed: {
    ...mapGetters([
      'userId'
    ])
  },
  created() {
    // 检查是否有userId，如果没有则从localStorage获取
    if (!this.userId) {
      const localUserId = localStorage.getItem('userId')
      if (localUserId) {
        this.$store.commit('SET_USERID', parseInt(localUserId))
      }
    }
    this.fetchBikes()
    console.log('当前用户ID:', this.userId || localStorage.getItem('userId'))

    // 初始化品牌选项
    this.brandOptions = ['雅迪', '爱玛', '台铃', '小刀', '立马', '绿源', '新日', '飞鸽', '美利达', '捷安特', '阿米尼', '未知品牌']

    // 初始化颜色选项，与commonColors中的颜色名称保持一致
    this.colorOptions = Object.keys(this.commonColors).sort()
  },
  methods: {
    // 获取车辆列表
    async fetchBikes() {
      // 优先使用Vuex中的userId，如果没有则尝试从localStorage获取
      const userId = this.userId || parseInt(localStorage.getItem('userId'))

      if (!userId) {
        this.showNotification('获取用户ID失败，请重新登录', 'error')
        return
      }

      this.loading = true
      try {
        console.log('正在获取用户车辆信息，用户ID:', userId)

        // 构建查询参数
        const params = {
          belong_to: userId,
          page: this.currentPage,
          limit: this.pageSize
        }

        // 添加搜索条件
        if (this.searchForm.bike_number) {
          params.bike_number = this.searchForm.bike_number
        }
        if (this.searchForm.brand) {
          params.brand = this.searchForm.brand
        }
        if (this.searchForm.status) {
          params.status = this.searchForm.status
        }

        // 调用API获取车辆列表
        const response = await getBikes(params)

        // 处理响应数据
        console.log('API响应:', response)

        if (response && response.data && response.data.bikes) {
          this.bikeList = response.data.bikes
          this.total = response.data.total || this.bikeList.length

          // 不再获取车辆的停车信息

          // 如果没有数据，显示提示信息
          if (this.bikeList.length === 0) {
            this.showNotification('暂无车辆信息', 'info')
          }
        } else {
          this.bikeList = []
          this.total = 0
          this.showNotification('获取车辆信息失败，返回数据格式错误', 'warning')
        }
      } catch (error) {
        console.error('获取车辆列表失败', error)
        this.showNotification(`获取车辆列表失败: ${error.message || '未知错误'}`, 'error')
        this.bikeList = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    // 品牌搜索建议
    queryBrandSearch(queryString, cb) {
      const results = queryString
        ? this.brandOptions.filter(brand => brand.toLowerCase().includes(queryString.toLowerCase()))
        : this.brandOptions

      // 返回格式化的建议列表
      cb(results.map(brand => ({ value: brand })))
    },

    // 处理添加车辆
    handleAdd() {
      this.dialogType = 'add'
      this.dialogVisible = true
      this.bikeForm = {
        id: null,
        bike_number: '',
        brand: '',
        type: '',
        color: '黑色',
        status: 'available'
      }
    },

    // 处理编辑车辆
    handleEdit(row) {
      this.dialogType = 'edit'
      this.currentBike = row
      this.bikeForm = {
        id: row.id || row.b_id,
        bike_number: row.bike_number,
        brand: row.brand,
        type: row.type,
        color: row.color,
        status: row.status
      }
      this.dialogVisible = true
    },

    // 处理下拉菜单命令
    handleCommand(command, row) {
      switch (command) {
        case 'edit':
          this.handleEdit(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },



    // 获取停车场列表
    fetchParkingLots() {
      this.parkingLotsLoading = true

      // 调用API获取停车场列表
      this.$http({
        url: '/api/parkinglots',
        method: 'get',
        params: {
          status: 1, // 只获取正常运营的停车场
          limit: 100
        }
      }).then(response => {
        if (response && response.code === 20000 && response.data) {
          // 处理停车场数据
          this.parkingLots = response.data.items || []
        } else {
          this.parkingLots = []
          this.showNotification('获取停车场列表失败', 'warning')
        }
      }).catch(error => {
        console.error('获取停车场列表失败', error)
        this.showNotification('获取停车场列表失败', 'error')
        this.parkingLots = []
      }).finally(() => {
        this.parkingLotsLoading = false
      })
    },

    // 当选择停车场时获取车位
    handleParkingLotChange(parkingLotId) {
      if (!parkingLotId) {
        this.parkingSpaces = []
        return
      }

      this.parkingSpacesLoading = true

      // 调用API获取车位列表
      this.$http({
        url: `/api/parkinglots/${parkingLotId}/spaces`,
        method: 'get'
      }).then(response => {
        if (response && response.code === 20000 && response.data) {
          // 只显示空闲的车位
          this.parkingSpaces = (response.data.spaces || []).filter(space => space.status === 0)
        } else {
          this.parkingSpaces = []
          this.showNotification('获取车位列表失败', 'warning')
        }
      }).catch(error => {
        console.error('获取车位列表失败', error)
        this.showNotification('获取车位列表失败', 'error')
        this.parkingSpaces = []
      }).finally(() => {
        this.parkingSpacesLoading = false
      })
    },

    // 提交停车表单
    submitParkingForm() {
      this.$refs.parkingForm.validate(valid => {
        if (!valid) return

        this.parkingSubmitLoading = true

        // 准备提交数据
        const formData = {
          vehicle_id: this.currentBike.id || this.currentBike.b_id,
          parking_lot_id: this.parkingForm.parkingLotId,
          parking_space_id: this.parkingForm.parkingSpaceId,
          user_id: this.userId || parseInt(localStorage.getItem('userId')),
          notes: this.parkingForm.notes
        }

        // 调用API创建停车记录
        this.$http({
          url: '/api/parking-records',
          method: 'post',
          data: formData
        }).then(response => {
          this.showNotification('停车记录创建成功', 'success')
          this.parkingDialogVisible = false
          this.resetParkingForm()
        }).catch(error => {
          console.error('创建停车记录失败', error)
          const errorMsg = error.response && error.response.data ?
            error.response.data.message : '创建停车记录失败'
          this.showNotification(errorMsg, 'error')
        }).finally(() => {
          this.parkingSubmitLoading = false
        })
      })
    },

    // 重置停车表单
    resetParkingForm() {
      this.parkingForm = {
        parkingLotId: '',
        parkingSpaceId: '',
        notes: ''
      }
      if (this.$refs.parkingForm) {
        this.$refs.parkingForm.resetFields()
      }
      this.parkingSpaces = []
    },

    // 处理删除车辆
    handleDelete(row) {
      this.currentBike = row
      this.deleteDialogVisible = true
    },

    // 确认删除车辆
    async confirmDelete() {
      this.deleteLoading = true
      try {
        await deleteBike(this.currentBike.id || this.currentBike.b_id)
        this.showNotification(`车牌号为 ${this.currentBike.bike_number} 的车辆已成功删除`, 'success')
        this.deleteDialogVisible = false
        // 重新获取车辆列表
        this.fetchBikes()
      } catch (error) {
        console.error('删除车辆失败', error)
        this.showNotification(`删除车辆失败: ${error.message || '未知错误'}`, 'error')
      } finally {
        this.deleteLoading = false
      }
    },

    // 提交表单
    submitForm() {
      this.$refs.bikeForm.validate(async valid => {
        if (valid) {
          this.submitLoading = true
          try {
            // 获取当前用户ID
            const userId = this.userId || parseInt(localStorage.getItem('userId'))
            if (!userId) {
              throw new Error('获取用户ID失败，请重新登录')
            }

            // 准备提交的数据
            const bikeData = {
              ...this.bikeForm,
              belong_to: userId
            }

            console.log('提交车辆数据:', bikeData)

            // 根据对话框类型决定是创建还是更新
            if (this.dialogType === 'add') {
              // 创建新车辆
              await createBike(bikeData)
              this.showNotification('车辆添加成功', 'success')
            } else {
              // 更新车辆
              await updateBike(bikeData.id, bikeData)
              this.showNotification('车辆信息更新成功', 'success')
            }

            // 关闭对话框
            this.dialogVisible = false
            // 重新获取车辆列表
            this.fetchBikes()
          } catch (error) {
            console.error('保存车辆信息失败', error)
            this.showNotification(`保存车辆信息失败: ${error.message || '未知错误'}`, 'error')
          } finally {
            this.submitLoading = false
          }
        }
      })
    },

    // 重置表单
    resetForm() {
      if (this.$refs.bikeForm) {
        this.$refs.bikeForm.resetFields()
      }
      this.bikeForm = {
        id: null,
        bike_number: '',
        brand: '',
        type: '',
        color: '黑色',
        status: 'available'
      }
    },

    // 处理搜索
    handleSearch() {
      this.currentPage = 1
      this.fetchBikes()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        bike_number: '',
        brand: '',
        status: ''
      }
      this.handleSearch()
    },

    // 处理分页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchBikes()
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchBikes()
    },

    // 根据状态设置行样式
    tableRowClassName({ row }) {
      if (row.status === 'unavailable') {
        return 'warning-row'
      }
      return ''
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'

      try {
        const dateObj = new Date(date)
        return dateObj.toLocaleDateString()
      } catch (e) {
        return date
      }
    },

    // 获取颜色值，用于显示颜色点
    getColorValue(color) {
      if (!color) return '#000000'

      // 如果color已经是十六进制颜色代码，直接返回
      if (color.startsWith('#')) {
        return color
      }

      // 根据颜色名称查找十六进制颜色代码
      const colorMap = {
        '黑色': '#000000',
        '白色': '#FFFFFF',
        '红色': '#FF0000',
        '蓝色': '#0000FF',
        '黄色': '#FFFF00',
        '绿色': '#00FF00',
        '灰色': '#808080',
        '银色': '#C0C0C0',
        '金色': '#FFD700',
        '棕色': '#A52A2A',
        '紫色': '#800080',
        '粉色': '#FFC0CB',
        '橙色': '#FFA500',
        '浅灰色': '#D3D3D3',
        '未知颜色': '#EEEEEE'
      }

      return colorMap[color] || '#000000'
    },

    // 获取颜色名称，用于显示颜色名
    getColorName(color) {
      if (!color) return '未知颜色'

      // 如果不是十六进制颜色代码，可能是直接的颜色名称，直接返回
      if (!color.startsWith('#')) {
        return color
      }

      // 根据十六进制颜色代码查找颜色名称
      const colorMap = {
        '#000000': '黑色',
        '#FFFFFF': '白色',
        '#FF0000': '红色',
        '#0000FF': '蓝色',
        '#FFFF00': '黄色',
        '#00FF00': '绿色',
        '#808080': '灰色',
        '#C0C0C0': '银色',
        '#FFD700': '金色',
        '#A52A2A': '棕色',
        '#800080': '紫色',
        '#FFC0CB': '粉色',
        '#FFA500': '橙色',
        '#D3D3D3': '浅灰色'
      }

      return colorMap[color.toLowerCase()] || '未知颜色'
    },

    // 获取颜色边框颜色
    getColorBorder(color) {
      const colorName = color.startsWith('#') ? this.getColorName(color) : color
      return colorName === '白色' || colorName === '银色' || colorName === '未知颜色' ? '#CCCCCC' : 'transparent'
    },

    // 显示通知
    showNotification(message, type = 'success') {
      this.alertMessage = message
      this.alertType = type
      this.showAlert = true

      // 5秒后自动关闭
      setTimeout(() => {
        this.showAlert = false
      }, 5000)
    },

    // 获取车位类型图标
    getSpaceTypeIcon(type) {
      const iconMap = {
        1: '🚕', // 小型车图标
        2: '♿', // 轮椅图标
        3: '⚡' // 充电图标
      }
      return iconMap[type] || '🚕'
    },

    // 获取车位类型文本
    getSpaceTypeText(type) {
      const typeMap = {
        1: '普通车位',
        2: '残疾人车位',
        3: '充电车位'
      }
      return typeMap[type] || '普通车位'
    },



    // 初始化HTTP请求对象
    $http: request
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.search-form {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border-left: 4px solid #409EFF;
}

.pagination-container {
  text-align: right;
  margin-top: 20px;
}

// 颜色显示
.color-dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
  border: 1px solid transparent;
}

.color-preview {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 表格行样式
.el-table .warning-row {
  background: oldlace;
}

// 车辆信息单元格
.vehicle-info-cell {
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    margin-right: 5px;
    font-size: 16px;
  }

  span {
    font-weight: 500;
  }
}

// 表格样式
::v-deep .el-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  th {
    background-color: #f0f9ff !important;
    color: #606266;
    font-weight: 600;
  }

  .el-table__row:hover {
    background-color: #f0f9ff !important;
  }
}

// 停车信息样式
.parking-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;

  .parking-details {
    display: flex;
    flex-direction: column;
    font-size: 12px;
    color: #606266;
    margin-top: 3px;

    span {
      line-height: 1.4;
    }
  }
}

// 删除确认对话框
.delete-confirm {
  text-align: center;
  padding: 20px 0;
}

.warning-icon {
  font-size: 48px;
  color: #e6a23c;
  margin-bottom: 20px;
}

.delete-item {
  color: #f56c6c;
  font-weight: bold;
}

.delete-warning {
  color: #909399;
  font-size: 14px;
  margin-top: 10px;
}

// 响应式调整
@media (max-width: 768px) {
  .search-form {
    .el-form-item {
      margin-bottom: 10px;
    }
  }

  .el-table {
    width: 100%;
    overflow-x: auto;
  }
}

// 颜色选择器容器
.color-picker-container {
  display: flex;
  align-items: center;
}

// 颜色选项
.color-option {
  display: flex;
  align-items: center;
}

// 颜色块
.color-block {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  margin-right: 5px;
  border: 1px solid #dcdfe6;
}

// 表单提示文字
.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}

// 颜色选择下拉框
::v-deep .color-select-dropdown {
  .el-select-dropdown__item {
    height: auto;
    padding: 8px 12px;

    &.selected {
      font-weight: bold;

      .color-block {
        border-width: 2px;
      }
    }
  }
}

// 停车对话框样式
.dialog-header {
  margin-bottom: 20px;

  .dialog-title {
    display: flex;
    align-items: center;
    margin-bottom: 5px;

    i {
      font-size: 20px;
      color: #409EFF;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .dialog-subtitle {
    font-size: 14px;
    color: #606266;
    margin-left: 28px;
    font-weight: 500;
  }
}

// 停车场选项
.parking-lot-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .lot-name {
    font-weight: 500;
  }

  .lot-info {
    font-size: 12px;
  }
}

// 车位选项
.space-option {
  display: flex;
  align-items: center;
  width: 100%;

  .space-tag {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 12px;
    background-color: #f5f7fa;

    &.type-2 {
      color: #409EFF;
    }

    &.type-3 {
      color: #67C23A;
    }

    &.type-4 {
      color: #E6A23C;
      font-size: 10px;
      font-weight: bold;
    }
  }

  .space-number {
    font-weight: 600;
    color: #303133;
    margin-right: 10px;
  }

  .space-type {
    color: #909399;
    font-size: 12px;
  }
}
</style>
