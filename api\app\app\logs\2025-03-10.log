2025/03/10 18:26:45 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/10 18:26:45 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/10 18:26:45 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/10 18:26:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:26:50] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 18:26:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:26:50] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/03/10 18:28:01 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/10 18:28:08 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/10 18:28:14 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/10 18:28:14 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/10 18:28:14 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/10 18:28:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:28:21] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 18:28:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:28:21] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/03/10 18:29:58 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/10 18:30:04 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/10 18:31:16 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/10 18:33:44 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/10 18:33:44 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/10 18:33:44 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/10 18:34:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:34:04] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 18:34:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:34:04] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/10 18:34:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:34:09] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/10 18:34:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:34:26] "OPTIONS /api/register HTTP/1.1" 200 -
2025/03/10 18:34:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:34:26] "[31m[1mPOST /api/register HTTP/1.1[0m" 422 -
2025/03/10 18:35:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:35:05] "OPTIONS /api/register HTTP/1.1" 200 -
2025/03/10 18:35:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:35:06] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/03/10 18:35:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:35:10] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 18:35:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:35:10] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/10 18:35:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:35:15] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/10 18:35:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:35:15] "GET /api/users HTTP/1.1" 200 -
2025/03/10 18:36:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:36:24] "OPTIONS /api/users/5 HTTP/1.1" 200 -
2025/03/10 18:36:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:36:24] "GET /api/users/5 HTTP/1.1" 200 -
2025/03/10 18:36:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:36:29] "OPTIONS /api/users/5 HTTP/1.1" 200 -
2025/03/10 18:36:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:36:29] "PUT /api/users/5 HTTP/1.1" 200 -
2025/03/10 18:37:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:37:10] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/10 18:37:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:37:10] "GET /api/users HTTP/1.1" 200 -
2025/03/10 18:42:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:42:54] "OPTIONS /api/check-username HTTP/1.1" 200 -
2025/03/10 18:42:54 flask_api __init__.py[146] handle_exception() ERROR: 未处理的异常: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025/03/10 18:42:54 flask_api __init__.py[148] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.

2025/03/10 18:42:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:42:54] "[35m[1mPOST /api/check-username HTTP/1.1[0m" 500 -
2025/03/10 18:42:58 flask_api __init__.py[146] handle_exception() ERROR: 未处理的异常: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025/03/10 18:42:58 flask_api __init__.py[148] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.

2025/03/10 18:42:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:42:58] "[35m[1mPOST /api/check-username HTTP/1.1[0m" 500 -
2025/03/10 18:42:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:42:58] "OPTIONS /api/register HTTP/1.1" 200 -
2025/03/10 18:42:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:42:58] "[31m[1mPOST /api/register HTTP/1.1[0m" 422 -
2025/03/10 18:43:10 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/10 18:43:10 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/10 18:43:10 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/10 18:43:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:13] "OPTIONS /api/check-username HTTP/1.1" 200 -
2025/03/10 18:43:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:13] "POST /api/check-username HTTP/1.1" 200 -
2025/03/10 18:43:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:21] "OPTIONS /api/check-username HTTP/1.1" 200 -
2025/03/10 18:43:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:21] "POST /api/check-username HTTP/1.1" 200 -
2025/03/10 18:43:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:21] "POST /api/check-username HTTP/1.1" 200 -
2025/03/10 18:43:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:21] "OPTIONS /api/register HTTP/1.1" 200 -
2025/03/10 18:43:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:21] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/03/10 18:43:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:25] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 18:43:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:25] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/10 18:43:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:26] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/10 18:43:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:26] "GET /api/users HTTP/1.1" 200 -
2025/03/10 18:43:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:36] "OPTIONS /api/users/6 HTTP/1.1" 200 -
2025/03/10 18:43:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:43:36] "DELETE /api/users/6 HTTP/1.1" 200 -
2025/03/10 18:45:14 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/10 18:45:14 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/10 18:45:14 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/10 18:45:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:45:20] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 18:45:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:45:20] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/10 18:45:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:45:29] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 18:45:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:45:29] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/10 18:45:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:45:35] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 18:45:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:45:35] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/10 18:45:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:45:35] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/10 18:45:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:45:35] "GET /api/users HTTP/1.1" 200 -
2025/03/10 18:47:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:47:12] "OPTIONS /api/check-username HTTP/1.1" 200 -
2025/03/10 18:47:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:47:12] "POST /api/check-username HTTP/1.1" 200 -
2025/03/10 18:47:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:47:14] "POST /api/check-username HTTP/1.1" 200 -
2025/03/10 18:47:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:47:20] "OPTIONS /api/check-username HTTP/1.1" 200 -
2025/03/10 18:47:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:47:20] "POST /api/check-username HTTP/1.1" 200 -
2025/03/10 18:48:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:48:03] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 18:48:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:48:03] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/10 18:48:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:48:03] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/10 18:48:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:48:03] "GET /api/users HTTP/1.1" 200 -
2025/03/10 18:48:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:48:14] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 18:48:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:48:14] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/03/10 18:48:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:48:21] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 18:48:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 18:48:21] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/03/10 20:11:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:11:39] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 20:11:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:11:39] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/10 20:11:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:11:55] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/10 20:11:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:11:55] "GET /api/users HTTP/1.1" 200 -
2025/03/10 20:12:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:12:01] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/10 20:12:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:12:01] "GET /api/users HTTP/1.1" 200 -
2025/03/10 20:12:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:12:07] "OPTIONS /vue-admin-template/table/list HTTP/1.1" 200 -
2025/03/10 20:12:07 flask_api __init__.py[127] not_found() ERROR: 路由未找到: /vue-admin-template/table/list, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0MTYwODY5OSwianRpIjoiZGYwOTZmZjYtMzg4Ny00N2UzLTk0MWYtNzc2YTliOGM1ZTBkIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6Imp5amEiLCJuYmYiOjE3NDE2MDg2OTksImNzcmYiOiJiOGJkNDQ1Ni1lNWQxLTQ0NmMtYWZjZi02MDJhN2QzN2RiZGEiLCJleHAiOjE3NDE2MDk1OTl9.-_QiWc9mB7KnqyI_-n4aIO3VF1FZn3QcMMCHHJQB98o

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/03/10 20:12:07 root __init__.py[128] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/10 20:12:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:12:07] "[33mGET /vue-admin-template/table/list HTTP/1.1[0m" 404 -
2025/03/10 20:12:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:12:13] "OPTIONS /vue-admin-template/table/list HTTP/1.1" 200 -
2025/03/10 20:12:13 flask_api __init__.py[127] not_found() ERROR: 路由未找到: /vue-admin-template/table/list, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0MTYwODY5OSwianRpIjoiZGYwOTZmZjYtMzg4Ny00N2UzLTk0MWYtNzc2YTliOGM1ZTBkIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6Imp5amEiLCJuYmYiOjE3NDE2MDg2OTksImNzcmYiOiJiOGJkNDQ1Ni1lNWQxLTQ0NmMtYWZjZi02MDJhN2QzN2RiZGEiLCJleHAiOjE3NDE2MDk1OTl9.-_QiWc9mB7KnqyI_-n4aIO3VF1FZn3QcMMCHHJQB98o

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/03/10 20:12:13 root __init__.py[128] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/10 20:12:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:12:13] "[33mGET /vue-admin-template/table/list HTTP/1.1[0m" 404 -
2025/03/10 20:41:33 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/10 20:41:34 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/10 20:41:34 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/10 20:41:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:41:43] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 20:41:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:41:43] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/10 20:41:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:41:43] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/10 20:41:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:41:43] "GET /api/users HTTP/1.1" 200 -
2025/03/10 20:41:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:41:48] "GET /api/users HTTP/1.1" 200 -
2025/03/10 20:41:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:41:52] "OPTIONS /bikes?user_id=2 HTTP/1.1" 200 -
2025/03/10 20:41:52 flask_api __init__.py[127] not_found() ERROR: 路由未找到: /bikes, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0MTYxMDUwMywianRpIjoiYmFjOTgwZmItMjE5MS00ZDc2LWJiYjktZTg2NzFhYWQ3NTlmIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6InRlc3R1c2VyIiwibmJmIjoxNzQxNjEwNTAzLCJjc3JmIjoiMDdmYjQwODktN2Q2YS00Y2U3LTk5NmItNDliNzA0YzQ2NmE4IiwiZXhwIjoxNzQxNjExNDAzfQ.6RWNl_8F7t457Nbm58eh4CyP4WYBwwnpdeJp6f_0n4U

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/03/10 20:41:52 root __init__.py[128] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/10 20:41:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:41:52] "[33mGET /bikes?user_id=2 HTTP/1.1[0m" 404 -
2025/03/10 20:42:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:42:03] "OPTIONS /bikes?user_id=2 HTTP/1.1" 200 -
2025/03/10 20:42:03 flask_api __init__.py[127] not_found() ERROR: 路由未找到: /bikes, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0MTYxMDUwMywianRpIjoiYmFjOTgwZmItMjE5MS00ZDc2LWJiYjktZTg2NzFhYWQ3NTlmIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6InRlc3R1c2VyIiwibmJmIjoxNzQxNjEwNTAzLCJjc3JmIjoiMDdmYjQwODktN2Q2YS00Y2U3LTk5NmItNDliNzA0YzQ2NmE4IiwiZXhwIjoxNzQxNjExNDAzfQ.6RWNl_8F7t457Nbm58eh4CyP4WYBwwnpdeJp6f_0n4U

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/03/10 20:42:03 root __init__.py[128] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/10 20:42:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:42:03] "[33mGET /bikes?user_id=2 HTTP/1.1[0m" 404 -
2025/03/10 20:42:04 flask_api __init__.py[127] not_found() ERROR: 路由未找到: /bikes, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0MTYxMDUwMywianRpIjoiYmFjOTgwZmItMjE5MS00ZDc2LWJiYjktZTg2NzFhYWQ3NTlmIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6InRlc3R1c2VyIiwibmJmIjoxNzQxNjEwNTAzLCJjc3JmIjoiMDdmYjQwODktN2Q2YS00Y2U3LTk5NmItNDliNzA0YzQ2NmE4IiwiZXhwIjoxNzQxNjExNDAzfQ.6RWNl_8F7t457Nbm58eh4CyP4WYBwwnpdeJp6f_0n4U

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/03/10 20:42:04 root __init__.py[128] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/10 20:42:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:42:04] "[33mGET /bikes?user_id=2 HTTP/1.1[0m" 404 -
2025/03/10 20:42:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:42:55] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/10 20:42:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:42:55] "GET /api/users HTTP/1.1" 200 -
2025/03/10 20:42:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:42:57] "OPTIONS /bikes?user_id=2 HTTP/1.1" 200 -
2025/03/10 20:42:57 flask_api __init__.py[127] not_found() ERROR: 路由未找到: /bikes, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0MTYxMDUwMywianRpIjoiYmFjOTgwZmItMjE5MS00ZDc2LWJiYjktZTg2NzFhYWQ3NTlmIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6InRlc3R1c2VyIiwibmJmIjoxNzQxNjEwNTAzLCJjc3JmIjoiMDdmYjQwODktN2Q2YS00Y2U3LTk5NmItNDliNzA0YzQ2NmE4IiwiZXhwIjoxNzQxNjExNDAzfQ.6RWNl_8F7t457Nbm58eh4CyP4WYBwwnpdeJp6f_0n4U

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/03/10 20:42:57 root __init__.py[128] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/10 20:42:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:42:57] "[33mGET /bikes?user_id=2 HTTP/1.1[0m" 404 -
2025/03/10 20:52:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:52:17] "OPTIONS /api/bikes?user_id=2 HTTP/1.1" 200 -
2025/03/10 20:52:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:52:17] "GET /api/bikes?user_id=2 HTTP/1.1" 200 -
2025/03/10 20:52:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:52:50] "[31m[1mGET /api/bikes HTTP/1.1[0m" 401 -
2025/03/10 20:54:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:54:25] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/10 20:54:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:54:25] "GET /api/bikes HTTP/1.1" 200 -
2025/03/10 20:58:51 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/10 20:58:51 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/10 20:58:51 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/10 20:59:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:59:04] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/10 20:59:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:59:04] "[31m[1mGET /api/bikes HTTP/1.1[0m" 401 -
2025/03/10 20:59:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:59:07] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 20:59:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:59:07] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/10 20:59:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:59:12] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/10 20:59:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:59:26] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 20:59:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:59:26] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/10 20:59:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:59:32] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 20:59:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 20:59:32] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/10 21:03:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 21:03:00] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/10 21:03:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [10/Mar/2025 21:03:01] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
