"""reset database

Revision ID: 5792f68673ff
Revises: 74517edfc864
Create Date: 2025-05-13 09:56:24.645292

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5792f68673ff'
down_revision = '74517edfc864'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('parking_lots',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('address', sa.String(length=255), nullable=False),
    sa.Column('total_spaces', sa.Integer(), nullable=False),
    sa.Column('occupied_spaces', sa.Integer(), nullable=False),
    sa.Column('longitude', sa.Float(), nullable=True),
    sa.Column('latitude', sa.Float(), nullable=True),
    sa.Column('opening_hours', sa.String(length=100), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('campus', sa.String(length=50), nullable=True),
    sa.Column('area', sa.String(length=50), nullable=True),
    sa.Column('manager', sa.String(length=50), nullable=True),
    sa.Column('contact', sa.String(length=20), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('users',
    sa.Column('u_id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('u_name', sa.String(length=50), nullable=False),
    sa.Column('u_pwd', sa.String(length=255), nullable=False),
    sa.Column('salt', sa.String(length=36), nullable=False),
    sa.Column('u_role', sa.String(length=20), nullable=False),
    sa.Column('u_belong', sa.String(length=50), nullable=True),
    sa.Column('u_phone', sa.String(length=20), nullable=True),
    sa.Column('u_email', sa.String(length=100), nullable=True),
    sa.Column('avatar', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('version', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('u_id'),
    sa.UniqueConstraint('u_name')
    )
    op.create_table('violation_types',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('needs_admin', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('announcements',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=100), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('type', sa.String(length=20), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.u_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('bikes',
    sa.Column('b_id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('belong_to', sa.Integer(), nullable=False),
    sa.Column('b_num', sa.String(length=20), nullable=False, comment='车牌号'),
    sa.Column('brand', sa.String(length=255), nullable=False),
    sa.Column('color', sa.String(length=20), nullable=False),
    sa.Column('b_type', sa.String(length=255), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['belong_to'], ['users.u_id'], ),
    sa.PrimaryKeyConstraint('b_id'),
    sa.UniqueConstraint('b_num')
    )
    with op.batch_alter_table('bikes', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_bikes_belong_to'), ['belong_to'], unique=False)

    op.create_table('evidences',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('related_id', sa.Integer(), nullable=False),
    sa.Column('related_type', sa.String(length=20), nullable=False),
    sa.Column('evidence_type', sa.String(length=20), nullable=False),
    sa.Column('file_path', sa.String(length=255), nullable=False),
    sa.Column('uploader_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['uploader_id'], ['users.u_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('players',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('username', sa.String(length=120), nullable=False),
    sa.Column('password', sa.String(length=120), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True, comment='对应users表的u_id'),
    sa.ForeignKeyConstraint(['user_id'], ['users.u_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('username')
    )
    op.create_table('charging_reservations',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('vehicle_id', sa.Integer(), nullable=False),
    sa.Column('parking_lot_id', sa.Integer(), nullable=False),
    sa.Column('start_time', sa.DateTime(), nullable=False),
    sa.Column('end_time', sa.DateTime(), nullable=False),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['parking_lot_id'], ['parking_lots.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.u_id'], ),
    sa.ForeignKeyConstraint(['vehicle_id'], ['bikes.b_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('parking_spaces',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('parking_lot_id', sa.Integer(), nullable=False),
    sa.Column('space_number', sa.String(length=20), nullable=False),
    sa.Column('type', sa.Integer(), nullable=False),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('current_vehicle_id', sa.Integer(), nullable=True),
    sa.Column('power', sa.Float(), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.Column('last_maintenance_time', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['current_vehicle_id'], ['bikes.b_id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['parking_lot_id'], ['parking_lots.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('violation_records',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('bike_number', sa.String(length=50), nullable=False),
    sa.Column('bike_id', sa.Integer(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('violation_time', sa.DateTime(), nullable=False),
    sa.Column('location', sa.String(length=100), nullable=False),
    sa.Column('violation_type', sa.String(length=50), nullable=False),
    sa.Column('violation_type_id', sa.Integer(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('result', sa.Text(), nullable=True),
    sa.Column('recorder_id', sa.Integer(), nullable=False),
    sa.Column('handler_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['bike_id'], ['bikes.b_id'], ),
    sa.ForeignKeyConstraint(['handler_id'], ['users.u_id'], ),
    sa.ForeignKeyConstraint(['recorder_id'], ['users.u_id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.u_id'], ),
    sa.ForeignKeyConstraint(['violation_type_id'], ['violation_types.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('appeals',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('violation_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('reason', sa.Text(), nullable=False),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('comment', sa.Text(), nullable=True),
    sa.Column('handler_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['handler_id'], ['users.u_id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.u_id'], ),
    sa.ForeignKeyConstraint(['violation_id'], ['violation_records.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('parking_records',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('vehicle_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('parking_lot_id', sa.Integer(), nullable=False),
    sa.Column('parking_space_id', sa.Integer(), nullable=False),
    sa.Column('entry_time', sa.DateTime(), nullable=False),
    sa.Column('exit_time', sa.DateTime(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['parking_lot_id'], ['parking_lots.id'], ),
    sa.ForeignKeyConstraint(['parking_space_id'], ['parking_spaces.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.u_id'], ),
    sa.ForeignKeyConstraint(['vehicle_id'], ['bikes.b_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('charging_records',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('parking_record_id', sa.Integer(), nullable=False),
    sa.Column('vehicle_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('parking_lot_id', sa.Integer(), nullable=False),
    sa.Column('parking_space_id', sa.Integer(), nullable=False),
    sa.Column('start_time', sa.DateTime(), nullable=False),
    sa.Column('end_time', sa.DateTime(), nullable=True),
    sa.Column('duration', sa.Integer(), nullable=True),
    sa.Column('power', sa.Float(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['parking_lot_id'], ['parking_lots.id'], ),
    sa.ForeignKeyConstraint(['parking_record_id'], ['parking_records.id'], ),
    sa.ForeignKeyConstraint(['parking_space_id'], ['parking_spaces.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.u_id'], ),
    sa.ForeignKeyConstraint(['vehicle_id'], ['bikes.b_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('charging_records')
    op.drop_table('parking_records')
    op.drop_table('appeals')
    op.drop_table('violation_records')
    op.drop_table('parking_spaces')
    op.drop_table('charging_reservations')
    op.drop_table('players')
    op.drop_table('evidences')
    with op.batch_alter_table('bikes', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_bikes_belong_to'))

    op.drop_table('bikes')
    op.drop_table('announcements')
    op.drop_table('violation_types')
    op.drop_table('users')
    op.drop_table('parking_lots')
    # ### end Alembic commands ###
