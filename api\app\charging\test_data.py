"""
充电功能测试数据生成脚本
"""
from app import db
from app.parkinglots.models import ParkingLot, ParkingSpace
from app.charging.models import ChargingRecord, ChargingReservation
from app.bikes.models import Bikes
from datetime import datetime, timedelta
import random

def create_charging_spaces():
    """创建充电车位测试数据"""
    print("开始创建充电车位测试数据...")

    # 获取所有停车场
    parking_lots = ParkingLot.query.all()
    if not parking_lots:
        print("没有找到停车场，请先创建停车场")
        return

    # 为每个停车场创建充电车位
    for lot in parking_lots:
        print(f"为停车场 {lot.name} 创建充电车位...")

        # 检查是否已经有充电车位
        existing_charging_spaces = ParkingSpace.query.filter_by(
            parking_lot_id=lot.id,
            type=3  # 充电车位类型
        ).count()

        if existing_charging_spaces > 0:
            print(f"停车场 {lot.name} 已有 {existing_charging_spaces} 个充电车位，跳过")
            continue

        # 创建5个充电车位
        for i in range(1, 6):
            space_number = f"C-{i}"  # C表示充电车位

            # 检查车位号是否已存在
            if ParkingSpace.query.filter_by(
                parking_lot_id=lot.id,
                space_number=space_number
            ).first():
                print(f"车位号 {space_number} 已存在，跳过")
                continue

            # 创建充电车位
            charging_space = ParkingSpace(
                parking_lot_id=lot.id,
                space_number=space_number,
                type=3,  # 充电车位类型
                status=0,  # 空闲状态
                location_x=random.randint(1, 10),
                location_y=random.randint(1, 10)
            )
            db.session.add(charging_space)

        # 提交事务
        try:
            db.session.commit()
            print(f"为停车场 {lot.name} 创建了5个充电车位")
        except Exception as e:
            db.session.rollback()
            print(f"创建充电车位失败: {str(e)}")

    print("充电车位测试数据创建完成")

def create_charging_records():
    """创建充电记录测试数据"""
    print("开始创建充电记录测试数据...")

    # 获取所有用户的车辆
    vehicles = Bikes.query.all()
    if not vehicles:
        print("没有找到车辆，请先创建车辆")
        return

    # 获取所有充电车位
    charging_spaces = ParkingSpace.query.filter_by(type=3).all()
    if not charging_spaces:
        print("没有找到充电车位，请先创建充电车位")
        return

    # 创建进行中的充电记录
    print("创建进行中的充电记录...")
    for i in range(3):  # 创建3条进行中的记录
        if i >= len(vehicles) or i >= len(charging_spaces):
            break

        vehicle = vehicles[i]
        space = charging_spaces[i]

        # 检查车辆是否已有进行中的充电记录
        existing_record = ChargingRecord.query.filter_by(
            vehicle_id=vehicle.b_id,
            status=0  # 进行中
        ).first()

        if existing_record:
            print(f"车辆 {vehicle.b_num} 已有进行中的充电记录，跳过")
            continue

        # 检查车位是否已被占用
        if space.status != 0:
            print(f"车位 {space.space_number} 已被占用，跳过")
            continue

        # 创建充电记录
        start_time = datetime.now() - timedelta(hours=random.randint(1, 5))
        record = ChargingRecord(
            parking_record_id=1,  # 使用默认值1，实际应该关联到真实的停车记录
            vehicle_id=vehicle.b_id,
            user_id=vehicle.belong_to,
            parking_lot_id=space.parking_lot_id,
            parking_space_id=space.id,
            power=random.choice([3.3, 7.0, 11.0]),  # 充电功率
            charging_type=random.randint(1, 3)  # 1=慢充, 2=标准充, 3=快充
        )
        # 手动设置其他属性
        record.start_time = start_time
        record.end_time = None
        record.status = 0  # 进行中
        record.fee = 0  # 初始费用为0
        record.remarks = f"测试充电记录 {i+1}"
        db.session.add(record)

        # 更新车位状态
        space.status = 1  # 占用状态
        space.occupied_by = vehicle.b_id

    # 创建历史充电记录
    print("创建历史充电记录...")
    for i in range(10):  # 创建10条历史记录
        vehicle_index = random.randint(0, len(vehicles)-1)
        vehicle = vehicles[vehicle_index]

        # 随机选择一个充电车位
        space_index = random.randint(0, len(charging_spaces)-1)
        space = charging_spaces[space_index]

        # 创建历史充电记录
        days_ago = random.randint(1, 30)
        hours_duration = random.randint(1, 8)

        start_time = datetime.now() - timedelta(days=days_ago, hours=hours_duration)
        end_time = start_time + timedelta(hours=hours_duration)

        charging_type = random.randint(1, 3)  # 1=慢充, 2=标准充, 3=快充
        power = random.choice([3.3, 7.0, 11.0])  # 充电功率

        # 计算费用：时长（小时）* 功率（kW）* 单价（元/kWh）
        price_per_kwh = 0.5  # 假设电费为0.5元/kWh
        fee = round(hours_duration * power * price_per_kwh, 2)

        record = ChargingRecord(
            parking_record_id=i+10,  # 使用i+10作为ID，避免与进行中的记录冲突
            vehicle_id=vehicle.b_id,
            user_id=vehicle.belong_to,
            parking_lot_id=space.parking_lot_id,
            parking_space_id=space.id,
            power=power,
            charging_type=charging_type
        )
        # 手动设置其他属性
        record.start_time = start_time
        record.end_time = end_time
        record.status = 1  # 已完成
        record.fee = fee
        record.duration = hours_duration * 60  # 转换为分钟
        record.remarks = f"历史充电记录 {i+1}"
        db.session.add(record)

    # 提交事务
    try:
        db.session.commit()
        print("充电记录测试数据创建完成")
    except Exception as e:
        db.session.rollback()
        print(f"创建充电记录失败: {str(e)}")

def create_charging_reservations():
    """创建充电预约测试数据"""
    print("开始创建充电预约测试数据...")

    # 获取所有用户的车辆
    vehicles = Bikes.query.all()
    if not vehicles:
        print("没有找到车辆，请先创建车辆")
        return

    # 获取所有停车场
    parking_lots = ParkingLot.query.all()
    if not parking_lots:
        print("没有找到停车场，请先创建停车场")
        return

    # 创建未开始的预约
    print("创建未开始的预约...")
    for i in range(3):  # 创建3条未开始的预约
        if i >= len(vehicles):
            break

        vehicle = vehicles[i]
        lot = random.choice(parking_lots)

        # 创建未来的预约
        start_time = datetime.now() + timedelta(hours=random.randint(1, 24))
        end_time = start_time + timedelta(hours=random.randint(1, 4))

        reservation = ChargingReservation(
            user_id=vehicle.belong_to,
            vehicle_id=vehicle.b_id,
            parking_lot_id=lot.id,
            start_time=start_time,
            end_time=end_time,
            remarks=f"未来充电预约 {i+1}"
        )
        # 手动设置状态
        reservation.status = 0  # 未开始
        db.session.add(reservation)

    # 创建进行中的预约
    print("创建进行中的预约...")
    for i in range(2):  # 创建2条进行中的预约
        if i+3 >= len(vehicles):
            break

        vehicle = vehicles[i+3]
        lot = random.choice(parking_lots)

        # 创建当前进行中的预约
        start_time = datetime.now() - timedelta(hours=random.randint(1, 3))
        end_time = datetime.now() + timedelta(hours=random.randint(1, 3))

        reservation = ChargingReservation(
            user_id=vehicle.belong_to,
            vehicle_id=vehicle.b_id,
            parking_lot_id=lot.id,
            start_time=start_time,
            end_time=end_time,
            remarks=f"进行中充电预约 {i+1}"
        )
        # 手动设置状态
        reservation.status = 1  # 进行中
        db.session.add(reservation)

    # 创建已结束的预约
    print("创建已结束的预约...")
    for i in range(5):  # 创建5条已结束的预约
        vehicle_index = random.randint(0, len(vehicles)-1)
        vehicle = vehicles[vehicle_index]
        lot = random.choice(parking_lots)

        # 创建已结束的预约
        days_ago = random.randint(1, 30)
        hours_duration = random.randint(1, 4)

        start_time = datetime.now() - timedelta(days=days_ago, hours=hours_duration+2)
        end_time = start_time + timedelta(hours=hours_duration)

        reservation = ChargingReservation(
            user_id=vehicle.belong_to,
            vehicle_id=vehicle.b_id,
            parking_lot_id=lot.id,
            start_time=start_time,
            end_time=end_time,
            remarks=f"已结束充电预约 {i+1}"
        )
        # 手动设置状态
        reservation.status = 2  # 已结束
        db.session.add(reservation)

    # 提交事务
    try:
        db.session.commit()
        print("充电预约测试数据创建完成")
    except Exception as e:
        db.session.rollback()
        print(f"创建充电预约失败: {str(e)}")

def create_admin_charging_records():
    """为管理员用户创建更多的充电记录测试数据"""
    print("开始为管理员用户创建更多的充电记录测试数据...")

    # 获取管理员用户
    from app.users.models import User
    admin_users = User.query.filter_by(role='admin').all()
    if not admin_users:
        print("没有找到管理员用户，请先创建管理员用户")
        return

    # 获取管理员用户的车辆
    admin_vehicles = []
    for admin in admin_users:
        vehicles = Bikes.query.filter_by(belong_to=admin.id).all()
        admin_vehicles.extend(vehicles)

    if not admin_vehicles:
        print("管理员用户没有车辆，请先为管理员用户创建车辆")
        return

    # 获取所有充电车位
    charging_spaces = ParkingSpace.query.filter_by(type=3).all()
    if not charging_spaces:
        print("没有找到充电车位，请先创建充电车位")
        return

    # 获取停车记录，用于关联充电记录
    from app.parking_records.models import ParkingRecord
    parking_records = ParkingRecord.query.all()
    if not parking_records:
        print("没有找到停车记录，将使用默认ID")
        # 创建一些默认的停车记录ID
        parking_record_ids = list(range(1000, 1050))
    else:
        # 使用现有的停车记录ID
        parking_record_ids = [record.id for record in parking_records]

    # 充电类型与功率的对应关系
    charging_types = {
        1: {'name': '慢充', 'power': 3.3},
        2: {'name': '标准充', 'power': 7.0},
        3: {'name': '快充', 'power': 11.0}
    }

    # 创建历史充电记录
    print("为管理员用户创建历史充电记录...")
    for i in range(20):  # 创建20条历史记录
        vehicle = random.choice(admin_vehicles)

        # 随机选择一个充电车位
        space = random.choice(charging_spaces)

        # 创建历史充电记录
        days_ago = random.randint(1, 30)
        hours_duration = random.randint(1, 8)

        start_time = datetime.now() - timedelta(days=days_ago, hours=hours_duration)
        end_time = start_time + timedelta(hours=hours_duration)

        # 随机选择充电类型
        charging_type = random.randint(1, 3)  # 1=慢充, 2=标准充, 3=快充
        power = charging_types[charging_type]['power']  # 根据充电类型设置对应的功率

        # 随机选择一个停车记录ID
        parking_record_id = random.choice(parking_record_ids)

        # 创建充电记录
        record = ChargingRecord(
            parking_record_id=parking_record_id,
            vehicle_id=vehicle.b_id,
            user_id=vehicle.belong_to,
            parking_lot_id=space.parking_lot_id,
            parking_space_id=space.id,
            power=power,
            charging_type=charging_type
        )

        # 手动设置其他属性
        record.start_time = start_time
        record.end_time = end_time
        record.status = 1  # 已完成
        record.duration = int((end_time - start_time).total_seconds() / 60)  # 精确计算充电时长（分钟）

        # 设置更详细的备注信息
        charging_type_name = charging_types[charging_type]['name']
        record.remarks = f"管理员用户 {vehicle.belong_to} 使用 {charging_type_name}({power}kW) 为车辆 {vehicle.b_num} 充电 {hours_duration}小时"

        db.session.add(record)

    # 创建进行中的充电记录
    print("为管理员用户创建进行中的充电记录...")
    for i in range(2):  # 创建2条进行中的记录
        if i >= len(admin_vehicles):
            break

        vehicle = admin_vehicles[i]

        # 找到一个空闲的充电车位
        available_spaces = [space for space in charging_spaces if space.status == 0]
        if not available_spaces:
            print("没有可用的充电车位，跳过创建进行中的充电记录")
            break

        space = random.choice(available_spaces)

        # 检查车辆是否已有进行中的充电记录
        existing_record = ChargingRecord.query.filter_by(
            vehicle_id=vehicle.b_id,
            status=0  # 进行中
        ).first()

        if existing_record:
            print(f"车辆 {vehicle.b_num} 已有进行中的充电记录，跳过")
            continue

        # 随机选择充电类型
        charging_type = random.randint(1, 3)  # 1=慢充, 2=标准充, 3=快充
        power = charging_types[charging_type]['power']  # 根据充电类型设置对应的功率

        # 随机选择一个停车记录ID
        parking_record_id = random.choice(parking_record_ids)

        # 创建充电记录
        start_time = datetime.now() - timedelta(hours=random.randint(1, 5))
        record = ChargingRecord(
            parking_record_id=parking_record_id,
            vehicle_id=vehicle.b_id,
            user_id=vehicle.belong_to,
            parking_lot_id=space.parking_lot_id,
            parking_space_id=space.id,
            power=power,
            charging_type=charging_type
        )

        # 手动设置其他属性
        record.start_time = start_time
        record.end_time = None
        record.status = 0  # 进行中

        # 设置更详细的备注信息
        charging_type_name = charging_types[charging_type]['name']
        estimated_duration = random.randint(1, 6)  # 预计充电时长（小时）
        record.remarks = f"管理员用户 {vehicle.belong_to} 正在使用 {charging_type_name}({power}kW) 为车辆 {vehicle.b_num} 充电，预计充电 {estimated_duration}小时"

        db.session.add(record)

        # 更新车位状态
        space.status = 1  # 占用状态
        space.occupied_by = vehicle.b_id

    # 提交事务
    try:
        db.session.commit()
        print("管理员用户充电记录测试数据创建完成")
    except Exception as e:
        db.session.rollback()
        print(f"创建管理员用户充电记录失败: {str(e)}")

def generate_all_test_data():
    """生成所有充电功能测试数据"""
    create_charging_spaces()
    create_charging_records()
    create_charging_reservations()
    create_admin_charging_records()
    print("所有充电功能测试数据生成完成")

if __name__ == "__main__":
    generate_all_test_data()
