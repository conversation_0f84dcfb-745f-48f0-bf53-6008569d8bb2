import request from '@/utils/request'

/**
 * 获取用户车辆列表
 * @param {number} userId 用户ID
 * @param {object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getUserVehicles(userId, params = {}) {
  return request({
    url: `/api/my-bikes`,
    method: 'get',
    params
  })
}

/**
 * 获取所有车辆列表
 * @param {object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getAllVehicles(params) {
  return request({
    url: '/api/bikes',
    method: 'get',
    params
  })
}

/**
 * 获取车辆详情
 * @param {number} vehicleId 车辆ID
 * @returns {Promise} 请求Promise
 */
export function getVehicleDetail(vehicleId) {
  return request({
    url: `/api/bikes/${vehicleId}`,
    method: 'get'
  })
}

/**
 * 创建车辆
 * @param {object} data 车辆数据
 * @returns {Promise} 请求Promise
 */
export function createVehicle(data) {
  return request({
    url: '/api/bikes',
    method: 'post',
    data
  })
}

/**
 * 更新车辆
 * @param {number} vehicleId 车辆ID
 * @param {object} data 车辆数据
 * @returns {Promise} 请求Promise
 */
export function updateVehicle(vehicleId, data) {
  return request({
    url: `/api/bikes/${vehicleId}`,
    method: 'put',
    data
  })
}

/**
 * 删除车辆
 * @param {number} vehicleId 车辆ID
 * @returns {Promise} 请求Promise
 */
export function deleteVehicle(vehicleId) {
  return request({
    url: `/api/bikes/${vehicleId}`,
    method: 'delete'
  })
}

/**
 * 获取车辆统计数据
 * @returns {Promise} 请求Promise
 */
export function getVehicleStats() {
  return request({
    url: '/api/bikes/stats',
    method: 'get'
  })
}

/**
 * 批量更新车辆状态
 * @param {object} data 批量更新数据
 * @returns {Promise} 请求Promise
 */
export function batchUpdateVehicleStatus(data) {
  return request({
    url: '/api/bikes/batch-update',
    method: 'post',
    data
  })
}

/**
 * 导出车辆数据为CSV
 * @returns {Promise} 请求Promise
 */
export function exportVehiclesCSV() {
  return request({
    url: '/api/bikes/export',
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 检查车辆禁用状态
 * @param {Number} vehicleId - 车辆ID
 * @returns {Promise} 请求Promise
 */
export function checkVehicleDisableStatus(vehicleId) {
  return request({
    url: `/api/bikes/${vehicleId}/disable-status`,
    method: 'get'
  })
}

/**
 * 禁用车辆
 * @param {Number} vehicleId - 车辆ID
 * @param {Object} data - 禁用信息
 * @returns {Promise} 请求Promise
 */
export function disableVehicle(vehicleId, data) {
  return request({
    url: `/api/bikes/${vehicleId}/disable`,
    method: 'post',
    data
  })
}

/**
 * 解除车辆禁用
 * @param {Number} vehicleId - 车辆ID
 * @param {Object} data - 解除禁用信息
 * @returns {Promise} 请求Promise
 */
export function enableVehicle(vehicleId, data = {}) {
  return request({
    url: `/api/bikes/${vehicleId}/enable`,
    method: 'post',
    data
  })
}

/**
 * 获取车辆禁用记录
 * @param {Number} vehicleId - 车辆ID
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求Promise
 */
export function getVehicleDisableRecords(vehicleId, params = {}) {
  return request({
    url: `/api/bikes/${vehicleId}/disable-records`,
    method: 'get',
    params
  })
}

/**
 * 获取所有禁用记录
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求Promise
 */
export function getAllDisableRecords(params = {}) {
  return request({
    url: '/api/bikes/disable-records',
    method: 'get',
    params
  })
}
