# 校园电动车管理系统 - Chen表示法E-R图

## 系统核心E-R图 (Chen表示法风格)

### 主要实体和关系图

```mermaid
graph TD
    %% 实体定义 (矩形)
    A[用户]
    B[车辆]
    C[停车场]
    D[车位]
    E[停车记录]
    F[违规记录]
    G[违规类型]
    H[申诉]
    I[充电记录]
    J[公告]

    %% 属性定义 (椭圆)
    A1((用户ID))
    A2((用户名))
    A3((密码))
    A4((角色))
    A5((学院))
    A6((手机号))

    B1((车辆ID))
    B2((车牌号))
    B3((品牌))
    B4((颜色))
    B5((类型))
    B6((状态))

    C1((停车场ID))
    C2((名称))
    C3((地址))
    C4((总车位数))
    C5((校区))
    C6((区域))

    D1((车位ID))
    D2((车位编号))
    D3((类型))
    D4((状态))
    D5((充电功率))

    E1((记录ID))
    E2((入场时间))
    E3((出场时间))
    E4((状态))

    F1((违规ID))
    F2((车牌号))
    F3((违规时间))
    F4((违规地点))
    F5((描述))

    G1((类型ID))
    G2((类型名称))
    G3((描述))

    H1((申诉ID))
    H2((申诉理由))
    H3((申诉状态))

    I1((充电ID))
    I2((开始时间))
    I3((结束时间))
    I4((充电时长))

    J1((公告ID))
    J2((标题))
    J3((内容))
    J4((类型))

    %% 关系定义 (菱形)
    R1{拥有}
    R2{停车}
    R3{包含}
    R4{使用}
    R5{违规}
    R6{分类}
    R7{申诉}
    R8{充电}
    R9{发布}

    %% 实体与属性连接
    A --- A1
    A --- A2
    A --- A3
    A --- A4
    A --- A5
    A --- A6

    B --- B1
    B --- B2
    B --- B3
    B --- B4
    B --- B5
    B --- B6

    C --- C1
    C --- C2
    C --- C3
    C --- C4
    C --- C5
    C --- C6

    D --- D1
    D --- D2
    D --- D3
    D --- D4
    D --- D5

    E --- E1
    E --- E2
    E --- E3
    E --- E4

    F --- F1
    F --- F2
    F --- F3
    F --- F4
    F --- F5

    G --- G1
    G --- G2
    G --- G3

    H --- H1
    H --- H2
    H --- H3

    I --- I1
    I --- I2
    I --- I3
    I --- I4

    J --- J1
    J --- J2
    J --- J3
    J --- J4

    %% 实体关系连接
    A ---|1| R1
    R1 ---|m| B

    A ---|1| R2
    R2 ---|m| E

    C ---|1| R3
    R3 ---|m| D

    D ---|1| R4
    R4 ---|m| E

    B ---|1| R5
    R5 ---|m| F

    G ---|1| R6
    R6 ---|m| F

    F ---|1| R7
    R7 ---|1| H

    B ---|1| R8
    R8 ---|m| I

    A ---|1| R9
    R9 ---|m| J

    %% 样式定义
    classDef entity fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef attribute fill:#f3e5f5,stroke:#4a148c,stroke-width:1px
    classDef relationship fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class A,B,C,D,E,F,G,H,I,J entity
    class A1,A2,A3,A4,A5,A6,B1,B2,B3,B4,B5,B6,C1,C2,C3,C4,C5,C6,D1,D2,D3,D4,D5,E1,E2,E3,E4,F1,F2,F3,F4,F5,G1,G2,G3,H1,H2,H3,I1,I2,I3,I4,J1,J2,J3,J4 attribute
    class R1,R2,R3,R4,R5,R6,R7,R8,R9 relationship
```

### 简化版核心关系图

```mermaid
erDiagram
    用户 {
        int 用户ID PK
        string 用户名
        string 角色
        string 学院
        string 手机号
    }

    车辆 {
        int 车辆ID PK
        int 所属用户ID FK
        string 车牌号
        string 品牌
        string 颜色
        string 状态
    }

    停车场 {
        int 停车场ID PK
        string 名称
        string 地址
        int 总车位数
        string 校区
        string 区域
    }

    车位 {
        int 车位ID PK
        int 停车场ID FK
        string 车位编号
        int 类型
        int 状态
    }

    停车记录 {
        int 记录ID PK
        int 车辆ID FK
        int 用户ID FK
        int 停车场ID FK
        int 车位ID FK
        datetime 入场时间
        datetime 出场时间
        int 状态
    }

    违规记录 {
        int 违规ID PK
        string 车牌号
        int 车辆ID FK
        int 用户ID FK
        datetime 违规时间
        string 违规地点
        string 违规类型
        int 状态
    }

    违规类型 {
        int 类型ID PK
        string 类型名称
        string 描述
        boolean 需要管理员
    }

    申诉 {
        int 申诉ID PK
        int 违规ID FK
        int 用户ID FK
        string 申诉理由
        int 状态
        string 处理意见
    }

    充电记录 {
        int 充电ID PK
        int 停车记录ID FK
        int 车辆ID FK
        int 用户ID FK
        datetime 开始时间
        datetime 结束时间
        int 充电时长
    }

    %% 关系定义 - 使用中文标签
    用户 ||--o{ 车辆 : "拥有"
    用户 ||--o{ 停车记录 : "停车"
    用户 ||--o{ 违规记录 : "违规"
    用户 ||--o{ 申诉 : "提交"
    用户 ||--o{ 充电记录 : "充电"

    停车场 ||--o{ 车位 : "包含"
    停车场 ||--o{ 停车记录 : "发生"

    车位 ||--o{ 停车记录 : "使用"
    车辆 ||--o{ 停车记录 : "停放"
    车辆 ||--o{ 违规记录 : "违规"
    车辆 ||--o{ 充电记录 : "充电"

    违规类型 ||--o{ 违规记录 : "分类"
    违规记录 ||--o| 申诉 : "申诉"

    停车记录 ||--o{ 充电记录 : "关联"
```

## Chen表示法设计说明

### 设计特点

参考您提供的ER图设计格式，我采用了经典的Chen表示法，具有以下特点：

#### 1. **图形符号规范**
- **矩形** 🔲：表示实体 (如：用户、车辆、停车场)
- **椭圆** ⭕：表示属性 (如：用户ID、用户名、车牌号)
- **菱形** 🔶：表示关系 (如：拥有、停车、违规)
- **线条** ➖：连接实体、属性和关系

#### 2. **基数标记**
- **1**：一对一关系的"一"端
- **m** 或 **n**：一对多关系的"多"端
- **1:1**：一对一关系
- **1:m**：一对多关系
- **m:n**：多对多关系

#### 3. **中文化设计**
- 所有实体名称使用中文 (用户、车辆、停车场等)
- 所有属性名称使用中文 (用户ID、车牌号、入场时间等)
- 所有关系名称使用中文动词 (拥有、停车、违规、申诉等)

### 核心实体关系分析

#### **用户中心的关系网络**
```
用户 ──1:m── 车辆     (一个用户可以拥有多辆车)
用户 ──1:m── 停车记录  (一个用户可以有多次停车)
用户 ──1:m── 违规记录  (一个用户可能有多次违规)
用户 ──1:m── 申诉     (一个用户可以提交多次申诉)
用户 ──1:m── 充电记录  (一个用户可以有多次充电)
用户 ──1:m── 公告     (管理员可以发布多个公告)
```

#### **停车场管理关系**
```
停车场 ──1:m── 车位     (一个停车场包含多个车位)
停车场 ──1:m── 停车记录  (一个停车场发生多次停车)
车位 ──1:m── 停车记录   (一个车位可以被多次使用)
```

#### **违规管理关系**
```
违规类型 ──1:m── 违规记录  (一种违规类型对应多条记录)
违规记录 ──1:1── 申诉     (一条违规记录最多一次申诉)
```

#### **充电管理关系**
```
停车记录 ──1:m── 充电记录  (一次停车可能包含多次充电)
车辆 ──1:m── 充电记录     (一辆车可以有多次充电记录)
```

### 属性设计原则

#### **主键属性** (下划线标识)
- 每个实体都有唯一标识符
- 采用自增整数ID作为主键
- 如：用户ID、车辆ID、停车场ID

#### **描述性属性**
- 用户：用户名、密码、角色、学院、手机号
- 车辆：车牌号、品牌、颜色、类型、状态
- 停车场：名称、地址、总车位数、校区、区域
- 车位：车位编号、类型、状态、充电功率

#### **时间属性**
- 停车记录：入场时间、出场时间
- 违规记录：违规时间
- 充电记录：开始时间、结束时间、充电时长

#### **状态属性**
- 车辆状态：可用/废弃
- 车位状态：空闲/已占用/故障/维修中/禁用
- 停车记录状态：进行中/已完成/异常
- 违规记录状态：待审核/已处理/申诉中/已撤销
- 申诉状态：待审核/已通过/未通过

### 业务规则体现

#### **完整性约束**
1. **实体完整性**：每个实体都有主键
2. **参照完整性**：外键关系保证数据一致性
3. **用户定义完整性**：业务规则约束

#### **业务逻辑约束**
1. **用户角色权限**：admin > security > user
2. **车位占用逻辑**：一个车位同时只能停一辆车
3. **违规申诉规则**：一条违规记录最多一次申诉
4. **充电依赖关系**：充电必须基于有效的停车记录

### 与传统ER图的对比

#### **优势**
- ✅ 符合经典Chen表示法标准
- ✅ 中文化便于理解
- ✅ 清晰的视觉层次
- ✅ 完整的属性展示

#### **适用场景**
- 📚 教学和学习用途
- 📋 系统设计文档
- 🔍 业务分析和讨论
- 📊 数据库设计规范

这种Chen表示法的E-R图设计完全遵循了您提供的参考格式，使用矩形表示实体、椭圆表示属性、菱形表示关系，并且采用中文标识，便于理解和交流。
