# 在导入任何其他模块之前进行monkey patch
import eventlet
eventlet.monkey_patch()

import os
import sys
import uuid
import hashlib
import json

# 将项目根目录加入系统路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from flask import request, Flask, redirect, url_for, make_response, jsonify
from flask_jwt_extended import create_access_token, JWTManager
from app.players.models import Players
from app.utils.response import api_response
from app import create_app, db, socketio
from app.users.models import Users
from app.utils.password_utils import get_salt_if_none

# 创建应用实例
app = create_app()
print("Flask应用已创建")

# 所有蓝图和插件应已在create_app中注册

# 重新打印路由映射
print("应用路由映射：")
for rule in app.url_map.iter_rules():
    print(f"{rule.endpoint}: {rule.rule} [{', '.join(rule.methods)}]")

# 直接添加登录路由，覆盖可能存在的其他定义
@app.route("/api/login", methods=['GET', 'POST', 'OPTIONS'])
def direct_login():
    print("接收到登录请求")
    if request.method == 'OPTIONS':
        # 处理预检请求 - 不要在这里设置CORS头，让CORS插件处理
        return '', 200

    # 处理GET请求 - 可能是用户直接在浏览器访问此URL
    if request.method == 'GET':
        print("收到GET请求，重定向到首页")
        # 重定向到首页或登录页面
        return redirect(url_for('root_index'))

    # 检查原始请求的内容类型和数据
    print(f"请求内容类型: {request.content_type}")
    print(f"请求头: {dict(request.headers)}")
    try:
        raw_data = request.get_data(as_text=True)
        print(f"原始请求数据: {raw_data}")
    except:
        print("无法获取原始请求数据")

    # 处理POST请求 - 正常的登录逻辑
    try:
        try:
            data = request.get_json(force=True) or {}
        except Exception as e:
            print(f"解析JSON数据失败: {str(e)}，使用空数据")
            return api_response(message="请求数据格式错误", status="error", code=422)

        print(f"登录请求数据: {data}")

        if not data or 'username' not in data or 'password' not in data:
            print("缺少username或password字段")
            return api_response(message="请提供用户名和密码", status="error", code=422)

        # 直接在Users表中查找用户
        print(f"尝试在Users表中查找用户: {data['username']}")

        # 在Users表中查找用户
        user = Users.query.filter_by(u_name=data['username']).first()

        if user:
            print(f"在Users表中找到用户: {user.u_name}, ID: {user.u_id}")
            print(f"用户盐值: {user.salt}")

            # 使用用户的盐值和输入的密码生成哈希
            salt = user.salt or ''
            input_password_hash = hashlib.sha256(f"{data['password']}{salt}".encode()).hexdigest()
            print(f"输入密码哈希: {input_password_hash[:10]}...")
            print(f"存储的密码哈希: {user.u_pwd[:10]}...")

            # 验证密码
            if input_password_hash == user.u_pwd:
                print("密码验证成功!")

                # 生成令牌
                identity = str(user.u_id)  # 使用用户ID作为标识符
                access_token = create_access_token(identity=identity)
                print(f"已生成令牌: {access_token[:20]}...")

                # 构建标准响应数据，符合前端期望的格式
                # 使用与前端完全匹配的格式
                response_data = {
                    "code": 20000,
                    "data": {
                        "token": access_token,
                        "user": {
                            "u_id": user.u_id,
                            "u_name": user.u_name,
                            "u_role": user.u_role,
                            "u_belong": user.u_belong,
                            "u_phone": user.u_phone
                        }
                    }
                }

                # 直接返回JSON响应，不使用response_with
                print(f"返回登录响应: {response_data}")
                return jsonify(response_data), 200
            else:
                print("密码验证失败!")
                return api_response(message="用户名或密码错误", status="error", code=401)

        # 如果Users表中没找到，返回错误
        print(f"用户不存在: {data['username']}")
        return api_response(message="用户名或密码错误", status="error", code=401)

    except Exception as e:
        print(f"登录发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return api_response(message="服务器内部错误", status="error", code=500)

@app.route("/api/register", methods=['GET', 'POST', 'OPTIONS'])
def direct_register():
    print("接收到注册请求")
    if request.method == 'OPTIONS':
        # 处理预检请求 - 不要在这里设置CORS头，让CORS插件处理
        return '', 200

    # 处理GET请求 - 可能是用户直接在浏览器访问此URL
    if request.method == 'GET':
        print("收到GET请求，重定向到首页")
        # 重定向到首页或注册页面
        return redirect(url_for('root_index'))

    # 处理POST请求 - 正常的注册逻辑
    try:
        try:
            data = request.get_json(force=True) or {}
        except Exception as e:
            print(f"解析JSON数据失败: {str(e)}，使用空数据")
            return api_response(message="请求数据格式错误", status="error", code=422)

        print(f"注册请求数据: {data}")

        if not data or 'username' not in data or 'password' not in data:
            print("缺少username或password字段")
            return api_response(message="请提供用户名和密码", status="error", code=422)

        # 检查用户名是否已存在于players表
        if Players.find_by_username(data['username']):
            print(f"用户名已存在于players表: {data['username']}")
            return api_response(message="用户名已存在", status="error", code=422)

        # 检查用户名是否已存在于users表
        existing_user = Users.query.filter_by(u_name=data['username']).first()
        if existing_user:
            print(f"用户名已存在于users表: {data['username']}")
            return api_response(message="用户名已存在", status="error", code=422)

        # 开始数据库事务
        try:
            # 1. 先创建Players表记录
            hashed_password = Players.generate_hash(data['password'])
            new_player = Players(
                username=data['username'],
                password=hashed_password
            )

            db.session.add(new_player)
            db.session.flush()  # 获取新创建的player的ID

            # 2. 创建Users表记录并关联 (使用安全的密码存储)
            # 生成随机盐值和哈希密码
            salt = str(uuid.uuid4())
            hashed_pwd = hashlib.sha256(f"{data['password']}{salt}".encode()).hexdigest()

            print(f"注册用户 {data['username']} 使用的盐值: {salt}")
            print(f"注册用户 {data['username']} 的哈希密码: {hashed_pwd[:10]}...")

            new_user = Users(
                u_name=data['username'],
                u_pwd=hashed_pwd,  # 直接传入哈希后的密码
                u_role=data.get('role', 'user'),  # 默认角色为user
                u_belong=data.get('department', ''),  # 使用统一的字段名department
                u_phone=data.get('phone', '')  # 可选电话
            )
            # 手动设置盐值
            new_user.salt = salt

            db.session.add(new_user)
            db.session.flush()  # 获取新创建的user的ID

            # 3. 更新players表，添加关联
            new_player.user_id = new_user.u_id
            db.session.commit()

            print(f"用户注册成功: {new_player.username}, players.id={new_player.id}, users.u_id={new_user.u_id}")

            # 使用标准格式返回成功响应
            return api_response(
                data={
                    "player_id": new_player.id,
                    "user_id": new_user.u_id
                },
                message="注册成功",
                code=201
            )

        except Exception as inner_e:
            db.session.rollback()
            print(f"注册过程中发生数据库错误: {str(inner_e)}")
            import traceback
            print(traceback.format_exc())

            # 提供更友好的错误信息
            error_message = str(inner_e)
            if "UNIQUE constraint failed" in error_message:
                return api_response(message="用户名已存在", status="error", code=422)

            return api_response(
                message="注册失败，请稍后重试",
                status="error",
                code=400,
                errors={"details": error_message}
            )

    except Exception as e:
        print(f"注册发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return api_response(message="服务器内部错误，请稍后重试", status="error", code=500)

# 确保根路由可用
@app.route("/")
def root_index():
  return app.send_static_file('index.html')

# 处理所有其他路由，如果是API路由返回404，否则重定向到前端路由
@app.route('/<path:path>')
def catch_all(path):
    print(f"捕获到未定义的路由: {path}")
    # 如果是API请求，返回404
    if path.startswith('api/'):
        return api_response(message="资源不存在", status="error", code=404)
    # 非API请求，重定向到前端应用（让前端路由处理）
    return app.send_static_file('index.html')

# 添加检查用户名是否存在的API
@app.route("/api/check-username", methods=['POST', 'OPTIONS'])
def check_username():
    print("接收到检查用户名请求")
    if request.method == 'OPTIONS':
        # 处理预检请求
        return '', 200

    try:
        data = request.get_json(force=True) or {}
        if not data or 'username' not in data:
            return api_response(message="请提供用户名", status="error", code=422)

        username = data['username']
        print(f"检查用户名是否存在: {username}")

        # 检查players表
        player_exists = Players.find_by_username(username) is not None

        # 检查users表
        user_exists = Users.query.filter_by(u_name=username).first() is not None

        exists = player_exists or user_exists

        if exists:
            print(f"用户名 {username} 已存在")
        else:
            print(f"用户名 {username} 可用")

        # 使用统一的响应格式
        return api_response(
            data={
                "exists": exists,
                "message": "用户名已存在" if exists else "用户名可用"
            },
            message="检查成功",
            code=200
        )
    except Exception as e:
        print(f"检查用户名失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return api_response(message="服务器内部错误", status="error", code=500)

# 在适当的位置添加登出API
@app.route("/api/logout", methods=['POST', 'OPTIONS'])
def direct_logout():
    print("接收到登出请求")
    if request.method == 'OPTIONS':
        # 处理预检请求
        return '', 200

    try:
        # 实际上无需在服务器端维护会话，JWT认证是无状态的
        # 前端应自行清除令牌
        return api_response(message="登出成功", code=200)
    except Exception as e:
        print(f"处理登出请求时发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        # 即使出错也返回成功，因为前端已经清除了token
        return api_response(message="登出成功", code=200)

if __name__ == '__main__':
    # 打印路由映射
    print("启动Flask应用，地址：http://127.0.0.1:5000")
    # 使用socketio.run代替app.run
    print("启动WebSocket服务，地址：http://127.0.0.1:5000/socket.io")

    # 使用socketio.run方法启动应用
    socketio.run(app, port=5000, host='0.0.0.0', debug=True, allow_unsafe_werkzeug=True)
