from app import db
from marshmallow_sqlalchemy import SQLAlchemyAutoSchema
from marshmallow import fields
from datetime import datetime


# 电动车信息模型
class Bikes(db.Model):
    __tablename__ = 'bikes'
    # 自增ID
    b_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 所属用户ID
    belong_to = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=False, index=True)
    # 车牌号
    b_num = db.Column(db.String(20), unique=True, nullable=False, comment='车牌号')
    # 品牌
    brand = db.Column(db.String(255), nullable=False, default="未知品牌")
    # 颜色
    color = db.Column(db.String(20), nullable=False, default="未知颜色")
    # 类型
    b_type = db.Column(db.String(255), nullable=False, default="普通型号")
    # 状态（只能是"可用"或"废弃"）
    status = db.Column(db.String(20), nullable=False, default="可用")
    # 创建时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 初始化函数
    def __init__(self, belong_to=1, b_num="未知车牌", brand="未知品牌", color="未知颜色", b_type="普通型号", status="可用"):
        self.belong_to = belong_to
        self.b_num = b_num
        self.brand = brand
        self.color = color
        self.b_type = b_type
        
        # 验证status字段值
        valid_status = ['可用', '废弃']
        if status in valid_status:
            self.status = status
        else:
            self.status = "可用"
            
        # 设置创建和更新时间
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

    def __repr__(self):
        if self.b_id is None:
            return '<Bike (new)>'
        return f'<Bike {self.b_id}: {self.b_num}>'

    # 创建bike信息接口
    def create(self):
        db.session.add(self)
        db.session.commit()
        return self
        
    # 更新bike信息
    def update(self, **kwargs):
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.now()
        db.session.commit()
        return self
        
    # 删除bike
    def delete(self):
        db.session.delete(self)
        db.session.commit()
