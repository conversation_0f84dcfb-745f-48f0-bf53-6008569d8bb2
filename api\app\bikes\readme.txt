一、bikes下的路由增加了用户身份认证功能
1.访问获取数据信息接口，没有做用户授权时：提示msg：missing xxx
2.增加用户授权的情况，这里使用的Token，就是我们调用登录接口时返回的access_token信息。
二、bikes下的接口完善了部分功能：
1.接受空的请求体，并提供所有字段的默认值
2.处理了content-type问题，无论客户端如何发送请求，都能使用默认空数据并提供日志


注意！！！以上功能仅在bikes模块下实现了，其余模块是否需要实现，需要后续考虑优化
--------------------------------------2025.03.08-------------------------------------------
JWT
在实际应用中，我们还可以使用电子邮件验证和限制用户注册，
我们还可以启用基于用户的访问控制，不同类型的用户可以访问特定的api。

思考：可以利用JWT实现
1、管理员可以访问所有接口，
2、用户访问：自己的用户信息、车辆信息接口
3、保安访问：自己的用户信息、车辆信息接口，违规的接口，停车场的接口


bike的数据表需要拓展：b_num

    b_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    b_num --> 车牌号
    belong_to = db.Column(db.Integer, db.ForeignKey('users.u_id'))  车主
    brand = db.Column(db.String(255))  品牌
    color = db.Column(db.String(20))   颜色
    b_type = db.Column(db.String(255))  型号
    status = db.Column(db.String(20))  # 状态只能是"可用"或"废弃"

优化：
1、为所有bikes相关API添加身份验证
2、完善schema定义：车牌号、颜色
3、优化数据库模型
3、前后端bike功能、组件开发相关字段统一并提高健壮性



