<template>
  <div class="start-charging-form">
    <div class="form-title">开始充电</div>
    
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="80px"
      size="small"
    >
      <el-form-item label="车辆" prop="vehicle_id">
        <el-select
          v-model="form.vehicle_id"
          placeholder="请选择车辆"
          style="width: 100%"
          @change="handleVehicleChange"
        >
          <el-option
            v-for="vehicle in vehicles"
            :key="vehicle.id"
            :label="vehicle.number"
            :value="vehicle.id"
          >
            <div class="vehicle-option">
              <span>{{ vehicle.number }}</span>
              <div class="vehicle-info">
                <span>{{ vehicle.brand }}</span>
                <span class="vehicle-color" :style="{ backgroundColor: vehicle.color }"></span>
              </div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="充电时长" prop="estimated_duration">
        <el-select
          v-model="form.estimated_duration"
          placeholder="请选择充电时长"
          style="width: 100%"
        >
          <el-option label="1小时" value="1" />
          <el-option label="2小时" value="2" />
          <el-option label="3小时" value="3" />
          <el-option label="4小时" value="4" />
          <el-option label="5小时" value="5" />
          <el-option label="6小时" value="6" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="form.remarks"
          type="textarea"
          :rows="2"
          placeholder="请输入备注信息（选填）"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" :loading="loading" @click="submitForm">开始充电</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getUserVehicles } from '@/api/vehicle'
import { startCharging } from '@/api/charging'

export default {
  name: 'StartChargingForm',
  props: {
    parkingLotId: {
      type: Number,
      required: true
    },
    parkingSpaceId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      form: {
        vehicle_id: '',
        estimated_duration: '3',
        remarks: ''
      },
      rules: {
        vehicle_id: [
          { required: true, message: '请选择车辆', trigger: 'change' }
        ],
        estimated_duration: [
          { required: true, message: '请选择充电时长', trigger: 'change' }
        ]
      },
      vehicles: [],
      loading: false,
      selectedVehicle: null
    }
  },
  created() {
    this.fetchUserVehicles()
  },
  methods: {
    // 获取用户车辆
    fetchUserVehicles() {
      getUserVehicles()
        .then(response => {
          this.vehicles = response.data || []
          
          // 如果有车辆，默认选择第一个
          if (this.vehicles.length > 0 && !this.form.vehicle_id) {
            this.form.vehicle_id = this.vehicles[0].id
            this.selectedVehicle = this.vehicles[0]
          }
        })
        .catch(error => {
          console.error('获取用户车辆失败', error)
          this.$message.error('获取用户车辆失败')
        })
    },
    
    // 处理车辆变更
    handleVehicleChange(vehicleId) {
      this.selectedVehicle = this.vehicles.find(v => v.id === vehicleId) || null
    },
    
    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          return
        }
        
        this.loading = true
        
        // 构建请求数据
        const data = {
          vehicle_id: this.form.vehicle_id,
          parking_lot_id: this.parkingLotId,
          parking_space_id: this.parkingSpaceId,
          estimated_duration: parseInt(this.form.estimated_duration, 10),
          remarks: this.form.remarks
        }
        
        // 发送请求
        startCharging(data)
          .then(response => {
            this.$message.success('开始充电成功')
            this.$emit('success', response.data)
            this.resetForm()
            this.loading = false
          })
          .catch(error => {
            console.error('开始充电失败', error)
            this.$message.error(error.response?.data?.message || '开始充电失败')
            this.loading = false
          })
      })
    },
    
    // 重置表单
    resetForm() {
      this.$refs.form.resetFields()
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.start-charging-form {
  margin-top: 20px;
  
  .form-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }
  
  .vehicle-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .vehicle-info {
      display: flex;
      align-items: center;
      
      .vehicle-color {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-left: 8px;
      }
    }
  }
}
</style>
