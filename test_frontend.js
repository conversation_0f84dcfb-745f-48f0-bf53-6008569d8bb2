/**
 * 前端测试脚本
 * 用于在浏览器控制台中测试违规录入功能
 * 
 * 使用方法：
 * 1. 登录系统，进入违规录入页面
 * 2. 打开浏览器控制台 (F12)
 * 3. 复制粘贴此脚本到控制台
 * 4. 运行脚本，观察结果
 */

// 测试数据
const testData = {
  // 测试用例1: 有效数据
  validData: {
    bike_number: "TEST-1-1",
    user_id: 1,
    violation_type: "违规停车",
    violation_time: new Date().toISOString().slice(0, 19).replace('T', ' '),
    location: "校园南门",
    description: "这是一条测试违规记录，使用有效数据创建。"
  },
  
  // 测试用例2: 缺少必填字段
  missingFieldsData: {
    bike_number: "TEST-1-1",
    // 缺少 user_id
    violation_type: "违规停车",
    // 缺少 violation_time
    location: "校园南门",
    description: "这是一条测试违规记录，缺少必填字段。"
  },
  
  // 测试用例3: 无效的用户ID
  invalidUserIdData: {
    bike_number: "TEST-1-1",
    user_id: "invalid_id",  // 无效的用户ID
    violation_type: "违规停车",
    violation_time: new Date().toISOString().slice(0, 19).replace('T', ' '),
    location: "校园南门",
    description: "这是一条测试违规记录，使用无效的用户ID。"
  }
};

// 测试函数
async function testViolationCreation() {
  console.log("开始测试违规录入功能...");
  
  // 获取表单组件
  const app = document.querySelector('.app-container').__vue__;
  if (!app || !app.$refs.violationForm) {
    console.error("找不到违规表单组件，请确保您在违规录入页面");
    return;
  }
  
  const formComponent = app.$refs.violationForm;
  
  // 测试用例1: 有效数据
  console.log("\n测试用例1: 提交有效数据");
  await testCase(formComponent, testData.validData, "有效数据测试");
  
  // 重置表单
  formComponent.resetForm();
  await wait(1000);
  
  // 测试用例2: 缺少必填字段
  console.log("\n测试用例2: 提交缺少必填字段的数据");
  await testCase(formComponent, testData.missingFieldsData, "缺少必填字段测试");
  
  // 重置表单
  formComponent.resetForm();
  await wait(1000);
  
  // 测试用例3: 无效的用户ID
  console.log("\n测试用例3: 提交无效的用户ID");
  await testCase(formComponent, testData.invalidUserIdData, "无效用户ID测试");
  
  console.log("\n测试完成！");
}

// 测试单个用例
async function testCase(formComponent, data, caseName) {
  console.log(`执行测试: ${caseName}`);
  console.log("测试数据:", data);
  
  // 填充表单数据
  Object.keys(data).forEach(key => {
    if (formComponent.form.hasOwnProperty(key)) {
      formComponent.form[key] = data[key];
    }
  });
  
  // 等待表单更新
  await wait(500);
  
  // 模拟表单提交
  try {
    // 手动验证表单
    let valid = true;
    formComponent.$refs.form.validate(isValid => {
      valid = isValid;
    });
    
    if (!valid) {
      console.log("表单验证失败，预期的错误");
      return;
    }
    
    // 提交表单
    console.log("提交表单...");
    const response = await formComponent.submitMethod(formComponent.form);
    console.log("提交成功，响应:", response);
  } catch (error) {
    console.error("提交失败，错误:", error);
  }
}

// 辅助函数：等待指定时间
function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 执行测试
testViolationCreation();
