<template>
  <div class="empty-container">
    <div class="empty-image">
      <i class="el-icon-folder-delete"></i>
    </div>
    <div class="empty-description">
      {{ description || '暂无数据' }}
    </div>
    <div v-if="$slots.default" class="empty-footer">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Empty',
  props: {
    description: {
      type: String,
      default: '暂无数据'
    }
  }
}
</script>

<style lang="scss" scoped>
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  
  .empty-image {
    font-size: 60px;
    color: #909399;
    margin-bottom: 20px;
  }
  
  .empty-description {
    color: #909399;
    font-size: 14px;
    margin-bottom: 20px;
  }
  
  .empty-footer {
    margin-top: 10px;
  }
}
</style>
