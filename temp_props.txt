  props: {
    parkingLotId: {
      type: [Number, String],
      required: true
    },
    parkingLotName: {
      type: String,
      required: true
    },
    spaces: {
      type: Array,
      default: () => []
    },
    parkingSpaceId: {
      type: [Number, String],
      default: null
    },
    selectedSpace: {
      type: Object,
      default: null
    },
    // 是否显示车位网格（仅在"我的停车"页面使用）
    showSpacesGrid: {
      type: Boolean,
      default: false
    }
  },
