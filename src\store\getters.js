const getters = {
  sidebar: state => state.app.sidebar,
  device: state => state.app.device,
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  userId: state => state.user.userId,
  role: state => state.user.role,
  roles: state => {
    // 如果roles是数组且不为空，直接返回
    if (state.user.roles && Array.isArray(state.user.roles) && state.user.roles.length > 0) {
      return state.user.roles
    }
    // 否则，如果role存在，返回包含role的数组
    else if (state.user.role) {
      return [state.user.role]
    }
    // 默认返回普通用户角色
    else {
      return ['user']
    }
  },
  isAdmin: state => {
    // 获取用户角色
    let roles = []

    // 如果roles是数组且不为空，使用roles
    if (state.user.roles && Array.isArray(state.user.roles) && state.user.roles.length > 0) {
      roles = state.user.roles
    }
    // 否则，如果role存在，使用role
    else if (state.user.role) {
      roles = [state.user.role]
    }
    // 默认为普通用户
    else {
      roles = ['user']
    }

    console.log('isAdmin getter - 用户角色:', roles)
    return roles.includes('admin')
  }
}
export default getters
