@echo off
echo ======================================
echo 校园电动车管理系统 - 虚拟环境启动脚本
echo ======================================
echo.

REM 检查虚拟环境是否存在
if not exist "venv" (
    echo 虚拟环境不存在，正在创建...
    python -m venv venv
    echo 虚拟环境创建完成
    echo.
)

REM 激活虚拟环境
echo 正在激活虚拟环境...
call venv\Scripts\activate.bat

REM 检查依赖是否已安装
if not exist "venv\Lib\site-packages\flask" (
    echo 正在安装依赖...
    pip install -r requirements.txt
    echo 依赖安装完成
    echo.
)

REM 设置环境变量
set FLASK_APP=wsgi.py
set FLASK_ENV=development
set FLASK_DEBUG=1

echo 环境变量设置完成:
echo FLASK_APP=%FLASK_APP%
echo FLASK_ENV=%FLASK_ENV%
echo FLASK_DEBUG=%FLASK_DEBUG%
echo.

REM 启动应用
echo 正在启动应用...
python wsgi.py

REM 停用虚拟环境
call venv\Scripts\deactivate.bat

echo.
echo 应用已关闭。
