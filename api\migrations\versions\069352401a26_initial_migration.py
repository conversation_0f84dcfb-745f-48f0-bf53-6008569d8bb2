"""initial migration

Revision ID: 069352401a26
Revises:
Create Date: 2025-04-18 15:14:29.340760

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '069352401a26'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bikes', schema=None) as batch_op:
        batch_op.alter_column('b_id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.create_unique_constraint('uq_bikes_b_num', ['b_num'])

    with op.batch_alter_table('parking_lots', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.create_unique_constraint('uq_parking_lots_name', ['name'])

    with op.batch_alter_table('parking_records', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)

    with op.batch_alter_table('parking_spaces', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)

    with op.batch_alter_table('players', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.create_unique_constraint('uq_players_username', ['username'])

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.alter_column('u_id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.create_unique_constraint('uq_users_u_name', ['u_name'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_constraint('uq_users_u_name', type_='unique')
        batch_op.alter_column('u_id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('players', schema=None) as batch_op:
        batch_op.drop_constraint('uq_players_username', type_='unique')
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('parking_spaces', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('parking_records', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('parking_lots', schema=None) as batch_op:
        batch_op.drop_constraint('uq_parking_lots_name', type_='unique')
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('bikes', schema=None) as batch_op:
        batch_op.drop_constraint('uq_bikes_b_num', type_='unique')
        batch_op.alter_column('b_id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    # ### end Alembic commands ###
