2025-03-01 23:32:06,096 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:51]
2025-03-01 23:32:11,825 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:51]
2025-03-01 23:51:39,595 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:51]
2025-03-01 23:51:42,776 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:51]
2025-03-01 23:59:54,481 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:51]
2025-03-01 23:59:58,106 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:51]
2025-03-02 00:00:05,891 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:51]
2025-03-02 00:02:13,405 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:51]
2025-03-02 00:03:20,317 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:51]
2025-03-02 00:05:44,387 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:52]
2025-03-02 00:07:08,702 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:52]
2025-03-02 00:09:09,138 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:52]
2025-03-02 00:15:08,643 flask_api ERROR: Exception on /users [POST] [in C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py:875]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\api\app\users\routes.py", line 46, in create_user
    new_user = user_schema.load(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_pwd': ['Missing data for required field.'], 'u_role': ['Missing data for required field.'], 'u_belong': ['Missing data for required field.'], 'u_phone': ['Missing data for required field.']}
2025-03-02 00:15:53,321 flask_api ERROR: Exception on /users [POST] [in C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py:875]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\api\app\users\routes.py", line 46, in create_user
    new_user = user_schema.load(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_belong': ['Missing data for required field.'], 'u_phone': ['Missing data for required field.']}
2025-03-02 00:17:43,394 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:52]
2025-03-02 00:17:46,874 flask_api ERROR: Exception on /users [POST] [in C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py:875]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\api\app\users\routes.py", line 48, in create_user
    user_instance = Users(**new_user)
TypeError: app.users.models.Users() argument after ** must be a mapping, not Users
2025-03-02 00:19:49,387 flask_api INFO: Flask Rest Api startup [in D:\try\api\app\__init__.py:52]
2025-03-02 15:21:22,641 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-02 15:21:47,476 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-02 16:21:57,616 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-02 16:36:38,977 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-02 16:39:41,145 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-02 16:43:37,174 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-02 16:43:42,247 flask_api ERROR: Exception on /api/users/login [POST] [in C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py:875]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: players

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 38, in authenticate_Players
    current_player = Players.find_by_username(data['username'])
  File "D:\try\try\vue-admin-template\api\app\players\models.py", line 23, in find_by_username
    return cls.query.filter_by(username=username).first()
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 2754, in first
    return self.limit(1)._iter().first()  # type: ignore
           ~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 2853, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
        params,
        ^^^^^^^
        execution_options={"_sa_orm_load_options": self.load_options},
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: players
[SQL: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('admin', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-06 22:52:11,703 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-06 22:52:51,962 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-06 22:59:13,760 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-06 23:00:59,616 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-06 23:05:28,670 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-06 23:06:45,178 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-06 23:13:49,945 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-06 23:15:10,359 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-06 23:22:37,700 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 13:22:40,813 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 13:37:28,424 flask_api INFO: Flask Rest Api startup [in d:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 13:43:30,193 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 13:43:40,192 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 13:43:50,125 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 13:46:20,850 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 13:51:20,281 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 13:52:36,086 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 13:54:58,799 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 13:59:31,068 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 13:59:55,313 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 14:00:45,144 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 14:04:44,810 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 14:04:57,026 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 14:07:03,667 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 14:07:18,093 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 14:07:26,803 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 14:08:45,082 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 14:08:59,541 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:51]
2025-03-08 14:09:39,517 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-08 14:13:32,356 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-08 14:17:51,247 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-08 14:18:12,864 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-08 14:18:29,545 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-08 14:18:40,506 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-08 14:18:49,356 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
2025-03-08 14:19:05,898 flask_api INFO: Flask Rest Api startup [in D:\try\try\vue-admin-template\api\app\__init__.py:52]
