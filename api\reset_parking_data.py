#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重置停车场数据，生成统一格式的车位编号：
- 充电车位以C开头，格式为C-停车场ID-序号
- 普通车位以N开头，格式为N-停车场ID-序号
- 残疾人车位以D开头，格式为D-停车场ID-序号
"""

import os
import sys
import random
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 确保能够导入app模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入应用和数据库模型
from app import create_app, db
from app.parkinglots.models import ParkingLot, ParkingSpace

# 创建应用和日志记录器
app = create_app()

# 车位类型常量
NORMAL_SPACE = 1  # 普通车位
DISABLED_SPACE = 2  # 残疾人车位
CHARGING_SPACE = 3  # 充电车位

# 车位状态常量
STATUS_AVAILABLE = 0  # 空闲
STATUS_OCCUPIED = 1  # 占用
STATUS_FAULT = 2  # 故障
STATUS_MAINTENANCE = 3  # 维护中
STATUS_DISABLED = 4  # 禁用

# 充电功率(kW)选项
CHARGING_POWERS = [3.5, 7, 11, 15, 20]

# 测试停车场数据
TEST_PARKING_LOTS = [
    {
        "name": "北校区停车场A",
        "address": "北校区图书馆北侧",
        "campus": "北校区",
        "area": "图书馆区",
        "manager": "张明",
        "contact": "13800001111",
        "description": "北校区主停车场，位于图书馆北侧，主要服务教学区",
        "normal_spaces": 50,  # 普通车位数量
        "charging_spaces": 20,  # 充电车位数量
        "disabled_spaces": 5,  # 残疾人车位数量
    },
    {
        "name": "南校区停车场B",
        "address": "南校区体育馆旁",
        "campus": "南校区",
        "area": "体育馆区",
        "manager": "李强",
        "contact": "13800002222",
        "description": "南校区主停车场，位于体育馆旁，主要服务运动区",
        "normal_spaces": 60,
        "charging_spaces": 25,
        "disabled_spaces": 5,
    },
    {
        "name": "教师公寓停车场",
        "address": "西校区教师公寓南侧",
        "campus": "西校区",
        "area": "教师公寓区",
        "manager": "王华",
        "contact": "13800003333",
        "description": "教师公寓专用停车场，仅供教职工使用",
        "normal_spaces": 40,
        "charging_spaces": 15,
        "disabled_spaces": 3,
    },
    {
        "name": "研究生宿舍停车场",
        "address": "东校区研究生宿舍区",
        "campus": "东校区",
        "area": "宿舍区",
        "manager": "赵艳",
        "contact": "13800004444",
        "description": "研究生宿舍停车场，主要服务研究生",
        "normal_spaces": 30,
        "charging_spaces": 20,
        "disabled_spaces": 2,
    }
]

def reset_parking_data():
    """删除所有现有停车场数据并创建新的测试数据"""
    with app.app_context():
        try:
            # 删除所有现有停车场和车位数据
            logger.info("正在删除现有停车场和车位数据...")
            
            # 先删除所有车位
            ParkingSpace.query.delete()
            
            # 再删除所有停车场
            ParkingLot.query.delete()
            
            # 提交事务
            db.session.commit()
            logger.info("现有数据已成功删除")
            
            # 创建新的测试停车场和车位
            logger.info("开始创建新的测试数据...")
            
            for lot_data in TEST_PARKING_LOTS:
                # 创建停车场
                parking_lot = ParkingLot(
                    name=lot_data["name"],
                    address=lot_data["address"],
                    total_spaces=0,  # 初始为0，稍后更新
                    campus=lot_data["campus"],
                    area=lot_data["area"],
                    manager=lot_data["manager"],
                    contact=lot_data["contact"],
                    description=lot_data["description"],
                )
                
                # 保存停车场以获取ID
                db.session.add(parking_lot)
                db.session.flush()
                
                lot_id = parking_lot.id
                logger.info(f"已创建停车场: {lot_data['name']} (ID: {lot_id})")
                
                # 添加普通车位
                for i in range(1, lot_data["normal_spaces"] + 1):
                    space_number = f"N-{lot_id}-{i}"
                    status = random.choices(
                        [STATUS_AVAILABLE, STATUS_OCCUPIED], 
                        weights=[0.7, 0.3], 
                        k=1
                    )[0]
                    
                    space = ParkingSpace(
                        parking_lot_id=lot_id,
                        space_number=space_number,
                        type=NORMAL_SPACE,
                        status=status,
                    )
                    db.session.add(space)
                
                # 添加充电车位
                for i in range(1, lot_data["charging_spaces"] + 1):
                    space_number = f"C-{lot_id}-{i}"
                    status = random.choices(
                        [STATUS_AVAILABLE, STATUS_OCCUPIED, STATUS_MAINTENANCE, STATUS_DISABLED], 
                        weights=[0.6, 0.2, 0.1, 0.1], 
                        k=1
                    )[0]
                    
                    # 随机选择充电功率
                    power = random.choice(CHARGING_POWERS)
                    
                    # 随机选择充电类型
                    charging_type = random.choices([1, 2, 3], weights=[0.3, 0.5, 0.2], k=1)[0]
                    
                    space = ParkingSpace(
                        parking_lot_id=lot_id,
                        space_number=space_number,
                        type=CHARGING_SPACE,
                        status=status,
                        power=power,
                        charging_type=charging_type
                    )
                    # 在创建后设置remarks属性
                    space.remarks = f"充电功率: {power}kW"
                    db.session.add(space)
                
                # 添加残疾人车位
                for i in range(1, lot_data["disabled_spaces"] + 1):
                    space_number = f"D-{lot_id}-{i}"
                    status = random.choices(
                        [STATUS_AVAILABLE, STATUS_OCCUPIED], 
                        weights=[0.8, 0.2], 
                        k=1
                    )[0]
                    
                    space = ParkingSpace(
                        parking_lot_id=lot_id,
                        space_number=space_number,
                        type=DISABLED_SPACE,
                        status=status,
                    )
                    db.session.add(space)
            
            # 提交事务
            db.session.commit()
            
            # 更新每个停车场的总车位数和占用车位数
            for lot in ParkingLot.query.all():
                lot.update_occupied_spaces()
            
            logger.info("测试数据已成功创建")
            print("停车场数据重置完成！现在所有车位编号都符合统一格式规范。")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"重置停车场数据时出错: {str(e)}")
            print(f"错误: {str(e)}")

if __name__ == "__main__":
    reset_parking_data() 