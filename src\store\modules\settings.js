// import defaultSettings from '@/settings'

// 下面的变量在解构后未使用，所以注释掉
// const { showSettings, fixedHeader, sidebarLogo } = defaultSettings

const state = {
  fixedHeader: true,
  showSettings: true,
  showTagsView: true,
  showSidebarLogo: true,
  sidebarTextTheme: true,
  showDynamicTitle: false,
  theme: '#409EFF',
  tagsView: true,
  dynamicTitle: false,
  sidebarLogo: true,
  layout: 'default',
  settings: {
    fixedHeader: true,
    showSettings: true,
    showTagsView: true,
    showSidebarLogo: true,
    sidebarTextTheme: true,
    showDynamicTitle: false,
    theme: '#409EFF',
    tagsView: true,
    dynamicTitle: false,
    sidebarLogo: true,
    layout: 'default'
  }
}

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    if (Object.prototype.hasOwnProperty.call(state, key)) {
      state[key] = value
    }
    if (Object.prototype.hasOwnProperty.call(state.settings, key)) {
      state.settings[key] = value
    }
  }
}

const actions = {
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

