"""
添加充电相关表的迁移脚本
"""
import os
import sys
from datetime import datetime, timedelta

# 添加当前目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入应用和模型
from app import create_app, db
from app.charging.models import ChargingRecord, ChargingPriceStrategy, ChargingReservation
from app.parkinglots.models import ParkingSpace

# 创建应用实例
app = create_app()

def upgrade():
    """升级数据库，添加充电相关表"""
    with app.app_context():
        # 创建充电记录表
        from sqlalchemy import inspect
        inspector = inspect(db.engine)

        if not inspector.has_table('charging_records'):
            ChargingRecord.__table__.create(db.engine)
            print("创建充电记录表成功")
        else:
            print("充电记录表已存在")

        # 创建充电价格策略表
        if not inspector.has_table('charging_price_strategies'):
            ChargingPriceStrategy.__table__.create(db.engine)
            print("创建充电价格策略表成功")

            # 添加默认充电价格策略
            default_strategy = ChargingPriceStrategy(
                name="默认策略",
                base_price=2.0,  # 基础价格：2元/小时
                peak_price=3.0,  # 高峰时段价格：3元/小时
                peak_start_hour=9,  # 高峰时段开始时间：9点
                peak_end_hour=18,  # 高峰时段结束时间：18点
                min_fee=1.0,  # 最低收费：1元
                status=1,  # 启用
                remarks="系统默认充电价格策略"
            )
            db.session.add(default_strategy)
            db.session.commit()
            print("添加默认充电价格策略成功")
        else:
            print("充电价格策略表已存在")

        # 创建充电预约表
        if not inspector.has_table('charging_reservations'):
            ChargingReservation.__table__.create(db.engine)
            print("创建充电预约表成功")
        else:
            print("充电预约表已存在")

        # 更新车位类型，添加充电车位类型
        print("开始更新车位类型...")

        # 获取所有车位
        spaces = ParkingSpace.query.all()
        print(f"共找到 {len(spaces)} 个车位")

        # 更新车位类型
        updated_count = 0
        for space in spaces:
            # 如果车位类型是旧的类型，更新为新的类型
            if space.type == 0:  # 旧的普通车位
                space.type = 1  # 新的普通车位
                updated_count += 1
            elif space.type == 1:  # 旧的残疾人车位
                space.type = 3  # 新的残疾人车位
                updated_count += 1
            elif space.type == 2:  # 旧的大型车位
                space.type = 4  # 新的大型车位
                updated_count += 1

        # 提交更改
        db.session.commit()
        print(f"更新了 {updated_count} 个车位的类型")

        # 添加一些充电车位
        print("开始添加充电车位...")

        # 获取所有停车场
        from app.parkinglots.models import ParkingLot
        parking_lots = ParkingLot.query.all()

        # 为每个停车场添加一些充电车位
        charging_spaces_added = 0
        for lot in parking_lots:
            # 获取该停车场的车位数量
            spaces_count = ParkingSpace.query.filter_by(parking_lot_id=lot.id).count()

            # 计算需要添加的充电车位数量（约10%的车位）
            charging_spaces_count = max(1, int(spaces_count * 0.1))

            # 添加充电车位
            for i in range(charging_spaces_count):
                # 生成车位编号
                space_number = f"C-{lot.id}-{i+1}"

                # 创建充电车位
                charging_space = ParkingSpace(
                    parking_lot_id=lot.id,
                    space_number=space_number,
                    type=3,  # 充电车位
                    status=0  # 空闲
                )

                db.session.add(charging_space)
                charging_spaces_added += 1

        # 提交更改
        db.session.commit()
        print(f"添加了 {charging_spaces_added} 个充电车位")

        # 添加示例充电记录和预约数据
        print("开始添加示例充电数据...")

        # 获取用户和车辆
        from app.users.models import Users
        from app.bikes.models import Bikes
        from app.parking_records.models import ParkingRecord

        # 获取第一个用户
        user = Users.query.first()
        if user:
            # 获取该用户的车辆
            vehicles = Bikes.query.filter_by(belong_to=user.u_id).all()

            if vehicles and len(vehicles) > 0:
                vehicle = vehicles[0]

                # 获取第一个停车场
                parking_lot = ParkingLot.query.first()

                if parking_lot:
                    # 创建示例充电预约
                    now = datetime.now()

                    # 今天的预约
                    today_start = now.replace(hour=14, minute=0, second=0, microsecond=0)
                    today_end = now.replace(hour=16, minute=0, second=0, microsecond=0)

                    # 明天的预约
                    tomorrow = now + timedelta(days=1)
                    tomorrow_start = tomorrow.replace(hour=10, minute=0, second=0, microsecond=0)
                    tomorrow_end = tomorrow.replace(hour=12, minute=0, second=0, microsecond=0)

                    # 创建预约
                    reservations = [
                        ChargingReservation(
                            user_id=user.u_id,
                            vehicle_id=vehicle.b_id,
                            parking_lot_id=parking_lot.id,
                            start_time=today_start,
                            end_time=today_end,
                            remarks="示例预约1 - 今天下午"
                        ),
                        ChargingReservation(
                            user_id=user.u_id,
                            vehicle_id=vehicle.b_id,
                            parking_lot_id=parking_lot.id,
                            start_time=tomorrow_start,
                            end_time=tomorrow_end,
                            remarks="示例预约2 - 明天上午"
                        )
                    ]

                    # 添加预约
                    for reservation in reservations:
                        db.session.add(reservation)

                    # 提交更改
                    db.session.commit()
                    print(f"添加了 {len(reservations)} 个示例充电预约")

                    # 添加示例充电记录
                    # 首先检查是否有进行中的停车记录
                    parking_record = ParkingRecord.query.filter_by(
                        user_id=user.u_id,
                        status=0  # 进行中
                    ).first()

                    if parking_record:
                        # 检查车位是否是充电车位
                        space = ParkingSpace.query.get(parking_record.parking_space_id)

                        if space and space.type == 2:  # 充电车位
                            # 创建充电记录
                            charging_record = ChargingRecord(
                                parking_record_id=parking_record.id,
                                vehicle_id=parking_record.vehicle_id,
                                user_id=parking_record.user_id,
                                parking_lot_id=parking_record.parking_lot_id,
                                parking_space_id=parking_record.parking_space_id
                            )

                            # 添加充电记录
                            db.session.add(charging_record)
                            db.session.commit()
                            print("添加了1个示例充电记录")
                        else:
                            print("未找到充电车位的停车记录，跳过添加示例充电记录")
                    else:
                        print("未找到进行中的停车记录，跳过添加示例充电记录")

                    # 添加示例已完成的充电记录
                    # 创建一个已完成的充电记录
                    completed_charging_record = ChargingRecord(
                        parking_record_id=1,  # 假设ID为1
                        vehicle_id=vehicle.b_id,
                        user_id=user.u_id,
                        parking_lot_id=parking_lot.id,
                        parking_space_id=1  # 假设ID为1
                    )

                    # 设置为已完成状态
                    completed_charging_record.start_time = datetime.now() - timedelta(days=1)
                    completed_charging_record.end_time = datetime.now() - timedelta(days=1, hours=2)
                    completed_charging_record.status = 1  # 已完成
                    completed_charging_record.duration = 120  # 2小时
                    completed_charging_record.fee = 4.0  # 4元

                    # 添加充电记录
                    db.session.add(completed_charging_record)
                    db.session.commit()
                    print("添加了1个示例已完成充电记录")
                else:
                    print("未找到停车场，跳过添加示例充电数据")
            else:
                print("未找到用户车辆，跳过添加示例充电数据")
        else:
            print("未找到用户，跳过添加示例充电数据")

        print("数据库升级完成")

def downgrade():
    """降级数据库，删除充电相关表"""
    with app.app_context():
        # 删除充电记录表
        from sqlalchemy import inspect
        inspector = inspect(db.engine)

        if inspector.has_table('charging_records'):
            ChargingRecord.__table__.drop(db.engine)
            print("删除充电记录表成功")
        else:
            print("充电记录表不存在")

        # 删除充电价格策略表
        if inspector.has_table('charging_price_strategies'):
            ChargingPriceStrategy.__table__.drop(db.engine)
            print("删除充电价格策略表成功")
        else:
            print("充电价格策略表不存在")

        # 删除充电预约表
        if inspector.has_table('charging_reservations'):
            ChargingReservation.__table__.drop(db.engine)
            print("删除充电预约表成功")
        else:
            print("充电预约表不存在")

        # 恢复车位类型
        print("开始恢复车位类型...")

        # 获取所有车位
        spaces = ParkingSpace.query.all()
        print(f"共找到 {len(spaces)} 个车位")

        # 更新车位类型
        updated_count = 0
        for space in spaces:
            # 如果车位类型是充电车位，更新为普通车位
            if space.type == 2:  # 充电车位
                space.type = 1  # 普通车位
                updated_count += 1
            # 恢复其他车位类型
            elif space.type == 1:  # 新的普通车位
                space.type = 0  # 旧的普通车位
                updated_count += 1
            elif space.type == 3:  # 新的残疾人车位
                space.type = 1  # 旧的残疾人车位
                updated_count += 1
            elif space.type == 4:  # 新的大型车位
                space.type = 2  # 旧的大型车位
                updated_count += 1

        # 提交更改
        db.session.commit()
        print(f"恢复了 {updated_count} 个车位的类型")

        print("数据库降级完成")

if __name__ == '__main__':
    # 根据命令行参数执行升级或降级
    if len(sys.argv) > 1 and sys.argv[1] == 'downgrade':
        downgrade()
    else:
        upgrade()
