"""
添加充电异常表新字段的迁移脚本
"""
import os
import sys
from datetime import datetime

# 添加父目录到系统路径
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, parent_dir)

# 导入应用和模型
from app import create_app, db
from app.charging.models import ChargingException, ChargingFault

# 创建应用实例
app = create_app()

def upgrade():
    """升级数据库，添加充电异常表新字段"""
    with app.app_context():
        # 使用原生SQL添加字段
        import sqlite3

        print("开始添加充电异常表新字段...")

        try:
            # 连接到数据库
            db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 检查字段是否已存在
            cursor.execute("PRAGMA table_info(charging_exceptions)")
            columns = [column[1] for column in cursor.fetchall()]

            # 添加fault_id字段
            if 'fault_id' not in columns:
                cursor.execute("""
                ALTER TABLE charging_exceptions
                ADD COLUMN fault_id INTEGER REFERENCES charging_faults(id) ON DELETE SET NULL
                """)
                print("添加fault_id字段成功")
            else:
                print("fault_id字段已存在")

            # 添加space_id字段
            if 'space_id' not in columns:
                cursor.execute("""
                ALTER TABLE charging_exceptions
                ADD COLUMN space_id INTEGER REFERENCES parking_spaces(id) ON DELETE SET NULL
                """)
                print("添加space_id字段成功")
            else:
                print("space_id字段已存在")

            # 添加parking_lot_id字段
            if 'parking_lot_id' not in columns:
                cursor.execute("""
                ALTER TABLE charging_exceptions
                ADD COLUMN parking_lot_id INTEGER REFERENCES parking_lots(id) ON DELETE SET NULL
                """)
                print("添加parking_lot_id字段成功")
            else:
                print("parking_lot_id字段已存在")

            # 修改charging_record_id字段为可空
            # SQLite不支持直接修改列约束，需要创建新表并复制数据
            # 这里我们不做这个操作，因为SQLAlchemy会处理nullable=True的情况

            # 提交更改
            conn.commit()

            # 更新现有记录，填充新字段
            print("开始更新现有记录...")

            # 获取所有充电异常记录
            exceptions = ChargingException.query.all()
            updated_count = 0

            for exception in exceptions:
                # 查找关联的充电故障
                faults = ChargingFault.query.filter_by(
                    space_id=exception.charging_record.parking_space_id
                ).all() if hasattr(exception, 'charging_record') and exception.charging_record else []

                # 如果找到故障，关联第一个
                if faults:
                    exception.fault_id = faults[0].id

                # 从充电记录获取车位ID和停车场ID
                if hasattr(exception, 'charging_record') and exception.charging_record:
                    exception.space_id = exception.charging_record.parking_space_id
                    exception.parking_lot_id = exception.charging_record.parking_lot_id
                    updated_count += 1

            # 提交更改
            db.session.commit()
            print(f"更新了 {updated_count} 条充电异常记录")

            print("数据库升级完成")
        except Exception as e:
            if 'conn' in locals():
                conn.rollback()
            db.session.rollback()
            print(f"数据库升级失败: {str(e)}")
            raise

def downgrade():
    """降级数据库，删除充电异常表新字段"""
    with app.app_context():
        print("开始删除充电异常表新字段...")

        # SQLite不支持直接删除列，我们只能将值设为NULL
        try:
            # 获取所有充电异常记录
            exceptions = ChargingException.query.all()

            for exception in exceptions:
                exception.fault_id = None
                exception.space_id = None
                exception.parking_lot_id = None

            # 提交更改
            db.session.commit()
            print("字段值清空完成")
        except Exception as e:
            db.session.rollback()
            print(f"清空字段值失败: {str(e)}")

        print("数据库降级完成")

if __name__ == '__main__':
    # 根据命令行参数执行升级或降级
    if len(sys.argv) > 1 and sys.argv[1] == 'downgrade':
        downgrade()
    else:
        upgrade()
