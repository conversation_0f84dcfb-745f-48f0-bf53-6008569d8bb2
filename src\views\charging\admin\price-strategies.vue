<template>
  <div class="app-container charging-price-strategies-container">
    <el-card class="list-container">
      <div class="list-header">
        <div class="header-title">
          <i class="el-icon-money"></i>
          <span>充电价格策略</span>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="handleAddStrategy">新增价格策略</el-button>
          <el-button type="text" @click="fetchData">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="加载中..."
        border
        fit
        highlight-current-row
        :header-cell-style="{background:'#f5f7fa'}"
      >
        <el-table-column
          align="center"
          label="ID"
          width="80"
        >
          <template slot-scope="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="策略名称"
          width="150"
        >
          <template slot-scope="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="基础价格"
          width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.base_price }} 元/小时
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="高峰价格"
          width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.peak_price }} 元/小时
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="高峰时段"
          width="150"
        >
          <template slot-scope="scope">
            {{ scope.row.peak_start_hour }}:00 - {{ scope.row.peak_end_hour }}:00
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="最低收费"
          width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.min_fee }} 元
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="状态"
          width="100"
        >
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status_text }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="创建时间"
          width="180"
        >
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="操作"
          width="250"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              v-if="scope.row.status === 0"
              size="mini"
              type="success"
              @click="handleUpdateStatus(scope.row, 1)"
            >启用</el-button>
            <el-button
              v-if="scope.row.status === 1"
              size="mini"
              type="warning"
              @click="handleUpdateStatus(scope.row, 0)"
            >禁用</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="listQuery.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="listQuery.per_page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </el-card>

    <!-- 价格策略模拟计算卡片 -->
    <el-card class="simulation-container">
      <div class="simulation-header">
        <div class="header-title">
          <i class="el-icon-data-analysis"></i>
          <span>价格策略模拟计算</span>
        </div>
      </div>

      <div class="simulation-content">
        <el-form :model="simulationForm" label-width="120px" class="simulation-form">
          <el-form-item label="选择策略">
            <el-select v-model="simulationForm.strategy_id" placeholder="请选择价格策略" @change="handleStrategyChange">
              <el-option
                v-for="item in list"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间">
            <el-date-picker
              v-model="simulationForm.start_time"
              type="datetime"
              placeholder="选择开始时间"
              @change="calculateSimulationFee"
            />
          </el-form-item>
          <el-form-item label="结束时间">
            <el-date-picker
              v-model="simulationForm.end_time"
              type="datetime"
              placeholder="选择结束时间"
              @change="calculateSimulationFee"
            />
          </el-form-item>
          <el-form-item label="充电时长">
            <span>{{ simulationDuration }}</span>
          </el-form-item>
          <el-form-item label="计算费用">
            <span class="simulation-fee">{{ simulationFee !== null ? `¥${simulationFee}` : '-' }}</span>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="calculateSimulationFee">计算费用</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 添加/编辑价格策略对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '新增价格策略' : '编辑价格策略'" :visible.sync="dialogFormVisible" width="500px">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="110px">
        <el-form-item label="策略名称" prop="name">
          <el-input v-model="temp.name" placeholder="请输入策略名称" />
        </el-form-item>
        <el-form-item label="基础价格" prop="base_price">
          <el-input-number v-model="temp.base_price" :precision="2" :step="0.1" :min="0" placeholder="元/小时" />
        </el-form-item>
        <el-form-item label="高峰价格" prop="peak_price">
          <el-input-number v-model="temp.peak_price" :precision="2" :step="0.1" :min="0" placeholder="元/小时" />
        </el-form-item>
        <el-form-item label="高峰开始时间" prop="peak_start_hour">
          <el-time-select
            v-model="temp.peak_start_hour_str"
            :picker-options="{
              start: '00:00',
              step: '01:00',
              end: '23:00'
            }"
            placeholder="选择时间"
            @change="handlePeakStartChange"
          />
        </el-form-item>
        <el-form-item label="高峰结束时间" prop="peak_end_hour">
          <el-time-select
            v-model="temp.peak_end_hour_str"
            :picker-options="{
              start: '00:00',
              step: '01:00',
              end: '23:00',
              minTime: temp.peak_start_hour_str
            }"
            placeholder="选择时间"
            @change="handlePeakEndChange"
          />
        </el-form-item>
        <el-form-item label="最低收费" prop="min_fee">
          <el-input-number v-model="temp.min_fee" :precision="2" :step="0.1" :min="0" placeholder="元" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="temp.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="temp.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getChargingPriceStrategies,
  createChargingPriceStrategy,
  updateChargingPriceStrategy,
  deleteChargingPriceStrategy,
  calculateChargingFeeByStrategy
} from '@/api/charging'

export default {
  name: 'ChargingPriceStrategies',
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        per_page: 10,
        sort_field: 'id',
        sort_order: 'asc'
      },
      dialogFormVisible: false,
      dialogStatus: '',
      temp: {
        id: undefined,
        name: '',
        base_price: 2.0,
        peak_price: 3.0,
        peak_start_hour: 9,
        peak_end_hour: 18,
        peak_start_hour_str: '09:00',
        peak_end_hour_str: '18:00',
        min_fee: 1.0,
        status: 1,
        remarks: ''
      },
      rules: {
        name: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
        base_price: [{ required: true, message: '请输入基础价格', trigger: 'blur' }],
        peak_price: [{ required: true, message: '请输入高峰价格', trigger: 'blur' }],
        peak_start_hour_str: [{ required: true, message: '请选择高峰开始时间', trigger: 'change' }],
        peak_end_hour_str: [{ required: true, message: '请选择高峰结束时间', trigger: 'change' }],
        min_fee: [{ required: true, message: '请输入最低收费', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      },
      simulationForm: {
        strategy_id: null,
        start_time: new Date(new Date().setHours(8, 0, 0, 0)),
        end_time: new Date(new Date().setHours(10, 0, 0, 0))
      },
      simulationDuration: '',
      simulationFee: null
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    // 获取价格策略列表
    async fetchData() {
      this.listLoading = true
      try {
        const params = { ...this.listQuery }
        const response = await getChargingPriceStrategies(params)
        this.list = response.data.items || []
        this.total = response.data.total || 0

        // 如果有策略，默认选择第一个进行模拟计算
        if (this.list.length > 0 && !this.simulationForm.strategy_id) {
          this.simulationForm.strategy_id = this.list[0].id
          this.calculateSimulationFee()
        }
      } catch (error) {
        console.error('获取价格策略列表失败:', error)
        this.$message.error('获取价格策略列表失败')
      } finally {
        this.listLoading = false
      }
    },
    // 格式化日期时间
    formatDateTime(dateStr) {
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    // 处理页面大小变化
    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchData()
    },
    // 处理页码变化
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.fetchData()
    },
    // 重置表单
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        base_price: 2.0,
        peak_price: 3.0,
        peak_start_hour: 9,
        peak_end_hour: 18,
        peak_start_hour_str: '09:00',
        peak_end_hour_str: '18:00',
        min_fee: 1.0,
        status: 1,
        remarks: ''
      }
    },
    // 处理添加价格策略
    handleAddStrategy() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    // 处理编辑价格策略
    handleEdit(row) {
      this.temp = Object.assign({}, row)
      // 转换时间格式
      this.temp.peak_start_hour_str = String(this.temp.peak_start_hour).padStart(2, '0') + ':00'
      this.temp.peak_end_hour_str = String(this.temp.peak_end_hour).padStart(2, '0') + ':00'
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    // 处理高峰开始时间变化
    handlePeakStartChange(val) {
      if (val) {
        this.temp.peak_start_hour = parseInt(val.split(':')[0])
      }
    },
    // 处理高峰结束时间变化
    handlePeakEndChange(val) {
      if (val) {
        this.temp.peak_end_hour = parseInt(val.split(':')[0])
      }
    },
    // 处理更新状态
    async handleUpdateStatus(row, status) {
      try {
        const tempData = Object.assign({}, row)
        tempData.status = status
        await updateChargingPriceStrategy(tempData.id, tempData)
        this.$message({
          type: 'success',
          message: status === 1 ? '启用成功!' : '禁用成功!'
        })
        this.fetchData()
      } catch (error) {
        console.error('更新状态失败:', error)
        this.$message.error('更新状态失败')
      }
    },
    // 处理删除价格策略
    handleDelete(row) {
      this.$confirm('确认删除该价格策略?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteChargingPriceStrategy(row.id)
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.fetchData()
        } catch (error) {
          console.error('删除价格策略失败:', error)
          this.$message.error('删除价格策略失败')
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 创建价格策略
    createData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          try {
            const tempData = Object.assign({}, this.temp)
            delete tempData.peak_start_hour_str
            delete tempData.peak_end_hour_str
            await createChargingPriceStrategy(tempData)
            this.dialogFormVisible = false
            this.$message({
              type: 'success',
              message: '创建成功!'
            })
            this.fetchData()
          } catch (error) {
            console.error('创建价格策略失败:', error)
            this.$message.error('创建价格策略失败')
          }
        }
      })
    },
    // 更新价格策略
    updateData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          try {
            const tempData = Object.assign({}, this.temp)
            delete tempData.peak_start_hour_str
            delete tempData.peak_end_hour_str
            await updateChargingPriceStrategy(tempData.id, tempData)
            this.dialogFormVisible = false
            this.$message({
              type: 'success',
              message: '更新成功!'
            })
            this.fetchData()
          } catch (error) {
            console.error('更新价格策略失败:', error)
            this.$message.error('更新价格策略失败')
          }
        }
      })
    },
    // 处理策略变更
    handleStrategyChange() {
      this.calculateSimulationFee()
    },
    // 计算模拟费用
    async calculateSimulationFee() {
      if (!this.simulationForm.strategy_id || !this.simulationForm.start_time || !this.simulationForm.end_time) {
        return
      }

      // 验证时间
      if (this.simulationForm.end_time <= this.simulationForm.start_time) {
        this.$message.warning('结束时间必须大于开始时间')
        return
      }

      try {
        const params = {
          strategy_id: this.simulationForm.strategy_id,
          start_time: this.simulationForm.start_time.toISOString(),
          end_time: this.simulationForm.end_time.toISOString()
        }

        const response = await calculateChargingFeeByStrategy(params)
        if (response.data) {
          this.simulationFee = response.data.fee
          this.simulationDuration = response.data.duration_formatted
        }
      } catch (error) {
        console.error('计算模拟费用失败:', error)
        this.$message.error('计算模拟费用失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-price-strategies-container {
  .list-container {
    margin-bottom: 20px;

    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .header-title {
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;

        i {
          margin-right: 10px;
          color: #F56C6C;
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }

  .simulation-container {
    .simulation-header {
      margin-bottom: 20px;

      .header-title {
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;

        i {
          margin-right: 10px;
          color: #409EFF;
        }
      }
    }

    .simulation-content {
      .simulation-form {
        max-width: 500px;

        .simulation-fee {
          font-size: 18px;
          font-weight: bold;
          color: #F56C6C;
        }
      }
    }
  }
}
</style>
