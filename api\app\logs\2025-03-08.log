2025/03/08 13:22:40 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 13:22:40 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/03/08 13:22:40 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 13:37:28 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 13:37:28 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/03/08 13:37:28 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 13:39:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:11] "GET / HTTP/1.1" 200 -
2025/03/08 13:39:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:11] "GET /static/css/app.949a0224.css HTTP/1.1" 200 -
2025/03/08 13:39:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:11] "GET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1" 200 -
2025/03/08 13:39:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:11] "GET /static/css/chunk-libs.3dfb7769.css HTTP/1.1" 200 -
2025/03/08 13:39:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:11] "GET /static/js/app.a7289db6.js HTTP/1.1" 200 -
2025/03/08 13:39:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:12] "GET /static/js/chunk-libs.4a5831c0.js HTTP/1.1" 200 -
2025/03/08 13:39:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:12] "GET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1" 200 -
2025/03/08 13:39:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:12] "GET /static/css/chunk-a8539586.94702ff7.css HTTP/1.1" 200 -
2025/03/08 13:39:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:12] "GET /static/js/chunk-a8539586.06bed4b6.js HTTP/1.1" 200 -
2025/03/08 13:39:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:13] "GET /static/fonts/element-icons.535877f5.woff HTTP/1.1" 200 -
2025/03/08 13:39:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:14] "GET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1" 200 -
2025/03/08 13:39:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:14] "GET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1" 200 -
2025/03/08 13:39:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:25] "OPTIONS /login HTTP/1.1" 200 -
2025/03/08 13:39:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:39:25] "[31m[1mPOST /login HTTP/1.1[0m" 405 -
2025/03/08 13:43:30 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 13:43:40 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 13:43:50 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 13:46:20 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 13:46:20 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 13:46:20 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 13:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:46:28] "GET / HTTP/1.1" 200 -
2025/03/08 13:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:46:28] "GET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1" 200 -
2025/03/08 13:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:46:28] "GET /static/css/app.949a0224.css HTTP/1.1" 200 -
2025/03/08 13:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:46:28] "GET /static/css/chunk-libs.3dfb7769.css HTTP/1.1" 200 -
2025/03/08 13:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:46:28] "GET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1" 200 -
2025/03/08 13:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:46:28] "GET /static/js/app.a7289db6.js HTTP/1.1" 200 -
2025/03/08 13:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:46:28] "GET /static/js/chunk-libs.4a5831c0.js HTTP/1.1" 200 -
2025/03/08 13:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:46:28] "GET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1" 200 -
2025/03/08 13:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:46:28] "GET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1" 200 -
2025/03/08 13:46:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:46:29] "GET /static/fonts/element-icons.535877f5.woff HTTP/1.1" 200 -
2025/03/08 13:46:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:46:34] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 13:46:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:46:34] "[31m[1mPOST /api/login HTTP/1.1[0m" 405 -
2025/03/08 13:47:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:47:14] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 13:47:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:47:14] "[31m[1mPOST /api/login HTTP/1.1[0m" 405 -
2025/03/08 13:48:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:48:20] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 13:48:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 13:48:20] "[31m[1mPOST /api/login HTTP/1.1[0m" 405 -
2025/03/08 13:51:20 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 13:52:36 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 13:54:58 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 13:59:31 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 13:59:55 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 14:00:45 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 14:04:44 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 14:04:57 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 14:07:03 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 14:07:18 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 14:07:26 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 14:08:45 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 14:08:59 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/08 14:09:39 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 14:13:32 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 14:17:51 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 14:18:12 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 14:18:29 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 14:18:40 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 14:18:49 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 14:19:05 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
