/* 电动车管理模块统一样式 */

// 主题颜色变量
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;
$background-color: #f5f7fa;
$border-color: #EBEEF5;
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;

// 卡片样式统一
.vehicle-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  background-color: #fff;
  margin-bottom: 20px;
  overflow: hidden;
  
  &:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
    transform: translateY(-3px);
  }
  
  .card-header {
    padding: 15px;
    border-bottom: 1px solid $border-color;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba($primary-color, 0.05);
    
    .title {
      font-size: 16px;
      font-weight: 600;
      color: $text-primary;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 8px;
        color: $primary-color;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .card-body {
    padding: 15px;
  }
}

// 统计卡片样式
.stat-card {
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  }
  
  .stat-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    
    .stat-title {
      margin-left: 8px;
      font-size: 14px;
      color: $text-regular;
    }
  }
  
  .stat-value {
    font-size: 24px;
    font-weight: 600;
    color: $text-primary;
    margin-bottom: 10px;
  }
  
  .stat-percent {
    font-size: 12px;
    color: $text-secondary;
  }
  
  .stat-action {
    margin-top: 10px;
    text-align: right;
  }
}

// 表格样式优化
.vehicle-table {
  border-radius: 8px;
  overflow: hidden;
  
  .el-table__header-wrapper {
    th {
      background-color: rgba($primary-color, 0.05);
      color: $text-primary;
      font-weight: 600;
    }
  }
  
  .el-table__row {
    transition: all 0.3s;
    
    &:hover {
      background-color: rgba($primary-color, 0.05);
    }
  }
}

// 颜色展示优化
.color-display {
  display: flex;
  align-items: center;
  
  .color-dot {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    border: 1px solid transparent;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
  }
}

// 表单样式优化
.vehicle-form {
  .el-form-item__label {
    font-weight: 500;
  }
  
  .form-tip {
    font-size: 12px;
    color: $text-secondary;
    margin-top: 5px;
    line-height: 1.4;
  }
}

// 过滤器容器样式
.filter-container {
  background-color: $background-color;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

// 详情页样式
.vehicle-detail {
  .back-button {
    margin-bottom: 20px;
  }
  
  .detail-card {
    border-radius: 8px;
    overflow: hidden;
    
    .el-descriptions {
      .el-descriptions__label {
        font-weight: 500;
      }
      
      .el-descriptions__content {
        word-break: break-word;
      }
    }
  }
  
  .operation-history {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid $border-color;
    
    h3 {
      margin-bottom: 15px;
      color: $text-regular;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      
      &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 16px;
        background-color: $primary-color;
        margin-right: 8px;
        border-radius: 2px;
      }
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: stretch;
    
    .el-input, .el-select {
      width: 100% !important;
    }
    
    .el-button {
      width: 100%;
      margin-left: 0 !important;
      margin-top: 10px;
    }
  }
  
  .stat-card {
    margin-bottom: 15px;
  }
}
