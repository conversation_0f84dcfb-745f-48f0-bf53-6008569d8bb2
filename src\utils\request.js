import axios from 'axios'
import store from '@/store'
import { getToken } from '@/utils/auth'
import { normalizeResponse, handleApiError } from '@/api/adapter/frontend_adapter'
import { setupMockApi } from '@/utils/mock-api'

// 用于防止重复请求的映射
const pendingRequests = new Map()

// 生成请求的唯一键
const getRequestKey = (config) => {
  const { url, method, params, data } = config
  return `${method}:${url}:${JSON.stringify(params)}:${JSON.stringify(data)}`
}

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API || 'http://127.0.0.1:5000', // 使用环境变量或默认值
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 10000 // 增加超时时间
})

// 初始化模拟API（如果需要）
setupMockApi(service)

// 拦截器处理请求
service.interceptors.request.use(
  config => {
    // do something before request is sent
    console.log('请求准备发送:', config.url)

    // 打印请求数据
    if (config.data) {
      console.log('请求数据:', config.data)
    }

    if (config.params) {
      console.log('请求参数:', JSON.stringify(config.params))
    }

    // 特殊处理停车记录API请求，防止重复请求
    if (config.url.includes('/api/parking-records')) {
      const requestKey = getRequestKey(config)

      // 如果存在相同的请求，则取消当前请求
      if (pendingRequests.has(requestKey)) {
        console.log('拦截到重复的停车记录请求:', config.url)

        // 创建取消令牌
        const source = axios.CancelToken.source()
        config.cancelToken = source.token

        // 立即取消请求
        source.cancel('重复的请求被自动取消')

        // 返回配置，让axios处理取消逻辑
        return config
      }

      // 将请求添加到映射中
      pendingRequests.set(requestKey, true)

      // 设置请求完成后的清理函数
      config.onComplete = () => {
        pendingRequests.delete(requestKey)
        console.log('请求完成，从映射中移除:', requestKey)
      }

      console.log('添加新请求到映射:', requestKey)
    }

    if (store.getters.token) {
      // let each request carry token
      // 使用Bearer格式的Authorization头
      const token = getToken()
      config.headers['Authorization'] = `Bearer ${token}`
      console.log('添加Authorization头:', `Bearer ${token}`)
    }
    return config
  },
  error => {
    // do something with request error
    console.log('请求错误:', error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    // 打印响应数据
    console.log('响应数据:', response.config.url, response.data)

    // 如果请求有完成回调，执行它
    if (response.config.onComplete) {
      response.config.onComplete()
    }

    // 使用适配器标准化响应
    return normalizeResponse(response)
  },
  error => {
    // 打印错误信息
    console.error('响应错误:', error.config?.url, error.response?.data || error.message)

    // 如果请求有完成回调，执行它
    if (error.config?.onComplete) {
      error.config.onComplete()
    }

    // 如果是取消的请求，返回特殊的响应
    if (axios.isCancel(error)) {
      console.log('请求已被取消:', error.message)
      return Promise.resolve({
        code: 20000,
        data: { items: [], total: 0 },
        message: '请求已被取消，避免重复请求'
      })
    }

    // 使用适配器处理错误
    return handleApiError(error)
  }
)

export default service
