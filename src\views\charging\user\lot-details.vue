<template>
  <div class="app-container">
    <div class="page-header">
      <el-button icon="el-icon-back" @click="goBack">返回</el-button>
      <h2 v-if="parkingLot">{{ parkingLot.name }} - 充电车位</h2>
    </div>

    <div v-loading="loading" class="lot-details-container">
      <template v-if="parkingLot">
        <el-card class="lot-info-card">
          <div class="lot-header">
            <div class="lot-title">
              <h3>{{ parkingLot.name }}</h3>
              <el-tag :type="parkingLot.status === 1 ? 'success' : 'danger'" size="mini">
                {{ parkingLot.status === 1 ? '正常运营' : '暂停使用' }}
              </el-tag>
            </div>
            <div class="lot-tags">
              <el-tag type="info" size="mini">{{ parkingLot.campus }}</el-tag>
              <el-tag type="info" size="mini">{{ parkingLot.area }}</el-tag>
            </div>
          </div>

          <div class="lot-content">
            <div class="lot-info-section">
              <div class="info-item">
                <i class="el-icon-location"></i>
                <span>地址: {{ parkingLot.address }}</span>
              </div>
              <div class="info-item">
                <i class="el-icon-time"></i>
                <span>开放时间: {{ parkingLot.opening_hours }}</span>
              </div>
              <div class="info-item">
                <i class="el-icon-user"></i>
                <span>管理员: {{ parkingLot.manager }}</span>
              </div>
              <div class="info-item">
                <i class="el-icon-phone"></i>
                <span>联系电话: {{ parkingLot.contact }}</span>
              </div>
            </div>

            <div class="lot-stats-section">
              <div class="stats-item">
                <div class="stats-label">充电车位总数</div>
                <div class="stats-value">{{ parkingLot.charging_spaces_total }}</div>
              </div>
              <div class="stats-item">
                <div class="stats-label">空闲车位</div>
                <div class="stats-value">{{ parkingLot.charging_spaces_available }}</div>
              </div>
              <div class="stats-item">
                <div class="stats-label">已用车位</div>
                <div class="stats-value">{{ parkingLot.charging_spaces_occupied }}</div>
              </div>
              <div class="stats-item">
                <div class="stats-label">利用率</div>
                <div class="stats-value">{{ parkingLot.charging_utilization_rate }}%</div>
              </div>
            </div>
          </div>

          <div class="lot-description" v-if="parkingLot.description">
            <h4>停车场说明</h4>
            <p>{{ parkingLot.description }}</p>
          </div>
        </el-card>

        <el-card class="spaces-card">
          <div slot="header" class="clearfix">
            <span>充电车位布局</span>
          </div>

          <div class="spaces-grid-container">
            <div v-if="filteredSpaces.length === 0" class="empty-spaces">
              <el-empty description="没有找到符合条件的充电车位" />
            </div>
            <div v-else class="spaces-grid">
              <div
                v-for="space in filteredSpaces"
                :key="space.id"
                class="space-item"
                :class="[
                  getSpaceStatusClass(space.status),
                  { 'space-selected': selectedSpace && selectedSpace.id === space.id }
                ]"
                @click="handleSpaceClick(space)"
              >
                <div class="space-number">{{ space.space_number }}</div>
                <div class="space-power">{{ space.power }}kW</div>
                <div v-if="space.status === 1" class="space-vehicle">
                  {{ space.vehicle_info }}
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <el-card v-if="selectedSpace" class="selected-space-card">
          <div slot="header" class="clearfix">
            <span>已选车位: {{ selectedSpace.space_number }}</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              @click="selectedSpace = null"
            >
              取消选择
            </el-button>
          </div>

          <div class="selected-space-info">
            <div class="info-row">
              <span class="info-label">车位编号</span>
              <span class="info-value">{{ selectedSpace.space_number }}</span>
            </div>

            <div class="info-row">
              <span class="info-label">充电功率</span>
              <span class="info-value">{{ selectedSpace.power }}kW</span>
            </div>
            <div class="info-row">
              <span class="info-label">车位状态</span>
              <span class="info-value">
                <el-tag :type="getStatusTagType(selectedSpace.status)" size="mini">
                  {{ getStatusText(selectedSpace.status) }}
                </el-tag>
              </span>
            </div>
          </div>

          <div v-if="selectedSpace.status === 0" class="start-charging-form">
            <h4>开始充电</h4>
            <el-form :model="chargingForm" label-width="100px" size="small">
              <el-form-item label="选择车辆">
                <el-select v-model="chargingForm.vehicleId" placeholder="请选择车辆" style="width: 100%;">
                  <el-option
                    v-for="vehicle in availableVehicles"
                    :key="vehicle.b_id"
                    :label="`${vehicle.number} (${vehicle.brand} ${vehicle.color})`"
                    :value="vehicle.b_id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="预计时长">
                <el-select v-model="chargingForm.duration" placeholder="请选择预计充电时长" style="width: 100%;">
                  <el-option label="1小时" :value="60" />
                  <el-option label="2小时" :value="120" />
                  <el-option label="3小时" :value="180" />
                  <el-option label="4小时" :value="240" />
                  <el-option label="5小时" :value="300" />
                </el-select>
              </el-form-item>

              <el-form-item label="备注">
                <el-input
                  v-model="chargingForm.remarks"
                  type="textarea"
                  placeholder="请输入备注信息"
                  :rows="2"
                />
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  :loading="startChargingLoading"
                  @click="handleStartCharging"
                >
                  开始充电
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <div v-else-if="selectedSpace.status === 1" class="space-occupied-info">
            <el-alert
              title="该车位已被占用"
              type="warning"
              :closable="false"
              show-icon
            >
              <template slot="title">
                该车位已被占用
              </template>
              <p>当前使用车辆: {{ selectedSpace.vehicle_info }}</p>
            </el-alert>
          </div>

          <div v-else class="space-unavailable-info">
            <el-alert
              :title="getStatusText(selectedSpace.status)"
              type="info"
              :closable="false"
              show-icon
            >
              <p>该车位当前不可用，请选择其他车位</p>
            </el-alert>
          </div>
        </el-card>
      </template>

      <div v-else-if="!loading" class="not-found">
        <el-empty description="未找到停车场信息" />
        <el-button type="primary" @click="goBack">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getParkingLotDetails } from '@/api/parkinglots'
import { getMyVehicles } from '@/api/vehicles'
import { startCharging } from '@/api/charging'
import request from '@/utils/request'

export default {
  name: 'ChargingLotDetails',
  data() {
    return {
      loading: true,
      parkingLot: null,
      spaces: [],
      selectedSpace: null,

      availableVehicles: [],
      chargingForm: {
        vehicleId: '',
        duration: 120,
        remarks: ''
      },
      startChargingLoading: false
    }
  },
  computed: {
    filteredSpaces() {
      return this.spaces
    }
  },
  created() {
    this.fetchData()
    this.fetchVehicles()

    // 检查是否有开始充电的操作
    if (this.$route.query.action === 'start-charging') {
      this.$message({
        message: '请选择一个空闲的充电车位',
        type: 'info'
      })
    }
  },
  methods: {
    fetchData() {
      this.loading = true
      const parkingLotId = this.$route.params.id

      // 获取停车场详情
      getParkingLotDetails(parkingLotId).then(response => {
        this.parkingLot = response.data

        // 获取停车场的充电车位 - 使用充电专用API
        return this.fetchChargingSpaces(parkingLotId)
      }).then(() => {
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    // 获取充电车位 - 使用与profile/charging页面相同的API
    fetchChargingSpaces(lotId) {
      // 使用充电专用API获取充电车位
      return request({
        url: `/api/charging/parking-lots/${lotId}/spaces`,
        method: 'get'
      })
        .then(response => {
          console.log('获取充电车位响应:', response)

          // 处理不同的响应格式
          let spacesData = null

          if (response.data) {
            if (Array.isArray(response.data)) {
              spacesData = response.data
            } else if (response.data.data && Array.isArray(response.data.data)) {
              spacesData = response.data.data
            } else if (response.data.items && Array.isArray(response.data.items)) {
              spacesData = response.data.items
            }
          } else if (Array.isArray(response)) {
            spacesData = response
          }

          if (spacesData) {
            // 确保每个车位都有必要的属性
            this.spaces = spacesData.map(space => {
              // 如果没有功率，设置默认值
              if (!space.power) {
                space.power = 7.0 // 默认7kW
              }

              return space
            })
          } else {
            console.error('无法解析充电车位数据:', response)
            this.spaces = []
          }

          return this.spaces
        })
        .catch(error => {
          console.error('获取充电车位失败', error)
          this.$message.error('获取充电车位失败')
          this.spaces = []
          return []
        })
    },
    fetchVehicles() {
      getMyVehicles().then(response => {
        // 过滤出可用的车辆（没有进行中的停车记录）
        this.availableVehicles = response.data.filter(vehicle => !vehicle.has_active_parking)
      })
    },
    goBack() {
      if (this.$route.query.from === 'charging-lots') {
        this.$router.push('/charging/lots')
      } else {
        this.$router.go(-1)
      }
    },
    getSpaceStatusClass(status) {
      switch (status) {
        case 0: return 'space-available'
        case 1: return 'space-occupied'
        case 2: return 'space-maintenance'
        default: return 'space-disabled'
      }
    },
    getStatusText(status) {
      switch (status) {
        case 0: return '空闲'
        case 1: return '已占用'
        case 2: return '维护中'
        default: return '禁用'
      }
    },
    getStatusTagType(status) {
      switch (status) {
        case 0: return 'success'
        case 1: return 'danger'
        case 2: return 'warning'
        default: return 'info'
      }
    },

    handleSpaceClick(space) {
      // 只有空闲的车位可以选择
      if (space.status === 0 || this.selectedSpace?.id === space.id) {
        this.selectedSpace = space
      } else {
        this.$message({
          message: `该车位当前${this.getStatusText(space.status)}，无法选择`,
          type: 'warning'
        })
      }
    },
    handleStartCharging() {
      if (!this.chargingForm.vehicleId) {
        this.$message({
          message: '请选择车辆',
          type: 'warning'
        })
        return
      }

      this.startChargingLoading = true

      // 构建充电请求数据
      const chargingData = {
        vehicle_id: this.chargingForm.vehicleId,
        parking_lot_id: this.parkingLot.id,
        parking_space_id: this.selectedSpace.id,
        duration: this.chargingForm.duration,
        remarks: this.chargingForm.remarks
      }

      startCharging(chargingData).then(response => {
        this.$message({
          message: '充电开始成功',
          type: 'success'
        })

        // 跳转到我的充电页面
        this.$router.push('/profile/charging')
      }).catch(error => {
        this.$message({
          message: error.message || '充电开始失败，请重试',
          type: 'error'
        })
      }).finally(() => {
        this.startChargingLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0 0 0 15px;
  }
}

.lot-details-container {
  .lot-info-card {
    margin-bottom: 20px;

    .lot-header {
      margin-bottom: 15px;

      .lot-title {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        h3 {
          margin: 0 10px 0 0;
        }
      }

      .lot-tags {
        display: flex;
        gap: 5px;
      }
    }

    .lot-content {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 15px;

      .lot-info-section {
        flex: 1;
        min-width: 300px;

        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          i {
            margin-right: 10px;
            color: #909399;
          }
        }
      }

      .lot-stats-section {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;

        .stats-item {
          background-color: #f5f7fa;
          border-radius: 4px;
          padding: 10px 15px;
          min-width: 100px;
          text-align: center;

          .stats-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 5px;
          }

          .stats-value {
            font-size: 18px;
            font-weight: bold;
            color: #409EFF;
          }
        }
      }
    }

    .lot-description {
      h4 {
        margin-top: 0;
        margin-bottom: 10px;
      }

      p {
        margin: 0;
        color: #606266;
      }
    }
  }

  .spaces-card {
    margin-bottom: 20px;

    .spaces-grid-container {
      .empty-spaces {
        padding: 30px 0;
      }

      .spaces-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 15px;
        padding: 10px;

        .space-item {
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          padding: 10px;
          cursor: pointer;
          transition: all 0.3s;
          position: relative;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }

          &.space-selected {
            border-color: #409EFF;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }

          .space-number {
            font-weight: bold;
            margin-bottom: 5px;
          }

          .space-type {
            font-size: 12px;
            margin-bottom: 5px;

            i {
              margin-right: 3px;
            }
          }

          .space-power {
            font-size: 12px;
            color: #909399;
            margin-bottom: 5px;
          }

          .space-vehicle {
            font-size: 12px;
            color: #f56c6c;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          &.space-available {
            background-color: #f0f9eb;
            border-color: #e1f3d8;

            .space-number {
              color: #67c23a;
            }
          }

          &.space-occupied {
            background-color: #fef0f0;
            border-color: #fde2e2;

            .space-number {
              color: #f56c6c;
            }
          }

          &.space-maintenance {
            background-color: #fdf6ec;
            border-color: #faecd8;

            .space-number {
              color: #e6a23c;
            }
          }

          &.space-disabled {
            background-color: #f4f4f5;
            border-color: #e9e9eb;
            color: #c0c4cc;
            cursor: not-allowed;

            .space-number {
              color: #909399;
            }
          }
        }
      }
    }
  }

  .selected-space-card {
    .selected-space-info {
      margin-bottom: 20px;

      .info-row {
        display: flex;
        margin-bottom: 10px;

        .info-label {
          width: 100px;
          color: #909399;
        }

        .info-value {
          flex: 1;
        }
      }
    }

    .start-charging-form {
      h4 {
        margin-top: 0;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
      }
    }

    .space-occupied-info,
    .space-unavailable-info {
      padding: 10px 0;
    }
  }

  .not-found {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;

    .el-button {
      margin-top: 20px;
    }
  }
}
</style>
