from flask import current_app, request
from flask_socketio import emit, join_room, leave_room, disconnect
from flask_jwt_extended import get_jwt_identity, decode_token
from app import socketio, jwt
from app.utils.auth_helpers import get_current_user_id, is_admin
import json

# JWT验证中间件
def authenticated_only(f):
    def wrapped(*args, **kwargs):
        try:
            # 从请求参数中获取token
            token = request.args.get('token')
            if not token:
                disconnect()
                return

            # 验证token
            try:
                decoded_token = decode_token(token)
                # 将用户ID存储在请求上下文中
                request.user_id = decoded_token['identity']
            except Exception as e:
                current_app.logger.error(f"Token验证失败: {str(e)}")
                disconnect()
                return

            return f(*args, **kwargs)
        except Exception as e:
            current_app.logger.error(f"WebSocket认证错误: {str(e)}")
            disconnect()
    return wrapped

# 连接事件
@socketio.on('connect')
def handle_connect():
    # 连接时不需要验证，因为这是初始连接
    # 在具体事件中验证
    try:
        token = request.args.get('token')
        if token:
            try:
                # 尝试解码token前先检查token格式
                if not token or len(token.split('.')) != 3:
                    current_app.logger.error("Token格式无效")
                    try:
                        emit('connection_response', {
                            'status': 'error',
                            'authenticated': False,
                            'error': 'Token格式无效'
                        })
                    except Exception as emit_error:
                        current_app.logger.error(f"发送错误响应失败: {str(emit_error)}")
                    finally:
                        try:
                            disconnect()
                        except Exception as disconnect_error:
                            current_app.logger.error(f"断开连接失败: {str(disconnect_error)}")
                    return

                # 尝试解码token
                decoded_token = decode_token(token)
                user_id = decoded_token['identity']
                current_app.logger.info(f'Client connected with user_id: {user_id}')
                emit('connection_response', {'status': 'connected', 'authenticated': True})
            except Exception as e:
                error_msg = str(e)
                current_app.logger.error(f"Token验证失败: {error_msg}")

                # 返回更详细的错误信息，帮助前端判断是否是token过期
                try:
                    emit('connection_response', {
                        'status': 'error',
                        'authenticated': False,
                        'error': f'Token验证失败: {error_msg}'
                    })
                except Exception as emit_error:
                    current_app.logger.error(f"发送错误响应失败: {str(emit_error)}")
                finally:
                    # 主动断开连接
                    current_app.logger.warning(f"Token验证失败，主动断开连接")
                    try:
                        disconnect()
                    except Exception as disconnect_error:
                        current_app.logger.error(f"断开连接失败: {str(disconnect_error)}")
                return
        else:
            current_app.logger.info('Client connected without token')
            try:
                emit('connection_response', {
                    'status': 'error',
                    'authenticated': False,
                    'error': 'Missing token'
                })
            except Exception as emit_error:
                current_app.logger.error(f"发送错误响应失败: {str(emit_error)}")
            finally:
                try:
                    disconnect()
                except Exception as disconnect_error:
                    current_app.logger.error(f"断开连接失败: {str(disconnect_error)}")
            return
    except Exception as e:
        error_msg = str(e)
        current_app.logger.error(f"WebSocket连接错误: {error_msg}")

        # 发送错误响应
        try:
            emit('connection_response', {'status': 'error', 'message': error_msg})
        except Exception as emit_error:
            current_app.logger.error(f"发送错误响应失败: {str(emit_error)}")

        # 发生错误时主动断开连接
        try:
            disconnect()
        except Exception as disconnect_error:
            current_app.logger.error(f"断开连接失败: {str(disconnect_error)}")

        return

# 断开连接事件
@socketio.on('disconnect')
def handle_disconnect():
    current_app.logger.info('Client disconnected')

# 加入房间（用于分组通知）
@socketio.on('join')
@authenticated_only
def on_join(data):
    """加入指定房间，用于接收特定类型的通知"""
    try:
        # 从请求上下文中获取用户ID
        user_id = request.user_id
        if not user_id:
            emit('error', {'message': '未授权'})
            return

        # 获取房间名称
        room = data.get('room')
        if not room:
            emit('error', {'message': '缺少房间名称'})
            return

        # 加入用户特定房间
        user_room = f'user_{user_id}'
        join_room(user_room)
        current_app.logger.info(f'用户 {user_id} 加入房间: {user_room}')

        # 如果是管理员，加入管理员房间
        # 从解码的token中获取角色信息
        token = request.args.get('token')
        decoded_token = decode_token(token)
        if 'role' in decoded_token and decoded_token['role'] == 'admin':
            join_room('admin')
            current_app.logger.info(f'管理员 {user_id} 加入管理员房间')

        # 加入指定房间
        join_room(room)
        current_app.logger.info(f'用户 {user_id} 加入房间: {room}')

        emit('join_response', {'status': 'success', 'room': room})
    except Exception as e:
        current_app.logger.error(f'加入房间错误: {str(e)}')
        emit('error', {'message': f'加入房间失败: {str(e)}'})

# 离开房间
@socketio.on('leave')
@authenticated_only
def on_leave(data):
    """离开指定房间"""
    try:
        room = data.get('room')
        if room:
            leave_room(room)
            current_app.logger.info(f'用户离开房间: {room}')
            emit('leave_response', {'status': 'success', 'room': room})
    except Exception as e:
        current_app.logger.error(f'离开房间错误: {str(e)}')
        emit('error', {'message': f'离开房间失败: {str(e)}'})

# 订阅停车场更新
@socketio.on('subscribe_parking_lot')
@authenticated_only
def subscribe_parking_lot(data):
    """订阅特定停车场的更新"""
    try:
        # 从请求上下文中获取用户ID
        user_id = request.user_id
        if not user_id:
            emit('error', {'message': '未授权'})
            return

        # 获取停车场ID
        parking_lot_id = data.get('parking_lot_id')
        if not parking_lot_id:
            emit('error', {'message': '缺少停车场ID'})
            return

        # 加入停车场特定房间
        room = f'parking_lot_{parking_lot_id}'
        join_room(room)
        current_app.logger.info(f'用户 {user_id} 订阅停车场: {parking_lot_id}')

        emit('subscribe_response', {
            'status': 'success',
            'parking_lot_id': parking_lot_id
        })
    except Exception as e:
        current_app.logger.error(f'订阅停车场错误: {str(e)}')
        emit('error', {'message': f'订阅停车场失败: {str(e)}'})

# 取消订阅停车场更新
@socketio.on('unsubscribe_parking_lot')
@authenticated_only
def unsubscribe_parking_lot(data):
    """取消订阅特定停车场的更新"""
    try:
        parking_lot_id = data.get('parking_lot_id')
        if parking_lot_id:
            room = f'parking_lot_{parking_lot_id}'
            leave_room(room)
            current_app.logger.info(f'用户取消订阅停车场: {parking_lot_id}')
            emit('unsubscribe_response', {
                'status': 'success',
                'parking_lot_id': parking_lot_id
            })
    except Exception as e:
        current_app.logger.error(f'取消订阅停车场错误: {str(e)}')
        emit('error', {'message': f'取消订阅停车场失败: {str(e)}'})
