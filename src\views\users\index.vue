<template>
  <div class="app-container">
    <!-- 权限控制 - 只有管理员可以访问 -->
    <div v-if="!isAdmin" class="permission-alert">
      <el-alert
        title="权限不足"
        type="error"
        description="您没有权限访问此页面，此功能仅对管理员开放"
        show-icon
        :closable="false"
      >
        <div slot="title">
          <i class="el-icon-warning" style="margin-right: 10px;" />
          <span style="font-weight: bold;">权限不足</span>
        </div>
        <div style="margin: 15px 0;">
          <el-button size="small" type="primary" @click="goBack">返回首页</el-button>
        </div>
      </el-alert>
    </div>

    <!-- 管理员可见内容 -->
    <div v-else>
      <!-- 添加user信息按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" icon="el-icon-refresh" size="mini" @click="refreshList">刷新</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button :type="showCharts ? 'warning' : 'info'" icon="el-icon-data-analysis" size="mini" @click="toggleCharts">
            {{ showCharts ? '隐藏图表' : '显示图表' }}
          </el-button>
        </el-col>
      </el-row>

      <!-- 数据可视化区域 -->
      <el-row v-if="tableData.length > 0 && showCharts" :gutter="20" class="chart-container">
        <el-col :span="12">
          <el-card shadow="hover">
            <div slot="header" class="clearfix">
              <span>用户角色分布</span>
            </div>
            <div ref="roleChart" class="chart" />
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <div slot="header" class="clearfix">
              <span>部门分布</span>
            </div>
            <div ref="deptChart" class="chart" />
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="filter-container" @submit.native.prevent>
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable size="small" @keyup.enter.native="handleSearch" />
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="searchForm.role" placeholder="请选择角色" clearable size="small">
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
            <el-option label="保安人员" value="security" />
          </el-select>
        </el-form-item>
        <el-form-item label="学院/部门">
          <el-input v-model="searchForm.department" placeholder="请输入学院/部门" clearable size="small" @keyup.enter.native="handleSearch" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable size="small">
            <el-option label="激活" :value="1" />
            <el-option label="未激活" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作成功提示条 -->
      <el-alert
        v-if="showAlert"
        :title="alertMessage"
        :type="alertType"
        show-icon
        class="mb8"
        @close="showAlert = false"
      />

      <!--user信息列表 -->
      <el-table v-loading="loading" :data="tableData" border style="width: 100%">
        <el-table-column prop="u_id" label="序号" width="80" />
        <el-table-column prop="u_name" label="姓名" width="120" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="u_role" label="角色" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.u_role === 'admin' ? 'danger' : scope.row.u_role === 'security' ? 'warning' : 'success'">
              {{ getRoleName(scope.row.u_role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="u_belong" label="学院/部门" />
        <el-table-column prop="u_phone" label="电话" width="130" />
        <el-table-column prop="u_email" label="邮箱" width="200" />
        <el-table-column label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? '激活' : '未激活' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页控件 -->
      <el-pagination
        v-if="total > 0"
        :current-page.sync="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        style="margin-top: 15px; text-align: right;"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />

      <!--添加user信息表单-->
      <el-dialog title="添加/编辑 用户信息" :visible.sync="dialogVisible" width="50%" center @closed="reset">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="姓名" prop="u_name">
            <el-input v-model="form.u_name" placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item v-if="!form.u_id" label="用户名" prop="username">
            <el-input v-model="form.username" placeholder="请输入用户名（用于登录）" />
          </el-form-item>
          <el-form-item label="角色" prop="u_role">
            <el-select v-model="form.u_role" placeholder="请选择角色" style="width: 100%">
              <el-option label="管理员" value="admin" />
              <el-option label="普通用户" value="user" />
              <el-option label="保安人员" value="security" />
            </el-select>
          </el-form-item>
          <el-form-item label="学院/部门" prop="u_belong">
            <el-input v-model="form.u_belong" placeholder="请输入学院或部门名称" />
          </el-form-item>
          <el-form-item label="电话" prop="u_phone">
            <el-input v-model="form.u_phone" placeholder="请输入手机号码" />
          </el-form-item>
          <el-form-item label="邮箱" prop="u_email">
            <el-input v-model="form.u_email" placeholder="请输入邮箱地址" />
          </el-form-item>
          <el-form-item v-if="!form.u_id" label="密码" prop="u_pwd">
            <el-input v-model="form.u_pwd" placeholder="请输入密码" show-password />
            <div class="form-tip">默认密码为123456，用户可以后续自行修改</div>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="form.status"
              :active-value="1"
              :inactive-value="0"
              active-text="激活"
              inactive-text="未激活"
            />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelForm">取 消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="saveForm">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 删除确认对话框 -->
      <el-dialog
        title="确认删除"
        :visible.sync="deleteDialogVisible"
        width="30%"
        center
      >
        <span>确定要删除用户 "{{ form.u_name }}" 吗？</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelDelete">取 消</el-button>
          <el-button type="primary" :loading="deleteLoading" @click="confirmDelete">确 定</el-button>
        </span>
      </el-dialog>


    </div>
  </div>
</template>

<script>
import {
  create_user,
  get_users,
  get_user_by_id,
  update_user_by_id,
  delete_user_by_id
} from '@/api/owner'
import { mapGetters } from 'vuex'
import * as echarts from 'echarts'

export default {
  data() {
    // 自定义邮箱验证规则
    const validateEmail = (rule, value, callback) => {
      if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        callback(new Error('请输入正确的邮箱地址'))
      } else {
        callback()
      }
    }

    return {
      tableData: [], // 页面表格数据，即用户信息列表
      dialogVisible: false, // 用于控制添加与编辑用户信息表单组件是否显示
      deleteDialogVisible: false, // 用于控制删除确认对话框是否显示
      loading: false, // 列表加载状态
      submitLoading: false, // 提交按钮加载状态
      deleteLoading: false, // 删除按钮加载状态
      showAlert: false, // 是否显示操作提示
      alertMessage: '', // 提示消息
      alertType: 'success', // 提示类型
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页显示条数
      total: 0, // 总记录数
      form: { // 用户信息
        u_id: null, // 用户序号
        u_name: '', // 用户姓名
        username: '', // 用户登录名
        u_role: 'user', // 用户角色，默认为普通用户
        u_pwd: '123456', // 用户密码，默认值
        u_belong: '', // 用户所属学院或部门
        u_phone: '', // 用户电话
        u_email: '', // 用户邮箱
        status: 1 // 用户状态，1表示激活，0表示未激活
      },
      rules: { // 表单验证规则
        u_name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度应为3-20个字符', trigger: 'blur' }
        ],
        u_role: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ],
        u_belong: [
          { required: true, message: '请输入学院或部门', trigger: 'blur' }
        ],
        u_phone: [
          { required: true, message: '请输入电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        u_email: [
          { validator: validateEmail, trigger: 'blur' }
        ],
        u_pwd: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
        ]
      },
      currentIndex: null, // 记录当前操作的用户在 tableData 中的索引
      searchForm: { // 搜索表单
        name: '',
        role: '',
        department: '',
        status: ''
      },
      roleChart: null,
      deptChart: null,
      showCharts: true
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'userId'
    ]),
    isAdmin() {
      return this.roles && this.roles.includes('admin')
    }
  },
  created() {
    // 检查权限，如果是管理员则获取用户列表
    if (this.isAdmin) {
      this.fetchUserInfo()
    } else {
      console.log('非管理员用户尝试访问用户管理页面')
    }
  },
  mounted() {
    // 初始化图表，但仅当数据加载完成后才显示
    this.$nextTick(() => {
      if (this.tableData.length > 0) {
        this.initCharts()
      }
    })
  },
  updated() {
    // 数据更新时重新渲染图表
    this.$nextTick(() => {
      if (this.tableData.length > 0) {
        this.initCharts()
      }
    })
  },
  methods: {
    goBack() {
      this.$router.push('/dashboard')
    },

    getRoleName(role) {
      const roleMap = {
        'admin': '管理员',
        'user': '普通用户',
        'security': '保安人员'
      }
      return roleMap[role] || '未知角色'
    },

    // 刷新用户列表
    refreshList() {
      this.fetchUserInfo()
    },

    // 显示操作结果提示
    showMessage(message, type = 'success') {
      this.alertMessage = message
      this.alertType = type
      this.showAlert = true

      // 5秒后自动关闭提示
      setTimeout(() => {
        this.showAlert = false
      }, 5000)
    },

    // 获取用户信息列表
    async fetchUserInfo() {
      this.loading = true
      try {
        // 准备查询参数，支持分页和搜索
        const params = {
          page: this.currentPage,
          per_page: this.pageSize
        }

        // 添加搜索条件
        if (this.searchForm.name) {
          params.name = this.searchForm.name
        }
        if (this.searchForm.role) {
          params.role = this.searchForm.role
        }
        if (this.searchForm.department) {
          params.department = this.searchForm.department
        }
        if (this.searchForm.status !== '') {
          params.status = this.searchForm.status
        }

        // 发送API请求获取用户列表
        const response = await get_users(params)
        console.log('获取用户列表响应:', response)

        // 提取用户列表和分页信息
        let users = []
        let total = 0

        // 深度递归查找users数组
        const findUsers = (obj) => {
          if (!obj || typeof obj !== 'object') return null

          // 直接找到users数组
          if (Array.isArray(obj.users)) {
            return {
              users: obj.users,
              total: obj.pagination?.total || obj.total || obj.users.length
            }
          }

          // 在data字段中查找
          if (obj.data) {
            const result = findUsers(obj.data)
            if (result) return result
          }

          // 检查所有键
          for (const key in obj) {
            if (key !== 'data' && typeof obj[key] === 'object') {
              const result = findUsers(obj[key])
              if (result) return result
            }
          }

          return null
        }

        // 如果是数组，直接使用
        if (Array.isArray(response)) {
          users = response
          total = response.length
        } else {
          // 否则尝试查找users数组
          const result = findUsers(response)
          if (result) {
            users = result.users
            total = result.total
          } else if (response && response.users) {
            users = response.users
            total = response.pagination?.total || response.total || users.length
          }
        }

        console.log('提取的用户数据:', users)

        // 格式化用户数据，统一字段名称
        if (users && users.length > 0) {
          this.tableData = users.map(user => ({
            u_id: user.id || user.u_id,
            u_name: user.username || user.u_name,
            username: user.username || user.u_name,
            u_role: user.role || user.u_role || 'user',
            u_belong: user.department || user.u_belong || '',
            u_phone: user.phone || user.u_phone || '',
            u_email: user.email || user.u_email || '',
            status: user.status !== undefined ? user.status : 1
          }))
          this.total = total

          // 数据加载完成后初始化图表
          this.$nextTick(() => {
            this.initCharts()
          })
        } else {
          this.tableData = []
          this.total = 0
          this.$message.info('没有找到用户数据')
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        this.$message.error('获取用户列表失败: ' + (error.message || '未知错误'))
        this.tableData = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    // 处理新增用户按钮点击事件
    handleAdd() {
      this.reset()
      this.dialogVisible = true
    },

    // 处理编辑按钮点击事件
    async handleEdit(index, row) {
      this.currentIndex = index
      this.loading = true

      try {
        // 获取详细信息
        const response = await get_user_by_id(row.u_id)
        this.loading = false

        // 格式化数据
        let userData = {}
        if (response && response.data && response.data.user) {
          userData = response.data.user
        } else if (response && response.user) {
          userData = response.user
        } else {
          userData = response
        }

        // 将API返回的数据格式映射到表单字段
        this.form = {
          u_id: userData.id || userData.u_id || row.u_id,
          u_name: userData.username || userData.u_name || row.u_name,
          username: userData.username || userData.u_name || row.username,
          u_role: userData.role || userData.u_role || row.u_role,
          u_belong: userData.department || userData.u_belong || row.u_belong,
          u_phone: userData.phone || userData.u_phone || row.u_phone,
          u_email: userData.email || userData.u_email || row.u_email,
          status: userData.status !== undefined ? userData.status : 1
        }

        this.dialogVisible = true
      } catch (error) {
        this.loading = false
        console.error('获取用户详情失败', error)

        // 如果获取详情失败，直接使用表格行数据
        this.form = { ...row }
        this.dialogVisible = true

        this.$message.warning('获取用户详情失败，显示基本信息')
      }
    },

    // 处理删除操作
    handleDelete(index, row) {
      this.deleteDialogVisible = true
      this.currentIndex = index
      this.form = { ...row }
    },

    // 重置表单数据
    reset() {
      this.form = {
        u_id: null,
        u_name: '',
        username: '',
        u_role: 'user', // 默认为普通用户
        u_pwd: '123456', // 默认密码
        u_belong: '',
        u_phone: '',
        u_email: '',
        status: 1
      }
      // 如果表单已挂载，则重置字段
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
    },

    // 取消表单对话框
    cancelForm() {
      this.dialogVisible = false
    },

    // 保存表单数据
    async saveForm() {
      this.$refs.formRef.validate(async(valid) => {
        if (valid) {
          this.submitLoading = true

          try {
            // 准备提交数据
            const data = {
              username: this.form.u_name,
              role: this.form.u_role, // 包含用户角色
              department: this.form.u_belong,
              phone: this.form.u_phone,
              email: this.form.u_email,
              status: this.form.status
            }

            // 添加密码字段（只在新增用户时）
            if (!this.form.u_id && this.form.u_pwd) {
              data.password = this.form.u_pwd
            }

            if (this.form.u_id) {
              // 更新用户
              await update_user_by_id(this.form.u_id, data)
              this.showMessage(`用户 "${this.form.u_name}" 信息更新成功`)
            } else {
              // 创建用户
              await create_user(data)
              this.showMessage(`用户 "${this.form.u_name}" 创建成功`)
            }

            // 关闭对话框
            this.dialogVisible = false

            // 刷新列表
            this.fetchUserInfo()
          } catch (error) {
            console.error('保存用户信息失败', error)
            const errorMsg = error.response?.data?.message || error.message || '未知错误'
            this.$message.error('保存失败: ' + errorMsg)
            this.showMessage('保存失败: ' + errorMsg, 'error')
          } finally {
            this.submitLoading = false
          }
        } else {
          return false
        }
      })
    },

    // 取消删除
    cancelDelete() {
      this.deleteDialogVisible = false
    },

    // 确认删除
    async confirmDelete() {
      this.deleteLoading = true
      try {
        await delete_user_by_id(this.form.u_id)

        // 显示成功提示
        this.showMessage(`用户 "${this.form.u_name}" 删除成功`)

        // 从表格数据中移除该项
        if (this.currentIndex !== null) {
          this.tableData.splice(this.currentIndex, 1)
        }
        this.deleteDialogVisible = false
      } catch (error) {
        console.error('删除用户信息失败', error)
        this.$message.error('删除用户信息失败')
        this.showMessage('删除失败：' + (error.response?.data?.message || error.message || '未知错误'), 'error')
      } finally {
        this.deleteLoading = false
      }
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchUserInfo()
    },

    // 处理每页数量变化
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchUserInfo()
    },

    // 处理搜索
    handleSearch() {
      this.currentPage = 1
      this.fetchUserInfo()
    },

    // 重置搜索条件
    resetSearch() {
      this.searchForm = {
        name: '',
        role: '',
        department: '',
        status: ''
      }
      this.currentPage = 1
      this.fetchUserInfo()
    },

    // 初始化图表
    initCharts() {
      this.initRoleChart()
      this.initDepartmentChart()
    },

    // 初始化角色分布图表
    initRoleChart() {
      // 清除可能存在的旧图表实例
      if (this.roleChart) {
        this.roleChart.dispose()
      }

      // 确保DOM元素存在
      if (!this.$refs.roleChart) return

      // 初始化图表
      this.roleChart = echarts.init(this.$refs.roleChart)

      // 统计各角色用户数量
      const roleCount = {}
      this.tableData.forEach(user => {
        const role = user.u_role || 'unknown'
        roleCount[role] = (roleCount[role] || 0) + 1
      })

      // 准备图表数据
      const data = Object.keys(roleCount).map(role => {
        const roleName = this.getRoleName(role)
        return { name: roleName, value: roleCount[role] }
      })

      // 设置图表选项
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '角色分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderWidth: 2
            },
            label: {
              show: true,
              formatter: '{b}: {c} ({d}%)'
            },
            labelLine: {
              show: true
            },
            data: data
          }
        ]
      }

      // 设置图表
      this.roleChart.setOption(option)
    },

    // 初始化部门分布图表
    initDepartmentChart() {
      // 清除可能存在的旧图表实例
      if (this.deptChart) {
        this.deptChart.dispose()
      }

      // 确保DOM元素存在
      if (!this.$refs.deptChart) return

      // 初始化图表
      this.deptChart = echarts.init(this.$refs.deptChart)

      // 统计各部门用户数量
      const deptCount = {}
      this.tableData.forEach(user => {
        const dept = user.u_belong || '未分配'
        deptCount[dept] = (deptCount[dept] || 0) + 1
      })

      // 准备图表数据
      const depts = Object.keys(deptCount)
      const data = depts.map(dept => deptCount[dept])

      // 设置图表选项
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: depts
        },
        series: [
          {
            name: '用户数',
            type: 'bar',
            data: data
          }
        ]
      }

      // 设置图表
      this.deptChart.setOption(option)
    },

    // 切换图表显示状态
    toggleCharts() {
      this.showCharts = !this.showCharts
    },


  }
}
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.empty-container {
  text-align: center;
  padding: 50px 0;
  color: #909399;
}

.empty-container i {
  font-size: 50px;
  margin-bottom: 20px;
}

.empty-container p {
  font-size: 14px;
}

.permission-alert {
  margin: 20px auto;
  max-width: 600px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  margin-top: 4px;
}

.el-pagination {
  margin-top: 15px;
}

.filter-container {
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.app-container .el-table .warning-row {
  background: #fdf6ec;
}

.app-container .el-table .success-row {
  background: #f0f9eb;
}

.app-container .el-tag {
  margin-right: 5px;
}

.chart-container {
  margin-top: 20px;
}

.chart {
  height: 300px;
}

.role-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.role-info p {
  margin: 8px 0;
}

.role-form {
  margin-top: 20px;
}
</style>

