<template>
  <div class="app-container">
    <div class="dashboard-header">
      <div class="dashboard-title">
        <el-page-header :content="parkingLot.name" @back="goBack" />
        <p class="subtitle">{{ isEditMode ? '编辑停车场信息' : '停车场详细信息和车位管理' }}</p>
      </div>
      <div class="dashboard-actions">
        <template v-if="!isEditMode">
          <el-button v-if="checkPermission(['admin'])" type="warning" icon="el-icon-s-grid" @click="handleEditSpaces">编辑车位</el-button>
          <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
        </template>
        <template v-else>
          <el-button type="primary" icon="el-icon-check" @click="submitEditForm">保存</el-button>
          <el-button icon="el-icon-close" @click="cancelEdit">取消</el-button>
        </template>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-if="isEditMode" v-loading="loading">
      <el-form ref="editForm" :model="editForm" :rules="editFormRules" label-width="120px" class="edit-form">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="名称" prop="name">
              <el-input v-model="editForm.name" placeholder="请输入停车场名称" />
            </el-form-item>
            <el-form-item label="地址" prop="address">
              <el-input v-model="editForm.address" placeholder="请输入地址" />
            </el-form-item>
            <el-form-item label="车位数量" prop="total_spaces">
              <el-input-number v-model="editForm.total_spaces" :min="1" :max="1000" />
            </el-form-item>
            <el-form-item label="状态">
              <el-radio-group v-model="editForm.status">
                <el-radio :label="1">正常运营</el-radio>
                <el-radio :label="0">暂停使用</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="开放时间">
              <el-input v-model="editForm.opening_hours" placeholder="例如：24小时" />
            </el-form-item>
            <el-form-item label="描述">
              <el-input v-model="editForm.description" type="textarea" :rows="3" placeholder="请输入停车场描述" />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="附加信息" name="additional">
            <el-form-item label="校区">
              <el-input v-model="editForm.campus" placeholder="请输入校区" />
            </el-form-item>
            <el-form-item label="区域">
              <el-input v-model="editForm.area" placeholder="请输入区域" />
            </el-form-item>
            <el-form-item label="管理员">
              <el-input v-model="editForm.manager" placeholder="请输入管理员姓名" />
            </el-form-item>
            <el-form-item label="联系方式">
              <el-input v-model="editForm.contact" placeholder="请输入联系方式" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
    </div>

    <!-- 查看模式 -->
    <el-row v-else v-loading="loading" :gutter="20" class="main-content">
      <!-- 左侧停车场基本信息 -->
      <el-col :xs="24" :sm="24" :md="6">
        <div class="parking-card">
          <div class="card-header">
            <h3 class="title">停车场信息</h3>
            <div class="status-tag" :class="parkingLot.status === 1 ? 'operating' : 'paused'">
              <span class="dot" :class="parkingLot.status === 1 ? 'operating' : 'paused'"></span>
              {{ parkingLot.status_text }}
            </div>
          </div>
          <div class="card-body">
            <div class="info-section">
              <div class="info-item">
                <i class="el-icon-office-building info-icon"></i>
                <div class="info-content">
                  <div class="info-label">名称</div>
                  <div class="info-value">{{ parkingLot.name }}</div>
                </div>
              </div>

              <div class="info-item">
                <i class="el-icon-location info-icon"></i>
                <div class="info-content">
                  <div class="info-label">地址</div>
                  <div class="info-value">{{ parkingLot.address }}</div>
                </div>
              </div>

              <div class="info-item">
                <i class="el-icon-time info-icon"></i>
                <div class="info-content">
                  <div class="info-label">开放时间</div>
                  <div class="info-value">{{ parkingLot.opening_hours || '24小时' }}</div>
                </div>
              </div>

              <div class="info-item">
                <i class="el-icon-document info-icon"></i>
                <div class="info-content">
                  <div class="info-label">描述</div>
                  <div class="info-value">{{ parkingLot.description || '无描述信息' }}</div>
                </div>
              </div>
            </div>

            <div class="stats-section">
              <div class="stats-header">车位统计</div>
              <div class="stats-grid">
                <div class="stats-item">
                  <div class="stats-value">{{ parkingLot.total_spaces }}</div>
                  <div class="stats-label">总车位数</div>
                </div>
                <div class="stats-item">
                  <div class="stats-value">{{ parkingLot.occupied_spaces }}</div>
                  <div class="stats-label">已占用</div>
                </div>
                <div class="stats-item">
                  <div class="stats-value">{{ parkingLot.available_spaces }}</div>
                  <div class="stats-label">空闲车位</div>
                </div>
                <div class="stats-item">
                  <div class="stats-value" :class="getUtilizationClass(parkingLot.utilization_rate)">{{ parkingLot.utilization_rate }}%</div>
                  <div class="stats-label">利用率</div>
                </div>
              </div>
            </div>

            <!-- 车位利用率图表 -->
            <div class="chart-section">
              <div class="chart-header">车位利用率</div>
              <div ref="utilizationChart" class="chart" />
            </div>
          </div>
        </div>


      </el-col>

      <!-- 右侧车位管理 -->
      <el-col :xs="24" :sm="24" :md="18">
        <div class="parking-card">
          <div class="card-header">
            <h3 class="title">车位管理</h3>
            <div class="actions">
              <!-- 已取消开始停车功能 -->
            </div>
          </div>

          <div class="card-body">
            <!-- 车位管理工具栏 -->
            <div class="spaces-toolbar">
              <!-- 左侧筛选器 -->
              <div class="spaces-filter">
                <el-input
                  v-model="spacesQuery.search"
                  placeholder="搜索车位编号"
                  prefix-icon="el-icon-search"
                  clearable
                  size="small"
                  style="width: 200px; margin-right: 10px;"
                  @keyup.enter.native="handleSpacesFilter"
                />
                <el-select
                  v-model="spacesQuery.status"
                  placeholder="状态"
                  clearable
                  size="small"
                  style="width: 120px; margin-right: 10px;"
                  @change="handleSpacesFilter"
                >
                  <el-option label="空闲" :value="0" />
                  <el-option label="已占用" :value="1" />
                  <el-option label="维护中" :value="2" />
                </el-select>
                <el-select
                  v-model="spacesQuery.type"
                  placeholder="类型"
                  clearable
                  size="small"
                  style="width: 120px; margin-right: 10px;"
                  @change="handleSpacesFilter"
                >
                  <el-option label="普通车位" :value="1" />
                  <el-option label="残疾人车位" :value="2" />
                  <el-option label="充电车位" :value="3" />
                </el-select>
              </div>

              <!-- 右侧操作区 -->
              <div class="spaces-actions">
                <!-- 批量编辑按钮已移除 -->
              </div>
            </div>

            <!-- 车位网格列表 -->
            <div class="spaces-container">
              <div class="spaces-grid">
                <div
                  v-for="space in filteredSpaces"
                  :key="space.id"
                  class="space-item"
                  :class="getSpaceClass(space)"
                  @click="showSpaceDetails(space)"
                >
                  <div class="space-number">{{ space.space_number }}</div>
                  <div class="space-type">{{ space.type_text }}</div>
                  <div class="space-status">{{ space.status_text }}</div>
                  <div v-if="space.status === 1" class="vehicle-info" :title="getVehicleInfo(space)">
                    <i class="el-icon-bicycle" style="margin-right: 3px;"></i>
                    {{ getVehicleInfo(space) }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 空数据提示 -->
            <div v-if="filteredSpaces.length === 0" class="empty-data">
              <el-alert
                title="没有找到符合条件的车位"
                type="info"
                :closable="false"
                center
                show-icon
              >
                <template slot="title">
                  <div class="empty-title">
                    <i class="el-icon-info"></i>
                    <span>没有找到符合条件的车位</span>
                  </div>
                </template>
              </el-alert>
            </div>
          </div>

          <!-- 车位分页 -->
          <div class="pagination-container">
            <el-pagination
              background
              layout="prev, pager, next, sizes"
              :total="totalSpaces"
              :page-size="spacesQuery.limit"
              :page-sizes="[50]"
              :current-page.sync="spacesQuery.page"
              @current-change="fetchSpaces"
              @size-change="handleSizeChange"
            />
          </div>
        </div>
      </el-col>
    </el-row>



    <!-- 批量编辑车位对话框 -->
    <el-dialog
      title="批量编辑车位"
      :visible.sync="batchEditDialogVisible"
      width="80%"
      :close-on-click-modal="false"
      custom-class="batch-edit-dialog"
    >
      <div v-loading="batchEditLoading" class="batch-edit-container">
        <!-- 左侧批量操作区域 -->
        <div class="batch-actions-panel">
          <div class="selected-count-info">
            已选择 <span class="selected-count">{{ selectedSpaces.length }}</span> 个车位
          </div>

          <el-divider content-position="left">修改车位类型</el-divider>
          <div class="action-group">
            <el-button
              size="small"
              :disabled="selectedSpaces.length === 0"
              @click="batchUpdateType(1)"
            >设为普通车位</el-button>
            <el-button
              size="small"
              type="success"
              :disabled="selectedSpaces.length === 0"
              @click="batchUpdateType(2)"
            >设为残疾人车位</el-button>
            <el-button
              size="small"
              type="primary"
              :disabled="selectedSpaces.length === 0"
              @click="batchUpdateType(3)"
            >设为充电车位</el-button>
          </div>

          <el-divider content-position="left">修改车位状态</el-divider>
          <div class="action-group">
            <el-button
              size="small"
              type="success"
              :disabled="selectedSpaces.length === 0 || hasOccupiedSpaces"
              @click="batchUpdateStatus(0)"
            >设为空闲</el-button>
            <el-button
              size="small"
              type="info"
              :disabled="selectedSpaces.length === 0 || hasOccupiedSpaces"
              @click="batchUpdateStatus(2)"
            >设为维护中</el-button>
          </div>

          <el-divider content-position="left">选择操作</el-divider>
          <div class="action-group">
            <el-button
              size="small"
              @click="selectAll"
            >全选</el-button>
            <el-button
              size="small"
              @click="deselectAll"
              :disabled="selectedSpaces.length === 0"
            >取消选择</el-button>
            <el-button
              size="small"
              @click="invertSelection"
            >反选</el-button>
          </div>

          <div class="save-actions">
            <el-button type="primary" :loading="saveLoading" @click="saveChanges">保存更改</el-button>
          </div>
        </div>

        <!-- 右侧车位网格 -->
        <div class="spaces-grid-panel">
          <div class="filter-actions">
            <el-input
              v-model="batchEditQuery.search"
              placeholder="搜索车位编号"
              style="width: 200px;"
              clearable
              @clear="handleBatchEditFilter"
              @keyup.enter.native="handleBatchEditFilter"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleBatchEditFilter"></el-button>
            </el-input>
            <el-select v-model="batchEditQuery.type" placeholder="车位类型" style="width: 140px;" @change="handleBatchEditFilter" clearable>
              <el-option label="全部类型" value=""></el-option>
              <el-option label="普通车位" :value="1"></el-option>
              <el-option label="残疾人车位" :value="2"></el-option>
              <el-option label="充电车位" :value="3"></el-option>
            </el-select>
            <el-select v-model="batchEditQuery.status" placeholder="车位状态" style="width: 140px;" @change="handleBatchEditFilter" clearable>
              <el-option label="全部状态" value=""></el-option>
              <el-option label="空闲" :value="0"></el-option>
              <el-option label="已占用" :value="1"></el-option>
              <el-option label="维护中" :value="2"></el-option>
            </el-select>
          </div>

          <div class="parking-spaces-grid">
            <template v-if="filteredBatchEditSpaces.length > 0">
              <div
                v-for="space in filteredBatchEditSpaces"
                :key="space.id"
                class="parking-space-item"
                :class="[
                  getSpaceStatusClass(space.status),
                  { 'selected': isSpaceSelected(space.id) }
                ]"
                @click="toggleSpaceSelection(space)"
              >
                <div class="space-number">{{ space.space_number }}</div>
                <div class="space-type">
                  <el-tag :type="getSpaceTypeTag(space.type)" size="mini">
                    {{ getSpaceTypeName(space.type) }}
                  </el-tag>
                </div>
                <div class="space-status">
                  <el-tag :type="getSpaceStatusType(space.status)" size="mini">
                    {{ getSpaceStatusName(space.status) }}
                  </el-tag>
                </div>
                <div v-if="space.current_vehicle_id" class="space-vehicle">
                  <i class="el-icon-bicycle"></i> 已停车
                </div>
              </div>
            </template>
            <template v-else>
              <div class="empty-spaces-message">
                <i class="el-icon-info-circle"></i>
                <p>没有找到符合条件的车位</p>
              </div>
            </template>
          </div>

          <!-- 批量编辑分页 -->
          <div class="batch-edit-pagination">
            <el-pagination
              background
              layout="prev, pager, next, sizes"
              :total="batchEditTotalSpaces"
              :page-size="batchEditQuery.limit"
              :page-sizes="[50]"
              :current-page.sync="batchEditQuery.page"
              @current-change="handleBatchEditPageChange"
              @size-change="handleBatchEditSizeChange"
            />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import request from '@/utils/request'
import * as echarts from 'echarts'
import { updateParkingSpaceStatus } from '@/api/parkinglot'

export default {
  name: 'ParkingDetails',
  data() {
    return {
      loading: true,
      activeTab: 'basic',
      isEditMode: false,
      parkingLot: {
        id: 0,
        name: '',
        address: '',
        total_spaces: 0,
        occupied_spaces: 0,
        available_spaces: 0,
        utilization_rate: 0,
        status: 1,
        status_text: '',
        opening_hours: '',
        description: '',
        campus: '',
        area: '',
        manager: '',
        contact: ''
      },
      spaces: [],
      totalSpaces: 0,
      spacesQuery: {
        page: 1,
        limit: 50,
        search: '',
        status: '',
        type: ''
      },

      utilizationChart: null,
      // 编辑表单
      editForm: {
        name: '',
        address: '',
        total_spaces: 0,
        status: 1,
        description: '',
        opening_hours: '',
        campus: '',
        area: '',
        manager: '',
        contact: ''
      },
      editFormRules: {
        name: [{ required: true, message: '停车场名称不能为空', trigger: 'blur' }],
        address: [{ required: true, message: '地址不能为空', trigger: 'blur' }],
        total_spaces: [{ required: true, message: '车位数量不能为空', trigger: 'blur' }]
      },
      // 批量编辑相关
      batchEditDialogVisible: false,
      batchEditLoading: false,
      saveLoading: false,
      selectedSpaces: [], // 存储选中的车位ID
      changedSpaces: {}, // 存储已修改的车位信息
      hasChanges: false, // 是否有未保存的更改
      batchEditQuery: {
        search: '',
        status: '',
        type: '',
        page: 1,
        limit: 50
      },
      batchEditTotalSpaces: 0
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'userId'
    ]),
    filteredSpaces() {
      let result = this.spaces

      if (this.spacesQuery.search) {
        const searchText = this.spacesQuery.search.toLowerCase()
        result = result.filter(space =>
          space.space_number.toLowerCase().includes(searchText)
        )
      }

      if (this.spacesQuery.status !== '') {
        result = result.filter(space => space.status === this.spacesQuery.status)
      }

      if (this.spacesQuery.type !== '') {
        result = result.filter(space => space.type === this.spacesQuery.type)
      }

      return result
    },
    filteredBatchEditSpaces() {
      let result = this.spaces

      if (this.batchEditQuery.search) {
        const searchText = this.batchEditQuery.search.toLowerCase()
        result = result.filter(space =>
          space.space_number.toLowerCase().includes(searchText)
        )
      }

      if (this.batchEditQuery.status !== '') {
        const statusValue = parseInt(this.batchEditQuery.status)
        result = result.filter(space => space.status === statusValue)
      }

      if (this.batchEditQuery.type !== '') {
        const typeValue = parseInt(this.batchEditQuery.type)
        result = result.filter(space => space.type === typeValue)
      }

      // 更新总数量
      this.batchEditTotalSpaces = result.length

      // 分页处理
      const startIndex = (this.batchEditQuery.page - 1) * this.batchEditQuery.limit
      const endIndex = startIndex + this.batchEditQuery.limit

      const paginatedResult = result.slice(startIndex, endIndex)
      console.log(`批量编辑分页: 第${this.batchEditQuery.page}页，每页${this.batchEditQuery.limit}条，总共${result.length}条，当前页${paginatedResult.length}条`)

      return paginatedResult
    },
    hasOccupiedSpaces() {
      // 检查选中的车位中是否有已占用的车位
      return this.selectedSpaces.some(spaceId => {
        const space = this.spaces.find(s => s.id === spaceId)
        return space && space.status === 1
      })
    }
  },
  // 监听窗口大小变化，重绘图表
  watch: {
    'parkingLot.occupied_spaces': function() {
      this.$nextTick(() => {
        this.updateUtilizationChart()
      })
    }
  },
  created() {
    // 检查当前路由是否为编辑模式
    this.isEditMode = this.$route.path.includes('/parking/edit/')
    console.log('当前模式:', this.isEditMode ? '编辑模式' : '查看模式')

    // 获取停车场数据
    this.fetchParkingLot()
  },
  mounted() {
    this.initCharts()
  },
  beforeDestroy() {
    if (this.utilizationChart) {
      this.utilizationChart.dispose()
      this.utilizationChart = null
    }
  },
  methods: {
    checkPermission(permissionRoles) {
      if (this.roles.includes('admin')) {
        return true
      }
      if (!permissionRoles) return false
      return permissionRoles.some(role => this.roles.includes(role))
    },
    goBack() {
      this.$router.push('/parking')
    },
    fetchParkingLot() {
      const lotId = this.$route.params.id
      if (!lotId) {
        this.$message.error('没有指定停车场ID')
        this.goBack()
        return
      }

      this.loading = true
      request({
        url: `/api/parkinglots/${lotId}`,
        method: 'get'
      }).then(response => {
        console.log('获取停车场详情成功:', response)
        console.log('停车场详情响应完整结构:', JSON.stringify(response))

        let lotData = response
        if (response.data) {
          lotData = response.data
        }

        // 检查数据结构，尝试多种可能的格式
        if (lotData.id) {
          // 直接是停车场对象
          this.parkingLot = lotData
          console.log('停车场详情数据(直接对象):', this.parkingLot)
        } else if (lotData.data && lotData.data.id) {
          // 嵌套在data字段中
          this.parkingLot = lotData.data
          console.log('停车场详情数据(从data提取):', this.parkingLot)
        } else {
          // 输出所有可能的属性路径
          console.log('尝试查找停车场详情数据:')
          for (const key in lotData) {
            console.log(`- ${key}:`, lotData[key])
            if (lotData[key] && typeof lotData[key] === 'object') {
              for (const subKey in lotData[key]) {
                console.log(`  - ${key}.${subKey}:`, lotData[key][subKey])
              }
            }
          }

          console.error('无法解析停车场详情数据，返回格式:', lotData)
          this.$message.error('停车场数据格式错误')
          this.loading = false
          return
        }

        // 计算空闲车位数和利用率
        if (!this.parkingLot.available_spaces) {
          this.parkingLot.available_spaces = this.parkingLot.total_spaces - this.parkingLot.occupied_spaces
        }

        if (!this.parkingLot.utilization_rate) {
          this.parkingLot.utilization_rate = this.parkingLot.total_spaces > 0
            ? Math.round((this.parkingLot.occupied_spaces / this.parkingLot.total_spaces) * 100)
            : 0
        }

        if (!this.parkingLot.status_text) {
          this.parkingLot.status_text = this.parkingLot.status === 1 ? '正常运营' : '暂停使用'
        }

        // 如果是编辑模式，初始化编辑表单
        if (this.isEditMode) {
          this.initEditForm()
        }

        // 获取车位信息
        this.fetchSpaces()

        // 更新图表
        this.$nextTick(() => {
          this.updateUtilizationChart()
        })
      }).catch(error => {
        console.error('获取停车场详情失败', error)
        this.$message.error('获取停车场详情失败')
        this.loading = false
      })
    },
    fetchSpaces() {
      const lotId = this.$route.params.id

      request({
        url: `/api/parkinglots/${lotId}/spaces`,
        method: 'get',
        params: {
          page: this.spacesQuery.page,
          limit: this.spacesQuery.limit
        }
      }).then(response => {
        console.log('获取车位列表成功:', response)
        console.log('车位列表响应完整结构:', JSON.stringify(response))

        let spacesData = response
        if (response.data) {
          spacesData = response.data
        }

        // 检查数据结构，尝试多种可能的数据格式
        if (spacesData.items) {
          // 标准分页格式
          this.spaces = spacesData.items
          this.totalSpaces = spacesData.pagination?.total || this.spaces.length
          console.log(`成功加载${this.spaces.length}条车位数据(items格式)`)
        } else if (Array.isArray(spacesData)) {
          // 直接是数组
          this.spaces = spacesData
          this.totalSpaces = this.spaces.length
          console.log(`成功加载${this.spaces.length}条车位数据(数组格式)`)
        } else if (spacesData.data && Array.isArray(spacesData.data)) {
          // 嵌套在data字段中的数组
          this.spaces = spacesData.data
          this.totalSpaces = this.spaces.length
          console.log(`成功加载${this.spaces.length}条车位数据(data数组格式)`)
        } else if (spacesData.data && spacesData.data.items) {
          // 双重嵌套
          this.spaces = spacesData.data.items
          this.totalSpaces = spacesData.data.pagination?.total || this.spaces.length
          console.log(`成功加载${this.spaces.length}条车位数据(data.items格式)`)
        } else {
          // 输出所有可能的属性路径
          console.log('尝试查找车位数据:')
          for (const key in spacesData) {
            console.log(`- ${key}:`, spacesData[key])
            if (spacesData[key] && typeof spacesData[key] === 'object') {
              for (const subKey in spacesData[key]) {
                console.log(`  - ${key}.${subKey}:`, spacesData[key][subKey])
              }
            }
          }

          console.error('无法解析车位数据，返回格式:', spacesData)
          this.spaces = []
          this.totalSpaces = 0
        }

        this.loading = false
      }).catch(error => {
        console.error('获取车位列表失败', error)
        this.$message.error('获取车位列表失败')
        this.loading = false
      })
    },
    getSpaceClass(space) {
      const statusClass = {
        0: 'space-free',
        1: 'space-occupied',
        2: 'space-maintenance'
      }

      const typeClass = {
        1: 'space-normal',
        2: 'space-disabled', // 修正：2是残疾人车位
        3: 'space-charging', // 修正：3是充电车位
        4: 'space-vip'
      }

      return `${statusClass[space.status] || ''} ${typeClass[space.type] || ''}`
    },

    getParkingSpaceClasses(space) {
      return {
        'available': space.status === 0,
        'occupied': space.status === 1,
        'maintenance': space.status === 2,
        [`type-${space.type}`]: true
      }
    },

    getUtilizationClass(rate) {
      if (rate >= 90) return 'high'
      if (rate >= 70) return 'medium'
      return 'low'
    },

    getVehicleInfo(space) {
      if (!space.vehicle_info) {
        return space.current_vehicle_id ? `车辆ID: ${space.current_vehicle_id}` : '无车辆信息'
      }
      return space.vehicle_info
    },

    refreshData() {
      this.fetchParkingLot()
      this.fetchSpaces()
    },
    getSpaceStatusType(status) {
      const statusMap = {
        0: 'success',
        1: 'warning',
        2: 'info'
      }
      return statusMap[status] || 'info'
    },
    getSpaceTypeName(type) {
      const typeMap = {
        1: '普通车位',
        2: '残疾人车位',
        3: '充电车位'
      }
      return typeMap[type] || '未知类型'
    },
    getSpaceTypeTag(type) {
      const typeMap = {
        1: '',
        2: 'success',
        3: 'primary'
      }
      return typeMap[type] || 'info'
    },
    getSpaceStatusName(status) {
      const statusMap = {
        0: '空闲',
        1: '已占用',
        2: '维护中'
      }
      return statusMap[status] || '未知状态'
    },
    getSpaceStatusClass(status) {
      const statusMap = {
        0: 'status-available',
        1: 'status-occupied',
        2: 'status-maintenance'
      }
      return statusMap[status] || ''
    },

    handleSpacesFilter() {
      this.spacesQuery.page = 1
      // 本地过滤，不需要重新请求
    },
    handleSizeChange(size) {
      this.spacesQuery.limit = size
      this.fetchSpaces()
    },
    handleEdit() {
      this.$router.push(`/parking/edit/${this.parkingLot.id}`)
    },
    handleEditSpaces() {
      // 打开批量编辑对话框
      this.selectedSpaces = []
      this.changedSpaces = {}
      this.hasChanges = false
      this.batchEditQuery = {
        search: '',
        status: '',
        type: '',
        page: 1,
        limit: 50
      }
      this.batchEditTotalSpaces = this.spaces.length
      console.log('打开批量编辑对话框，车位总数:', this.spaces.length)
      console.log('第一个车位示例:', this.spaces[0])
      this.batchEditDialogVisible = true
    },

    showSpaceDetails(space) {
      // 当点击车位卡片时，打开批量编辑对话框并选中该车位
      if (this.checkPermission(['admin'])) {
        // 如果是管理员，添加到选中列表并打开批量编辑对话框
        if (!this.selectedSpaces.includes(space.id)) {
          this.selectedSpaces.push(space.id)
        }
        this.batchEditDialogVisible = true
      } else {
        // 如果不是管理员，只显示一个提示信息
        this.$message.info(`车位 ${space.space_number} - ${space.type_text} - ${space.status_text}`)
      }
    },

    initCharts() {
      // 初始化车位利用率图表
      this.utilizationChart = echarts.init(this.$refs.utilizationChart)
      this.updateUtilizationChart()
    },
    // 处理车位操作
    handleSpaceAction(command, space) {
      if (!space) return

      // 根据命令执行相应操作
      switch (command) {
        case 'setAvailable':
          this.updateSpaceStatus(space, 0) // 设置为空闲
          break
        case 'setMaintenance':
          this.updateSpaceStatus(space, 2) // 设置为维护中
          break
        default:
          console.warn('未知的车位操作命令:', command)
      }
    },

    // 更新车位状态
    updateSpaceStatus(space, status) {
      if (!this.checkPermission(['admin'])) {
        this.$message.error('没有权限执行此操作')
        return
      }

      const statusText = status === 0 ? '空闲' : status === 1 ? '已占用' : '维护中'
      const loadingInstance = this.$loading({
        lock: true,
        text: `正在将车位 ${space.space_number} 设置为${statusText}...`,
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      updateParkingSpaceStatus(space.id, status)
        .then(response => {
          this.$message.success(`车位 ${space.space_number} 已设置为${statusText}`)

          // 更新本地车位状态
          space.status = status
          space.status_text = statusText
          if (status !== 1) {
            space.current_vehicle_id = null
          }

          // 刷新停车场信息和车位列表
          this.fetchParkingLot()
          this.fetchSpaces()
        })
        .catch(error => {
          console.error('更新车位状态失败', error)
          this.$message.error(
            error.response && error.response.data
              ? error.response.data.message
              : '更新车位状态失败'
          )
        })
        .finally(() => {
          loadingInstance.close()
        })
    },

    updateUtilizationChart() {
      if (!this.utilizationChart) return

      // 计算维护中的车位数
      const maintenanceSpaces = this.spaces.filter(space => space.status === 2).length

      const option = {
        tooltip: {
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['已占用', '空闲', '维护中']
        },
        series: [
          {
            name: '车位状态',
            type: 'pie',
            radius: '55%',
            center: ['50%', '60%'],
            data: [
              { value: this.parkingLot.occupied_spaces, name: '已占用' },
              { value: this.parkingLot.available_spaces, name: '空闲' },
              { value: maintenanceSpaces, name: '维护中' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            itemStyle: {
              color: function(params) {
                const colorList = ['#ff6b6b', '#4ecdc4', '#909399']
                return colorList[params.dataIndex]
              }
            }
          }
        ]
      }

      this.utilizationChart.setOption(option)
    },

    // 初始化编辑表单
    initEditForm() {
      // 复制停车场数据到编辑表单
      this.editForm = {
        name: this.parkingLot.name,
        address: this.parkingLot.address,
        total_spaces: this.parkingLot.total_spaces,
        status: this.parkingLot.status,
        description: this.parkingLot.description || '',
        opening_hours: this.parkingLot.opening_hours || '24小时',
        campus: this.parkingLot.campus || '',
        area: this.parkingLot.area || '',
        manager: this.parkingLot.manager || '',
        contact: this.parkingLot.contact || ''
      }
      console.log('编辑表单已初始化:', this.editForm)
    },

    // 提交编辑表单
    submitEditForm() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          this.loading = true

          // 准备提交的数据
          const data = { ...this.editForm }

          // 发送更新请求
          request({
            url: `/api/parkinglots/${this.parkingLot.id}`,
            method: 'put',
            data: data
          }).then(response => {
            // 处理响应数据
            let responseData = response
            if (response.data) {
              responseData = response.data
            }

            // 更新成功提示
            this.$message({
              message: `停车场"${data.name}"信息更新成功`,
              type: 'success',
              duration: 3000
            })

            // 更新本地数据
            this.parkingLot = {
              ...this.parkingLot,
              ...data
            }

            // 重新计算一些派生属性
            if (!this.parkingLot.available_spaces) {
              this.parkingLot.available_spaces = this.parkingLot.total_spaces - this.parkingLot.occupied_spaces
            }

            if (!this.parkingLot.utilization_rate) {
              this.parkingLot.utilization_rate = this.parkingLot.total_spaces > 0
                ? Math.round((this.parkingLot.occupied_spaces / this.parkingLot.total_spaces) * 100)
                : 0
            }

            // 重定向到详情页
            this.$router.push(`/parking/details/${this.parkingLot.id}`)
          }).catch(error => {
            console.error('更新停车场失败', error)
            // 显示更友好的错误消息
            let errorMsg = '更新停车场失败'
            if (error.response && error.response.data && error.response.data.message) {
              errorMsg += ': ' + error.response.data.message
            } else if (error.message) {
              errorMsg += ': ' + error.message
            }
            this.$message.error(errorMsg)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },

    // 取消编辑
    cancelEdit() {
      this.$router.push(`/parking/details/${this.parkingLot.id}`)
    },

    // 批量编辑相关方法
    handleBatchEditFilter() {
      // 重置页码到第一页
      this.batchEditQuery.page = 1
    },

    // 处理批量编辑分页变化
    handleBatchEditPageChange(page) {
      this.batchEditQuery.page = page
    },

    // 处理批量编辑每页显示数量变化
    handleBatchEditSizeChange(size) {
      this.batchEditQuery.limit = size
      this.batchEditQuery.page = 1
    },

    // 选择相关方法
    isSpaceSelected(spaceId) {
      return this.selectedSpaces.includes(spaceId)
    },

    toggleSpaceSelection(space) {
      const index = this.selectedSpaces.indexOf(space.id)
      if (index === -1) {
        // 添加到选中列表
        this.selectedSpaces.push(space.id)

        // 如果是管理员且批量编辑对话框未打开，则自动打开
        if (this.checkPermission(['admin']) && !this.batchEditDialogVisible) {
          this.batchEditDialogVisible = true
        }
      } else {
        // 从选中列表中移除
        this.selectedSpaces.splice(index, 1)
      }
    },

    selectAll() {
      this.selectedSpaces = this.filteredBatchEditSpaces.map(space => space.id)
    },

    deselectAll() {
      this.selectedSpaces = []
    },

    invertSelection() {
      const allSpaceIds = this.filteredBatchEditSpaces.map(space => space.id)
      this.selectedSpaces = allSpaceIds.filter(id => !this.selectedSpaces.includes(id))
    },

    // 批量更新车位类型
    batchUpdateType(type) {
      if (this.selectedSpaces.length === 0) {
        this.$message.warning('请先选择要修改的车位')
        return
      }

      const typeNames = {
        1: '普通车位',
        2: '残疾人车位',
        3: '充电车位'
      }

      this.$confirm(`确定将选中的 ${this.selectedSpaces.length} 个车位类型修改为${typeNames[type]}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.batchEditLoading = true

        // 更新本地数据
        this.spaces.forEach(space => {
          if (this.selectedSpaces.includes(space.id)) {
            space.type = type
          }
        })

        // 记录更改，但不立即提交到服务器
        this.selectedSpaces.forEach(spaceId => {
          if (!this.changedSpaces[spaceId]) {
            this.changedSpaces[spaceId] = {}
          }
          this.changedSpaces[spaceId].type = type
        })

        this.hasChanges = true
        this.batchEditLoading = false
        this.$message.success(`已将 ${this.selectedSpaces.length} 个车位类型修改为${typeNames[type]}，点击"保存更改"按钮提交到服务器`)
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 批量更新车位状态
    batchUpdateStatus(status) {
      if (this.selectedSpaces.length === 0) {
        this.$message.warning('请先选择要修改的车位')
        return
      }

      // 检查是否有已占用的车位
      if (this.hasOccupiedSpaces) {
        this.$message.error('选中的车位中包含已占用的车位，无法修改状态')
        return
      }

      const statusNames = {
        0: '空闲',
        2: '维护中'
      }

      this.$confirm(`确定将选中的 ${this.selectedSpaces.length} 个车位状态修改为${statusNames[status]}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.batchEditLoading = true

        // 更新本地数据
        this.spaces.forEach(space => {
          if (this.selectedSpaces.includes(space.id) && space.status !== 1) {
            space.status = status
          }
        })

        // 记录更改，但不立即提交到服务器
        this.selectedSpaces.forEach(spaceId => {
          const space = this.spaces.find(s => s.id === spaceId)
          if (space && space.status !== 1) {
            if (!this.changedSpaces[spaceId]) {
              this.changedSpaces[spaceId] = {}
            }
            this.changedSpaces[spaceId].status = status
          }
        })

        this.hasChanges = true
        this.batchEditLoading = false
        this.$message.success(`已将 ${this.selectedSpaces.length} 个车位状态修改为${statusNames[status]}，点击"保存更改"按钮提交到服务器`)
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 保存所有更改
    saveChanges() {
      if (!this.hasChanges) {
        this.$message.info('没有需要保存的更改')
        return
      }

      this.$confirm('确定要保存所有更改吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.saveLoading = true

        // 将更改分组为类型更改和状态更改
        const typeChanges = {}
        const statusChanges = {}

        Object.entries(this.changedSpaces).forEach(([spaceId, changes]) => {
          if ('type' in changes) {
            const type = changes.type
            if (!typeChanges[type]) {
              typeChanges[type] = []
            }
            typeChanges[type].push(parseInt(spaceId))
          }

          if ('status' in changes) {
            const status = changes.status
            if (!statusChanges[status]) {
              statusChanges[status] = []
            }
            statusChanges[status].push(parseInt(spaceId))
          }
        })

        // 创建批量更新请求
        const promises = []

        // 批量更新类型
        Object.entries(typeChanges).forEach(([type, spaceIds]) => {
          if (spaceIds.length > 0) {
            promises.push(
              request({
                url: '/api/spaces/batch-update-type',
                method: 'post',
                data: {
                  space_ids: spaceIds,
                  type: parseInt(type)
                }
              }).catch(error => {
                console.error(`批量更新车位类型失败 (类型=${type})`, error)
                throw error
              })
            )
          }
        })

        // 批量更新状态
        Object.entries(statusChanges).forEach(([status, spaceIds]) => {
          if (spaceIds.length > 0) {
            promises.push(
              request({
                url: '/api/spaces/batch-update',
                method: 'post',
                data: {
                  space_ids: spaceIds,
                  status: parseInt(status)
                }
              }).catch(error => {
                console.error(`批量更新车位状态失败 (状态=${status})`, error)
                throw error
              })
            )
          }
        })

        // 执行所有请求
        Promise.all(promises)
          .then(() => {
            this.$message.success('所有更改已成功保存')
            this.changedSpaces = {}
            this.hasChanges = false
            this.batchEditDialogVisible = false
            this.refreshData() // 刷新数据
          })
          .catch(error => {
            this.$message.error('保存更改失败: ' + (error.message || '未知错误'))
          })
          .finally(() => {
            this.saveLoading = false
          })
      }).catch(() => {
        // 用户取消操作
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

// 顶部标题和操作区
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;

  .dashboard-title {
    .subtitle {
      margin: 5px 0 0 0;
      font-size: 14px;
      color: #909399;
    }
  }

  .dashboard-actions {
    display: flex;
    gap: 10px;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;

    .dashboard-title {
      margin-bottom: 15px;
    }

    .dashboard-actions {
      width: 100%;
      justify-content: flex-start;
    }
  }
}

// 主要内容区域
.main-content {
  margin-bottom: 20px;
}

// 停车场信息卡片
.info-section {
  margin-bottom: 20px;

  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      background-color: #f5f7fa;
    }

    .info-icon {
      font-size: 18px;
      color: #409EFF;
      margin-right: 10px;
      margin-top: 2px;
      background-color: rgba(64, 158, 255, 0.1);
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .info-content {
      flex: 1;

      .info-label {
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
      }

      .info-value {
        font-size: 14px;
        color: #303133;
        word-break: break-word;
        font-weight: 500;
      }
    }
  }
}

// 统计部分
.stats-section {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 15px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #409EFF, #67C23A);
  }

  .stats-header {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background-color: #409EFF;
      margin-right: 8px;
      border-radius: 2px;
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;

    .stats-item {
      text-align: center;
      padding: 15px 10px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background-color: #e0e0e0;
      }

      &:nth-child(1)::after {
        background-color: #409EFF;
      }

      &:nth-child(2)::after {
        background-color: #E6A23C;
      }

      &:nth-child(3)::after {
        background-color: #67C23A;
      }

      &:nth-child(4)::after {
        background-color: #F56C6C;
      }

      .stats-value {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;

        &.low {
          color: #67c23a;
        }

        &.medium {
          color: #e6a23c;
        }

        &.high {
          color: #f56c6c;
        }
      }

      .stats-label {
        font-size: 13px;
        color: #909399;
      }
    }
  }
}

// 图表部分
.chart-section {
  margin-top: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 15px;

  .chart-header {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background-color: #409EFF;
      margin-right: 8px;
      border-radius: 2px;
    }
  }

  .chart {
    height: 250px;
  }
}

// 车位管理部分
.spaces-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;

    .spaces-filter, .spaces-actions {
      margin-bottom: 10px;
    }
  }
}

.spaces-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  flex: 1;
}

.spaces-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
}

// 网格视图
.spaces-container {
  min-height: 300px;
  background-color: #f9fafc;
  border-radius: 8px;
  padding: 15px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><rect x="0" y="0" width="100" height="100" fill="none" stroke="%23e0e0e0" stroke-width="0.5" stroke-dasharray="5,5" /></svg>');
    opacity: 0.3;
    pointer-events: none;
    border-radius: 8px;
  }
}

.spaces-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.space-item {
  width: 120px;
  height: 120px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 8px;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }



  .space-number {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 5px;
  }

  .space-type, .space-status {
    font-size: 12px;
    color: #606266;
  }

  .space-type {
    margin-bottom: 3px;
  }

  .space-status {
    font-weight: 500;
    margin-bottom: 5px;
  }

  .vehicle-info {
    font-size: 11px;
    background-color: rgba(230, 162, 60, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    text-align: center;
    width: 100%;
    margin-top: 5px;
    word-break: break-all;
    max-height: 40px;
    overflow: hidden;
    color: #e6a23c;
    font-weight: 600;
    border: 1px dashed rgba(230, 162, 60, 0.3);
    position: relative;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(230, 162, 60, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

.space-free {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #67c23a;
  }

  .space-status {
    color: #67c23a;
    font-weight: 600;
  }
}

.space-occupied {
  background-color: #fdf6ec;
  border-color: #faecd8;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #e6a23c;
  }

  .space-status {
    color: #e6a23c;
    font-weight: 600;
  }
}

.space-maintenance {
  background-color: #f4f4f5;
  border-color: #e9e9eb;
  position: relative;
  overflow: hidden;
  background-image: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0, 0, 0, 0.03) 10px, rgba(0, 0, 0, 0.03) 20px);

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #909399;
  }

  &::before {
    content: '维护中';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-30deg);
    font-size: 18px;
    font-weight: bold;
    color: rgba(144, 147, 153, 0.3);
    white-space: nowrap;
    z-index: 0;
  }

  .space-status {
    color: #909399;
    font-weight: 600;
  }
}

.space-normal {
  // 默认样式
}

.space-charging::before {
  content: "⚡"; // 充电图标
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 14px;
  color: #409EFF;
  background-color: rgba(255, 255, 255, 0.8);
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.space-disabled::before {
  content: "♿"; // 残疾人图标
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 14px;
  color: #0066CC; // 使用更深的蓝色
  background-color: rgba(255, 255, 255, 0.8);
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 102, 204, 0.2);
}

.space-vip::before {
  content: "VIP";
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 10px;
  color: #ff9900;
  font-weight: bold;
  background-color: rgba(255, 255, 255, 0.8);
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(255, 153, 0, 0.2);
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

// 停车场布局视图
.parking-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f9fafc;
  border-radius: 12px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><rect x="0" y="0" width="100" height="100" fill="none" stroke="%23e0e0e0" stroke-width="0.5" stroke-dasharray="5,5" /></svg>');
    opacity: 0.3;
    pointer-events: none;
    border-radius: 12px;
  }

  .parking-space {
    position: relative;
    width: 110px;
    height: 160px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 15px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    z-index: 1;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255,255,255,0.5) 0%, rgba(255,255,255,0) 100%);
      z-index: -1;
    }

    &:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .space-number {
      font-weight: 600;
      font-size: 16px;
      text-align: center;
    }

    .vehicle-info {
      background-color: rgba(0, 0, 0, 0.1);
      padding: 6px;
      border-radius: 4px;
      font-size: 12px;
      text-align: center;
      word-break: break-all;
    }

    .maintenance-info {
      background-color: rgba(144, 147, 153, 0.2);
      padding: 6px;
      border-radius: 4px;
      font-size: 12px;
      text-align: center;
      font-weight: bold;
    }

    .admin-actions {
      position: absolute;
      top: 5px;
      right: 5px;
      z-index: 5;
      opacity: 0;
      transition: opacity 0.2s ease;

      .el-dropdown-link {
        cursor: pointer;
        color: #606266;
        font-size: 16px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          color: #409EFF;
          background: white;
        }
      }
    }

    &:hover .admin-actions {
      opacity: 1;
    }

    // 状态样式
    &.available {
      background-color: #f0f9eb;
      border: 2px solid #67C23A;
      color: #333;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #67C23A, #95d475);
      }
    }

    &.occupied {
      background-color: #fdf6ec;
      border: 2px solid #E6A23C;
      color: #333;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #E6A23C, #f3d19e);
      }
    }

    &.maintenance {
      background-color: #f4f4f5;
      border: 2px solid #909399;
      color: #333;
      background-image: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0, 0, 0, 0.03) 10px, rgba(0, 0, 0, 0.03) 20px);

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #909399, #c8c9cc);
      }

      &::before {
        content: '维护中';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-30deg);
        font-size: 20px;
        font-weight: bold;
        color: rgba(144, 147, 153, 0.4);
        white-space: nowrap;
        z-index: 0;
        text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
      }
    }
  }
}

// 对话框样式
::v-deep .space-dialog {
  .el-dialog__header {
    padding: 16px 20px;
    background: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;

    .el-dialog__title {
      font-weight: 600;
      font-size: 18px;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 24px;
  }
}

.space-details {
  // 车位状态卡片
  .space-status-card {
    display: flex;
    align-items: center;
    padding: 16px;
    border-radius: 10px;
    margin-bottom: 20px;
    background-color: #ffffff;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);

    &.status-available {
      border-left: 4px solid #67c23a;

      .status-icon i {
        color: #67c23a;
      }

      .status-title {
        color: #67c23a;
      }
    }

    &.status-occupied {
      border-left: 4px solid #e6a23c;

      .status-icon i {
        color: #e6a23c;
      }

      .status-title {
        color: #e6a23c;
      }
    }

    &.status-maintenance {
      border-left: 4px solid #909399;

      .status-icon i {
        color: #909399;
      }

      .status-title {
        color: #909399;
      }
    }

    .status-icon {
      font-size: 32px;
      margin-right: 16px;

      i {
        font-size: 32px;
      }
    }

    .status-info {
      flex: 1;

      .status-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 6px;
      }

      .status-desc {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  // 信息卡片
  .info-card {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 16px;
    margin-bottom: 20px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      display: flex;
      align-items: center;

      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }

    .info-content {
      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 12px;
        border-bottom: 1px dashed #ebeef5;

        &:last-child {
          margin-bottom: 0;
          padding-bottom: 0;
          border-bottom: none;
        }

        .info-label {
          color: #909399;
          font-size: 14px;
        }

        .info-value {
          color: #303133;
          font-size: 14px;
          font-weight: 500;

          &.highlight {
            color: #409EFF;
            font-weight: 600;
          }

          &.vehicle-info-dialog {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 5px;

            .license-plate-tag {
              font-weight: bold;
              font-size: 14px;
              padding: 4px 8px;
              border-radius: 4px;
            }

            .vehicle-details {
              font-size: 12px;
              color: #606266;
            }
          }
        }
      }
    }
  }

  // 操作按钮
  .space-actions {
    display: flex;
    flex-direction: column;

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 15px;
      gap: 10px;
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .stats-section .stats-grid {
    grid-template-columns: 1fr;
  }

  .spaces-filter {
    flex-direction: column;
    align-items: stretch;

    .el-input, .el-select {
      width: 100% !important;
      margin-right: 0 !important;
    }
  }

  .parking-layout .parking-space {
    width: 90px;
    height: 130px;
    padding: 8px;
  }
}

// 编辑表单样式
.edit-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .el-tabs__header {
    margin-bottom: 20px;
  }

  .el-form-item {
    margin-bottom: 22px;
  }

  .el-input-number {
    width: 180px;
  }
}

/* 批量编辑对话框样式 */
.batch-edit-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }
}

.batch-edit-container {
  display: flex;
  gap: 20px;
  min-height: 500px;

  .batch-actions-panel {
    width: 250px;
    flex-shrink: 0;
    background-color: #f5f7fa;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    .selected-count-info {
      margin-bottom: 20px;
      font-size: 15px;
      text-align: center;
      padding: 10px;
      background-color: rgba($primaryColor, 0.05);
      border-radius: 6px;
      border-left: 3px solid $primaryColor;

      .selected-count {
        font-weight: bold;
        color: $primaryColor;
        font-size: 16px;
      }
    }

    .action-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin-bottom: 20px;

      .el-button {
        width: 90%;
        text-align: center;
        justify-content: center;
        margin: 0 auto;
        padding: 8px 12px;
        font-size: 13px;
      }
    }

    .el-divider {
      margin: 20px 0;

      .el-divider__text {
        font-size: 15px;
        font-weight: 600;
        color: $primaryTextColor;
        background-color: #f5f7fa;
      }
    }

    .save-actions {
      margin-top: 25px;
      text-align: center;

      .el-button {
        width: 90%;
        padding: 10px 15px;
        font-size: 14px;
      }
    }
  }

  .spaces-grid-panel {
    flex: 1;

    .filter-actions {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
      flex-wrap: wrap;
    }

    .parking-spaces-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
      min-height: 400px; /* 确保网格有最小高度，避免分页时高度跳动 */

      .empty-spaces-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 300px;
        background-color: #f9f9f9;
        border-radius: 8px;
        color: #909399;

        i {
          font-size: 48px;
          margin-bottom: 15px;
          color: #dcdfe6;
        }

        p {
          font-size: 16px;
          margin: 0;
        }
      }

      .parking-space-item {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 12px;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        &.selected {
          border: 2px solid $primaryColor;
          background-color: rgba($primaryColor, 0.05);
        }

        &.status-available {
          background-color: rgba(103, 194, 58, 0.1);
        }

        &.status-occupied {
          background-color: rgba(230, 162, 60, 0.1);
        }

        &.status-maintenance {
          background-color: rgba(144, 147, 153, 0.1);
        }

        .space-number {
          font-weight: bold;
          font-size: 16px;
          margin-bottom: 8px;
        }

        .space-type, .space-status {
          margin-bottom: 5px;
        }

        .space-vehicle {
          margin-top: 8px;
          font-size: 12px;
          color: #e6a23c;
        }
      }
    }

    .batch-edit-pagination {
      margin-top: 20px;
      text-align: center;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 8px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    }
  }
}
</style>
