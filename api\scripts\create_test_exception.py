#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建一条使用旧类型名称的充电异常记录，用于测试数据迁移脚本
"""

import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入应用和数据库模型
from app import create_app, db
from app.charging.models import ChargingException, ChargingRecord
from datetime import datetime

# 创建应用实例
app = create_app()

def create_test_exception():
    """创建一条使用旧类型名称的充电异常记录"""
    with app.app_context():
        try:
            # 查找一条充电记录
            charging_record = ChargingRecord.query.first()
            
            if not charging_record:
                print("未找到充电记录，无法创建测试异常")
                return False
            
            # 创建使用旧类型名称的充电异常记录
            exception = ChargingException(
                charging_record_id=charging_record.id,
                exception_type='charging_gun',  # 旧类型名称
                description='测试异常记录，使用旧类型名称'
            )
            
            # 保存到数据库
            db.session.add(exception)
            db.session.commit()
            
            print(f"成功创建测试异常记录，ID: {exception.id}, 类型: {exception.exception_type}")
            return True
        
        except Exception as e:
            db.session.rollback()
            print(f"创建测试异常记录失败: {str(e)}")
            return False

if __name__ == '__main__':
    print("开始创建测试异常记录")
    
    success = create_test_exception()
    
    if success:
        print("测试异常记录创建成功")
    else:
        print("测试异常记录创建失败")
        sys.exit(1)
    
    sys.exit(0)
