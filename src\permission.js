import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login', '/403'] // 白名单路由 - 不需要登录也能访问

// 需要管理员权限的页面路径
const adminRoutes = [
  '/bikes',
  '/parking/index',
  '/parking/records',
  '/parking/stats',
  '/charging-admin/spaces',
  '/charging-admin/records',
  '/charging-admin/dashboard',
  '/charging-admin/exceptions',
  '/violation/admin/management'
]

// 需要保安权限的页面路径
const securityRoutes = [
  '/violation/security/create',
  '/charging-admin/faults',
  '/violation/admin/dashboard',  // 允许保安访问违规统计页面
  '/charging-admin/dashboard',   // 允许保安访问充电管理中心仪表盘页面
  '/charging-admin/records',     // 允许保安访问充电记录管理页面
  '/parking/stats'               // 允许保安访问停车趋势分析页面
]

// 检查用户是否有权限访问指定路径
function hasPermission(roles, path) {
  console.log('权限检查 - 角色:', roles, '路径:', path)

  // 管理员可以访问所有页面
  if (roles.includes('admin')) {
    console.log('用户是管理员，允许访问所有页面')
    return true
  }

  // 保安可以访问保安权限的页面
  if (roles.includes('security') && securityRoutes.includes(path)) {
    console.log('用户是保安，允许访问保安权限页面')
    return true
  }

  // 检查是否是管理员专属页面
  if (adminRoutes.includes(path)) {
    console.log('该页面需要管理员权限，用户没有权限')
    return false
  }

  // 检查是否是保安专属页面
  if (securityRoutes.includes(path) && !roles.includes('security')) {
    console.log('该页面需要保安权限，用户没有权限')
    return false
  }

  // 其他页面都可以访问
  console.log('该页面不需要特殊权限，允许访问')
  return true
}

router.beforeEach(async(to, from, next) => {
  // 开始进度条
  NProgress.start()

  // 设置页面标题
  document.title = getPageTitle(to.meta.title)

  // 获取用户令牌，判断用户是否已登录
  const hasToken = getToken()

  // 增加调试日志
  console.log('路由权限检查 - 目标路径:', to.path)
  console.log('路由权限检查 - 令牌状态:', hasToken ? '有效' : '无效')

  if (hasToken) {
    if (to.path === '/login') {
      // 如果用户已登录且尝试访问登录页，重定向到首页
      console.log('已登录用户访问登录页，重定向到首页')
      next({ path: '/' })
      NProgress.done() // 停止进度条
    } else {
      // 尝试从localStorage获取用户信息
      const savedUserId = localStorage.getItem('userId')
      const savedUserName = localStorage.getItem('userName')
      const hasGetUserInfo = (store.getters.name || savedUserName) && (store.getters.userId || savedUserId)

      console.log('权限检查 - store中的用户名:', store.getters.name)
      console.log('权限检查 - localStorage中的用户名:', savedUserName)
      console.log('权限检查 - store中的用户ID:', store.getters.userId)
      console.log('权限检查 - localStorage中的用户ID:', savedUserId)

      if (hasGetUserInfo) {
        // 如果有保存的用户ID但store中没有，恢复它
        if (savedUserId && !store.getters.userId) {
          store.commit('user/SET_USERID', parseInt(savedUserId))
        }

        // 如果有保存的用户名但store中没有，恢复它
        if (savedUserName && !store.getters.name) {
          console.log('从localStorage恢复用户名到store:', savedUserName)
          store.commit('user/SET_NAME', savedUserName)
        }

        // 检查用户是否有权限访问该页面
        const roles = store.getters.roles || []
        const role = store.getters.role || ''

        // 确保userRoles是数组，并且至少包含一个角色
        let userRoles = []
        if (Array.isArray(roles) && roles.length > 0) {
          userRoles = roles
        } else if (role) {
          userRoles = [role]
        } else {
          userRoles = ['user'] // 默认为普通用户
        }

        console.log('检查用户权限 - 用户角色:', userRoles, '目标路径:', to.path)

        // 检查是否是需要权限的页面
        const needsPermission = adminRoutes.includes(to.path) || securityRoutes.includes(to.path)

        if (!needsPermission || hasPermission(userRoles, to.path)) {
          console.log('用户有权限访问该页面')
          next()
        } else {
          console.log('用户没有权限访问该页面，重定向到403页面')
          Message.error('您没有权限访问该页面')
          next('/403')
          NProgress.done()
        }
      } else {
        try {
          await store.dispatch('user/getInfo')
          next()
        } catch (error) {
          await store.dispatch('user/resetToken')
          Message.error(error || 'Has Error')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    /* 没有令牌 - 未登录状态 */
    console.log('未登录状态')

    if (whiteList.indexOf(to.path) !== -1) {
      // 如果要访问的页面在白名单中，直接访问
      console.log('访问白名单页面，允许访问')
      next()
    } else {
      // 需要登录的页面，重定向到登录页面，并记住原始访问路由
      console.log('访问需要登录的页面，重定向到登录页')
      Message.warning('请先登录')
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // 结束进度条
  NProgress.done()
})
