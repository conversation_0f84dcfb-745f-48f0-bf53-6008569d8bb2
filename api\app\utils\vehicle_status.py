"""
车辆状态检查工具

提供统一的车辆状态检查功能，用于检查车辆是否被禁用、获取禁用原因等。
使用统一的状态定义，确保前后端一致性。
"""

from app.bikes.models import Bikes
from app.utils.constants import VehicleStatus
from flask import current_app
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

def is_vehicle_disabled(bike_id):
    """
    检查车辆是否被禁用

    Args:
        bike_id: 车辆ID

    Returns:
        tuple: (是否禁用, 禁用原因, 禁用记录)
    """
    try:
        # 查找车辆
        bike = Bikes.query.get(bike_id)
        if not bike:
            logger.warning(f"未找到车辆 ID: {bike_id}")
            return False, "车辆不存在", None

        # 检查车辆状态
        if bike.status != VehicleStatus.DISABLED:
            return False, None, None

        # 导入车辆禁用记录模型
        from app.violations.models import VehicleDisableRecord

        # 查找有效的禁用记录
        disable_record = VehicleDisableRecord.query.filter_by(
            bike_id=bike_id,
            is_active=True
        ).first()

        if not disable_record:
            # 状态为禁用但没有禁用记录，可能是数据不一致
            logger.warning(f"车辆 {bike.b_num} (ID: {bike.b_id}) 状态为禁用，但没有有效的禁用记录")
            return True, "车辆已禁用，但无详细原因", None

        # 导入违规记录模型
        from app.violations.models import ViolationRecord

        # 获取关联的违规记录
        violation = ViolationRecord.query.get(disable_record.violation_id)
        if violation:
            reason = f"违规原因: {violation.violation_type}, 地点: {violation.location}, 时间: {violation.violation_time}"
            return True, reason, disable_record
        else:
            return True, "车辆已禁用，但无法获取详细原因", disable_record

    except Exception as e:
        logger.error(f"检查车辆禁用状态失败: {str(e)}")
        return False, f"检查禁用状态出错: {str(e)}", None

def get_vehicle_status_info(bike_id):
    """
    获取车辆状态详细信息

    Args:
        bike_id: 车辆ID

    Returns:
        dict: 车辆状态信息
    """
    try:
        # 查找车辆
        bike = Bikes.query.get(bike_id)
        if not bike:
            return {
                'exists': False,
                'message': '车辆不存在'
            }

        # 基本信息
        result = {
            'exists': True,
            'bike_id': bike.b_id,
            'bike_number': bike.b_num,
            'status': bike.status,
            'frontend_status': VehicleStatus.convert_to_frontend(bike.status),
            'status_text': VehicleStatus.get_text(bike.status),
            'is_available': bike.status == VehicleStatus.AVAILABLE,
            'is_disabled': bike.status == VehicleStatus.DISABLED,
            'disable_info': None
        }

        # 如果车辆被禁用，获取禁用信息
        if bike.status == VehicleStatus.DISABLED:
            # 导入车辆禁用记录模型
            from app.violations.models import VehicleDisableRecord

            # 查找有效的禁用记录
            disable_record = VehicleDisableRecord.query.filter_by(
                bike_id=bike_id,
                is_active=True
            ).first()

            if disable_record:
                # 导入违规记录模型
                from app.violations.models import ViolationRecord

                # 获取关联的违规记录
                violation = ViolationRecord.query.get(disable_record.violation_id)

                disable_info = {
                    'disable_id': disable_record.id,
                    'disable_time': disable_record.disable_start_time,
                    'violation_id': disable_record.violation_id,
                    'violation_info': None
                }

                if violation:
                    disable_info['violation_info'] = {
                        'id': violation.id,
                        'type': violation.violation_type,
                        'location': violation.location,
                        'time': violation.violation_time,
                        'status': violation.status,
                        'status_text': violation.get_status_text() if hasattr(violation, 'get_status_text') else None,
                        'has_appeal': violation.status == 2  # 2表示申诉中
                    }

                result['disable_info'] = disable_info

        return result

    except Exception as e:
        logger.error(f"获取车辆状态信息失败: {str(e)}")
        return {
            'exists': True if bike else False,
            'error': str(e),
            'message': '获取车辆状态信息失败'
        }

def check_vehicle_for_operation(bike_id, operation_type="parking"):
    """
    检查车辆是否可以进行特定操作（停车、充电等）

    Args:
        bike_id: 车辆ID
        operation_type: 操作类型，可选值: "parking", "charging"

    Returns:
        tuple: (是否可用, 原因)
    """
    try:
        # 检查车辆是否被禁用
        is_disabled, reason, _ = is_vehicle_disabled(bike_id)
        if is_disabled:
            return False, f"车辆已被禁用，无法{operation_type_text(operation_type)}。{reason}"

        # 查找车辆
        bike = Bikes.query.get(bike_id)
        if not bike:
            return False, f"车辆不存在，无法{operation_type_text(operation_type)}"

        # 检查车辆状态
        if bike.status != VehicleStatus.AVAILABLE:
            status_text = VehicleStatus.get_text(bike.status)
            return False, f"车辆状态为 {status_text}，无法{operation_type_text(operation_type)}"

        return True, None

    except Exception as e:
        logger.error(f"检查车辆操作权限失败: {str(e)}")
        return False, f"检查车辆状态出错: {str(e)}"

def operation_type_text(operation_type):
    """获取操作类型的中文描述"""
    type_map = {
        "parking": "停车",
        "charging": "充电",
        "general": "使用"
    }
    return type_map.get(operation_type, "使用")
