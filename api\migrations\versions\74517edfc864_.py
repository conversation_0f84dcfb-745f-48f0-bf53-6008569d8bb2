"""empty message

Revision ID: 74517edfc864
Revises: 069352401a26
Create Date: 2025-04-19 11:58:09.943758

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '74517edfc864'
down_revision = '069352401a26'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bikes', schema=None) as batch_op:
        batch_op.alter_column('b_id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.drop_index('idx_bikes_status')
        batch_op.drop_index('idx_bikes_type')
        batch_op.create_unique_constraint(None, ['b_num'])

    with op.batch_alter_table('parking_lots', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.drop_index('idx_parking_lots_status')
        batch_op.create_unique_constraint(None, ['name'])

    with op.batch_alter_table('parking_records', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.drop_index('idx_parking_records_entry_time')
        batch_op.drop_index('idx_parking_records_exit_time')
        batch_op.drop_index('idx_parking_records_parking_lot_id')
        batch_op.drop_index('idx_parking_records_parking_space_id')
        batch_op.drop_index('idx_parking_records_status')
        batch_op.drop_index('idx_parking_records_user_id')
        batch_op.drop_index('idx_parking_records_user_status')
        batch_op.drop_index('idx_parking_records_vehicle_id')
        batch_op.drop_index('idx_parking_records_vehicle_status')

    with op.batch_alter_table('parking_spaces', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.drop_index('idx_parking_spaces_lot_status')
        batch_op.drop_index('idx_parking_spaces_status')
        batch_op.drop_index('idx_parking_spaces_type')

    with op.batch_alter_table('players', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.create_unique_constraint(None, ['username'])

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.alter_column('u_id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.create_unique_constraint(None, ['u_name'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')
        batch_op.alter_column('u_id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('players', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('parking_spaces', schema=None) as batch_op:
        batch_op.create_index('idx_parking_spaces_type', ['type'], unique=False)
        batch_op.create_index('idx_parking_spaces_status', ['status'], unique=False)
        batch_op.create_index('idx_parking_spaces_lot_status', ['parking_lot_id', 'status'], unique=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('parking_records', schema=None) as batch_op:
        batch_op.create_index('idx_parking_records_vehicle_status', ['vehicle_id', 'status'], unique=False)
        batch_op.create_index('idx_parking_records_vehicle_id', ['vehicle_id'], unique=False)
        batch_op.create_index('idx_parking_records_user_status', ['user_id', 'status'], unique=False)
        batch_op.create_index('idx_parking_records_user_id', ['user_id'], unique=False)
        batch_op.create_index('idx_parking_records_status', ['status'], unique=False)
        batch_op.create_index('idx_parking_records_parking_space_id', ['parking_space_id'], unique=False)
        batch_op.create_index('idx_parking_records_parking_lot_id', ['parking_lot_id'], unique=False)
        batch_op.create_index('idx_parking_records_exit_time', ['exit_time'], unique=False)
        batch_op.create_index('idx_parking_records_entry_time', ['entry_time'], unique=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('parking_lots', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')
        batch_op.create_index('idx_parking_lots_status', ['status'], unique=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('bikes', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')
        batch_op.create_index('idx_bikes_type', ['b_type'], unique=False)
        batch_op.create_index('idx_bikes_status', ['status'], unique=False)
        batch_op.alter_column('b_id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    # ### end Alembic commands ###
