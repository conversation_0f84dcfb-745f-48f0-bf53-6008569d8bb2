"""
更新停车位类型的迁移脚本
将车位类型调整为：1普通车位，2残疾人车位，3充电车位
"""
import os
import sys
import sqlite3
import logging

# 添加父目录到系统路径
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, parent_dir)

# 导入应用和模型
from app import create_app, db
from app.parkinglots.models import ParkingSpace

# 创建应用实例
app = create_app()

def upgrade():
    """升级数据库，更新车位类型"""
    with app.app_context():
        print("开始更新车位类型...")
        
        try:
            # 获取所有车位
            spaces = ParkingSpace.query.all()
            print(f"找到 {len(spaces)} 个车位")
            
            # 更新车位类型
            updated_count = 0
            for space in spaces:
                # 原类型：1普通车位，2充电车位，3残疾人专用车位，4大型车位
                # 新类型：1普通车位，2残疾人车位，3充电车位
                old_type = space.type
                
                if old_type == 2:  # 原充电车位
                    space.type = 3  # 新充电车位
                    updated_count += 1
                elif old_type == 3:  # 原残疾人专用车位
                    space.type = 2  # 新残疾人车位
                    updated_count += 1
                elif old_type == 4:  # 原大型车位
                    space.type = 1  # 转为普通车位
                    space.remarks = "原大型车位" + (f", {space.remarks}" if space.remarks else "")
                    updated_count += 1
            
            # 提交更改
            db.session.commit()
            print(f"成功更新 {updated_count} 个车位类型")
            
        except Exception as e:
            db.session.rollback()
            print(f"更新车位类型失败: {str(e)}")
            raise
        
        print("车位类型更新完成")

def downgrade():
    """降级数据库，恢复原车位类型"""
    with app.app_context():
        print("开始恢复车位类型...")
        
        try:
            # 获取所有车位
            spaces = ParkingSpace.query.all()
            print(f"找到 {len(spaces)} 个车位")
            
            # 恢复车位类型
            updated_count = 0
            for space in spaces:
                # 新类型：1普通车位，2残疾人车位，3充电车位
                # 原类型：1普通车位，2充电车位，3残疾人专用车位，4大型车位
                new_type = space.type
                
                if new_type == 3:  # 新充电车位
                    space.type = 2  # 原充电车位
                    updated_count += 1
                elif new_type == 2:  # 新残疾人车位
                    space.type = 3  # 原残疾人专用车位
                    updated_count += 1
                
                # 注意：无法准确恢复原大型车位，因为已经转为普通车位
            
            # 提交更改
            db.session.commit()
            print(f"成功恢复 {updated_count} 个车位类型")
            
        except Exception as e:
            db.session.rollback()
            print(f"恢复车位类型失败: {str(e)}")
            raise
        
        print("车位类型恢复完成")

if __name__ == '__main__':
    # 根据命令行参数执行升级或降级
    if len(sys.argv) > 1 and sys.argv[1] == 'downgrade':
        downgrade()
    else:
        upgrade()
