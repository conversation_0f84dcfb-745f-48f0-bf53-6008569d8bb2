# api/app/utils/auth.py

from functools import wraps
from flask import g
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity, get_jwt
from app.users.models import Users
from app.utils.response import api_response
from app.utils.password_utils import verify_password, hash_password, get_salt_if_none

def is_admin():
    """
    检查当前用户是否为管理员

    Returns:
        bool: 如果当前用户是管理员则返回True，否则返回False
    """
    if hasattr(g, 'user_role'):
        return g.user_role == 'admin'
    elif hasattr(g, 'user') and hasattr(g.user, 'role'):
        return g.user.role == 'admin'
    return False

def jwt_required(fresh=False, refresh=False, locations=None):
    """
    JWT认证装饰器 - 使用Flask-JWT-Extended进行正确的JWT验证

    Args:
        fresh (bool): 是否要求fresh token
        refresh (bool): 是否使用refresh token
        locations (list): token位置列表

    Returns:
        function: 装饰器函数
    """
    def decorator(fn):
        @wraps(fn)
        def wrapper(*args, **kwargs):
            try:
                # 使用Flask-JWT-Extended进行JWT验证
                verify_jwt_in_request(fresh=fresh, refresh=refresh, locations=locations)

                # 获取用户ID
                user_id = get_jwt_identity()

                # 获取JWT声明
                jwt_claims = get_jwt()

                # 获取用户角色
                user_role = jwt_claims.get('role', 'user')

                # 查找用户
                from flask import current_app
                user = Users.query.get(user_id)

                if not user:
                    if current_app:
                        current_app.logger.warning(f"JWT验证成功但找不到用户ID={user_id}")
                    return api_response(message="用户不存在", status="error", code=401)

                # 将用户对象和角色存储在g对象中，以便在路由函数中使用
                g.user = user
                g.user_role = user_role

                return fn(*args, **kwargs)
            except Exception as e:
                from flask import current_app
                if current_app:
                    current_app.logger.error(f"JWT验证失败: {str(e)}")
                return api_response(message="认证失败，请重新登录", status="error", code=401)
        return wrapper
    return decorator

def admin_required(fn=None):
    """
    管理员权限检查装饰器

    使用方法:
    @admin_required
    def admin_only_route():
        ...
    """
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            # 先进行JWT验证
            verify_jwt_in_request()

            # 获取用户ID和角色
            user_id = get_jwt_identity()
            jwt_claims = get_jwt()
            user_role = jwt_claims.get('role', 'user')

            # 查找用户
            user = Users.query.get(user_id)

            # 检查用户是否存在
            if not user:
                return api_response(message="用户不存在", status="error", code=401)

            # 将用户对象和角色存储在g对象中
            g.user = user
            g.user_role = user_role

            # 检查是否为管理员
            if g.user_role != 'admin':
                return api_response(message="需要管理员权限", status="error", code=403)

            return f(*args, **kwargs)
        return wrapper

    # 这允许装饰器被用作 @admin_required 或 @admin_required()
    if fn:
        return decorator(fn)
    return decorator

def resource_owner_required(resource_id_param='id'):
    """
    资源所有者权限检查装饰器

    Args:
        resource_id_param (str): 资源ID参数名，默认为'id'

    使用方法:
    @resource_owner_required('user_id')
    def update_user_resource(user_id):
        ...
    """
    def decorator(fn):
        @wraps(fn)
        def wrapper(*args, **kwargs):
            # 先进行JWT验证
            verify_jwt_in_request()

            # 获取用户ID和角色
            user_id = get_jwt_identity()
            jwt_claims = get_jwt()
            user_role = jwt_claims.get('role', 'user')

            # 查找用户
            user = Users.query.get(user_id)

            # 检查用户是否存在
            if not user:
                return api_response(message="用户不存在", status="error", code=401)

            # 将用户对象和角色存储在g对象中
            g.user = user
            g.user_role = user_role

            # 获取资源ID
            resource_id = kwargs.get(resource_id_param)
            if not resource_id:
                return api_response(message="无效的资源ID", status="error", code=400)

            # 获取资源所有者ID
            try:
                # 这里需要根据实际情况获取资源所有者ID
                # 例如，对于用户资源，资源所有者ID就是资源ID本身
                owner_id = int(resource_id)
            except (ValueError, TypeError):
                return api_response(message="无效的资源ID", status="error", code=400)

            # 检查是否为资源所有者或管理员
            if g.user.u_id != owner_id and g.user_role != 'admin':
                return api_response(message="没有权限访问此资源", status="error", code=403)

            return fn(*args, **kwargs)
        return wrapper
    return decorator

def owner_or_admin_required(resource_owner_id_fn):
    """
    资源所有者或管理员权限检查装饰器

    Args:
        resource_owner_id_fn (function): 一个函数，用于从请求参数中获取资源所有者ID

    使用方法:
    @owner_or_admin_required(lambda *args, **kwargs: kwargs.get('user_id'))
    def update_user_resource(user_id):
        ...
    """
    def decorator(fn):
        @wraps(fn)
        def wrapper(*args, **kwargs):
            # 先进行JWT验证
            verify_jwt_in_request()

            # 获取用户ID和角色
            user_id = get_jwt_identity()
            jwt_claims = get_jwt()
            user_role = jwt_claims.get('role', 'user')

            # 查找用户
            user = Users.query.get(user_id)

            # 检查用户是否存在
            if not user:
                return api_response(message="用户不存在", status="error", code=401)

            # 将用户对象和角色存储在g对象中
            g.user = user
            g.user_role = user_role

            # 获取资源所有者ID
            try:
                resource_owner_id = resource_owner_id_fn(*args, **kwargs)
                if resource_owner_id is None:
                    return api_response(message="无法确定资源所有者", status="error", code=400)
            except Exception as e:
                return api_response(message=f"无法确定资源所有者: {str(e)}", status="error", code=400)

            # 检查是否为资源所有者或管理员
            if g.user.u_id != resource_owner_id and g.user_role != 'admin':
                return api_response(message="没有权限操作此资源", status="error", code=403)

            return fn(*args, **kwargs)
        return wrapper
    return decorator

def role_required(role):
    """
    角色检查装饰器

    Args:
        role (str or list): 所需的角色，可以是单个角色字符串或角色列表

    使用方法:
    @role_required('admin')
    def admin_only_route():
        ...

    @role_required(['admin', 'manager'])
    def admin_or_manager_route():
        ...
    """
    def decorator(fn):
        @wraps(fn)
        def wrapper(*args, **kwargs):
            # 先进行JWT验证
            verify_jwt_in_request()

            # 获取用户ID和角色
            user_id = get_jwt_identity()
            jwt_claims = get_jwt()
            user_role = jwt_claims.get('role', 'user')

            # 查找用户
            user = Users.query.get(user_id)

            # 检查用户是否存在
            if not user:
                return api_response(message="用户不存在", status="error", code=401)

            # 将用户对象和角色存储在g对象中
            g.user = user
            g.user_role = user_role

            # 检查角色
            required_roles = [role] if isinstance(role, str) else role
            if g.user_role not in required_roles:
                return api_response(message=f"需要{', '.join(required_roles)}角色", status="error", code=403)

            return fn(*args, **kwargs)
        return wrapper
    return decorator

