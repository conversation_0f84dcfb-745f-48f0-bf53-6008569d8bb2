/**
 * 前端适配器 - 用于处理API响应格式
 *
 * 该适配器用于标准化后端API响应，使其符合前端期望的格式
 * 后端API已经统一返回标准格式，此适配器确保兼容旧版本API
 */

/**
 * 标准化API响应
 * @param {Object} response - 后端返回的原始响应
 * @return {Object} 标准化后的响应对象
 */
export function normalizeResponse(response) {
  console.log('规范化API响应:', response.config.url)

  // 如果开启调试模式，打印响应数据
  if (process.env.NODE_ENV === 'development') {
    console.log('响应数据:', response.data)
  }

  // 1. 如果响应已经是标准格式（包含code字段），直接返回
  if (response.data && response.data.code) {
    return response.data
  }

  // 2. 处理旧版本API响应格式
  if (response.data) {
    // 2.1 处理登录/注册接口
    if (response.config.url.includes('/login') || response.config.url.includes('/register')) {
      console.log('前端适配器处理登录响应:', response.data)

      // 处理包含access_token的响应
      if (response.data.access_token) {
        console.log('发现access_token，进行格式转换')
        return {
          code: 20000,
          data: {
            token: response.data.access_token,
            user: response.data.user
          }
        }
      }

      // 处理已经是标准格式的响应（包含token字段）
      if (response.data.code === 20000 && response.data.data && response.data.data.token) {
        console.log('发现标准格式的登录响应，直接返回')
        return response.data
      }
    }

    // 2.2 处理用户信息接口
    if (response.config.url.includes('/users/me') || response.config.url.includes('/users/')) {
      // 确保用户信息中包含id字段
      if (response.data.user) {
        const user = response.data.user
        if (!user.id && user.u_id) {
          user.id = user.u_id
        }
      }
    }

    // 2.3 处理公告接口
    if (response.config.url.includes('/announcements')) {
      console.log('处理公告接口响应:', response.data);

      // 检查响应是否包含items和total字段
      if (response.data && Array.isArray(response.data.items)) {
        return {
          code: 20000,
          data: response.data
        };
      }

      // 如果响应是数组，转换为标准格式
      if (Array.isArray(response.data)) {
        return {
          code: 20000,
          data: {
            items: response.data,
            total: response.data.length
          }
        };
      }

      // 如果响应是对象但没有items字段，尝试查找announcements字段
      if (response.data && response.data.announcements) {
        return {
          code: 20000,
          data: {
            items: response.data.announcements,
            total: response.data.total || response.data.announcements.length
          }
        };
      }
    }

    // 2.4 处理停车记录API响应
  if (response.config.url.includes('/api/parking-records')) {
    // 特殊处理停车统计API
    if (response.config.url.includes('/api/parking-records/stats')) {
      console.log('处理停车统计API响应:', response.data)
      return {
        code: 20000,
        data: response.data
      }
    }
    console.log('处理停车记录API响应:', response.config.url)

    // 如果响应为空或无效，返回空数组
    if (!response.data) {
      console.log('停车记录响应为空，返回空数组')
      return {
        code: 20000,
        data: {
          items: [],
          total: 0
        },
        message: '没有停车记录'
      }
    }

    // 如果响应是数组，将其包装为标准格式
    if (Array.isArray(response.data)) {
      console.log('停车记录响应是数组，将其包装为标准格式')
      return {
        code: 20000,
        data: {
          items: response.data,
          total: response.data.length
        },
        message: '获取停车记录成功'
      }
    }

    // 如果响应是对象
    if (typeof response.data === 'object') {
      // 如果响应已经包含items字段
      if (response.data.items && Array.isArray(response.data.items)) {
        console.log('停车记录响应包含items字段')
        return {
          code: 20000,
          data: response.data,
          message: response.data.message || '获取停车记录成功'
        }
      }

      // 如果响应包含data.items字段
      if (response.data.data && response.data.data.items && Array.isArray(response.data.data.items)) {
        console.log('停车记录响应包含data.items字段')
        return {
          code: 20000,
          data: response.data.data,
          message: response.data.message || '获取停车记录成功'
        }
      }
    }
  }

  // 2.5 处理其他成功响应
    if (response.status >= 200 && response.status < 300) {
      return {
        code: 20000,
        data: response.data
      }
    }
  }

  // 3. 处理错误响应
  if (response.data && response.data.message) {
    // 根据HTTP状态码确定错误码
    const statusCodeMap = {
      400: 40000, // 错误请求
      401: 40100, // 未授权
      403: 40300, // 禁止访问
      404: 40400, // 未找到
      422: 42200  // 无效输入
    }

    const code = statusCodeMap[response.status] || 50000 // 默认服务器错误码

    return {
      code: code,
      data: {
        message: response.data.message,
        status: 'error'
      }
    }
  }

  // 4. 默认标准格式包装
  return {
    code: 20000,
    data: response.data
  }
}

/**
 * 处理API错误
 * @param {Error} error - 捕获的错误对象
 * @return {Object} 标准化的错误响应
 */
export function handleApiError(error) {
  console.log('处理API错误:', error)

  // 1. 网络错误（无响应）
  if (!error.response) {
    console.error('网络错误，无响应:', error.message)
    return Promise.reject({
      code: 50000,
      data: {
        message: '网络错误，请检查您的网络连接',
        status: 'error'
      }
    })
  }

  // 2. 服务器返回的错误
  const status = error.response.status
  console.error(`服务器返回错误 (${status}):`, error.response.data)

  // 2.1 错误消息映射表
  const errorMessages = {
    400: '请求参数错误',
    401: '未授权，请重新登录',
    403: '您没有权限执行此操作',
    404: '请求的资源不存在',
    422: '输入数据验证失败',
    500: '服务器错误'
  }

  // 2.2 错误代码映射表
  const errorCodes = {
    400: 40000,
    401: 40100,
    403: 40300,
    404: 40400,
    422: 42200,
    500: 50000
  }

  // 2.3 获取错误消息和代码
  const message = error.response.data?.message || errorMessages[status] || `请求失败 (${status})`
  const code = errorCodes[status] || 50000

  // 2.4 如果后端已经返回了标准格式的错误响应，直接使用
  if (error.response.data && error.response.data.code) {
    return Promise.reject(error.response.data)
  }

  // 2.5 返回标准格式的错误响应
  return Promise.reject({
    code: code,
    data: {
      message: message,
      status: 'error',
      originalError: error.response.data // 包含原始错误信息
    }
  })
}
