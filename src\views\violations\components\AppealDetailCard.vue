<template>
  <div class="appeal-detail-card">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <div class="card-header">
          <div class="header-left">
            <span class="card-title">
              <i class="el-icon-chat-dot-square"></i> 申诉记录
            </span>
            <div class="appeal-status-badge" :class="'status-' + appeal.status">
              <i :class="getAppealStatusIcon(appeal.status)"></i>
              <span>{{ getAppealStatusText(appeal.status) }}</span>
            </div>
          </div>
          <div class="header-right">
            <span class="appeal-user-info">
              <i class="el-icon-user"></i>
              <span>{{ appeal.user_name || '未知用户' }}</span>
            </span>
          </div>
        </div>
      </div>

      <div class="detail-content">
        <!-- 申诉理由 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-document"></i>
            <span>申诉理由</span>
          </div>
          <div class="appeal-reason">{{ appeal.reason }}</div>
        </div>

        <!-- 申诉证据 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-picture-outline"></i>
            <span>申诉证据</span>
          </div>

          <div v-if="appealEvidences.length === 0" class="empty-evidence">
            <el-empty description="暂无证据" :image-size="100"></el-empty>
          </div>

          <div v-else class="evidence-gallery">
            <el-row :gutter="16">
              <el-col
                v-for="(evidence, index) in appealEvidences"
                :key="index"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
                class="evidence-col"
              >
                <div class="evidence-card">
                  <div class="evidence-type-tag" :class="evidence.evidence_type">
                    <i :class="evidence.evidence_type === 'image' ? 'el-icon-picture-outline' : 'el-icon-video-camera'"></i>
                    <span>{{ evidence.evidence_type === 'image' ? '图片' : '视频' }}</span>
                  </div>
                  
                  <div v-if="evidence.evidence_type === 'image'" class="evidence-image">
                    <el-image
                      :src="evidence.file_path"
                      :preview-src-list="[evidence.file_path]"
                      fit="cover"
                    >
                      <div slot="error" class="image-error">
                        <i class="el-icon-picture-outline"></i>
                        <span>图片加载失败</span>
                      </div>
                    </el-image>
                  </div>
                  <div v-else-if="evidence.evidence_type === 'video'" class="evidence-video">
                    <video :src="evidence.file_path" controls width="100%" />
                  </div>
                  
                  <div class="evidence-info">
                    <div class="evidence-meta">
                      <div class="evidence-uploader">
                        <i class="el-icon-user"></i>
                        <span>{{ evidence.uploader_name || '未知用户' }}</span>
                      </div>
                      <div class="evidence-time">
                        <i class="el-icon-time"></i>
                        <span>{{ formatDateTime(evidence.created_at) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 处理结果 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-finished"></i>
            <span>处理结果</span>
          </div>
          <div class="appeal-result">
            <div class="result-grid">
              <div class="result-item">
                <div class="result-label">处理结果</div>
                <div class="result-value">
                  <el-tag
                    :type="appeal.status === 1 ? 'success' : 'danger'"
                    effect="dark"
                  >
                    {{ appeal.status === 1 ? '已通过' : '未通过' }}
                  </el-tag>
                </div>
              </div>
              
              <div class="result-item">
                <div class="result-label">处理人</div>
                <div class="result-value">{{ appeal.handler_name || '-' }}</div>
              </div>
              
              <div class="result-item">
                <div class="result-label">处理时间</div>
                <div class="result-value">{{ formatDateTime(appeal.updated_at) || '-' }}</div>
              </div>
              
              <div class="result-item full-width">
                <div class="result-label">处理意见</div>
                <div class="result-value comment">{{ appeal.comment || '无' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="appeal-footer">
        <span class="appeal-time">{{ formatDateTime(appeal.created_at) }}</span>
      </div>
    </el-card>
  </div>
</template>

<script>
import { parseTime } from '@/utils'

export default {
  name: 'AppealDetailCard',
  props: {
    appeal: {
      type: Object,
      required: true
    },
    evidences: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    appealEvidences() {
      return this.evidences.filter(item => 
        item.related_type === 'appeal' && item.related_id === this.appeal.id
      )
    }
  },
  methods: {
    formatDateTime(time) {
      if (!time) return ''
      return parseTime(new Date(time), '{y}-{m}-{d} {h}:{i}')
    },
    getAppealStatusIcon(status) {
      const iconMap = {
        0: 'el-icon-time',        // 待审核
        1: 'el-icon-circle-check', // 已通过
        2: 'el-icon-circle-close'  // 未通过
      }
      return iconMap[status] || 'el-icon-time'
    },
    getAppealStatusText(status) {
      const textMap = {
        0: '待审核',
        1: '已通过',
        2: '未通过'
      }
      return textMap[status] || '未知状态'
    }
  }
}
</script>

<style lang="scss" scoped>
.appeal-detail-card {
  .box-card {
    margin-bottom: 24px;
    transition: all 0.3s;
    border-radius: 8px;
    overflow: hidden;

    &:hover {
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .header-left {
      display: flex;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        margin-right: 12px;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
        }
      }

      .appeal-status-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 13px;
        font-weight: bold;
        
        i {
          margin-right: 6px;
        }
        
        &.status-0 {
          background-color: #fdf6ec;
          color: #E6A23C;
        }
        
        &.status-1 {
          background-color: #f0f9eb;
          color: #67C23A;
        }
        
        &.status-2 {
          background-color: #fef0f0;
          color: #F56C6C;
        }
      }
    }

    .header-right {
      .appeal-user-info {
        display: flex;
        align-items: center;
        font-size: 13px;
        color: #606266;
        
        i {
          margin-right: 6px;
        }
      }
    }
  }

  .detail-content {
    padding: 16px;
  }

  .detail-section {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #EBEEF5;
      
      i {
        font-size: 18px;
        color: #409EFF;
        margin-right: 8px;
      }
      
      span {
        font-size: 15px;
        font-weight: bold;
        color: #303133;
      }
    }
  }

  .appeal-reason {
    padding: 12px;
    background-color: #f8f8f8;
    border-radius: 6px;
    line-height: 1.6;
    white-space: pre-line;
    color: #303133;
  }

  .appeal-footer {
    padding: 12px 16px;
    border-top: 1px solid #EBEEF5;
    text-align: right;
    
    .appeal-time {
      font-size: 12px;
      color: #909399;
    }
  }

  // 其他样式与ViolationDetail组件中的相同
  @import './appeal-detail-styles.scss';
}
</style>
