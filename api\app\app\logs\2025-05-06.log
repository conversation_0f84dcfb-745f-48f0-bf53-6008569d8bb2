2025/05/06 23:42:00 flask_api __init__.py[81] create_app() INFO: Flask Rest Api startup
2025/05/06 23:42:00 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/05/06 23:42:00 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/05/06 23:44:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:44:12] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/06 23:44:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:44:12] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 401 -
2025/05/06 23:44:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:44:55] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/05/06 23:45:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:45:20] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/05/06 23:45:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:45:20] "OPTIONS /api/register HTTP/1.1" 200 -
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00114s] ('jyj', 1, 0)
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: INSERT INTO users (u_name, u_pwd, salt, u_role, u_belong, u_phone, u_email, avatar, created_at, updated_at, version) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00101s] ('jyj', '通过Players认证', '46692656-64e6-4cfb-a8ee-b13fb9657e41', 'user', None, None, None, None, '2025-05-06 23:45:20.224784', '2025-05-06 23:45:20.224791', 1)
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: INSERT INTO players (username, password, user_id) VALUES (?, ?, ?)
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00080s] ('jyj', '$pbkdf2-sha256$29000$kvLee6.VEmJMSQnhfC8l5A$SjKgZMUOQvbNp58Mpk2DM7GdlUdRAGoqyHzokvf3S1g', 6)
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[2705] _connection_commit_impl() INFO: COMMIT
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.id = ?
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00100s] (1,)
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00126s] (6,)
2025/05/06 23:45:20 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/06 23:45:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:45:20] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/05/06 23:46:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:46:13] "OPTIONS /api/login HTTP/1.1" 200 -
2025/05/06 23:46:13 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/06 23:46:13 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?
2025/05/06 23:46:13 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 52.92s ago] ('jyj', 1, 0)
2025/05/06 23:46:13 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/06 23:46:13 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00124s] (6,)
2025/05/06 23:46:13 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/06 23:46:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:46:13] "POST /api/login HTTP/1.1" 200 -
2025/05/06 23:46:13 engineio.server socket.py[78] send() INFO: fvqcL5ra0IzmLskaAAAA: Sending packet OPEN data {'sid': 'fvqcL5ra0IzmLskaAAAA', 'upgrades': [], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025/05/06 23:46:13 engineio.server socket.py[90] handle_get_request() INFO: fvqcL5ra0IzmLskaAAAA: Received request to upgrade to websocket
2025/05/06 23:46:13 engineio.server socket.py[219] _websocket_handler() INFO: fvqcL5ra0IzmLskaAAAA: Upgrade to websocket successful
2025/05/06 23:46:13 engineio.server socket.py[78] send() INFO: hUlTwKt5ffmIiOV_AAAB: Sending packet OPEN data {'sid': 'hUlTwKt5ffmIiOV_AAAB', 'upgrades': [], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025/05/06 23:46:13 engineio.server socket.py[90] handle_get_request() INFO: hUlTwKt5ffmIiOV_AAAB: Received request to upgrade to websocket
2025/05/06 23:46:13 engineio.server socket.py[219] _websocket_handler() INFO: hUlTwKt5ffmIiOV_AAAB: Upgrade to websocket successful
2025/05/06 23:46:13 engineio.server socket.py[39] receive() INFO: hUlTwKt5ffmIiOV_AAAB: Received packet MESSAGE data 0
2025/05/06 23:46:13 engineio.server socket.py[39] receive() INFO: fvqcL5ra0IzmLskaAAAA: Received packet MESSAGE data 0
2025/05/06 23:46:13 flask_api routes.py[45] handle_connect() INFO: Client connected with user_id: 6
2025/05/06 23:46:13 flask_api routes.py[45] handle_connect() INFO: Client connected with user_id: 6
2025/05/06 23:46:13 socketio.server server.py[164] emit() INFO: emitting event "connection_response" to xUbcu1kUoiCXpmeDAAAC [/]
2025/05/06 23:46:13 socketio.server server.py[164] emit() INFO: emitting event "connection_response" to FgrAWxp5_ZE3XtPbAAAD [/]
2025/05/06 23:46:13 engineio.server socket.py[78] send() INFO: hUlTwKt5ffmIiOV_AAAB: Sending packet MESSAGE data 2["connection_response",{"status":"connected","authenticated":true}]
2025/05/06 23:46:13 engineio.server socket.py[78] send() INFO: fvqcL5ra0IzmLskaAAAA: Sending packet MESSAGE data 2["connection_response",{"status":"connected","authenticated":true}]
2025/05/06 23:46:13 engineio.server socket.py[78] send() INFO: hUlTwKt5ffmIiOV_AAAB: Sending packet MESSAGE data 0{"sid":"xUbcu1kUoiCXpmeDAAAC"}
2025/05/06 23:46:13 engineio.server socket.py[78] send() INFO: fvqcL5ra0IzmLskaAAAA: Sending packet MESSAGE data 0{"sid":"FgrAWxp5_ZE3XtPbAAAD"}
2025/05/06 23:46:13 engineio.server socket.py[39] receive() INFO: hUlTwKt5ffmIiOV_AAAB: Received packet MESSAGE data 2["join",{"room":"user_6"}]
2025/05/06 23:46:13 socketio.server server.py[576] _handle_event() INFO: received event "join" from xUbcu1kUoiCXpmeDAAAC [/]
2025/05/06 23:46:13 socketio.server server.py[284] enter_room() INFO: xUbcu1kUoiCXpmeDAAAC is entering room user_6 [/]
2025/05/06 23:46:13 engineio.server socket.py[39] receive() INFO: hUlTwKt5ffmIiOV_AAAB: Received packet MESSAGE data 2["join",{"room":"user_6"}]
2025/05/06 23:46:13 socketio.server server.py[576] _handle_event() INFO: received event "join" from xUbcu1kUoiCXpmeDAAAC [/]
2025/05/06 23:46:13 flask_api routes.py[83] on_join() INFO: 用户 6 加入房间: user_6
2025/05/06 23:46:13 socketio.server server.py[284] enter_room() INFO: xUbcu1kUoiCXpmeDAAAC is entering room user_6 [/]
2025/05/06 23:46:13 socketio.server server.py[284] enter_room() INFO: xUbcu1kUoiCXpmeDAAAC is entering room user_6 [/]
2025/05/06 23:46:13 flask_api routes.py[83] on_join() INFO: 用户 6 加入房间: user_6
2025/05/06 23:46:13 socketio.server server.py[284] enter_room() INFO: xUbcu1kUoiCXpmeDAAAC is entering room user_6 [/]
2025/05/06 23:46:13 flask_api routes.py[95] on_join() INFO: 用户 6 加入房间: user_6
2025/05/06 23:46:13 socketio.server server.py[164] emit() INFO: emitting event "join_response" to xUbcu1kUoiCXpmeDAAAC [/]
2025/05/06 23:46:13 flask_api routes.py[95] on_join() INFO: 用户 6 加入房间: user_6
2025/05/06 23:46:13 engineio.server socket.py[78] send() INFO: hUlTwKt5ffmIiOV_AAAB: Sending packet MESSAGE data 2["join_response",{"status":"success","room":"user_6"}]
2025/05/06 23:46:13 socketio.server server.py[164] emit() INFO: emitting event "join_response" to xUbcu1kUoiCXpmeDAAAC [/]
2025/05/06 23:46:13 engineio.server socket.py[78] send() INFO: hUlTwKt5ffmIiOV_AAAB: Sending packet MESSAGE data 2["join_response",{"status":"success","room":"user_6"}]
2025/05/06 23:46:13 flask_api routes.py[60] handle_disconnect() INFO: Client disconnected
2025/05/06 23:46:13 flask_api routes.py[60] handle_disconnect() INFO: Client disconnected
2025/05/06 23:46:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:46:14] "OPTIONS /api/users/6 HTTP/1.1" 200 -
2025/05/06 23:46:14 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/06 23:46:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/06 23:46:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00107s] (6,)
2025/05/06 23:46:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/06 23:46:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.004132s ago] (6,)
2025/05/06 23:46:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/06 23:46:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00106s] (6,)
2025/05/06 23:46:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/06 23:46:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00101s] (6,)
2025/05/06 23:46:14 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/06 23:46:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:46:14] "GET /api/users/6 HTTP/1.1" 200 -
2025/05/06 23:46:14 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet OPEN data {'sid': '7JJXaRF7sOGMPfMnAAAE', 'upgrades': [], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025/05/06 23:46:14 engineio.server socket.py[90] handle_get_request() INFO: 7JJXaRF7sOGMPfMnAAAE: Received request to upgrade to websocket
2025/05/06 23:46:14 engineio.server socket.py[219] _websocket_handler() INFO: 7JJXaRF7sOGMPfMnAAAE: Upgrade to websocket successful
2025/05/06 23:46:14 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet MESSAGE data 0
2025/05/06 23:46:14 flask_api routes.py[45] handle_connect() INFO: Client connected with user_id: 6
2025/05/06 23:46:14 socketio.server server.py[164] emit() INFO: emitting event "connection_response" to 8cR3YCPAnIsjD-kUAAAF [/]
2025/05/06 23:46:14 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet MESSAGE data 2["connection_response",{"status":"connected","authenticated":true}]
2025/05/06 23:46:14 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet MESSAGE data 0{"sid":"8cR3YCPAnIsjD-kUAAAF"}
2025/05/06 23:46:14 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet MESSAGE data 2["join",{"room":"user_6"}]
2025/05/06 23:46:14 socketio.server server.py[576] _handle_event() INFO: received event "join" from 8cR3YCPAnIsjD-kUAAAF [/]
2025/05/06 23:46:14 socketio.server server.py[284] enter_room() INFO: 8cR3YCPAnIsjD-kUAAAF is entering room user_6 [/]
2025/05/06 23:46:14 flask_api routes.py[83] on_join() INFO: 用户 6 加入房间: user_6
2025/05/06 23:46:14 socketio.server server.py[284] enter_room() INFO: 8cR3YCPAnIsjD-kUAAAF is entering room user_6 [/]
2025/05/06 23:46:14 flask_api routes.py[95] on_join() INFO: 用户 6 加入房间: user_6
2025/05/06 23:46:14 socketio.server server.py[164] emit() INFO: emitting event "join_response" to 8cR3YCPAnIsjD-kUAAAF [/]
2025/05/06 23:46:14 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet MESSAGE data 2["join_response",{"status":"success","room":"user_6"}]
2025/05/06 23:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:46:28] "OPTIONS /api/users/6 HTTP/1.1" 200 -
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 13.83s ago] (6,)
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 13.83s ago] (6,)
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 13.82s ago] (6,)
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 13.81s ago] (6,)
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/06 23:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:46:28] "GET /api/users/6 HTTP/1.1" 200 -
2025/05/06 23:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:46:28] "OPTIONS /api/bikes?belong_to=6 HTTP/1.1" 200 -
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 13.87s ago] (6,)
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.belong_to = ?
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00133s] (6,)
2025/05/06 23:46:28 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/06 23:46:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:46:28] "GET /api/bikes?belong_to=6 HTTP/1.1" 200 -
2025/05/06 23:46:39 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:46:39 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:47:04 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:47:04 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:47:29 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:47:29 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:47:54 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:47:54 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:48:19 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:48:19 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:48:44 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:48:44 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:49:09 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:49:09 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:49:34 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:49:34 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:49:59 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:49:59 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:50:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:50:14] "OPTIONS /api/users/6 HTTP/1.1" 200 -
2025/05/06 23:50:14 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/06 23:50:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/06 23:50:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 240s ago] (6,)
2025/05/06 23:50:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/06 23:50:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 240s ago] (6,)
2025/05/06 23:50:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/06 23:50:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 240s ago] (6,)
2025/05/06 23:50:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/06 23:50:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 240s ago] (6,)
2025/05/06 23:50:14 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/06 23:50:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:50:14] "GET /api/users/6 HTTP/1.1" 200 -
2025/05/06 23:50:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:50:15] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/05/06 23:50:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:50:15] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 241s ago] (6,)
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 241s ago] (6,)
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.belong_to = ?
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.belong_to = ?) AS anon_1
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 227.2s ago] (6,)
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00151s] (6,)
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/06 23:50:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:50:15] "GET /api/bikes HTTP/1.1" 200 -
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.belong_to = ? AND bikes.status = ?) AS anon_1
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00119s] (6, '可用')
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.belong_to = ? AND bikes.status = ?) AS anon_1
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.004264s ago] (6, '废弃')
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.brand AS bikes_brand, count(bikes.b_id) AS count_1 
FROM bikes 
WHERE bikes.belong_to = ? GROUP BY bikes.brand
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00252s] (6,)
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_type AS bikes_b_type, count(bikes.b_id) AS count_1 
FROM bikes 
WHERE bikes.belong_to = ? GROUP BY bikes.b_type
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00103s] (6,)
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.color AS bikes_color, count(bikes.b_id) AS count_1 
FROM bikes 
WHERE bikes.belong_to = ? GROUP BY bikes.color
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00098s] (6,)
2025/05/06 23:50:15 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/06 23:50:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/May/2025 23:50:15] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/05/06 23:50:24 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:50:24 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:50:49 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:50:49 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:51:14 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:51:14 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:51:39 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:51:39 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:52:04 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:52:04 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:52:29 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:52:29 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:52:54 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:52:54 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:53:19 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:53:19 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:53:44 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:53:44 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:54:09 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:54:09 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:54:34 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:54:34 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:54:59 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:54:59 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:55:24 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:55:24 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:55:49 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:55:49 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:56:14 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:56:14 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:56:39 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:56:39 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:57:04 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:57:04 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:57:29 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:57:29 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:57:54 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:57:54 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:58:19 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:58:19 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:58:44 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:58:44 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:59:09 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:59:09 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:59:34 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:59:34 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/06 23:59:59 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/06 23:59:59 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
