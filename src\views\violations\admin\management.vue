<template>
  <div class="app-container">
    <permission-wrapper :allowed-roles="['admin']" message="您没有权限访问违规管理数据，请联系管理员获取权限。">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>违规管理中心</span>
      </div>

      <el-tabs v-model="activeTab" @tab-click="handleTabChange" type="border-card">
        <el-tab-pane label="违规记录" name="violations">
          <div class="filter-container">
            <div class="filter-row">
              <el-select v-model="violationListQuery.status" placeholder="处理状态" clearable class="filter-item" style="width: 120px">
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>

              <el-select v-model="violationListQuery.violation_type_id" placeholder="违规类型" clearable class="filter-item" style="width: 140px">
                <el-option v-for="item in violationTypes" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>

              <el-input
                v-model="violationListQuery.bike_number"
                placeholder="车牌号"
                clearable
                class="filter-item"
                style="width: 140px"
              />

              <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleViolationFilter">
                搜索
              </el-button>
            </div>
          </div>

          <div class="violation-table-container">
            <el-table
              v-loading="listLoading"
              :data="list"
              element-loading-text="加载中..."
              border
              fit
              highlight-current-row
              style="width: 100%"
              :header-cell-style="{background:'#f5f7fa', color:'#606266', fontWeight: 'bold'}"
              :row-class-name="tableRowClassName"
            >
              <el-table-column label="ID" align="center" width="60">
                <template slot-scope="{row}">
                  <span>{{ row.id }}</span>
                </template>
              </el-table-column>

              <el-table-column label="车牌号" align="center" width="90">
                <template slot-scope="{row}">
                  <span>{{ row.bike_number }}</span>
                </template>
              </el-table-column>

              <el-table-column label="车主" align="center" width="90">
                <template slot-scope="{row}">
                  <template v-if="row.user_name">
                    {{ row.user_name }}
                  </template>
                  <template v-else-if="row.user_id">
                    ID: {{ row.user_id }}
                  </template>
                  <template v-else>
                    未知
                  </template>
                </template>
              </el-table-column>

              <el-table-column label="违规类型" align="center" width="100">
                <template slot-scope="{row}">
                  <span>{{ row.violation_type }}</span>
                </template>
              </el-table-column>

              <el-table-column label="违规地点" align="center" width="120">
                <template slot-scope="{row}">
                  <el-tooltip :content="row.location" placement="top" :disabled="row.location.length < 10">
                    <span>{{ row.location.length > 10 ? row.location.slice(0, 10) + '...' : row.location }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>

              <el-table-column label="处理状态" align="center" width="90">
                <template slot-scope="{row}">
                  <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
                </template>
              </el-table-column>

              <el-table-column label="违规时间" align="center" width="140">
                <template slot-scope="{row}">
                  <span>{{ formatDateTime(row.violation_time) }}</span>
                </template>
              </el-table-column>

              <el-table-column label="录入人" align="center" width="80">
                <template slot-scope="{row}">
                  <template v-if="row.recorder_name">
                    {{ row.recorder_name }}
                  </template>
                  <template v-else-if="row.recorder_id">
                    ID: {{ row.recorder_id }}
                  </template>
                  <template v-else>
                    未知
                  </template>
                </template>
              </el-table-column>

              <el-table-column label="操作" align="center" width="170" class-name="small-padding fixed-width">
                <template slot-scope="{row}">
                  <div class="operation-buttons">
                    <el-button
                      type="primary"
                      size="mini"
                      icon="el-icon-edit"
                      @click.stop.prevent="(event) => handleEdit(row, event)"
                    >
                      编辑
                    </el-button>

                    <el-button
                      type="danger"
                      size="mini"
                      icon="el-icon-delete"
                      @click.stop.prevent="(event) => handleDelete(row, event)"
                    >
                      删除
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="violationListQuery.page"
        :limit.sync="violationListQuery.per_page"
        @pagination="getViolationList"
      />
        </el-tab-pane>

        <el-tab-pane label="申诉记录" name="appeals">
          <div class="filter-container">
            <div class="filter-row">
              <el-select v-model="appealListQuery.status" placeholder="申诉状态" clearable class="filter-item" style="width: 120px">
                <el-option v-for="item in appealStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>

              <el-input
                v-model="appealListQuery.violation_id"
                placeholder="违规记录ID"
                clearable
                class="filter-item"
                style="width: 140px"
              />

              <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleAppealFilter">
                搜索
              </el-button>
            </div>
          </div>

          <el-table
            v-loading="appealListLoading"
            :data="appealList"
            element-loading-text="加载中..."
            border
            fit
            highlight-current-row
            style="width: 100%"
            :header-cell-style="{background:'#f5f7fa', color:'#606266', fontWeight: 'bold'}"
            :row-class-name="tableRowClassName"
          >
            <el-table-column label="ID" align="center" width="60">
              <template slot-scope="{row}">
                <span>{{ row.id }}</span>
              </template>
            </el-table-column>

            <el-table-column label="违规ID" align="center" width="80">
              <template slot-scope="{row}">
                <el-button type="text" @click="viewDetail(row.violation_id)">{{ row.violation_id }}</el-button>
              </template>
            </el-table-column>

            <el-table-column label="申诉用户" align="center" width="90">
              <template slot-scope="{row}">
                <span>{{ row.user_name }}</span>
              </template>
            </el-table-column>

            <el-table-column label="申诉理由" align="center" min-width="200">
              <template slot-scope="{row}">
                <el-tooltip :content="row.reason" placement="top" :disabled="row.reason.length < 20">
                  <span>{{ row.reason.length > 20 ? row.reason.slice(0, 20) + '...' : row.reason }}</span>
                </el-tooltip>
              </template>
            </el-table-column>

            <el-table-column label="申诉状态" align="center" width="90">
              <template slot-scope="{row}">
                <el-tag :type="getAppealStatusType(row.status)">{{ row.status_text || getAppealStatusText(row.status) }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column label="申诉时间" align="center" width="140">
              <template slot-scope="{row}">
                <span>{{ formatDateTime(row.created_at) }}</span>
              </template>
            </el-table-column>

            <el-table-column label="处理人" align="center" width="80">
              <template slot-scope="{row}">
                <span>{{ row.handler_name || '-' }}</span>
              </template>
            </el-table-column>

            <el-table-column label="操作" align="center" width="170" class-name="small-padding fixed-width">
              <template slot-scope="{row}">
                <div class="operation-buttons">
                  <el-button
                    type="primary"
                    size="mini"
                    icon="el-icon-edit"
                    @click.stop.prevent="(event) => handleAppealEdit(row, event)"
                  >
                    编辑
                  </el-button>

                  <el-button
                    type="danger"
                    size="mini"
                    icon="el-icon-delete"
                    @click.stop.prevent="(event) => handleAppealDelete(row, event)"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="appealTotal > 0"
            :total="appealTotal"
            :page.sync="appealListQuery.page"
            :limit.sync="appealListQuery.per_page"
            @pagination="getAppealList"
          />
        </el-tab-pane>
      </el-tabs>



      <!-- 处理申诉对话框 -->
      <appeal-process-form
        :visible.sync="appealProcessDialogVisible"
        :appeal="currentAppeal || {}"
        :submit-method="handleAppealSubmit"
        @success="handleAppealSuccess"
        @view-violation="viewDetail"
      />

      <!-- 编辑违规记录对话框 -->
      <el-dialog
        title="编辑违规记录"
        :visible.sync="editDialogVisible"
        width="800px"
        :append-to-body="true"
        :modal-append-to-body="false"
        :destroy-on-close="true"
        :close-on-click-modal="false"
        custom-class="violation-edit-dialog"
      >
        <violation-form
          v-if="editDialogVisible && currentEditViolation"
          :is-edit="true"
          :initial-data="currentEditViolation"
          :submit-method="updateViolation"
          @success="handleEditSuccess"
        />
      </el-dialog>

      <!-- 删除确认对话框 -->
      <el-dialog
        title="确认删除"
        :visible.sync="deleteDialogVisible"
        width="400px"
        :close-on-click-modal="false"
        :append-to-body="true"
        :destroy-on-close="false"
        :modal-append-to-body="false"
        custom-class="delete-confirmation-dialog"
      >
        <div class="delete-confirm-content">
          <i class="el-icon-warning-outline warning-icon"></i>
          <div class="confirm-text">
            <p>确定要删除该违规记录吗？</p>
            <p class="warning-text">此操作将永久删除该记录，且无法恢复！</p>
            <p v-if="currentDeleteViolation" class="record-info">
              记录ID: {{ currentDeleteViolation.id }}<br>
              车牌号: {{ currentDeleteViolation.bike_number }}<br>
              违规类型: {{ currentDeleteViolation.violation_type }}
            </p>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" :loading="deleteLoading" @click="confirmDelete">确认删除</el-button>
        </span>
      </el-dialog>
    </el-card>
    </permission-wrapper>
  </div>
</template>

<script>
import { getAllViolations, handleViolation, getViolationTypes, getAllAppeals, handleAppeal, deleteViolation, getViolationDetail, deleteAppeal } from '@/api/violations'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
import ViolationForm from '../components/ViolationForm'
import AppealProcessForm from '../components/AppealProcessForm'
import PermissionWrapper from '@/components/PermissionWrapper'

export default {
  name: 'AdminViolationManagement',
  components: {
    Pagination,
    ViolationForm,
    AppealProcessForm,
    PermissionWrapper
  },
  data() {
    return {
      // 激活的标签页
      activeTab: 'violations',

      // 违规记录相关数据
      list: [],
      total: 0,
      listLoading: true,
      violationListQuery: {
        page: 1,
        per_page: 10,
        status: undefined,
        violation_type_id: undefined,
        bike_number: undefined
      },
      statusOptions: [
        { label: '待审核', value: 0 },
        { label: '已处理', value: 1 },
        { label: '申诉中', value: 2 },
        { label: '已撤销', value: 3 }
      ],
      violationTypes: [],


      // 申诉记录相关数据
      appealList: [],
      appealTotal: 0,
      appealListLoading: false,
      appealListQuery: {
        page: 1,
        per_page: 10,
        status: undefined,
        violation_id: undefined,
        sort: 'id',
        order: 'asc'
      },
      appealStatusOptions: [
        { label: '待审核', value: 0 },
        { label: '已通过', value: 1 },
        { label: '未通过', value: 2 }
      ],
      appealProcessDialogVisible: false,
      currentAppeal: null,
      appealSubmitting: false,
      appealProcessForm: {
        status: 1,
        comment: ''
      },

      // 防止重复点击
      isProcessing: false,

      // 编辑对话框相关
      editDialogVisible: false,
      currentEditViolation: null,

      // 删除确认对话框相关
      deleteDialogVisible: false,
      currentDeleteViolation: null,
      deleteLoading: false
    }
  },
  created() {
    this.getViolationTypes()

    // 默认排除申诉中的记录
    this.violationListQuery.exclude_status = 2
    this.getViolationList()

    // 检查URL中是否有违规记录ID参数
    const violationId = this.$route.query.violation_id
    if (violationId) {
      this.activeTab = 'appeals'
      this.appealListQuery.violation_id = violationId
      this.getAppealList()
    } else {
      // 如果没有指定违规记录ID，也加载申诉记录
      this.getAppealList()
    }
  },
  methods: {
    // 标签页切换处理
    handleTabChange(tab) {
      if (tab.name === 'violations') {
        // 确保排除申诉中的记录
        if (this.violationListQuery.status !== 2) {
          this.violationListQuery.exclude_status = 2
        }
        this.getViolationList()
      } else if (tab.name === 'appeals') {
        this.getAppealList()
      }
    },

    // 违规记录相关方法
    getViolationList() {
      this.listLoading = true
      // 添加参数，排除申诉中的记录（状态为2的记录）
      const params = { ...this.violationListQuery }

      // 始终排除申诉中的记录，除非用户明确选择了查看申诉中的记录
      if (params.status !== 2) {
        params.exclude_status = 2
      }

      // 如果用户选择了查看申诉中的记录，则只显示申诉中的记录
      if (params.status === 2) {
        params.status = 2
        delete params.exclude_status
      }

      getAllViolations(params).then(response => {
        this.list = response.data.items
        this.total = response.data.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleViolationFilter() {
      this.violationListQuery.page = 1

      // 如果用户选择了查看申诉中的记录，则只显示申诉中的记录
      if (this.violationListQuery.status === 2) {
        delete this.violationListQuery.exclude_status
      } else {
        // 否则排除申诉中的记录
        this.violationListQuery.exclude_status = 2
      }

      this.getViolationList()
    },
    getViolationTypes() {
      getViolationTypes().then(response => {
        this.violationTypes = response.data
      }).catch(error => {
        console.error('获取违规类型失败:', error)
      })
    },


    formatDateTime(time) {
      if (!time) return ''
      return parseTime(new Date(time), '{y}-{m}-{d} {h}:{i}')
    },
    getStatusType(status) {
      const statusMap = {
        0: 'info',    // 待审核
        1: 'success', // 已处理
        2: 'warning', // 申诉中
        3: 'danger'   // 已撤销
      }
      return statusMap[status] || 'info'
    },
    getStatusText(status) {
      const statusMap = {
        0: '待审核',
        1: '已处理',
        2: '申诉中',
        3: '已撤销'
      }
      return statusMap[status] || '未知'
    },
    getViolationTypeTag(type) {
      const typeMap = {
        '违规停车': 'danger',
        '占用消防通道': 'danger',
        '占用无障碍通道': 'warning',
        '超时停车': 'warning',
        '车辆损坏公物': 'danger',
        '无证驾驶': 'danger',
        '其他违规': 'info'
      }
      return typeMap[type] || 'info'
    },


    viewDetail(id) {
      this.$router.push({ name: 'AdminViolationDetail', params: { id }})
    },
    goToAppealManagement(violationId, event) {
      console.log('处理申诉按钮被点击（跳转）', violationId);

      // 防止重复点击
      if (this.isProcessing) {
        console.log('正在处理中，请勿重复点击');
        return;
      }

      this.isProcessing = true;

      // 阻止事件冒泡，避免触发其他事件
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // 确保关闭对话框
      this.appealProcessDialogVisible = false;

      // 切换到申诉标签页，并设置筛选条件
      this.activeTab = 'appeals';
      this.appealListQuery.violation_id = violationId;
      // 重置页码并获取数据
      this.appealListQuery.page = 1;
      this.getAppealList();

      // 延迟重置处理标志，避免快速点击
      setTimeout(() => {
        this.isProcessing = false;
      }, 500);
    },


    // 申诉记录相关方法
    getAppealList() {
      this.appealListLoading = true

      // 创建一个新的查询参数对象，避免修改原始对象
      const params = { ...this.appealListQuery }

      // 确保申诉记录按ID升序排列
      params.sort = 'id'
      params.order = 'asc'

      getAllAppeals(params).then(response => {
        // 获取所有申诉记录
        let appeals = response.data.items

        // 如果没有指定状态，则只显示未处理(0)和已处理结束(1,2)的申诉记录
        // 这里不需要额外的过滤，因为申诉记录只有这三种状态

        this.appealList = appeals
        this.appealTotal = response.data.total
        this.appealListLoading = false
      }).catch(() => {
        this.appealListLoading = false
      })
    },
    handleAppealFilter() {
      this.appealListQuery.page = 1
      this.getAppealList()
    },
    clearViolationIdFilter() {
      this.appealListQuery.violation_id = undefined
      this.appealListQuery.page = 1
      this.getAppealList()
    },
    getAppealStatusType(status) {
      const statusMap = {
        0: 'warning', // 待审核
        1: 'success', // 已通过
        2: 'danger'   // 未通过
      }
      return statusMap[status] || 'info'
    },
    getAppealStatusText(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '未通过'
      }
      return statusMap[status] || '未知'
    },
    handleAppealProcess(row, event) {
      console.log('处理申诉按钮被点击', row.id);

      // 防止重复点击
      if (this.isProcessing) {
        console.log('正在处理中，请勿重复点击');
        return;
      }

      this.isProcessing = true;

      // 阻止事件冒泡，避免触发其他事件
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // 先准备数据，再一次性显示对话框，避免闪烁
      this.currentAppeal = JSON.parse(JSON.stringify(row)); // 深拷贝，避免引用问题

      // 直接显示对话框，不需要先关闭再打开
      this.appealProcessDialogVisible = true;

      // 延迟重置处理标志，避免快速点击
      setTimeout(() => {
        this.isProcessing = false;
      }, 300);
    },
    submitAppealProcess() {
      if (!this.appealProcessForm.comment) {
        this.$message.warning('请输入处理意见')
        return
      }

      // 确认对话框
      const confirmMessage = this.appealProcessForm.status === 1
        ? '确认通过该申诉吗？该操作将撤销相关违规记录。'
        : '确认设置申诉为未通过吗？该操作将维持相关违规记录。'

      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: this.appealProcessForm.status === 1 ? 'warning' : 'info'
      }).then(() => {
        this.appealSubmitting = true

        handleAppeal(this.currentAppeal.id, this.appealProcessForm)
          .then(response => {
            this.$notify({
              title: '成功',
              message: this.appealProcessForm.status === 1 ? '申诉已通过，相关违规记录已撤销' : '申诉未通过，相关违规记录已维持',
              type: 'success',
              duration: 2000
            })
            this.appealProcessDialogVisible = false
            // 刷新申诉列表和违规列表
            this.getAppealList()
            this.getViolationList()
          })
          .catch(error => {
            console.error('处理申诉失败:', error)
            this.$message.error(error.message || '处理失败，请重试')
          })
          .finally(() => {
            this.appealSubmitting = false
          })
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 对话框事件处理方法

    onAppealDialogOpen() {
      console.log('处理申诉对话框打开');
    },

    onAppealDialogClose() {
      console.log('处理申诉对话框关闭');
      // 清理数据
      this.currentAppeal = null;
      this.appealProcessForm = {
        status: 1,
        comment: ''
      };
    },

    // 编辑违规记录
    handleEdit(row, event) {
      console.log('编辑违规记录按钮被点击', row.id);

      // 防止重复点击
      if (this.isProcessing) {
        console.log('正在处理中，请勿重复点击');
        return;
      }

      this.isProcessing = true;

      // 阻止事件冒泡，避免触发其他事件
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // 获取完整的违规记录详情
      getViolationDetail(row.id)
        .then(response => {
          if (response.data && response.data.violation) {
            // 设置当前编辑的违规记录
            const violationData = response.data.violation;

            // 确保填写人字段正确设置
            if (!violationData.recorder_name && row.recorder_name) {
              violationData.recorder_name = row.recorder_name;
            }

            // 如果仍然没有填写人信息，尝试从表格行数据中获取
            if (!violationData.recorder_name) {
              // 从表格行数据中获取录入人信息
              console.log('从表格行数据中获取录入人信息:', row);
              violationData.recorder_name = row.recorder_name || '';
            }

            // 如果仍然没有填写人信息，使用当前登录用户的名称
            if (!violationData.recorder_name) {
              violationData.recorder_name = this.$store.getters.name || '';
              console.log('使用当前登录用户名称作为填写人:', violationData.recorder_name);
            }

            console.log('编辑违规记录数据:', violationData);

            this.currentEditViolation = violationData;
            // 显示编辑对话框
            this.editDialogVisible = true;
          } else {
            this.$message.error('获取违规记录详情失败');
          }
        })
        .catch(error => {
          console.error('获取违规记录详情失败:', error);
          this.$message.error('获取违规记录详情失败');
        })
        .finally(() => {
          // 延迟重置处理标志，避免快速点击
          setTimeout(() => {
            this.isProcessing = false;
          }, 500);
        });
    },

    // 更新违规记录
    updateViolation(data) {
      return handleViolation(this.currentEditViolation.id, data);
    },

    // 编辑成功处理
    handleEditSuccess() {
      this.$message.success('违规记录更新成功');
      this.editDialogVisible = false;
      // 刷新列表
      this.getViolationList();
    },

    // 删除违规记录
    handleDelete(row, event) {
      console.log('删除违规记录按钮被点击', row.id);

      // 阻止事件冒泡，避免触发其他事件
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // 防止重复点击
      if (this.isProcessing) {
        console.log('正在处理中，请勿重复点击');
        return;
      }

      this.isProcessing = true;

      // 设置当前要删除的违规记录
      this.currentDeleteViolation = row;

      // 使用 nextTick 确保在 DOM 更新后再显示对话框
      this.$nextTick(() => {
        // 显示删除确认对话框
        this.deleteDialogVisible = true;
        // 重置处理标志
        this.isProcessing = false;
      });
    },

    // 确认删除
    confirmDelete() {
      if (!this.currentDeleteViolation || !this.currentDeleteViolation.id) {
        this.$message.error('无效的违规记录');
        return;
      }

      this.deleteLoading = true;
      this.isProcessing = true; // 防止重复点击

      deleteViolation(this.currentDeleteViolation.id)
        .then(() => {
          this.$message.success('违规记录删除成功');

          // 使用 nextTick 确保在 DOM 更新后再关闭对话框
          this.$nextTick(() => {
            this.deleteDialogVisible = false;
            // 刷新列表
            this.getViolationList();
          });
        })
        .catch(error => {
          console.error('删除违规记录失败:', error);
          this.$message.error(error.message || '删除失败，请重试');
        })
        .finally(() => {
          this.deleteLoading = false;

          // 延迟重置处理标志，避免快速点击
          setTimeout(() => {
            this.isProcessing = false;
          }, 500);
        });
    },

    // 处理申诉编辑
    handleAppealEdit(row, event) {
      console.log('编辑申诉记录按钮被点击', row.id);

      // 防止重复点击
      if (this.isProcessing) {
        console.log('正在处理中，请勿重复点击');
        return;
      }

      this.isProcessing = true;

      // 阻止事件冒泡，避免触发其他事件
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // 先准备数据，再一次性显示对话框，避免闪烁
      this.currentAppeal = JSON.parse(JSON.stringify(row)); // 深拷贝，避免引用问题

      // 直接显示对话框，不需要先关闭再打开
      this.appealProcessDialogVisible = true;

      // 延迟重置处理标志，避免快速点击
      setTimeout(() => {
        this.isProcessing = false;
      }, 300);
    },

    // 处理申诉提交
    handleAppealSubmit(appealId, formData) {
      return new Promise((resolve, reject) => {
        handleAppeal(appealId, formData)
          .then(response => {
            resolve(response)
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 处理申诉成功回调
    handleAppealSuccess() {
      // 刷新申诉列表
      this.getAppealList()
    },

    // 处理申诉删除
    handleAppealDelete(row, event) {
      console.log('删除申诉记录按钮被点击', row.id);

      // 防止重复点击
      if (this.isProcessing) {
        console.log('正在处理中，请勿重复点击');
        return;
      }

      this.isProcessing = true;

      // 阻止事件冒泡，避免触发其他事件
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // 确认对话框
      this.$confirm('确定要删除该申诉记录吗？此操作将永久删除该记录，且无法恢复！', '确认删除', {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用删除申诉的API
        deleteAppeal(row.id)
          .then(() => {
            this.$message.success('申诉记录已删除');
            // 刷新列表
            this.getAppealList();
            // 刷新违规记录列表，因为删除申诉可能会改变违规记录的状态
            this.getViolationList();
          })
          .catch(error => {
            console.error('删除申诉记录失败:', error);
            this.$message.error(error.message || '删除失败，请重试');
          });
      }).catch(() => {
        // 用户取消操作
      }).finally(() => {
        // 延迟重置处理标志，避免快速点击
        setTimeout(() => {
          this.isProcessing = false;
        }, 500);
      });
    },

    // 表格行样式
    tableRowClassName({ row, rowIndex }) {
      // 根据状态设置不同的行样式
      if (row.status === 0) {
        return 'waiting-row'; // 待审核
      } else if (row.status === 2) {
        return 'appeal-row'; // 申诉中
      } else if (row.status === 3) {
        return 'canceled-row'; // 已撤销
      }
      return ''; // 默认样式
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .filter-container {
    padding-bottom: 15px;

    .filter-row {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
    }

    .filter-item {
      margin-right: 12px;
      margin-bottom: 8px;
    }
  }

  .violation-table-container {
    margin-top: 15px;
    margin-bottom: 15px;
  }

  // 标签页样式
  .el-tabs {
    margin-top: 10px;

    .el-tabs__content {
      padding: 15px;
    }
  }

  // 操作按钮样式
  .operation-buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    .el-button {
      margin: 0 3px;
      padding: 5px 10px;
      font-size: 12px;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  // 表格行样式
  ::v-deep .waiting-row {
    background-color: #f8f8f8;
  }

  ::v-deep .appeal-row {
    background-color: #fdf6ec;
  }

  ::v-deep .canceled-row {
    background-color: #fef0f0;
    color: #909399;
  }

  ::v-deep .el-table td {
    padding: 8px 0;
  }

  ::v-deep .el-table th {
    padding: 10px 0;
  }

  // 处理对话框样式
  .process-summary, .appeal-summary {
    background-color: #f8faff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #e6effd;

    .summary-header {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e6effd;

      .summary-title {
        font-size: 16px;
        font-weight: bold;
        color: #409EFF;
      }
    }

    .summary-row {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .summary-item {
      margin-bottom: 12px;

      &.appeal-reason {
        display: flex;
        margin-top: 10px;
        margin-bottom: 0;
      }

      .summary-label {
        display: inline-block;
        width: 80px;
        color: #606266;
        font-weight: 500;
        font-size: 14px;
        line-height: 1.5;

        &.reason-label {
          align-self: flex-start;
          margin-top: 8px;
          font-weight: 600;
        }
      }

      .summary-value {
        color: #303133;
        line-height: 1.5;

        &.highlight {
          color: #409EFF;
          font-weight: bold;
        }

        &.description {
          margin-top: 8px;
          padding: 15px;
          background-color: #fff;
          border-radius: 6px;
          border: 1px solid #e6effd;
          line-height: 1.8;
          min-height: 40px;
          font-size: 14px;
          color: #606266;
          word-break: break-all;
          white-space: pre-line;
        }
      }


    }
  }

  // 处理违规对话框样式
  .violation-process-dialog {
    .el-dialog__header {
      padding: 20px;
      border-bottom: 1px solid #EBEEF5;

      .el-dialog__title {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
      }
    }

    .el-dialog__body {
      padding: 25px 30px;
    }

    .el-dialog__footer {
      padding: 15px 30px 20px;
      border-top: 1px solid #EBEEF5;
    }
  }

  // 处理申诉对话框样式
  .appeal-process-dialog {
    .el-dialog__header {
      background-color: #f0f7ff;
      padding: 15px 20px;
      border-bottom: 1px solid #e6effd;
    }

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #1989fa;
    }

    .el-dialog__body {
      padding: 0;
    }

    .el-dialog__footer {
      padding: 15px 20px;
      border-top: 1px solid #ebeef5;
      background-color: #f8f9fa;
    }
  }

  .appeal-process-container {
    .appeal-info-card {
      padding: 20px;
      background-color: #fff;

      .appeal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .appeal-id {
          .label {
            color: #606266;
            font-size: 14px;
          }

          .value {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-left: 5px;
          }
        }
      }

      .appeal-meta {
        display: flex;
        margin-bottom: 20px;

        .meta-item {
          display: flex;
          align-items: center;
          margin-right: 30px;

          i {
            color: #909399;
            margin-right: 5px;
          }

          .label {
            color: #606266;
            margin-right: 5px;
          }

          .value {
            color: #303133;
            font-weight: 500;
          }
        }
      }

      .violation-info {
        background-color: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;

        .violation-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          .title {
            font-size: 15px;
            font-weight: 600;
            color: #303133;
          }
        }

        .violation-content {
          .info-row {
            display: flex;
            flex-wrap: wrap;

            .info-item {
              margin-right: 20px;
              margin-bottom: 8px;

              .label {
                color: #909399;
                margin-right: 5px;
              }

              .value {
                color: #303133;

                &.highlight {
                  color: #409EFF;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }

      .appeal-reason {
        background-color: #fff;
        border: 1px solid #ebeef5;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;

        .reason-header {
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          i {
            color: #409EFF;
            margin-right: 5px;
            font-size: 16px;
          }

          .title {
            font-size: 15px;
            font-weight: 600;
            color: #303133;
          }
        }

        .reason-content {
          color: #606266;
          line-height: 1.6;
          padding: 10px;
          background-color: #f8f9fa;
          border-radius: 4px;
          min-height: 60px;
        }
      }
    }

    .process-form-container {
      background-color: #f8f9fa;
      padding: 20px;
      border-top: 1px solid #ebeef5;

      .form-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        i {
          color: #409EFF;
          margin-right: 8px;
          font-size: 18px;
        }

        span {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }

      .process-form {
        .el-radio-group {
          display: flex;
          width: 100%;

          .el-radio {
            margin-right: 20px;
            width: calc(50% - 10px);

            &.is-bordered {
              padding: 12px 15px;
              border-radius: 6px;

              &.is-checked {
                border-color: #409EFF;
                background-color: #ecf5ff;
              }
            }

            .radio-content {
              display: flex;
              align-items: center;

              .success-icon {
                color: #67C23A;
                margin-right: 5px;
                font-size: 16px;
              }

              .danger-icon {
                color: #F56C6C;
                margin-right: 5px;
                font-size: 16px;
              }

              .radio-label {
                font-weight: 600;
                margin-right: 8px;
              }

              .radio-desc {
                color: #909399;
                font-size: 12px;
              }
            }
          }
        }

        .el-textarea__inner {
          border-radius: 4px;
          padding: 10px;
          font-family: Arial, sans-serif;
        }
      }
    }
  }



  .radio-label {
    font-weight: bold;
    margin-right: 5px;
  }



  .form-tip {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
  }

  .input-counter {
    position: absolute;
    bottom: 8px;
    right: 10px;
    font-size: 12px;
    color: #909399;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 2px 6px;
    border-radius: 3px;

    &.warning {
      color: #E6A23C;
    }
  }

  .violation-info {
    margin-top: 5px;
    padding: 10px;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #EBEEF5;

    p {
      margin: 5px 0;
    }
  }

  // 编辑违规记录对话框样式
  .violation-edit-dialog {
    .el-dialog__body {
      max-height: 75vh;
      overflow-y: auto;
      padding: 20px 30px;
    }

    .el-dialog__header {
      padding: 20px;
      border-bottom: 1px solid #EBEEF5;
      background-color: #f5f7fa;

      .el-dialog__title {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
      }
    }

    .el-dialog__footer {
      padding: 15px 30px 20px;
      border-top: 1px solid #EBEEF5;
      background-color: #f9f9f9;
    }

    // 设置对话框最小高度，确保能显示所有字段
    min-height: 600px;

    // 调整表单在对话框中的样式
    .violation-form {
      .el-divider {
        margin: 24px 0 20px;
      }

      .el-form-item {
        margin-bottom: 18px;
      }

      // 添加卡片样式
      .form-card {
        background-color: #f9f9f9;
        border-radius: 4px;
        padding: 16px;
        margin-bottom: 20px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
      }

      // 添加分组标题样式
      .form-section-title {
        font-size: 16px;
        font-weight: bold;
        color: #409EFF;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #EBEEF5;
      }
    }
  }

  // 删除确认对话框样式
  .delete-confirmation-dialog {
    ::v-deep .el-dialog {
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      transform: scale(1);
      opacity: 1;
    }

    ::v-deep .el-dialog__header {
      background-color: #FEF0F0;
      padding: 15px 20px;
      border-bottom: 1px solid #FCDEDE;
    }

    ::v-deep .el-dialog__title {
      color: #F56C6C;
      font-weight: 600;
    }

    ::v-deep .el-dialog__body {
      padding: 20px;
    }

    ::v-deep .el-dialog__footer {
      padding: 10px 20px 15px;
      border-top: 1px solid #EBEEF5;
      background-color: #F8F9FA;
    }
  }

  .delete-confirm-content {
    display: flex;
    align-items: flex-start;
    padding: 10px;

    .warning-icon {
      font-size: 24px;
      color: #F56C6C;
      margin-right: 15px;
      margin-top: 5px;
    }

    .confirm-text {
      flex: 1;

      p {
        margin: 5px 0;
        line-height: 1.5;
      }

      .warning-text {
        color: #F56C6C;
        font-weight: bold;
        margin: 10px 0;
      }

      .record-info {
        background-color: #F8F8F8;
        padding: 10px;
        border-radius: 4px;
        margin-top: 15px;
        font-size: 14px;
        line-height: 1.8;
        color: #606266;
      }
    }
  }
}
</style>
