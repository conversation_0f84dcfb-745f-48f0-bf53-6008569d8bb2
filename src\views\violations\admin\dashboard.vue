<template>
  <div class="app-container">
    <permission-wrapper :allowed-roles="['admin', 'security']" message="您没有权限访问违规统计数据，此功能仅对管理员和保安开放">
      <div class="dashboard-header">
        <h2 class="dashboard-title">校园电动车违规统计分析</h2>
        <el-button class="refresh-btn" type="primary" icon="el-icon-refresh" size="small" @click="refreshData">
          刷新数据
        </el-button>
      </div>

    <div v-loading="loading">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stat-cards">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card total-card">
            <div class="stat-card-icon"><i class="el-icon-data-analysis"></i></div>
            <div class="stat-card-content">
              <div class="stat-card-title">总违规数</div>
              <div class="stat-card-value">{{ statistics.total || 0 }}</div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card pending-card">
            <div class="stat-card-icon"><i class="el-icon-warning-outline"></i></div>
            <div class="stat-card-content">
              <div class="stat-card-title">未处理</div>
              <div class="stat-card-value">{{ statistics.status[0] || 0 }}</div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card appeal-card">
            <div class="stat-card-icon"><i class="el-icon-chat-dot-square"></i></div>
            <div class="stat-card-content">
              <div class="stat-card-title">申诉中</div>
              <div class="stat-card-value">{{ statistics.status[2] || 0 }}</div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card shadow="hover" class="stat-card cancel-card">
            <div class="stat-card-icon"><i class="el-icon-circle-close"></i></div>
            <div class="stat-card-content">
              <div class="stat-card-title">已撤销</div>
              <div class="stat-card-value">{{ statistics.status[3] || 0 }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 违规状态和类型图表 -->
      <el-row :gutter="20" class="chart-container">
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card shadow="hover" class="chart-card status-chart-card">
            <div slot="header" class="clearfix">
              <span><i class="el-icon-pie-chart"></i> 违规状态分布</span>
              <el-tooltip content="显示不同状态的违规记录数量占比" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </div>
            <div class="chart" ref="statusChart"></div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card shadow="hover" class="chart-card type-chart-card">
            <div slot="header" class="clearfix">
              <span><i class="el-icon-pie-chart"></i> 违规类型分布</span>
              <el-tooltip content="显示不同类型的违规记录数量占比" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </div>
            <div class="chart" ref="typeChart"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 申诉状态和地点分布图表 -->
      <el-row :gutter="20" class="chart-container">
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card shadow="hover" class="chart-card appeal-chart-card">
            <div slot="header" class="clearfix">
              <span><i class="el-icon-pie-chart"></i> 申诉状态分布</span>
              <el-tooltip content="显示不同状态的申诉记录数量占比" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </div>
            <div class="chart" ref="appealChart"></div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card shadow="hover" class="chart-card location-chart-card">
            <div slot="header" class="clearfix">
              <span><i class="el-icon-map-location"></i> 违规地点分布</span>
              <el-tooltip content="显示不同地点的违规记录数量占比" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </div>
            <div class="chart" ref="locationChart"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 时间趋势图表 -->
      <el-card shadow="hover" class="chart-card time-trend-card">
        <div slot="header" class="clearfix">
          <span><i class="el-icon-data-line"></i> 违规时间趋势分析</span>
          <el-tooltip content="显示不同时间单位的违规记录数量变化趋势" placement="top">
            <i class="el-icon-question help-icon"></i>
          </el-tooltip>
          <div class="chart-controls">
            <el-radio-group v-model="timeUnit" size="mini" @change="handleTimeUnitChange">
              <el-radio-button label="day">日</el-radio-button>
              <el-radio-button label="week">周</el-radio-button>
              <el-radio-button label="month">月</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart time-chart" ref="timeChart"></div>
      </el-card>


      </div>
    </permission-wrapper>
  </div>
</template>

<script>
import { getViolationStatistics } from '@/api/violations'
import * as echarts from 'echarts'
import PermissionWrapper from '@/components/PermissionWrapper'

export default {
  name: 'AdminViolationDashboard',
  components: {
    PermissionWrapper
  },
  data() {
    return {
      loading: true,
      dateRange: [],
      timeUnit: 'day',
      activeTab: 'status',
      statistics: {
        total: 0,
        status: {
          0: 0, // 未处理
          1: 0, // 已处理
          2: 0, // 申诉中
          3: 0  // 已撤销
        },
        types: {},
        months: {},
        weeks: {},
        days: {},
        locations: {},
        appeals: {}
      },
      charts: {
        statusChart: null,
        typeChart: null,
        locationChart: null,
        appealChart: null,
        timeChart: null
      },
      colorMap: {
        '未处理': '#909399',
        '已处理': '#67C23A',
        '申诉中': '#E6A23C',
        '已撤销': '#F56C6C',
        '待审核': '#409EFF',
        '已通过': '#67C23A',
        '已拒绝': '#F56C6C'
      },
      // 为地点分布图表提供的和谐颜色方案
      locationColors: [
        '#5470c6', // 蓝色
        '#91cc75', // 绿色
        '#fac858', // 黄色
        '#ee6666', // 红色
        '#73c0de', // 浅蓝色
        '#3ba272', // 深绿色
        '#fc8452', // 橙色
        '#9a60b4', // 紫色
        '#ea7ccc', // 粉色
        '#48b3c5', // 青色
        '#8378ea', // 淡紫色
        '#d88273', // 淡红色
        '#60c0cb', // 浅青色
        '#a5a5a5', // 灰色
        '#7b9a3b', // 橄榄绿
        '#c23531', // 深红色
        '#2f4554', // 深蓝色
        '#61a0a8', // 青绿色
        '#d48265', // 棕色
        '#749f83'  // 灰绿色
      ],
    }
  },
  computed: {
    statusTableData() {
      const statusMap = {
        0: '未处理',
        1: '已处理',
        2: '申诉中',
        3: '已撤销'
      }

      const total = this.statistics.total || 0
      return Object.keys(this.statistics.status).map(key => {
        const value = this.statistics.status[key] || 0
        const name = statusMap[key]
        return {
          name,
          value,
          percentage: total > 0 ? Math.round((value / total) * 100) : 0,
          color: this.colorMap[name]
        }
      }).sort((a, b) => b.value - a.value)
    },
    typeTableData() {
      const total = this.statistics.total || 0
      return Object.keys(this.statistics.types).map(key => {
        const value = this.statistics.types[key] || 0
        return {
          name: key,
          value,
          percentage: total > 0 ? Math.round((value / total) * 100) : 0
        }
      }).sort((a, b) => b.value - a.value)
    },
    locationTableData() {
      const total = this.statistics.total || 0
      return Object.keys(this.statistics.locations || {}).map(key => {
        const value = this.statistics.locations[key] || 0
        return {
          name: key,
          value,
          percentage: total > 0 ? Math.round((value / total) * 100) : 0
        }
      }).sort((a, b) => b.value - a.value)
    }
  },
  created() {
    this.fetchData()
  },
  mounted() {
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts)
    this.disposeCharts()
  },
  methods: {
    fetchData() {
      this.loading = true

      // 如果没有设置日期范围，默认使用过去一年的数据
      if (!this.dateRange || this.dateRange.length !== 2) {
        // 设置默认日期范围为过去一年
        const today = new Date()
        const oneYearAgo = new Date()
        oneYearAgo.setFullYear(today.getFullYear() - 1)

        const startDate = this.formatDate(oneYearAgo, 'yyyy-MM-dd')
        const endDate = this.formatDate(today, 'yyyy-MM-dd')

        // 更新日期范围选择器
        this.dateRange = [startDate, endDate]
      }

      const params = {
        start_date: this.dateRange[0],
        end_date: this.dateRange[1]
      }

      console.log('请求参数:', params)

      getViolationStatistics(params)
        .then(response => {
          this.statistics = response.data
          console.log('获取到的统计数据:', this.statistics)
          this.loading = false
          this.$nextTick(() => {
            this.initCharts()
          })
        })
        .catch(error => {
          console.error('获取统计数据失败:', error)
          this.$message.error('获取统计数据失败')
          this.loading = false
        })
    },
    refreshData() {
      this.fetchData()
      this.$notify({
        title: '成功',
        message: '数据已刷新',
        type: 'success'
      })
    },
    handleDateRangeChange(val) {
      this.dateRange = val
    },
    handleTimeUnitChange() {
      this.initTimeChart()
    },
    getRandomColor(key) {
      // 根据字符串生成一个稳定的颜色
      let hash = 0
      for (let i = 0; i < key.length; i++) {
        hash = key.charCodeAt(i) + ((hash << 5) - hash)
      }
      const color = '#' + ('00000' + (hash & 0x00FFFFFF).toString(16)).slice(-6)
      return color
    },
    initCharts() {
      this.disposeCharts()
      this.initStatusChart()
      this.initTypeChart()
      this.initLocationChart()
      this.initAppealChart()
      this.initTimeChart()
    },
    initStatusChart() {
      const statusData = [
        { name: '未处理', value: this.statistics.status[0] || 0, itemStyle: { color: this.colorMap['未处理'] } },
        { name: '已处理', value: this.statistics.status[1] || 0, itemStyle: { color: this.colorMap['已处理'] } },
        { name: '申诉中', value: this.statistics.status[2] || 0, itemStyle: { color: this.colorMap['申诉中'] } },
        { name: '已撤销', value: this.statistics.status[3] || 0, itemStyle: { color: this.colorMap['已撤销'] } }
      ]

      this.charts.statusChart = echarts.init(this.$refs.statusChart)
      this.charts.statusChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: statusData.map(item => item.name)
        },
        series: [
          {
            name: '违规状态',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: statusData
          }
        ]
      })
    },
    initTypeChart() {
      // 获取类型数据并按违规数量降序排序
      const sortedTypes = Object.keys(this.statistics.types).map(key => {
        return {
          name: key,
          value: this.statistics.types[key]
        }
      }).sort((a, b) => b.value - a.value)

      // 为每个类型分配颜色
      const typeData = sortedTypes.map((item, index) => {
        return {
          name: item.name,
          value: item.value,
          itemStyle: {
            // 使用预定义的颜色数组，如果超出范围则循环使用
            color: this.locationColors[index % this.locationColors.length]
          }
        }
      })

      if (typeData.length === 0) {
        if (this.$refs.typeChart) {
          this.$refs.typeChart.innerHTML = '<div class="no-data">暂无数据</div>'
        }
        return
      }

      this.charts.typeChart = echarts.init(this.$refs.typeChart)
      this.charts.typeChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: typeData.map(item => item.name),
          textStyle: {
            fontSize: 12
          },
          formatter: function(name) {
            // 如果名称太长，截断并添加省略号
            return name.length > 12 ? name.substring(0, 10) + '...' : name
          }
        },
        series: [
          {
            name: '违规类型',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: typeData
          }
        ],
        color: this.locationColors // 设置全局颜色方案
      })
    },
    initLocationChart() {
      // 获取地点数据并按违规数量降序排序
      const sortedLocations = Object.keys(this.statistics.locations || {})
        .map(key => ({
          name: key,
          value: this.statistics.locations[key]
        }))
        .sort((a, b) => b.value - a.value)

      // 为每个地点分配颜色
      const locationData = sortedLocations.map((item, index) => {
        return {
          name: item.name,
          value: item.value,
          itemStyle: {
            // 使用预定义的颜色数组，如果超出范围则循环使用
            color: this.locationColors[index % this.locationColors.length]
          }
        }
      })

      if (locationData.length === 0) {
        if (this.$refs.locationChart) {
          this.$refs.locationChart.innerHTML = '<div class="no-data">暂无数据</div>'
        }
        return
      }

      this.charts.locationChart = echarts.init(this.$refs.locationChart)
      this.charts.locationChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: locationData.map(item => item.name),
          textStyle: {
            fontSize: 12
          },
          formatter: function(name) {
            // 如果名称太长，截断并添加省略号
            return name.length > 12 ? name.substring(0, 10) + '...' : name
          }
        },
        series: [
          {
            name: '违规地点',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: locationData
          }
        ],
        color: this.locationColors // 设置全局颜色方案
      })
    },
    initAppealChart() {
      const appealMap = {
        0: '待审核',
        1: '已通过',
        2: '已拒绝'
      }

      const appealData = Object.keys(this.statistics.appeals || {}).map(key => {
        const name = appealMap[key]
        return {
          name,
          value: this.statistics.appeals[key],
          itemStyle: {
            color: this.colorMap[name]
          }
        }
      })

      if (appealData.length === 0) {
        if (this.$refs.appealChart) {
          this.$refs.appealChart.innerHTML = '<div class="no-data">暂无数据</div>'
        }
        return
      }

      this.charts.appealChart = echarts.init(this.$refs.appealChart)
      this.charts.appealChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: appealData.map(item => item.name)
        },
        series: [
          {
            name: '申诉状态',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: appealData
          }
        ]
      })
    },
    initTimeChart() {
      let timeData = {}
      let title = ''
      let subtitle = ''

      // 根据选择的时间单位获取数据
      if (this.timeUnit === 'day') {
        timeData = this.statistics.days || {}
        title = '日违规趋势'
        subtitle = '按天统计违规数量'
      } else if (this.timeUnit === 'week') {
        timeData = this.statistics.weeks || {}
        title = '周违规趋势'
        subtitle = '按周统计违规数量'
      } else {
        timeData = this.statistics.months || {}
        title = '月违规趋势'
        subtitle = '按月统计违规数量'
      }

      // 如果没有数据，显示无数据提示
      if (Object.keys(timeData).length === 0) {
        if (this.$refs.timeChart) {
          this.$refs.timeChart.innerHTML = '<div class="no-data">暂无数据</div>'
        }
        return
      }

      const times = Object.keys(timeData).sort()
      const data = times.map(time => timeData[time])

      // 计算数据的最大值和平均值
      const maxValue = Math.max(...data)
      const avgValue = data.reduce((sum, val) => sum + val, 0) / data.length

      // 计算数据的增长率
      let growthRate = 0
      if (data.length >= 2) {
        const firstValue = data[0]
        const lastValue = data[data.length - 1]
        if (firstValue > 0) {
          growthRate = ((lastValue - firstValue) / firstValue * 100).toFixed(2)
        }
      }

      this.charts.timeChart = echarts.init(this.$refs.timeChart)
      this.charts.timeChart.setOption({
        title: {
          text: title,
          subtext: subtitle,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const dataIndex = params[0].dataIndex
            const time = times[dataIndex]
            const value = data[dataIndex]
            return `${time}<br/>违规数量：${value}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: times,
          axisTick: {
            alignWithLabel: true
          },
          axisLabel: {
            interval: 'auto',
            formatter: function(value) {
              // 如果标签太长，截断并添加省略号
              return value.length > 10 ? value.substring(0, 8) + '...' : value
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '违规数量',
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '违规数量',
            type: 'bar',
            barWidth: '60%',
            data: data,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ])
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#2378f7' },
                  { offset: 0.7, color: '#2378f7' },
                  { offset: 1, color: '#83bff6' }
                ])
              }
            },
            markLine: {
              data: [
                { type: 'average', name: '平均值' }
              ],
              label: {
                formatter: '平均: {c}'
              },
              lineStyle: {
                color: '#5FB878'
              }
            },
            markPoint: {
              data: [
                { type: 'max', name: '最大值' }
              ],
              label: {
                formatter: '最大: {c}'
              }
            }
          },
          {
            name: '趋势线',
            type: 'line',
            smooth: true,
            showSymbol: false,
            data: data,
            lineStyle: {
              width: 2,
              color: '#FF9900'
            },
            itemStyle: {
              color: '#FF9900'
            }
          }
        ]
      })

      // 添加数据概要到图表下方
      const summaryEl = document.createElement('div')
      summaryEl.className = 'time-chart-summary'
      summaryEl.innerHTML = `
        <div class="summary-item">
          <span class="summary-label">总违规数：</span>
          <span class="summary-value">${data.reduce((sum, val) => sum + val, 0)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">平均每${this.timeUnit === 'day' ? '天' : this.timeUnit === 'week' ? '周' : '月'}：</span>
          <span class="summary-value">${avgValue.toFixed(1)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">增长率：</span>
          <span class="summary-value ${growthRate >= 0 ? 'positive' : 'negative'}">${growthRate >= 0 ? '+' : ''}${growthRate}%</span>
        </div>
      `

      // 先移除旧的概要元素
      const oldSummary = this.$refs.timeChart.parentNode.querySelector('.time-chart-summary')
      if (oldSummary) {
        oldSummary.remove()
      }

      // 添加新的概要元素
      this.$refs.timeChart.parentNode.appendChild(summaryEl)
    },

    // 日期格式化工具函数
    formatDate(date, fmt) {
      const o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
        'q+': Math.floor((date.getMonth() + 3) / 3),
        'S': date.getMilliseconds()
      }
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
      }
      for (const k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
          fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
        }
      }
      return fmt
    },
    resizeCharts() {
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.resize()
        }
      })
    },
    disposeCharts() {
      Object.keys(this.charts).forEach(key => {
        if (this.charts[key]) {
          this.charts[key].dispose()
          this.charts[key] = null
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;

    .dashboard-title {
      font-size: 22px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }

    .refresh-btn {
      font-size: 14px;
    }
  }

  .stat-cards {
    margin-bottom: 20px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      border-radius: 8px;
      transition: all 0.3s;
      overflow: hidden;
      position: relative;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      }

      .stat-card-icon {
        font-size: 36px;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
      }

      .stat-card-content {
        flex: 1;
      }

      .stat-card-title {
        font-size: 16px;
        margin-bottom: 10px;
        font-weight: 500;
      }

      .stat-card-value {
        font-size: 28px;
        font-weight: bold;
      }

      &.total-card {
        background: linear-gradient(135deg, #409EFF, #53a8ff);
        color: white;
      }

      &.pending-card {
        background: linear-gradient(135deg, #E6A23C, #f3b760);
        color: white;
      }

      &.appeal-card {
        background: linear-gradient(135deg, #67C23A, #85ce61);
        color: white;
      }

      &.cancel-card {
        background: linear-gradient(135deg, #F56C6C, #f78989);
        color: white;
      }
    }
  }

  .chart-container {
    margin-bottom: 20px;

    .chart-card {
      border-radius: 8px;
      transition: all 0.3s;
      background-color: white;

      &:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      }

      .chart {
        height: 300px;
      }

      .help-icon {
        margin-left: 5px;
        color: #909399;
        cursor: help;
      }

      &.status-chart-card {
        border-top: 3px solid #409EFF;
      }

      &.type-chart-card {
        border-top: 3px solid #67C23A;
      }

      &.appeal-chart-card {
        border-top: 3px solid #E6A23C;
      }

      &.location-chart-card {
        border-top: 3px solid #F56C6C;
      }
    }
  }

  .time-trend-card {
    margin-bottom: 20px;
    transition: all 0.3s;
    border-radius: 8px;
    background-color: white;
    border-top: 3px solid #409EFF;

    &:hover {
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .chart-controls {
      float: right;
      margin-right: 10px;
    }

    .time-chart {
      height: 400px;
    }
  }

  .time-chart-summary {
    display: flex;
    justify-content: space-around;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 0 0 8px 8px;
    margin-top: -10px;
    border-top: 1px solid #ebeef5;

    .summary-item {
      text-align: center;

      .summary-label {
        font-size: 14px;
        color: #606266;
        margin-right: 5px;
      }

      .summary-value {
        font-size: 16px;
        font-weight: bold;
        color: #303133;

        &.positive {
          color: #67C23A;
        }

        &.negative {
          color: #F56C6C;
        }
      }
    }
  }

  .no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    color: #909399;
    font-size: 16px;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .app-container {
    .dashboard-header {
      flex-direction: column;
      align-items: flex-start;

      .refresh-btn {
        margin-top: 10px;
      }
    }

    .chart-controls {
      float: none;
      margin-top: 10px;
      text-align: center;
    }

    .time-chart {
      height: 300px;
    }

    .time-chart-summary {
      flex-direction: column;

      .summary-item {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
