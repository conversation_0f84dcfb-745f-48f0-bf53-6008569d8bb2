<template>
  <div class="app-container">
    <el-card class="filter-container">
      <div class="filter-item">
        <el-input
          v-model="listQuery.name"
          placeholder="停车场名称"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select v-model="listQuery.campus" placeholder="校区" clearable style="width: 120px" class="filter-item">
          <el-option v-for="item in campusOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="listQuery.area" placeholder="区域" clearable style="width: 120px" class="filter-item">
          <el-option v-for="item in areaOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="listQuery.status" placeholder="运营状态" clearable style="width: 120px" class="filter-item">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="listQuery.availability" placeholder="充电车位" clearable style="width: 120px" class="filter-item">
          <el-option v-for="item in availabilityOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
          搜索
        </el-button>
        <el-button v-waves class="filter-item" type="info" icon="el-icon-refresh" @click="resetFilter">
          重置
        </el-button>
      </div>
    </el-card>

    <div v-loading="listLoading" class="parking-lots-container">
      <div v-if="filteredParkingLots.length === 0" class="empty-container">
        <Empty description="没有找到符合条件的停车场" />
      </div>
      <el-row :gutter="20">
        <el-col v-for="lot in filteredParkingLots" :key="lot.id" :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
          <el-card class="parking-lot-card" :body-style="{ padding: '0px' }">
            <div class="card-header">
              <span class="lot-name">{{ lot.name }}</span>
              <el-tag :type="lot.status === 1 ? 'success' : 'danger'" size="mini">
                {{ lot.status === 1 ? '正常运营' : '暂停使用' }}
              </el-tag>
            </div>
            <div class="card-tags">
              <el-tag type="info" size="mini">{{ lot.campus }}</el-tag>
              <el-tag type="info" size="mini">{{ lot.area }}</el-tag>
            </div>
            <div class="card-content">
              <div class="address">
                <i class="el-icon-location"></i>
                <span>{{ lot.address }}</span>
              </div>
              <div class="charging-info">
                <div class="charging-title">充电车位利用率</div>
                <el-progress
                  :percentage="lot.charging_utilization_rate"
                  :color="getProgressColor(lot.charging_utilization_rate)"
                />
                <div class="charging-stats">
                  <div class="stat-item">
                    <div class="stat-value">{{ lot.charging_spaces_total }}</div>
                    <div class="stat-label">总数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ lot.charging_spaces_occupied }}</div>
                    <div class="stat-label">已用</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ lot.charging_spaces_available }}</div>
                    <div class="stat-label">空闲</div>
                  </div>
                </div>
              </div>
              <div class="additional-info">
                <div class="info-item">
                  <i class="el-icon-time"></i>
                  <span>开放时间: {{ lot.opening_hours }}</span>
                </div>
                <div class="info-item">
                  <i class="el-icon-user"></i>
                  <span>管理员: {{ lot.manager }}</span>
                </div>
                <div class="info-item">
                  <i class="el-icon-phone"></i>
                  <span>联系电话: {{ lot.contact }}</span>
                </div>
              </div>
            </div>
            <div class="card-footer">
              <el-button type="primary" size="mini" @click="handleViewDetails(lot)">查看详情</el-button>
              <el-button
                type="success"
                size="mini"
                :disabled="lot.charging_spaces_available === 0 || lot.status !== 1"
                @click="handleStartCharging(lot)"
              >
                我要充电
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { getChargingParkingLots } from '@/api/charging'
import waves from '@/directive/waves'
import Empty from '@/components/Empty'

export default {
  name: 'ChargingLots',
  components: { Empty },
  directives: { waves },
  data() {
    return {
      listLoading: true,
      parkingLots: [],
      listQuery: {
        name: '',
        campus: '',
        area: '',
        status: '',
        availability: ''
      },
      campusOptions: ['主校区', '东校区', '南校区', '北校区', '西校区'],
      areaOptions: ['教学区', '宿舍区', '图书馆区', '食堂区', '体育区', '行政区'],
      statusOptions: [
        { label: '正常运营', value: 1 },
        { label: '暂停使用', value: 0 }
      ],
      availabilityOptions: [
        { label: '有空位', value: 'available' },
        { label: '即将满员', value: 'almost_full' },
        { label: '已满员', value: 'full' }
      ]
    }
  },
  computed: {
    filteredParkingLots() {
      let result = this.parkingLots

      // 按名称筛选
      if (this.listQuery.name) {
        const name = this.listQuery.name.toLowerCase()
        result = result.filter(lot => lot.name.toLowerCase().includes(name))
      }

      // 按校区筛选
      if (this.listQuery.campus) {
        result = result.filter(lot => lot.campus === this.listQuery.campus)
      }

      // 按区域筛选
      if (this.listQuery.area) {
        result = result.filter(lot => lot.area === this.listQuery.area)
      }

      // 按运营状态筛选
      if (this.listQuery.status !== '') {
        result = result.filter(lot => lot.status === this.listQuery.status)
      }

      // 按充电车位可用状态筛选
      if (this.listQuery.availability) {
        switch (this.listQuery.availability) {
          case 'available':
            result = result.filter(lot => lot.charging_spaces_available > 0)
            break
          case 'almost_full':
            result = result.filter(lot =>
              lot.charging_spaces_available > 0 &&
              lot.charging_utilization_rate >= 80
            )
            break
          case 'full':
            result = result.filter(lot => lot.charging_spaces_available === 0)
            break
        }
      }

      return result
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.listLoading = true
      getChargingParkingLots().then(response => {
        this.parkingLots = response.data.filter(lot => lot.charging_spaces_total > 0)
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleFilter() {
      // 触发筛选，由计算属性处理
    },
    resetFilter() {
      this.listQuery = {
        name: '',
        campus: '',
        area: '',
        status: '',
        availability: ''
      }
    },
    getProgressColor(percentage) {
      if (percentage < 70) return '#67c23a'
      if (percentage < 90) return '#e6a23c'
      return '#f56c6c'
    },
    handleViewDetails(lot) {
      this.$router.push({
        path: `/charging/lot-details/${lot.id}`,
        query: { from: 'charging-lots' }
      })
    },
    handleStartCharging(lot) {
      this.$router.push({
        path: `/charging/lot-details/${lot.id}`,
        query: { from: 'charging-lots', action: 'start-charging' }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
  .filter-item {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
  }
}

.parking-lots-container {
  margin-top: 20px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.parking-lot-card {
  margin-bottom: 20px;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
  }

  .card-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;

    .lot-name {
      font-size: 16px;
      font-weight: bold;
    }
  }

  .card-tags {
    padding: 10px 15px;
    display: flex;
    gap: 5px;
  }

  .card-content {
    padding: 15px;

    .address {
      display: flex;
      align-items: flex-start;
      margin-bottom: 15px;

      i {
        margin-right: 5px;
        margin-top: 3px;
      }
    }

    .charging-info {
      margin-bottom: 15px;

      .charging-title {
        font-weight: bold;
        margin-bottom: 10px;
      }

      .charging-stats {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;

        .stat-item {
          text-align: center;

          .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #409EFF;
          }

          .stat-label {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .additional-info {
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;

        i {
          margin-right: 5px;
          color: #909399;
        }

        span {
          font-size: 13px;
        }
      }
    }
  }

  .card-footer {
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    border-top: 1px solid #ebeef5;
  }
}
</style>
