<template>
  <div class="permission-wrapper">
    <!-- 如果用户有权限，显示原始内容 -->
    <template v-if="hasPermission">
      <slot></slot>
    </template>

    <!-- 如果用户没有权限，显示权限不足提示 -->
    <permission-denied
      v-else
      :title="title"
      :message="message"
    />
  </div>
</template>

<script>
import PermissionDenied from '@/components/PermissionDenied'
import permissionCheck from '@/mixins/permission-check'

export default {
  name: 'PermissionWrapper',
  components: {
    PermissionDenied
  },
  mixins: [permissionCheck],
  props: {
    // 允许访问的角色列表
    allowedRoles: {
      type: Array,
      default: () => ['admin']
    },
    // 权限不足时显示的标题
    title: {
      type: String,
      default: '权限不足'
    },
    // 权限不足时显示的消息
    message: {
      type: String,
      default: '您没有权限访问此页面的数据'
    }
  },
  computed: {
    // 检查用户是否有权限访问
    hasPermission() {
      console.log('PermissionWrapper - 检查权限:', this.allowedRoles)
      const result = this.checkPagePermission(this.allowedRoles)
      console.log('PermissionWrapper - 权限检查结果:', result)
      return result
    }
  },
  created() {
    console.log('PermissionWrapper - 组件创建, 允许角色:', this.allowedRoles)
    console.log('PermissionWrapper - 当前用户角色:', this.roles, '单一角色:', this.role)
  }
}
</script>

<style scoped>
.permission-wrapper {
  width: 100%;
  height: 100%;
}
</style>
