<template>
  <div class="app-container">
    <permission-wrapper :allowed-roles="['admin', 'security']" message="您没有权限访问充电统计数据，此功能仅对管理员和保安开放">
      <div class="dashboard-header">
        <h2>充电中心统计分析</h2>
        <div>
          <el-button type="primary" icon="el-icon-refresh" @click="fetchData">刷新数据</el-button>
        </div>
      </div>

      <!-- 统计概览卡片 -->
      <el-row v-loading="loading" :gutter="20" class="stat-cards">
        <el-col :xs="12" :sm="8" :md="6" :lg="4" v-for="(item, index) in overviewItems" :key="index">
          <el-card shadow="hover" class="stat-card" :body-style="{ padding: '15px' }">
            <div class="card-content">
              <div class="card-icon" :style="{ backgroundColor: item.color }">
                <i :class="item.icon"></i>
              </div>
              <div class="card-data">
                <div class="card-value">{{ item.value }}</div>
                <div class="card-label">{{ item.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 充电记录趋势图 -->
      <el-card v-loading="loading" shadow="hover" class="chart-card">
        <div slot="header" class="clearfix">
          <span>充电记录趋势</span>
          <el-radio-group v-model="trendChartType" size="mini" style="float: right;">
            <el-radio-button label="daily">日视图</el-radio-button>
            <el-radio-button label="weekly">周视图</el-radio-button>
            <el-radio-button label="monthly">月视图</el-radio-button>
          </el-radio-group>
        </div>
        <div class="chart-container">
          <div ref="dailyCountChart" class="chart"></div>
        </div>
      </el-card>

      <!-- 充电高峰时段分析 -->
      <el-card v-loading="loading" shadow="hover" class="chart-card">
        <div slot="header" class="clearfix">
          <span>充电高峰时段分析</span>
        </div>
        <div class="chart-container">
          <div ref="peakHoursChart" class="chart"></div>
        </div>
      </el-card>
    </permission-wrapper>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mapGetters } from 'vuex'
import { getParkingLots } from '@/api/parkinglot'
import PermissionWrapper from '@/components/PermissionWrapper'
import {
  getChargingStats,
  getChargingDailyStats
} from '@/api/charging'
import request from '@/utils/request'

export default {
  name: 'ChargingDashboard',
  components: {
    PermissionWrapper
  },
  data() {
    return {
      loading: true,
      trendChartType: 'daily',
      stats: {
        total_spaces: 0,
        active_charging: 0,
        today_count: 0
      },
      dailyStats: [],
      charts: {
        dailyCount: null,
        peakHours: null
      }
    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ]),
    isAdmin() {
      return this.roles && this.roles.includes('admin')
    },
    overviewItems() {
      return [
        {
          label: '充电车位总数',
          value: this.stats.total_spaces || 0,
          icon: 'el-icon-s-grid',
          color: '#409EFF'
        },
        {
          label: '正在充电',
          value: this.stats.active_charging || 0,
          icon: 'el-icon-lightning',
          color: '#67C23A'
        },
        {
          label: '今日充电次数',
          value: this.stats.today_count || 0,
          icon: 'el-icon-data-line',
          color: '#E6A23C'
        }
      ]
    }
  },
  watch: {
    trendChartType() {
      this.renderDailyChart()
    }
  },
  created() {
    this.fetchData()
  },
  mounted() {
    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.resizeCharts)

    // 销毁图表实例
    Object.keys(this.charts).forEach(key => {
      if (this.charts[key]) {
        this.charts[key].dispose()
      }
    })
  },
  methods: {
    // 获取所有数据
    fetchData() {
      this.loading = true
      console.log('开始获取充电统计数据')

      // 构建查询参数
      const params = {
        date_range: 'week', // 默认获取一周的数据
        _t: new Date().getTime() // 添加时间戳防止缓存
      }

      console.log('请求参数:', params)

      // 并行请求所有数据
      Promise.all([
        this.fetchStats(params),
        this.fetchDailyStats(params)
      ]).then(([statsResult, dailyStatsResult]) => {
        console.log('获取充电统计数据成功:', { statsResult, dailyStatsResult })
        this.loading = false
        this.$nextTick(() => {
          this.initCharts()
        })
      }).catch(error => {
        console.error('获取充电统计数据失败', error)
        this.$message.error('获取充电统计数据失败')
        this.loading = false
      })
    },

    // 获取停车场列表
    async fetchParkingLots() {
      try {
        const response = await getParkingLots()
        return response.data.items || []
      } catch (error) {
        console.error('获取停车场列表失败:', error)
        this.$message.error('获取停车场列表失败')
        return []
      }
    },

    // 获取充电统计数据
    async fetchStats(params) {
      try {
        const response = await getChargingStats(params)
        this.stats = response.data || {
          total_spaces: 0,
          active_charging: 0,
          today_count: 0
        }
        return this.stats
      } catch (error) {
        console.error('获取充电统计数据失败:', error)
        this.$message.error('获取充电统计数据失败')
        return null
      }
    },

    // 获取每日充电统计数据
    async fetchDailyStats(params) {
      try {
        const response = await getChargingDailyStats(params)
        console.log('每日充电统计API响应:', response)

        // 处理嵌套的数据结构
        if (response.data && response.data.data) {
          // 正确的数据结构
          this.dailyStats = response.data.data
        } else if (Array.isArray(response.data)) {
          // 兼容旧的数据结构
          this.dailyStats = response.data
        } else {
          console.warn('API返回的每日充电统计数据格式不正确:', response)
          this.dailyStats = []
        }

        console.log('处理后的每日充电统计数据:', this.dailyStats)
        return this.dailyStats
      } catch (error) {
        console.error('获取每日充电统计数据失败:', error)
        this.$message.error('获取每日充电统计数据失败，请检查后端API')
        this.dailyStats = []
        return this.dailyStats
      }
    },




    // 初始化所有图表
    initCharts() {
      this.initDailyChart()
      this.initPeakHoursChart()
    },

    // 初始化每日充电统计图表
    initDailyChart() {
      if (this.charts.dailyCount) {
        this.charts.dailyCount.dispose()
      }

      // 检查DOM元素是否存在
      if (!this.$refs.dailyCountChart) {
        console.warn('dailyCountChart DOM元素不存在，无法初始化图表')
        return
      }

      this.charts.dailyCount = echarts.init(this.$refs.dailyCountChart)
      this.renderDailyChart()
    },

    // 渲染每日充电统计图表
    renderDailyChart() {
      if (!this.charts.dailyCount) return

      const dailyStats = this.dailyStats || []
      console.log('渲染每日充电统计图表，数据:', dailyStats)

      // 检查是否有数据
      if (!dailyStats || dailyStats.length === 0) {
        console.warn('没有每日充电趋势数据')
        // 设置空数据图表
        this.charts.dailyCount.setOption({
          title: {
            text: '暂无充电记录数据',
            left: 'center',
            top: 'center'
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            minInterval: 1
          },
          series: [
            {
              type: 'bar',
              data: []
            }
          ]
        })
        return
      }

      // 检查数据是否全部为0
      const hasData = dailyStats.some(item => item.count > 0)
      if (!hasData) {
        console.warn('每日充电趋势数据全部为0')
      }

      // 根据视图类型处理数据
      let chartData = []
      let xAxisData = []

      if (this.trendChartType === 'daily') {
        // 日视图：直接使用每日数据
        chartData = dailyStats
        xAxisData = dailyStats.map(item => {
          const date = new Date(item.date)
          return `${date.getMonth() + 1}/${date.getDate()}`
        })
      } else if (this.trendChartType === 'weekly') {
        // 周视图：按周聚合数据
        const weeklyData = {}
        dailyStats.forEach(item => {
          const date = new Date(item.date)
          const weekNum = Math.floor(date.getDate() / 7) + 1
          const weekKey = `第${weekNum}周`

          if (!weeklyData[weekKey]) {
            weeklyData[weekKey] = { count: 0 }
          }

          weeklyData[weekKey].count += item.count || 0
        })

        xAxisData = Object.keys(weeklyData)
        chartData = xAxisData.map(key => ({
          date: key,
          ...weeklyData[key]
        }))
      } else if (this.trendChartType === 'monthly') {
        // 月视图：按月聚合数据
        const monthlyData = {}
        dailyStats.forEach(item => {
          const date = new Date(item.date)
          const monthKey = `${date.getMonth() + 1}月`

          if (!monthlyData[monthKey]) {
            monthlyData[monthKey] = { count: 0 }
          }

          monthlyData[monthKey].count += item.count || 0
        })

        xAxisData = Object.keys(monthlyData)
        chartData = xAxisData.map(key => ({
          date: key,
          ...monthlyData[key]
        }))
      }

      // 准备图表数据
      const counts = chartData.map(item => item.count || 0)

      // 设置图表选项
      const option = {
        title: {
          text: '充电记录趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          name: '充电次数',
          minInterval: 1
        },
        series: [
          {
            name: '充电次数',
            type: 'bar',
            data: counts,
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }

      this.charts.dailyCount.setOption(option)
    },



    // 初始化充电高峰时段分析图表
    initPeakHoursChart() {
      if (this.charts.peakHours) {
        this.charts.peakHours.dispose()
      }

      // 检查DOM元素是否存在
      if (!this.$refs.peakHoursChart) {
        console.warn('peakHoursChart DOM元素不存在，无法初始化图表')
        return
      }

      this.charts.peakHours = echarts.init(this.$refs.peakHoursChart)

      // 请求参数
      const params = {
        date_range: 'week',
        _t: new Date().getTime() // 添加时间戳防止缓存
      }

      console.log('请求充电高峰时段数据，参数:', params)

      // 请求按小时分布的充电记录数据
      request({
        url: '/api/charging-hourly-stats',
        method: 'get',
        params: params
      }).then(response => {
        console.log('获取充电高峰时段数据成功:', response)

        // 处理嵌套的数据结构
        let hourlyData = null

        // 检查响应数据格式
        if (response.data && response.data.data) {
          // 正确的数据结构
          hourlyData = response.data.data
        } else if (Array.isArray(response.data)) {
          // 兼容旧的数据结构
          hourlyData = response.data
        } else {
          console.warn('API返回的充电高峰时段数据格式不正确:', response.data)
          this.renderPeakHoursChartWithEmptyData()
          return
        }

        if (!hourlyData || hourlyData.length === 0) {
          console.warn('API返回的充电高峰时段数据为空')
          this.renderPeakHoursChartWithEmptyData()
          return
        }

        // 检查数据是否全部为0
        const hasData = hourlyData.some(item => item.count > 0)
        if (!hasData) {
          console.warn('API返回的充电高峰时段数据全部为0')
        }

        // 准备图表数据
        const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`)
        const counts = Array(24).fill(0)

        // 填充实际数据
        hourlyData.forEach(item => {
          if (item.hour >= 0 && item.hour < 24) {
            counts[item.hour] = item.count
          }
        })

        // 找出高峰时段
        const maxCount = Math.max(...counts)

        // 设置图表选项
        const option = {
          title: {
            text: '充电高峰时段分析',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: hours,
            axisLabel: {
              interval: 1,
              rotate: 45
            }
          },
          yAxis: {
            type: 'value',
            name: '充电次数',
            minInterval: 1
          },
          series: [
            {
              name: '充电次数',
              type: 'bar',
              data: counts,
              itemStyle: {
                color: params => {
                  const value = params.value
                  return value > (maxCount * 0.7) ? '#F56C6C' : '#409EFF'
                }
              },
              markLine: {
                data: [
                  { type: 'average', name: '平均值' }
                ]
              }
            }
          ]
        }

        this.charts.peakHours.setOption(option)
      }).catch(error => {
        console.error('获取充电高峰时段数据失败', error)
        this.renderPeakHoursChartWithEmptyData()
      })
    },

    // 渲染空的高峰时段图表
    renderPeakHoursChartWithEmptyData() {
      if (!this.charts.peakHours) return

      const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`)

      const option = {
        title: {
          text: '充电高峰时段分析',
          left: 'center',
          subtext: '暂无数据'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: hours,
          axisLabel: {
            interval: 2,
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          name: '充电次数',
          minInterval: 1
        },
        series: [
          {
            name: '充电次数',
            type: 'bar',
            data: Array(24).fill(0),
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }

      this.charts.peakHours.setOption(option)
    },

    // 调整图表大小
    resizeCharts() {
      Object.keys(this.charts).forEach(key => {
        if (this.charts[key]) {
          this.charts[key].resize()
        }
      })
    },

    // 获取进度条颜色
    getProgressColor(value) {
      if (value < 30) {
        return '#67C23A'
      } else if (value < 70) {
        return '#E6A23C'
      } else {
        return '#F56C6C'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    font-size: 20px;
  }
}

.stat-cards {
  margin-bottom: 20px;
}

.stat-card {
  margin-bottom: 15px;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .card-content {
    display: flex;
    align-items: center;

    .card-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        color: white;
        font-size: 20px;
      }
    }

    .card-data {
      flex: 1;

      .card-value {
        font-size: 24px;
        font-weight: bold;
        line-height: 1.2;
      }

      .card-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

// 响应式调整
@media (max-width: 768px) {
  .chart-container {
    height: 300px;
  }

  .stat-card {
    .card-content {
      .card-data {
        .card-value {
          font-size: 20px;
        }
      }
    }
  }
}
</style>
