2025/05/07 00:00:24 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:00:24 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:00:49 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:00:49 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:01:14 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:01:14 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:01:39 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:01:39 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:02:04 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:02:04 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:02:29 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:02:29 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:02:54 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:02:54 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:03:19 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:03:19 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:03:44 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:03:44 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:04:09 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:04:09 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:04:34 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:04:34 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:04:59 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:04:59 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:05:24 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:05:24 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:05:49 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:05:49 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:06:14 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:06:14 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:06:39 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:06:39 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:07:04 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:07:04 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:07:29 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:07:29 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:07:54 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:07:54 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:08:19 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:08:19 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:08:44 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:08:44 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:09:09 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:09:09 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:09:34 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:09:34 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:09:59 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:09:59 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:10:24 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:10:24 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:10:49 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:10:49 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:11:14 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:11:14 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:11:39 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:11:39 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:12:04 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:12:04 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:12:29 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:12:29 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:12:54 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:12:54 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:13:19 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:13:19 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:13:44 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:13:44 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:14:09 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:14:09 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:14:34 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:14:34 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:14:59 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:14:59 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:15:24 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:15:24 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:15:49 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:15:49 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:16:14 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:16:14 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:16:39 engineio.server socket.py[78] send() INFO: 7JJXaRF7sOGMPfMnAAAE: Sending packet PING data None
2025/05/07 00:16:39 engineio.server socket.py[39] receive() INFO: 7JJXaRF7sOGMPfMnAAAE: Received packet PONG data 
2025/05/07 00:16:52 flask_api routes.py[60] handle_disconnect() INFO: Client disconnected
2025/05/07 00:30:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 00:30:13] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 00:30:13 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 00:30:13 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 00:30:13 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 2640s ago] (6,)
2025/05/07 00:30:13 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 00:30:13 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 2640s ago] (6,)
2025/05/07 00:30:13 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 00:30:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 00:30:13] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/05/07 00:32:36 flask_api __init__.py[81] create_app() INFO: Flask Rest Api startup
2025/05/07 00:32:36 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 00:32:36 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 00:32:36 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00096s] ()
2025/05/07 00:32:36 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 00:32:36 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/05/07 00:32:36 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/05/07 00:32:36 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/05/07 00:32:39 flask_api __init__.py[81] create_app() INFO: Flask Rest Api startup
2025/05/07 00:32:40 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 00:32:40 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 00:32:40 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00105s] ()
2025/05/07 00:32:40 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 00:32:40 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/05/07 00:32:40 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/05/07 13:15:58 flask_api __init__.py[81] create_app() INFO: Flask Rest Api startup
2025/05/07 13:15:58 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/05/07 13:15:58 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/05/07 13:18:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:18:50] "OPTIONS /api/login HTTP/1.1" 200 -
2025/05/07 13:18:50 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:18:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?
2025/05/07 13:18:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00094s] ('admin', 1, 0)
2025/05/07 13:18:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_name = ?
 LIMIT ? OFFSET ?
2025/05/07 13:18:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00098s] ('admin', 1, 0)
2025/05/07 13:18:50 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:18:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:18:50] "POST /api/login HTTP/1.1" 200 -
2025/05/07 13:18:50 engineio.server socket.py[78] send() INFO: RiaF64yhbt8lJSa8AAAA: Sending packet OPEN data {'sid': 'RiaF64yhbt8lJSa8AAAA', 'upgrades': [], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025/05/07 13:18:50 engineio.server socket.py[90] handle_get_request() INFO: RiaF64yhbt8lJSa8AAAA: Received request to upgrade to websocket
2025/05/07 13:18:50 engineio.server socket.py[219] _websocket_handler() INFO: RiaF64yhbt8lJSa8AAAA: Upgrade to websocket successful
2025/05/07 13:18:51 engineio.server socket.py[78] send() INFO: tzq--Q-mMxq6Pa_BAAAB: Sending packet OPEN data {'sid': 'tzq--Q-mMxq6Pa_BAAAB', 'upgrades': [], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025/05/07 13:18:51 engineio.server socket.py[90] handle_get_request() INFO: tzq--Q-mMxq6Pa_BAAAB: Received request to upgrade to websocket
2025/05/07 13:18:51 engineio.server socket.py[219] _websocket_handler() INFO: tzq--Q-mMxq6Pa_BAAAB: Upgrade to websocket successful
2025/05/07 13:18:51 engineio.server socket.py[39] receive() INFO: tzq--Q-mMxq6Pa_BAAAB: Received packet MESSAGE data 0
2025/05/07 13:18:51 engineio.server socket.py[39] receive() INFO: RiaF64yhbt8lJSa8AAAA: Received packet MESSAGE data 0
2025/05/07 13:18:51 flask_api routes.py[45] handle_connect() INFO: Client connected with user_id: 1
2025/05/07 13:18:51 flask_api routes.py[45] handle_connect() INFO: Client connected with user_id: 1
2025/05/07 13:18:51 socketio.server server.py[164] emit() INFO: emitting event "connection_response" to 2Fpa9t5fJP5bFUlMAAAC [/]
2025/05/07 13:18:51 socketio.server server.py[164] emit() INFO: emitting event "connection_response" to yQDJciykI8LsQu7RAAAD [/]
2025/05/07 13:18:51 engineio.server socket.py[78] send() INFO: tzq--Q-mMxq6Pa_BAAAB: Sending packet MESSAGE data 2["connection_response",{"status":"connected","authenticated":true}]
2025/05/07 13:18:51 engineio.server socket.py[78] send() INFO: RiaF64yhbt8lJSa8AAAA: Sending packet MESSAGE data 2["connection_response",{"status":"connected","authenticated":true}]
2025/05/07 13:18:51 engineio.server socket.py[78] send() INFO: tzq--Q-mMxq6Pa_BAAAB: Sending packet MESSAGE data 0{"sid":"2Fpa9t5fJP5bFUlMAAAC"}
2025/05/07 13:18:51 engineio.server socket.py[78] send() INFO: RiaF64yhbt8lJSa8AAAA: Sending packet MESSAGE data 0{"sid":"yQDJciykI8LsQu7RAAAD"}
2025/05/07 13:18:51 engineio.server socket.py[39] receive() INFO: tzq--Q-mMxq6Pa_BAAAB: Received packet MESSAGE data 2["join",{"room":"user_1"}]
2025/05/07 13:18:51 socketio.server server.py[576] _handle_event() INFO: received event "join" from 2Fpa9t5fJP5bFUlMAAAC [/]
2025/05/07 13:18:51 socketio.server server.py[284] enter_room() INFO: 2Fpa9t5fJP5bFUlMAAAC is entering room user_1 [/]
2025/05/07 13:18:51 engineio.server socket.py[39] receive() INFO: tzq--Q-mMxq6Pa_BAAAB: Received packet MESSAGE data 2["join",{"room":"user_1"}]
2025/05/07 13:18:51 socketio.server server.py[576] _handle_event() INFO: received event "join" from 2Fpa9t5fJP5bFUlMAAAC [/]
2025/05/07 13:18:51 flask_api routes.py[83] on_join() INFO: 用户 1 加入房间: user_1
2025/05/07 13:18:51 socketio.server server.py[284] enter_room() INFO: 2Fpa9t5fJP5bFUlMAAAC is entering room admin [/]
2025/05/07 13:18:51 socketio.server server.py[284] enter_room() INFO: 2Fpa9t5fJP5bFUlMAAAC is entering room user_1 [/]
2025/05/07 13:18:51 flask_api routes.py[91] on_join() INFO: 管理员 1 加入管理员房间
2025/05/07 13:18:51 socketio.server server.py[284] enter_room() INFO: 2Fpa9t5fJP5bFUlMAAAC is entering room user_1 [/]
2025/05/07 13:18:51 flask_api routes.py[83] on_join() INFO: 用户 1 加入房间: user_1
2025/05/07 13:18:51 socketio.server server.py[284] enter_room() INFO: 2Fpa9t5fJP5bFUlMAAAC is entering room admin [/]
2025/05/07 13:18:51 flask_api routes.py[95] on_join() INFO: 用户 1 加入房间: user_1
2025/05/07 13:18:51 socketio.server server.py[164] emit() INFO: emitting event "join_response" to 2Fpa9t5fJP5bFUlMAAAC [/]
2025/05/07 13:18:51 flask_api routes.py[91] on_join() INFO: 管理员 1 加入管理员房间
2025/05/07 13:18:51 engineio.server socket.py[78] send() INFO: tzq--Q-mMxq6Pa_BAAAB: Sending packet MESSAGE data 2["join_response",{"status":"success","room":"user_1"}]
2025/05/07 13:18:51 socketio.server server.py[284] enter_room() INFO: 2Fpa9t5fJP5bFUlMAAAC is entering room user_1 [/]
2025/05/07 13:18:51 flask_api routes.py[95] on_join() INFO: 用户 1 加入房间: user_1
2025/05/07 13:18:51 socketio.server server.py[164] emit() INFO: emitting event "join_response" to 2Fpa9t5fJP5bFUlMAAAC [/]
2025/05/07 13:18:51 engineio.server socket.py[78] send() INFO: tzq--Q-mMxq6Pa_BAAAB: Sending packet MESSAGE data 2["join_response",{"status":"success","room":"user_1"}]
2025/05/07 13:18:51 flask_api routes.py[60] handle_disconnect() INFO: Client disconnected
2025/05/07 13:18:51 flask_api routes.py[60] handle_disconnect() INFO: Client disconnected
2025/05/07 13:18:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:18:51] "[35m[1mGET /socket.io/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NjU5NTEzMCwianRpIjoiZDFkMTU4NWEtMzdlMS00OWFjLWIxZjMtOTRjNjkwZTM2OTk5IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5IjoxLCJuYmYiOjE3NDY1OTUxMzAsImNzcmYiOiIyN2E2MzMxNC05MTY1LTQwZDAtOWJiMS03ZjgyNjliZjNkMzgiLCJleHAiOjE3NDY2ODE1MzAsInJvbGUiOiJhZG1pbiIsInN1YiI6IjEifQ.PSP4zVlHxmxHo5iOWLNl4pXcx5O70jDmQQ2kVq9e7D4&EIO=4&transport=websocket HTTP/1.1[0m" 500 -
2025/05/07 13:18:51 werkzeug _internal.py[97] _log() ERROR: Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
    ~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 336, in execute
    write(b"")
    ~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 261, in write
    assert status_set is not None, "write() before start_response"
           ^^^^^^^^^^^^^^^^^^^^^^
AssertionError: write() before start_response
2025/05/07 13:18:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:18:51] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:18:51 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:18:51 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:18:51 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00108s] (1,)
2025/05/07 13:18:51 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:18:51 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.004826s ago] (1,)
2025/05/07 13:18:51 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:18:51 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00106s] (1,)
2025/05/07 13:18:51 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:18:51 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00095s] (1,)
2025/05/07 13:18:51 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:18:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:18:51] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:18:51 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet OPEN data {'sid': 'KIIC3A6TM0jV5N_mAAAE', 'upgrades': [], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025/05/07 13:18:51 engineio.server socket.py[90] handle_get_request() INFO: KIIC3A6TM0jV5N_mAAAE: Received request to upgrade to websocket
2025/05/07 13:18:51 engineio.server socket.py[219] _websocket_handler() INFO: KIIC3A6TM0jV5N_mAAAE: Upgrade to websocket successful
2025/05/07 13:18:52 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet MESSAGE data 0
2025/05/07 13:18:52 flask_api routes.py[45] handle_connect() INFO: Client connected with user_id: 1
2025/05/07 13:18:52 socketio.server server.py[164] emit() INFO: emitting event "connection_response" to L2GV2DqTcD8FURIzAAAF [/]
2025/05/07 13:18:52 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet MESSAGE data 2["connection_response",{"status":"connected","authenticated":true}]
2025/05/07 13:18:52 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet MESSAGE data 0{"sid":"L2GV2DqTcD8FURIzAAAF"}
2025/05/07 13:18:52 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet MESSAGE data 2["join",{"room":"user_1"}]
2025/05/07 13:18:52 socketio.server server.py[576] _handle_event() INFO: received event "join" from L2GV2DqTcD8FURIzAAAF [/]
2025/05/07 13:18:52 socketio.server server.py[284] enter_room() INFO: L2GV2DqTcD8FURIzAAAF is entering room user_1 [/]
2025/05/07 13:18:52 flask_api routes.py[83] on_join() INFO: 用户 1 加入房间: user_1
2025/05/07 13:18:52 socketio.server server.py[284] enter_room() INFO: L2GV2DqTcD8FURIzAAAF is entering room admin [/]
2025/05/07 13:18:52 flask_api routes.py[91] on_join() INFO: 管理员 1 加入管理员房间
2025/05/07 13:18:52 socketio.server server.py[284] enter_room() INFO: L2GV2DqTcD8FURIzAAAF is entering room user_1 [/]
2025/05/07 13:18:52 flask_api routes.py[95] on_join() INFO: 用户 1 加入房间: user_1
2025/05/07 13:18:52 socketio.server server.py[164] emit() INFO: emitting event "join_response" to L2GV2DqTcD8FURIzAAAF [/]
2025/05/07 13:18:52 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet MESSAGE data 2["join_response",{"status":"success","room":"user_1"}]
2025/05/07 13:19:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:19:04] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:19:04 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:19:04 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:19:04 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 12.59s ago] (1,)
2025/05/07 13:19:04 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:19:04 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 12.6s ago] (1,)
2025/05/07 13:19:04 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:19:04 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 12.59s ago] (1,)
2025/05/07 13:19:04 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:19:04 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 12.58s ago] (1,)
2025/05/07 13:19:04 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:19:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:19:04] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:19:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:19:05] "OPTIONS /api/bikes?page=1&limit=10&user_id=1 HTTP/1.1" 200 -
2025/05/07 13:19:05 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:19:05 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:19:05 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 13.27s ago] (1,)
2025/05/07 13:19:05 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.belong_to = ?
2025/05/07 13:19:05 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00094s] (1,)
2025/05/07 13:19:05 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:19:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:19:05] "GET /api/bikes?page=1&limit=10&user_id=1 HTTP/1.1" 200 -
2025/05/07 13:19:16 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:19:16 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:19:41 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:19:41 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:20:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:06] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:20:06 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 74.48s ago] (1,)
2025/05/07 13:20:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 74.48s ago] (1,)
2025/05/07 13:20:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:20:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 74.47s ago] (1,)
2025/05/07 13:20:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:20:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 74.47s ago] (1,)
2025/05/07 13:20:06 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:06] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:20:06 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:20:06 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:20:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:07] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/05/07 13:20:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:07] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 75.4s ago] (1,)
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00094s] ()
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:07] "GET /api/bikes HTTP/1.1" 200 -
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 75.42s ago] (1,)
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes) AS anon_1
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00148s] ()
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.status = ?) AS anon_1
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00126s] ('可用',)
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.status = ?) AS anon_1
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.005783s ago] ('废弃',)
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.brand AS bikes_brand, count(bikes.b_id) AS count_1 
FROM bikes GROUP BY bikes.brand
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00109s] ()
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_type AS bikes_b_type, count(bikes.b_id) AS count_1 
FROM bikes GROUP BY bikes.b_type
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00083s] ()
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.color AS bikes_color, count(bikes.b_id) AS count_1 
FROM bikes GROUP BY bikes.color
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00085s] ()
2025/05/07 13:20:07 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:07] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/05/07 13:20:31 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:20:31 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:20:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:42] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:20:42 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 110.6s ago] (1,)
2025/05/07 13:20:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 110.6s ago] (1,)
2025/05/07 13:20:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:20:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 110.6s ago] (1,)
2025/05/07 13:20:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:20:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 110.6s ago] (1,)
2025/05/07 13:20:42 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:42] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "OPTIONS /api/parkinglots?page=1&limit=8&search=&status=&campus=&area=&availableSpaces=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "OPTIONS /api/parkinglots?page=1&limit=8&search=&status=&campus=&area=&availableSpaces=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 111.4s ago] (1,)
2025/05/07 13:20:43 flask_api routes.py[22] get_all_parkinglots() INFO: 开始处理获取停车场列表请求
2025/05/07 13:20:43 flask_api routes.py[32] get_all_parkinglots() INFO: 请求参数: page=1, limit=8, search='', status=
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00143s] ()
2025/05/07 13:20:43 flask_api routes.py[36] get_all_parkinglots() INFO: 数据库中停车场总数: 4
2025/05/07 13:20:43 flask_api routes.py[109] get_all_parkinglots() WARNING: 无效的状态值: 
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots ORDER BY parking_lots.id ASC) AS anon_1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00135s] ()
2025/05/07 13:20:43 flask_api routes.py[122] get_all_parkinglots() INFO: 过滤后但分页前的记录数: 4
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots ORDER BY parking_lots.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00109s] (8, 0)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.02515s ago] ()
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00140s] (1, 2)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.005439s ago] (2, 2)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.009046s ago] (3, 2)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.01187s ago] (4, 2)
2025/05/07 13:20:43 flask_api routes.py[129] get_all_parkinglots() INFO: 获取到 4 条停车场数据
2025/05/07 13:20:43 flask_api routes.py[143] get_all_parkinglots() INFO: 返回停车场数据: 4 条记录
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "GET /api/parkinglots?page=1&limit=8&search=&status=&campus=&area=&availableSpaces=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 111.5s ago] (1,)
2025/05/07 13:20:43 flask_api routes.py[22] get_all_parkinglots() INFO: 开始处理获取停车场列表请求
2025/05/07 13:20:43 flask_api routes.py[32] get_all_parkinglots() INFO: 请求参数: page=1, limit=8, search='', status=
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.06122s ago] ()
2025/05/07 13:20:43 flask_api routes.py[36] get_all_parkinglots() INFO: 数据库中停车场总数: 4
2025/05/07 13:20:43 flask_api routes.py[109] get_all_parkinglots() WARNING: 无效的状态值: 
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots ORDER BY parking_lots.id ASC) AS anon_1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.05403s ago] ()
2025/05/07 13:20:43 flask_api routes.py[122] get_all_parkinglots() INFO: 过滤后但分页前的记录数: 4
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots ORDER BY parking_lots.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.05061s ago] (8, 0)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.07221s ago] ()
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.04456s ago] (1, 2)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.04879s ago] (2, 2)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.05341s ago] (3, 2)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.05673s ago] (4, 2)
2025/05/07 13:20:43 flask_api routes.py[129] get_all_parkinglots() INFO: 获取到 4 条停车场数据
2025/05/07 13:20:43 flask_api routes.py[143] get_all_parkinglots() INFO: 返回停车场数据: 4 条记录
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "GET /api/parkinglots?page=1&limit=8&search=&status=&campus=&area=&availableSpaces=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "OPTIONS /api/parkinglots/1/spaces?type=1&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "OPTIONS /api/parkinglots/1/spaces?type=2&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "OPTIONS /api/parkinglots/1/spaces?type=3&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 111.7s ago] (1,)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 111.8s ago] (1,)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 111.8s ago] (1,)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00175s] (1,)
2025/05/07 13:20:43 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=20, status=0, type=1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.007683s ago] (1,)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.008894s ago] (1,)
2025/05/07 13:20:43 flask_api routes.py[394] get_parking_spaces() INFO: 按状态过滤: 0
2025/05/07 13:20:43 flask_api routes.py[403] get_parking_spaces() INFO: 按车位类型过滤: 1
2025/05/07 13:20:43 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=20, status=0, type=2
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:43 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=20, status=0, type=3
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00257s] (1, 0, 1, 20, 0)
2025/05/07 13:20:43 flask_api routes.py[394] get_parking_spaces() INFO: 按状态过滤: 0
2025/05/07 13:20:43 flask_api routes.py[394] get_parking_spaces() INFO: 按状态过滤: 0
2025/05/07 13:20:43 flask_api routes.py[403] get_parking_spaces() INFO: 按车位类型过滤: 2
2025/05/07 13:20:43 flask_api routes.py[403] get_parking_spaces() INFO: 按车位类型过滤: 3
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ?) AS anon_1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.01787s ago] (1, 0, 2, 20, 0)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00298s] (1, 0, 1)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.01916s ago] (1, 0, 3, 20, 0)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ?) AS anon_1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "GET /api/parkinglots/1/spaces?type=1&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.009053s ago] (1, 0, 2)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ?) AS anon_1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.01241s ago] (1, 0, 3)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "GET /api/parkinglots/1/spaces?type=2&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "GET /api/parkinglots/1/spaces?type=3&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "OPTIONS /api/parkinglots/2/spaces?type=1&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "OPTIONS /api/parkinglots/2/spaces?type=2&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "OPTIONS /api/parkinglots/2/spaces?type=3&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 112s ago] (1,)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 112s ago] (1,)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 112s ago] (1,)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.2382s ago] (2,)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.2386s ago] (2,)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.2397s ago] (2,)
2025/05/07 13:20:43 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=20, status=0, type=1
2025/05/07 13:20:43 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=20, status=0, type=2
2025/05/07 13:20:43 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=20, status=0, type=3
2025/05/07 13:20:43 flask_api routes.py[394] get_parking_spaces() INFO: 按状态过滤: 0
2025/05/07 13:20:43 flask_api routes.py[394] get_parking_spaces() INFO: 按状态过滤: 0
2025/05/07 13:20:43 flask_api routes.py[394] get_parking_spaces() INFO: 按状态过滤: 0
2025/05/07 13:20:43 flask_api routes.py[403] get_parking_spaces() INFO: 按车位类型过滤: 1
2025/05/07 13:20:43 flask_api routes.py[403] get_parking_spaces() INFO: 按车位类型过滤: 2
2025/05/07 13:20:43 flask_api routes.py[403] get_parking_spaces() INFO: 按车位类型过滤: 3
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.2385s ago] (2, 0, 1, 20, 0)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.2391s ago] (2, 0, 2, 20, 0)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.2395s ago] (2, 0, 3, 20, 0)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ?) AS anon_1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ?) AS anon_1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ?) AS anon_1
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.23s ago] (2, 0, 2)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.2306s ago] (2, 0, 1)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.2313s ago] (2, 0, 3)
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "GET /api/parkinglots/2/spaces?type=2&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:43 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "GET /api/parkinglots/2/spaces?type=1&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:43] "GET /api/parkinglots/2/spaces?type=3&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:44] "OPTIONS /api/parkinglots/3/spaces?type=1&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:44] "OPTIONS /api/parkinglots/3/spaces?type=2&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:44] "OPTIONS /api/parkinglots/3/spaces?type=3&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 112.2s ago] (1,)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 112.2s ago] (1,)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 112.2s ago] (1,)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.4075s ago] (3,)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.4081s ago] (3,)
2025/05/07 13:20:44 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=20, status=0, type=1
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:20:44 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=20, status=0, type=2
2025/05/07 13:20:44 flask_api routes.py[394] get_parking_spaces() INFO: 按状态过滤: 0
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.4149s ago] (3,)
2025/05/07 13:20:44 flask_api routes.py[394] get_parking_spaces() INFO: 按状态过滤: 0
2025/05/07 13:20:44 flask_api routes.py[403] get_parking_spaces() INFO: 按车位类型过滤: 1
2025/05/07 13:20:44 flask_api routes.py[403] get_parking_spaces() INFO: 按车位类型过滤: 2
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:44 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=20, status=0, type=3
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.4086s ago] (3, 0, 1, 20, 0)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.4098s ago] (3, 0, 2, 20, 0)
2025/05/07 13:20:44 flask_api routes.py[394] get_parking_spaces() INFO: 按状态过滤: 0
2025/05/07 13:20:44 flask_api routes.py[403] get_parking_spaces() INFO: 按车位类型过滤: 3
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ?) AS anon_1
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ?) AS anon_1
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.4016s ago] (3, 0, 2)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.4022s ago] (3, 0, 1)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.4187s ago] (3, 0, 3, 20, 0)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:44] "GET /api/parkinglots/3/spaces?type=2&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:44] "GET /api/parkinglots/3/spaces?type=1&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ?) AS anon_1
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.4124s ago] (3, 0, 3)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:44] "GET /api/parkinglots/3/spaces?type=3&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:44] "OPTIONS /api/parkinglots/4/spaces?type=1&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:44] "OPTIONS /api/parkinglots/4/spaces?type=2&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:44] "OPTIONS /api/parkinglots/4/spaces?type=3&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 112.4s ago] (1,)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 112.4s ago] (1,)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 112.4s ago] (1,)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.608s ago] (4,)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.6086s ago] (4,)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.6092s ago] (4,)
2025/05/07 13:20:44 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=20, status=0, type=1
2025/05/07 13:20:44 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=20, status=0, type=2
2025/05/07 13:20:44 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=20, status=0, type=3
2025/05/07 13:20:44 flask_api routes.py[394] get_parking_spaces() INFO: 按状态过滤: 0
2025/05/07 13:20:44 flask_api routes.py[394] get_parking_spaces() INFO: 按状态过滤: 0
2025/05/07 13:20:44 flask_api routes.py[394] get_parking_spaces() INFO: 按状态过滤: 0
2025/05/07 13:20:44 flask_api routes.py[403] get_parking_spaces() INFO: 按车位类型过滤: 1
2025/05/07 13:20:44 flask_api routes.py[403] get_parking_spaces() INFO: 按车位类型过滤: 2
2025/05/07 13:20:44 flask_api routes.py[403] get_parking_spaces() INFO: 按车位类型过滤: 3
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.6085s ago] (4, 0, 1, 20, 0)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.609s ago] (4, 0, 2, 20, 0)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.6098s ago] (4, 0, 3, 20, 0)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ?) AS anon_1
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ?) AS anon_1
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ? AND parking_spaces.type = ?) AS anon_1
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.6007s ago] (4, 0, 2)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.6013s ago] (4, 0, 3)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.6022s ago] (4, 0, 1)
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:44] "GET /api/parkinglots/4/spaces?type=2&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:44] "GET /api/parkinglots/4/spaces?type=3&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:44 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:20:44] "GET /api/parkinglots/4/spaces?type=1&status=0 HTTP/1.1" 200 -
2025/05/07 13:20:56 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:20:56 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:21:21 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:21:22 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:21:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:21:39] "OPTIONS /api/bikes?_t=1746595299541&limit=100&belong_to=1 HTTP/1.1" 200 -
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 167.7s ago] (1,)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.belong_to = ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 154.4s ago] (1,)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:21:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:21:39] "GET /api/bikes?_t=1746595299541&limit=100&belong_to=1 HTTP/1.1" 200 -
2025/05/07 13:21:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:21:39] "OPTIONS /api/parking-records/user?status=0&per_page=100 HTTP/1.1" 200 -
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 167.8s ago] (1,)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_records.id AS parking_records_id, parking_records.vehicle_id AS parking_records_vehicle_id, parking_records.user_id AS parking_records_user_id, parking_records.parking_lot_id AS parking_records_parking_lot_id, parking_records.parking_space_id AS parking_records_parking_space_id, parking_records.entry_time AS parking_records_entry_time, parking_records.exit_time AS parking_records_exit_time, parking_records.status AS parking_records_status, parking_records.created_at AS parking_records_created_at, parking_records.updated_at AS parking_records_updated_at, parking_records.remarks AS parking_records_remarks 
FROM parking_records 
WHERE parking_records.user_id = ? AND parking_records.status = ? ORDER BY parking_records.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00158s] (1, 0, 100, 0)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_records.id AS parking_records_id, parking_records.vehicle_id AS parking_records_vehicle_id, parking_records.user_id AS parking_records_user_id, parking_records.parking_lot_id AS parking_records_parking_lot_id, parking_records.parking_space_id AS parking_records_parking_space_id, parking_records.entry_time AS parking_records_entry_time, parking_records.exit_time AS parking_records_exit_time, parking_records.status AS parking_records_status, parking_records.created_at AS parking_records_created_at, parking_records.updated_at AS parking_records_updated_at, parking_records.remarks AS parking_records_remarks 
FROM parking_records 
WHERE parking_records.user_id = ? AND parking_records.status = ?) AS anon_1
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00136s] (1, 0)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00114s] (10,)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 56.06s ago] (1,)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00118s] (84,)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.01389s ago] (11,)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 56.07s ago] (2,)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.01178s ago] (165,)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.02541s ago] (1,)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 56.08s ago] (1,)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.0227s ago] (81,)
2025/05/07 13:21:39 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:21:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:21:39] "GET /api/parking-records/user?status=0&per_page=100 HTTP/1.1" 200 -
2025/05/07 13:21:47 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:21:47 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:21:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:21:57] "OPTIONS /api/parking-records/check-active?vehicle_id=2 HTTP/1.1" 200 -
2025/05/07 13:21:57 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:21:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:21:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 185.2s ago] (1,)
2025/05/07 13:21:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:21:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 17.43s ago] (2,)
2025/05/07 13:21:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_records.id AS parking_records_id, parking_records.vehicle_id AS parking_records_vehicle_id, parking_records.user_id AS parking_records_user_id, parking_records.parking_lot_id AS parking_records_parking_lot_id, parking_records.parking_space_id AS parking_records_parking_space_id, parking_records.entry_time AS parking_records_entry_time, parking_records.exit_time AS parking_records_exit_time, parking_records.status AS parking_records_status, parking_records.created_at AS parking_records_created_at, parking_records.updated_at AS parking_records_updated_at, parking_records.remarks AS parking_records_remarks 
FROM parking_records 
WHERE parking_records.vehicle_id = ? AND parking_records.status = ?
 LIMIT ? OFFSET ?
2025/05/07 13:21:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00126s] (2, 0, 1, 0)
2025/05/07 13:21:57 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:21:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:21:57] "GET /api/parking-records/check-active?vehicle_id=2 HTTP/1.1" 200 -
2025/05/07 13:22:12 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:22:12 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:22:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:22:23] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:22:23 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:22:23 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:22:23 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 211.9s ago] (1,)
2025/05/07 13:22:23 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:22:23 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 211.9s ago] (1,)
2025/05/07 13:22:23 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:22:23 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 211.9s ago] (1,)
2025/05/07 13:22:23 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:22:23 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 211.9s ago] (1,)
2025/05/07 13:22:23 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:22:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:22:23] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:22:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:22:24] "OPTIONS /api/parkinglots/1 HTTP/1.1" 200 -
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 212.7s ago] (1,)
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 101s ago] (1,)
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 101.2s ago] (1, 2)
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:22:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:22:24] "GET /api/parkinglots/1 HTTP/1.1" 200 -
2025/05/07 13:22:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:22:24] "OPTIONS /api/parkinglots/1/spaces?limit=100 HTTP/1.1" 200 -
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 212.7s ago] (1,)
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 101s ago] (1,)
2025/05/07 13:22:24 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=100, status=None, type=None
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00124s] (1, 100, 0)
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00174s] (1,)
2025/05/07 13:22:24 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:22:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:22:24] "GET /api/parkinglots/1/spaces?limit=100 HTTP/1.1" 200 -
2025/05/07 13:22:37 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:22:37 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:22:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:22:54] "OPTIONS /api/parking-records?status=0&per_page=100&_t=1746595374890 HTTP/1.1" 200 -
2025/05/07 13:22:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:22:54] "OPTIONS /api/parkinglots/1/spaces?limit=100 HTTP/1.1" 200 -
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 243s ago] (1,)
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_records.id AS parking_records_id, parking_records.vehicle_id AS parking_records_vehicle_id, parking_records.user_id AS parking_records_user_id, parking_records.parking_lot_id AS parking_records_parking_lot_id, parking_records.parking_space_id AS parking_records_parking_space_id, parking_records.entry_time AS parking_records_entry_time, parking_records.exit_time AS parking_records_exit_time, parking_records.status AS parking_records_status, parking_records.created_at AS parking_records_created_at, parking_records.updated_at AS parking_records_updated_at, parking_records.remarks AS parking_records_remarks 
FROM parking_records 
WHERE parking_records.status = ? ORDER BY parking_records.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 243.1s ago] (1,)
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00155s] (0, 100, 0)
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_records.id AS parking_records_id, parking_records.vehicle_id AS parking_records_vehicle_id, parking_records.user_id AS parking_records_user_id, parking_records.parking_lot_id AS parking_records_parking_lot_id, parking_records.parking_space_id AS parking_records_parking_space_id, parking_records.entry_time AS parking_records_entry_time, parking_records.exit_time AS parking_records_exit_time, parking_records.status AS parking_records_status, parking_records.created_at AS parking_records_created_at, parking_records.updated_at AS parking_records_updated_at, parking_records.remarks AS parking_records_remarks 
FROM parking_records 
WHERE parking_records.status = ?) AS anon_1
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 131.3s ago] (1,)
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00166s] (0,)
2025/05/07 13:22:54 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=100, status=None, type=None
2025/05/07 13:22:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 75.27s ago] (8,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 30.31s ago] (1, 100, 0)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00122s] (3,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 131.3s ago] (1,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 30.32s ago] (1,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:22:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:22:55] "GET /api/parkinglots/1/spaces?limit=100 HTTP/1.1" 200 -
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 75.28s ago] (18,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 75.29s ago] (10,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 131.3s ago] (1,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 75.29s ago] (84,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 75.31s ago] (11,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 131.4s ago] (2,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 75.3s ago] (165,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 75.32s ago] (1,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 131.4s ago] (1,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 75.31s ago] (81,)
2025/05/07 13:22:55 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:22:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:22:55] "GET /api/parking-records?status=0&per_page=100&_t=1746595374890 HTTP/1.1" 200 -
2025/05/07 13:23:02 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:23:02 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:23:27 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:23:27 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:23:52 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:23:52 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:24:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:24:06] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:24:06 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:24:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:24:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 315s ago] (1,)
2025/05/07 13:24:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:24:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 315s ago] (1,)
2025/05/07 13:24:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:24:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 315s ago] (1,)
2025/05/07 13:24:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:24:06 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 315s ago] (1,)
2025/05/07 13:24:06 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:24:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:24:06] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:24:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:24:07] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 315.8s ago] (1,)
2025/05/07 13:24:07 flask_api routes.py[22] get_all_parkinglots() INFO: 开始处理获取停车场列表请求
2025/05/07 13:24:07 flask_api routes.py[32] get_all_parkinglots() INFO: 请求参数: page=1, limit=10, search='', status=
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 204.4s ago] ()
2025/05/07 13:24:07 flask_api routes.py[36] get_all_parkinglots() INFO: 数据库中停车场总数: 4
2025/05/07 13:24:07 flask_api routes.py[109] get_all_parkinglots() WARNING: 无效的状态值: 
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots ORDER BY parking_lots.id ASC) AS anon_1
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 204.4s ago] ()
2025/05/07 13:24:07 flask_api routes.py[122] get_all_parkinglots() INFO: 过滤后但分页前的记录数: 4
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots ORDER BY parking_lots.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 204.4s ago] (10, 0)
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 204.4s ago] ()
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 204.3s ago] (1, 2)
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 204.4s ago] (2, 2)
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 204.4s ago] (3, 2)
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 204.4s ago] (4, 2)
2025/05/07 13:24:07 flask_api routes.py[129] get_all_parkinglots() INFO: 获取到 4 条停车场数据
2025/05/07 13:24:07 flask_api routes.py[143] get_all_parkinglots() INFO: 返回停车场数据: 4 条记录
2025/05/07 13:24:07 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:24:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:24:07] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/05/07 13:24:17 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:24:17 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:24:42 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:24:42 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:25:07 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:25:07 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:25:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:09] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:25:09 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:25:09 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:25:09 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 377.5s ago] (1,)
2025/05/07 13:25:09 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:25:09 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 377.5s ago] (1,)
2025/05/07 13:25:09 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:25:09 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 377.5s ago] (1,)
2025/05/07 13:25:09 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:25:09 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 377.5s ago] (1,)
2025/05/07 13:25:09 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:25:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:09] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:25:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:10] "OPTIONS /api/parkinglots/1 HTTP/1.1" 200 -
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 378.2s ago] (1,)
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 266.4s ago] (1,)
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 266.7s ago] (1, 2)
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:25:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:10] "GET /api/parkinglots/1 HTTP/1.1" 200 -
2025/05/07 13:25:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:10] "OPTIONS /api/parkinglots/1/spaces?page=1&limit=50 HTTP/1.1" 200 -
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 378.3s ago] (1,)
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 266.5s ago] (1,)
2025/05/07 13:25:10 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=50, status=None, type=None
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 165.5s ago] (1, 50, 0)
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 165.5s ago] (1,)
2025/05/07 13:25:10 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:25:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:10] "GET /api/parkinglots/1/spaces?page=1&limit=50 HTTP/1.1" 200 -
2025/05/07 13:25:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:31] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:25:31 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:25:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:25:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 399.8s ago] (1,)
2025/05/07 13:25:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:25:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 399.8s ago] (1,)
2025/05/07 13:25:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:25:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 399.8s ago] (1,)
2025/05/07 13:25:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:25:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 399.8s ago] (1,)
2025/05/07 13:25:31 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:25:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:31] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:25:32 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:25:32 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:25:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:32] "OPTIONS /api/parkinglots/1 HTTP/1.1" 200 -
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 400.5s ago] (1,)
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 288.7s ago] (1,)
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 289s ago] (1, 2)
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:25:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:32] "GET /api/parkinglots/1 HTTP/1.1" 200 -
2025/05/07 13:25:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:32] "OPTIONS /api/parkinglots/1/spaces?page=1&limit=50 HTTP/1.1" 200 -
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 400.6s ago] (1,)
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 288.8s ago] (1,)
2025/05/07 13:25:32 flask_api routes.py[384] get_parking_spaces() INFO: get_parking_spaces 函数接收到的查询参数: page=1, limit=50, status=None, type=None
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? ORDER BY parking_spaces.space_number ASC
 LIMIT ? OFFSET ?
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 187.8s ago] (1, 50, 0)
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 187.8s ago] (1,)
2025/05/07 13:25:32 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:25:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:32] "GET /api/parkinglots/1/spaces?page=1&limit=50 HTTP/1.1" 200 -
2025/05/07 13:25:33 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:25:33 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:25:33 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 401.9s ago] (1,)
2025/05/07 13:25:33 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:25:33 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 401.9s ago] (1,)
2025/05/07 13:25:33 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:25:33 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 401.8s ago] (1,)
2025/05/07 13:25:33 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:25:33 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 401.8s ago] (1,)
2025/05/07 13:25:33 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:25:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:33] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:25:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:34] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 402.5s ago] (1,)
2025/05/07 13:25:34 flask_api routes.py[22] get_all_parkinglots() INFO: 开始处理获取停车场列表请求
2025/05/07 13:25:34 flask_api routes.py[32] get_all_parkinglots() INFO: 请求参数: page=1, limit=10, search='', status=
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 291s ago] ()
2025/05/07 13:25:34 flask_api routes.py[36] get_all_parkinglots() INFO: 数据库中停车场总数: 4
2025/05/07 13:25:34 flask_api routes.py[109] get_all_parkinglots() WARNING: 无效的状态值: 
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots ORDER BY parking_lots.id ASC) AS anon_1
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 291s ago] ()
2025/05/07 13:25:34 flask_api routes.py[122] get_all_parkinglots() INFO: 过滤后但分页前的记录数: 4
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots ORDER BY parking_lots.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 291s ago] (10, 0)
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 291.1s ago] ()
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 291s ago] (1, 2)
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 291s ago] (2, 2)
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 291s ago] (3, 2)
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 291s ago] (4, 2)
2025/05/07 13:25:34 flask_api routes.py[129] get_all_parkinglots() INFO: 获取到 4 条停车场数据
2025/05/07 13:25:34 flask_api routes.py[143] get_all_parkinglots() INFO: 返回停车场数据: 4 条记录
2025/05/07 13:25:34 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:25:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:25:34] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/05/07 13:25:57 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:25:57 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:26:22 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:26:22 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:26:47 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:26:47 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:27:12 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:27:12 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:27:37 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:27:37 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:28:02 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:28:02 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:28:27 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:28:27 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:28:52 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:28:52 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:29:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:02] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:29:02 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:29:02 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:02 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 610.5s ago] (1,)
2025/05/07 13:29:02 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:02 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 610.5s ago] (1,)
2025/05/07 13:29:02 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:29:02 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 610.5s ago] (1,)
2025/05/07 13:29:02 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:29:02 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 610.5s ago] (1,)
2025/05/07 13:29:02 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:29:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:02] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:29:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:03] "OPTIONS /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 611.2s ago] (1,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 611.2s ago] (1,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_records.id AS parking_records_id, parking_records.vehicle_id AS parking_records_vehicle_id, parking_records.user_id AS parking_records_user_id, parking_records.parking_lot_id AS parking_records_parking_lot_id, parking_records.parking_space_id AS parking_records_parking_space_id, parking_records.entry_time AS parking_records_entry_time, parking_records.exit_time AS parking_records_exit_time, parking_records.status AS parking_records_status, parking_records.created_at AS parking_records_created_at, parking_records.updated_at AS parking_records_updated_at, parking_records.remarks AS parking_records_remarks 
FROM parking_records ORDER BY parking_records.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00113s] (10, 0)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_records.id AS parking_records_id, parking_records.vehicle_id AS parking_records_vehicle_id, parking_records.user_id AS parking_records_user_id, parking_records.parking_lot_id AS parking_records_parking_lot_id, parking_records.parking_space_id AS parking_records_parking_space_id, parking_records.entry_time AS parking_records_entry_time, parking_records.exit_time AS parking_records_exit_time, parking_records.status AS parking_records_status, parking_records.created_at AS parking_records_created_at, parking_records.updated_at AS parking_records_updated_at, parking_records.remarks AS parking_records_remarks 
FROM parking_records) AS anon_1
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00106s] ()
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.4s ago] (1,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 499.5s ago] (2,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.4s ago] (176,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.4s ago] (1,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 499.5s ago] (2,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.4s ago] (109,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.4s ago] (1,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 499.5s ago] (2,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.4s ago] (128,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.4s ago] (1,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 499.5s ago] (5,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.4s ago] (466,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.5s ago] (1,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 499.5s ago] (2,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.5s ago] (154,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.5s ago] (2,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 499.5s ago] (2,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.5s ago] (172,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.5s ago] (2,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 499.5s ago] (2,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.5s ago] (117,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.5s ago] (2,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 499.5s ago] (5,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.5s ago] (491,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.5s ago] (2,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 499.5s ago] (4,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.5s ago] (317,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.5s ago] (2,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 499.6s ago] (3,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 443.5s ago] (222,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:29:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:03] "GET /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/05/07 13:29:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:03] "OPTIONS /api/parkinglots/5 HTTP/1.1" 200 -
2025/05/07 13:29:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:03] "OPTIONS /api/parkinglots/5 HTTP/1.1" 200 -
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 611.4s ago] (1,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 499.6s ago] (5,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:29:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:03] "[33mGET /api/parkinglots/5 HTTP/1.1[0m" 404 -
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 611.4s ago] (1,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 499.6s ago] (5,)
2025/05/07 13:29:03 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:29:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:03] "[33mGET /api/parkinglots/5 HTTP/1.1[0m" 404 -
2025/05/07 13:29:17 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:29:17 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:29:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:30] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:29:30 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:29:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 638.8s ago] (1,)
2025/05/07 13:29:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 638.8s ago] (1,)
2025/05/07 13:29:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:29:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 638.8s ago] (1,)
2025/05/07 13:29:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:29:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 638.8s ago] (1,)
2025/05/07 13:29:30 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:29:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:30] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:29:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:31] "OPTIONS /api/violations/user/records?page=1&per_page=15&sort=id&order=asc HTTP/1.1" 200 -
2025/05/07 13:29:31 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:29:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 639.4s ago] (1,)
2025/05/07 13:29:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT violation_records.id AS violation_records_id, violation_records.bike_number AS violation_records_bike_number, violation_records.bike_id AS violation_records_bike_id, violation_records.user_id AS violation_records_user_id, violation_records.violation_time AS violation_records_violation_time, violation_records.location AS violation_records_location, violation_records.violation_type AS violation_records_violation_type, violation_records.violation_type_id AS violation_records_violation_type_id, violation_records.description AS violation_records_description, violation_records.status AS violation_records_status, violation_records.result AS violation_records_result, violation_records.recorder_id AS violation_records_recorder_id, violation_records.handler_id AS violation_records_handler_id, violation_records.created_at AS violation_records_created_at, violation_records.updated_at AS violation_records_updated_at 
FROM violation_records 
WHERE violation_records.user_id = ? ORDER BY violation_records.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:29:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00180s] (1, 15, 0)
2025/05/07 13:29:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT violation_records.id AS violation_records_id, violation_records.bike_number AS violation_records_bike_number, violation_records.bike_id AS violation_records_bike_id, violation_records.user_id AS violation_records_user_id, violation_records.violation_time AS violation_records_violation_time, violation_records.location AS violation_records_location, violation_records.violation_type AS violation_records_violation_type, violation_records.violation_type_id AS violation_records_violation_type_id, violation_records.description AS violation_records_description, violation_records.status AS violation_records_status, violation_records.result AS violation_records_result, violation_records.recorder_id AS violation_records_recorder_id, violation_records.handler_id AS violation_records_handler_id, violation_records.created_at AS violation_records_created_at, violation_records.updated_at AS violation_records_updated_at 
FROM violation_records 
WHERE violation_records.user_id = ?) AS anon_1
2025/05/07 13:29:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00138s] (1,)
2025/05/07 13:29:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00107s] (5,)
2025/05/07 13:29:31 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:29:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:31] "GET /api/violations/user/records?page=1&per_page=15&sort=id&order=asc HTTP/1.1" 200 -
2025/05/07 13:29:42 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:29:42 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:29:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:52] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:29:52 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:29:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 660.9s ago] (1,)
2025/05/07 13:29:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 660.9s ago] (1,)
2025/05/07 13:29:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:29:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 660.9s ago] (1,)
2025/05/07 13:29:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:29:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 660.9s ago] (1,)
2025/05/07 13:29:52 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:29:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:52] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:29:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:53] "OPTIONS /api/violations/type-options HTTP/1.1" 200 -
2025/05/07 13:29:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:53] "OPTIONS /api/violations/types HTTP/1.1" 200 -
2025/05/07 13:29:53 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:29:53 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:29:53 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:53 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:29:53 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 661.6s ago] (1,)
2025/05/07 13:29:53 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 661.6s ago] (1,)
2025/05/07 13:29:53 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:29:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:53] "GET /api/violations/type-options HTTP/1.1" 200 -
2025/05/07 13:29:53 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT violation_types.id AS violation_types_id, violation_types.name AS violation_types_name, violation_types.description AS violation_types_description, violation_types.needs_admin AS violation_types_needs_admin, violation_types.created_at AS violation_types_created_at, violation_types.updated_at AS violation_types_updated_at 
FROM violation_types
2025/05/07 13:29:53 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00203s] ()
2025/05/07 13:29:53 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:29:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:29:53] "GET /api/violations/types HTTP/1.1" 200 -
2025/05/07 13:30:07 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:30:07 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:30:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:30:15] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:30:15 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:30:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 684s ago] (1,)
2025/05/07 13:30:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 684s ago] (1,)
2025/05/07 13:30:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:30:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 684s ago] (1,)
2025/05/07 13:30:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:30:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 684s ago] (1,)
2025/05/07 13:30:15 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:30:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:30:15] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:30:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:30:16] "OPTIONS /api/violations/admin/statistics HTTP/1.1" 200 -
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 684.6s ago] (1,)
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 684.6s ago] (1,)
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT violation_records.id AS violation_records_id, violation_records.bike_number AS violation_records_bike_number, violation_records.bike_id AS violation_records_bike_id, violation_records.user_id AS violation_records_user_id, violation_records.violation_time AS violation_records_violation_time, violation_records.location AS violation_records_location, violation_records.violation_type AS violation_records_violation_type, violation_records.violation_type_id AS violation_records_violation_type_id, violation_records.description AS violation_records_description, violation_records.status AS violation_records_status, violation_records.result AS violation_records_result, violation_records.recorder_id AS violation_records_recorder_id, violation_records.handler_id AS violation_records_handler_id, violation_records.created_at AS violation_records_created_at, violation_records.updated_at AS violation_records_updated_at 
FROM violation_records) AS anon_1
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00170s] ()
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT violation_records.status AS violation_records_status, count(violation_records.id) AS count_1 
FROM violation_records GROUP BY violation_records.status
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00080s] ()
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT violation_records.violation_type AS violation_records_violation_type, count(violation_records.id) AS count_1 
FROM violation_records GROUP BY violation_records.violation_type
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00080s] ()
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT violation_records.location AS violation_records_location, count(violation_records.id) AS count_1 
FROM violation_records GROUP BY violation_records.location
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00078s] ()
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT appeals.status AS appeals_status, count(appeals.id) AS count_1 
FROM appeals JOIN violation_records ON appeals.violation_id = violation_records.id GROUP BY appeals.status
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00069s] ()
2025/05/07 13:30:16 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:30:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:30:16] "GET /api/violations/admin/statistics HTTP/1.1" 200 -
2025/05/07 13:30:32 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:30:32 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:30:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:30:41] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:30:41 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:30:41 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:41 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 709.7s ago] (1,)
2025/05/07 13:30:41 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:41 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 709.7s ago] (1,)
2025/05/07 13:30:41 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:30:41 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 709.7s ago] (1,)
2025/05/07 13:30:41 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:30:41 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 709.7s ago] (1,)
2025/05/07 13:30:41 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:30:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:30:41] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:30:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:30:42] "OPTIONS /api/violations/types HTTP/1.1" 200 -
2025/05/07 13:30:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:30:42] "OPTIONS /api/violations/admin/records?page=1&per_page=10&exclude_status=2 HTTP/1.1" 200 -
2025/05/07 13:30:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:30:42] "OPTIONS /api/violations/admin/appeals?page=1&per_page=10&sort=id&order=asc HTTP/1.1" 200 -
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 710.4s ago] (1,)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT violation_types.id AS violation_types_id, violation_types.name AS violation_types_name, violation_types.description AS violation_types_description, violation_types.needs_admin AS violation_types_needs_admin, violation_types.created_at AS violation_types_created_at, violation_types.updated_at AS violation_types_updated_at 
FROM violation_types
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 48.82s ago] ()
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:30:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:30:42] "GET /api/violations/types HTTP/1.1" 200 -
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 710.4s ago] (1,)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 710.4s ago] (1,)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 710.4s ago] (1,)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 710.4s ago] (1,)
2025/05/07 13:30:42 flask_api routes.py[395] get_all_violations() INFO: 管理员违规记录查询参数: status=None, exclude_status=2, user_id=None, recorder_id=None, sort=id, order=asc
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT violation_records.id AS violation_records_id, violation_records.bike_number AS violation_records_bike_number, violation_records.bike_id AS violation_records_bike_id, violation_records.user_id AS violation_records_user_id, violation_records.violation_time AS violation_records_violation_time, violation_records.location AS violation_records_location, violation_records.violation_type AS violation_records_violation_type, violation_records.violation_type_id AS violation_records_violation_type_id, violation_records.description AS violation_records_description, violation_records.status AS violation_records_status, violation_records.result AS violation_records_result, violation_records.recorder_id AS violation_records_recorder_id, violation_records.handler_id AS violation_records_handler_id, violation_records.created_at AS violation_records_created_at, violation_records.updated_at AS violation_records_updated_at 
FROM violation_records 
WHERE violation_records.status != ? ORDER BY violation_records.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00152s] (2, 10, 0)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT appeals.id AS appeals_id, appeals.violation_id AS appeals_violation_id, appeals.user_id AS appeals_user_id, appeals.reason AS appeals_reason, appeals.status AS appeals_status, appeals.comment AS appeals_comment, appeals.handler_id AS appeals_handler_id, appeals.created_at AS appeals_created_at, appeals.updated_at AS appeals_updated_at 
FROM appeals ORDER BY appeals.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00129s] (10, 0)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT violation_records.id AS violation_records_id, violation_records.bike_number AS violation_records_bike_number, violation_records.bike_id AS violation_records_bike_id, violation_records.user_id AS violation_records_user_id, violation_records.violation_time AS violation_records_violation_time, violation_records.location AS violation_records_location, violation_records.violation_type AS violation_records_violation_type, violation_records.violation_type_id AS violation_records_violation_type_id, violation_records.description AS violation_records_description, violation_records.status AS violation_records_status, violation_records.result AS violation_records_result, violation_records.recorder_id AS violation_records_recorder_id, violation_records.handler_id AS violation_records_handler_id, violation_records.created_at AS violation_records_created_at, violation_records.updated_at AS violation_records_updated_at 
FROM violation_records 
WHERE violation_records.status != ?) AS anon_1
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT appeals.id AS appeals_id, appeals.violation_id AS appeals_violation_id, appeals.user_id AS appeals_user_id, appeals.reason AS appeals_reason, appeals.status AS appeals_status, appeals.comment AS appeals_comment, appeals.handler_id AS appeals_handler_id, appeals.created_at AS appeals_created_at, appeals.updated_at AS appeals_updated_at 
FROM appeals) AS anon_1
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00866s] (2,)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00165s] ()
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00270s] (3,)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00148s] (5,)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 71.03s ago] (5,)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.005987s ago] (2,)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:30:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:30:42] "GET /api/violations/admin/appeals?page=1&per_page=10&sort=id&order=asc HTTP/1.1" 200 -
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.01313s ago] (2,)
2025/05/07 13:30:42 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:30:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:30:42] "GET /api/violations/admin/records?page=1&per_page=10&exclude_status=2 HTTP/1.1" 200 -
2025/05/07 13:30:57 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:30:57 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:31:22 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:31:22 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:31:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:31:31] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 759.3s ago] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 759.3s ago] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 759.3s ago] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 759.3s ago] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:31:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:31:31] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:31:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:31:31] "OPTIONS /api/charging/records/active HTTP/1.1" 200 -
2025/05/07 13:31:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:31:31] "OPTIONS /api/charging/records/history?page=1&limit=10&license=&start_date=&end_date=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/05/07 13:31:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:31:31] "OPTIONS /api/my-bikes HTTP/1.1" 200 -
2025/05/07 13:31:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:31:31] "OPTIONS /api/charging/parking-lots HTTP/1.1" 200 -
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 760s ago] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 760s ago] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 760s ago] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT charging_records.id AS charging_records_id, charging_records.parking_record_id AS charging_records_parking_record_id, charging_records.vehicle_id AS charging_records_vehicle_id, charging_records.user_id AS charging_records_user_id, charging_records.parking_lot_id AS charging_records_parking_lot_id, charging_records.parking_space_id AS charging_records_parking_space_id, charging_records.start_time AS charging_records_start_time, charging_records.end_time AS charging_records_end_time, charging_records.duration AS charging_records_duration, charging_records.power AS charging_records_power, charging_records.charging_type AS charging_records_charging_type, charging_records.status AS charging_records_status, charging_records.created_at AS charging_records_created_at, charging_records.updated_at AS charging_records_updated_at, charging_records.remarks AS charging_records_remarks 
FROM charging_records 
WHERE charging_records.user_id = ? AND charging_records.status = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 760s ago] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.status = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00576s] (1, 0)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.belong_to = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00417s] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT charging_records.id AS charging_records_id, charging_records.parking_record_id AS charging_records_parking_record_id, charging_records.vehicle_id AS charging_records_vehicle_id, charging_records.user_id AS charging_records_user_id, charging_records.parking_lot_id AS charging_records_parking_lot_id, charging_records.parking_space_id AS charging_records_parking_space_id, charging_records.start_time AS charging_records_start_time, charging_records.end_time AS charging_records_end_time, charging_records.duration AS charging_records_duration, charging_records.power AS charging_records_power, charging_records.charging_type AS charging_records_charging_type, charging_records.status AS charging_records_status, charging_records.created_at AS charging_records_created_at, charging_records.updated_at AS charging_records_updated_at, charging_records.remarks AS charging_records_remarks 
FROM charging_records 
WHERE charging_records.user_id = ? AND charging_records.status = ? ORDER BY charging_records.id ASC) AS anon_1
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 746.7s ago] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00423s] (1, 1)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.2s ago] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT charging_records.id AS charging_records_id, charging_records.parking_record_id AS charging_records_parking_record_id, charging_records.vehicle_id AS charging_records_vehicle_id, charging_records.user_id AS charging_records_user_id, charging_records.parking_lot_id AS charging_records_parking_lot_id, charging_records.parking_space_id AS charging_records_parking_space_id, charging_records.start_time AS charging_records_start_time, charging_records.end_time AS charging_records_end_time, charging_records.duration AS charging_records_duration, charging_records.power AS charging_records_power, charging_records.charging_type AS charging_records_charging_type, charging_records.status AS charging_records_status, charging_records.created_at AS charging_records_created_at, charging_records.updated_at AS charging_records_updated_at, charging_records.remarks AS charging_records_remarks 
FROM charging_records 
WHERE charging_records.user_id = ? AND charging_records.status = ? ORDER BY charging_records.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.6s ago] (1, 2)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00561s] (1, 1, 10, 0)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.3s ago] (1,)
2025/05/07 13:31:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:31:31] "GET /api/my-bikes HTTP/1.1" 200 -
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.6s ago] (2, 2)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.2s ago] (81,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:31:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:31:31] "GET /api/charging/records/active HTTP/1.1" 200 -
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.3s ago] (1,)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.6s ago] (3, 2)
2025/05/07 13:31:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (1,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.6s ago] (4, 2)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:31:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:31:32] "GET /api/charging/parking-lots HTTP/1.1" 200 -
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (1,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.3s ago] (2,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (528,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (1,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.4s ago] (4,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (546,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (1,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.4s ago] (5,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (560,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (2,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.4s ago] (4,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (538,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (2,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.4s ago] (1,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (516,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (2,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.4s ago] (1,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (517,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (3,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.4s ago] (2,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (524,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.4s ago] (3,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.4s ago] (5,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.3s ago] (558,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.b_id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.4s ago] (3,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 648.4s ago] (4,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.id = ?
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 592.4s ago] (543,)
2025/05/07 13:31:32 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:31:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:31:32] "GET /api/charging/records/history?page=1&limit=10&license=&start_date=&end_date=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/05/07 13:31:47 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:31:47 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:31:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:31:57] "OPTIONS /api/charging/parking-lots/1/spaces HTTP/1.1" 200 -
2025/05/07 13:31:57 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:31:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:31:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 785.2s ago] (1,)
2025/05/07 13:31:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:31:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 673.4s ago] (1,)
2025/05/07 13:31:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:31:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 673.7s ago] (1, 2)
2025/05/07 13:31:57 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:31:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:31:57] "GET /api/charging/parking-lots/1/spaces HTTP/1.1" 200 -
2025/05/07 13:32:12 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:32:12 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:32:37 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:32:37 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:33:02 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:33:02 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:33:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:33:16] "OPTIONS /api/charging/parking-lots/1/spaces HTTP/1.1" 200 -
2025/05/07 13:33:16 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:33:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:33:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 864.8s ago] (1,)
2025/05/07 13:33:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots 
WHERE parking_lots.id = ?
2025/05/07 13:33:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 753.1s ago] (1,)
2025/05/07 13:33:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:33:16 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 753.3s ago] (1, 2)
2025/05/07 13:33:16 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:33:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:33:16] "GET /api/charging/parking-lots/1/spaces HTTP/1.1" 200 -
2025/05/07 13:33:27 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:33:27 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:33:52 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:33:52 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:34:17 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:34:17 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:34:42 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:34:42 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:35:07 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:35:07 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:35:32 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:35:32 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:35:57 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:35:57 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:36:22 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:36:22 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:36:47 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:36:47 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:37:12 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:37:12 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:37:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:37:30] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:37:30 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:37:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:37:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1118s ago] (1,)
2025/05/07 13:37:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:37:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1118s ago] (1,)
2025/05/07 13:37:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:37:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1118s ago] (1,)
2025/05/07 13:37:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:37:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1118s ago] (1,)
2025/05/07 13:37:30 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:37:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:37:30] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:37:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:37:31] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/05/07 13:37:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:37:31] "OPTIONS /api/charging-spaces?page=1&per_page=20 HTTP/1.1" 200 -
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1119s ago] (1,)
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1119s ago] (1,)
2025/05/07 13:37:31 flask_api routes.py[22] get_all_parkinglots() INFO: 开始处理获取停车场列表请求
2025/05/07 13:37:31 flask_api routes.py[32] get_all_parkinglots() INFO: 请求参数: page=1, limit=10, search='', status=None
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.type = ? ORDER BY parking_spaces.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00297s] (2, 20, 0)
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1008s ago] ()
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.type = ?) AS anon_1
2025/05/07 13:37:31 flask_api routes.py[36] get_all_parkinglots() INFO: 数据库中停车场总数: 4
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00189s] (2,)
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots ORDER BY parking_lots.id ASC) AS anon_1
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1008s ago] ()
2025/05/07 13:37:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:37:31] "GET /api/charging-spaces?page=1&per_page=20 HTTP/1.1" 200 -
2025/05/07 13:37:31 flask_api routes.py[122] get_all_parkinglots() INFO: 过滤后但分页前的记录数: 4
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots ORDER BY parking_lots.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1008s ago] (10, 0)
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1008s ago] ()
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1008s ago] (1, 2)
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1008s ago] (2, 2)
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1008s ago] (3, 2)
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1008s ago] (4, 2)
2025/05/07 13:37:31 flask_api routes.py[129] get_all_parkinglots() INFO: 获取到 4 条停车场数据
2025/05/07 13:37:31 flask_api routes.py[143] get_all_parkinglots() INFO: 返回停车场数据: 4 条记录
2025/05/07 13:37:31 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:37:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:37:31] "GET /api/parkinglots HTTP/1.1" 200 -
2025/05/07 13:37:37 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:37:37 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:37:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:37:56] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:37:56 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:37:56 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:37:56 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1145s ago] (1,)
2025/05/07 13:37:56 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:37:56 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1145s ago] (1,)
2025/05/07 13:37:56 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:37:56 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1145s ago] (1,)
2025/05/07 13:37:56 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:37:56 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1145s ago] (1,)
2025/05/07 13:37:56 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:37:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:37:56] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:37:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:37:57] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1145s ago] (1,)
2025/05/07 13:37:57 flask_api routes.py[22] get_all_parkinglots() INFO: 开始处理获取停车场列表请求
2025/05/07 13:37:57 flask_api routes.py[32] get_all_parkinglots() INFO: 请求参数: page=1, limit=10, search='', status=None
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1034s ago] ()
2025/05/07 13:37:57 flask_api routes.py[36] get_all_parkinglots() INFO: 数据库中停车场总数: 4
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots ORDER BY parking_lots.id ASC) AS anon_1
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1034s ago] ()
2025/05/07 13:37:57 flask_api routes.py[122] get_all_parkinglots() INFO: 过滤后但分页前的记录数: 4
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots ORDER BY parking_lots.id ASC
 LIMIT ? OFFSET ?
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1034s ago] (10, 0)
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description, parking_lots.campus AS parking_lots_campus, parking_lots.area AS parking_lots_area, parking_lots.manager AS parking_lots_manager, parking_lots.contact AS parking_lots_contact 
FROM parking_lots) AS anon_1
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1034s ago] ()
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1034s ago] (1, 2)
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1034s ago] (2, 2)
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1034s ago] (3, 2)
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.power AS parking_spaces_power, parking_spaces.charging_type AS parking_spaces_charging_type, parking_spaces.remarks AS parking_spaces_remarks, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.type = ?
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1034s ago] (4, 2)
2025/05/07 13:37:57 flask_api routes.py[129] get_all_parkinglots() INFO: 获取到 4 条停车场数据
2025/05/07 13:37:57 flask_api routes.py[143] get_all_parkinglots() INFO: 返回停车场数据: 4 条记录
2025/05/07 13:37:57 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:37:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:37:57] "GET /api/parkinglots HTTP/1.1" 200 -
2025/05/07 13:38:02 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:38:02 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:38:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:38:22] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:38:22 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:38:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:38:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1171s ago] (1,)
2025/05/07 13:38:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:38:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1171s ago] (1,)
2025/05/07 13:38:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:38:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1171s ago] (1,)
2025/05/07 13:38:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:38:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1171s ago] (1,)
2025/05/07 13:38:22 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:38:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:38:22] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:38:27 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:38:27 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:38:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:38:46] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:38:46 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:38:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:38:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1194s ago] (1,)
2025/05/07 13:38:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/07 13:38:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1194s ago] (1,)
2025/05/07 13:38:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/07 13:38:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1194s ago] (1,)
2025/05/07 13:38:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/07 13:38:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 1194s ago] (1,)
2025/05/07 13:38:46 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:38:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:38:46] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/07 13:38:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:38:47] "OPTIONS /api/announcements?page=1&per_page=10&type= HTTP/1.1" 200 -
2025/05/07 13:38:47 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/07 13:38:47 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT announcements.id AS announcements_id, announcements.title AS announcements_title, announcements.content AS announcements_content, announcements.type AS announcements_type, announcements.created_by AS announcements_created_by, announcements.created_at AS announcements_created_at, announcements.updated_at AS announcements_updated_at 
FROM announcements ORDER BY announcements.created_at DESC
 LIMIT ? OFFSET ?
2025/05/07 13:38:47 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00119s] (10, 0)
2025/05/07 13:38:47 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT announcements.id AS announcements_id, announcements.title AS announcements_title, announcements.content AS announcements_content, announcements.type AS announcements_type, announcements.created_by AS announcements_created_by, announcements.created_at AS announcements_created_at, announcements.updated_at AS announcements_updated_at 
FROM announcements) AS anon_1
2025/05/07 13:38:47 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00096s] ()
2025/05/07 13:38:47 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/07 13:38:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [07/May/2025 13:38:47] "GET /api/announcements?page=1&per_page=10&type= HTTP/1.1" 200 -
2025/05/07 13:38:52 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:38:52 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:39:17 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:39:17 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:39:42 engineio.server socket.py[78] send() INFO: KIIC3A6TM0jV5N_mAAAE: Sending packet PING data None
2025/05/07 13:39:42 engineio.server socket.py[39] receive() INFO: KIIC3A6TM0jV5N_mAAAE: Received packet PONG data 
2025/05/07 13:39:53 flask_api routes.py[60] handle_disconnect() INFO: Client disconnected
