<template>
  <div class="charging-reservation-component">
    <!-- 预约表单对话框 -->
    <el-dialog
      title="充电预约"
      :visible.sync="dialogVisible"
      width="500px"
      class="charging-reservation-dialog"
      @close="resetForm"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="right"
        size="medium"
      >
        <!-- 停车场选择 -->
        <el-form-item label="停车场" prop="parking_lot_id">
          <el-select
            v-model="form.parking_lot_id"
            placeholder="请选择停车场"
            filterable
            style="width: 100%"
            @change="handleParkingLotChange"
          >
            <el-option
              v-for="item in parkingLots"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <!-- 车辆选择 -->
        <el-form-item label="车辆" prop="vehicle_id">
          <el-select
            v-model="form.vehicle_id"
            placeholder="请选择车辆"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="item in vehicles"
              :key="item.id"
              :label="`${item.number} (${item.brand} ${item.color})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <!-- 预约日期 -->
        <el-form-item label="预约日期" prop="date">
          <el-date-picker
            v-model="form.date"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
            :picker-options="datePickerOptions"
            @change="handleDateChange"
          />
        </el-form-item>

        <!-- 预约时间段 -->
        <el-form-item label="时间段" prop="time_slot">
          <el-select
            v-model="form.time_slot"
            placeholder="请选择时间段"
            style="width: 100%"
            @change="handleTimeSlotChange"
          >
            <el-option
              v-for="slot in availableTimeSlots"
              :key="slot.value"
              :label="slot.label"
              :value="slot.value"
              :disabled="slot.disabled"
            />
          </el-select>
        </el-form-item>

        <!-- 预约时长 -->
        <el-form-item label="预约时长" prop="duration">
          <el-select
            v-model="form.duration"
            placeholder="请选择预约时长"
            style="width: 100%"
            @change="handleDurationChange"
          >
            <el-option label="30分钟" value="30" />
            <el-option label="1小时" value="60" />
            <el-option label="1.5小时" value="90" />
            <el-option label="2小时" value="120" />
            <el-option label="2.5小时" value="150" />
            <el-option label="3小时" value="180" />
          </el-select>
        </el-form-item>

        <!-- 备注 -->
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="form.remarks"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息（选填）"
          />
        </el-form-item>

        <!-- 预约信息摘要 -->
        <div class="reservation-summary" v-if="showSummary">
          <div class="summary-title">预约信息</div>
          <div class="summary-content">
            <div class="summary-item">
              <span class="label">开始时间：</span>
              <span class="value">{{ formatDateTime(form.start_time) }}</span>
            </div>
            <div class="summary-item">
              <span class="label">结束时间：</span>
              <span class="value">{{ formatDateTime(form.end_time) }}</span>
            </div>
            <div class="summary-item">
              <span class="label">预约时长：</span>
              <span class="value">{{ getDurationText(form.duration) }}</span>
            </div>
          </div>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确认预约</el-button>
      </div>
    </el-dialog>

    <!-- 预约列表 -->
    <div class="reservations-list" v-loading="loading">
      <div class="list-header">
        <div class="header-title">
          <i class="el-icon-date"></i>
          <span>我的充电预约</span>
        </div>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="openReservationDialog">新增预约</el-button>
          <el-button type="text" size="small" @click="refreshReservations">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </div>

      <!-- 无预约时显示 -->
      <el-empty
        v-if="reservations.length === 0"
        description="暂无充电预约"
        :image-size="120"
      >
        <el-button type="primary" size="small" @click="openReservationDialog">立即预约</el-button>
      </el-empty>

      <!-- 预约卡片列表 -->
      <div v-else class="reservation-cards">
        <el-card
          v-for="item in reservations"
          :key="item.id"
          class="reservation-card"
          :class="{ 'status-pending': item.status === 0, 'status-used': item.status === 1, 'status-canceled': item.status === 2, 'status-expired': item.status === 3 }"
          shadow="hover"
        >
          <div class="card-header">
            <div class="card-title">
              <span class="parking-lot">{{ getParkingLotName(item) }}</span>
              <el-tag
                :type="getStatusType(item.status)"
                size="small"
              >{{ item.status_text }}</el-tag>
            </div>
            <div class="card-time">{{ formatDate(item.start_time) }}</div>
          </div>

          <div class="card-content">
            <div class="info-row">
              <span class="info-label">车辆信息</span>
              <span class="info-value">{{ getVehicleInfo(item) }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">预约时间</span>
              <span class="info-value">{{ formatTime(item.start_time) }} - {{ formatTime(item.end_time) }}</span>
            </div>
            <div class="info-row" v-if="item.remarks">
              <span class="info-label">备注</span>
              <span class="info-value">{{ item.remarks }}</span>
            </div>
          </div>

          <div class="card-actions">
            <el-button
              v-if="item.status === 0"
              type="danger"
              size="mini"
              @click="cancelReservation(item)"
            >取消预约</el-button>
          </div>
        </el-card>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="listQuery.page"
          :page-sizes="[5, 10, 20]"
          :page-size="listQuery.per_page"
          layout="total, sizes, prev, pager, next"
          :total="total"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getUserChargingReservations, createChargingReservation, cancelChargingReservation } from '@/api/charging'
import { getUserVehicles } from '@/api/vehicle'
import { getParkingLots } from '@/api/parkinglot'

export default {
  name: 'ChargingReservation',
  props: {
    // 是否显示预约列表
    showList: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 对话框显示状态
      dialogVisible: false,
      // 表单数据
      form: {
        user_id: '',
        vehicle_id: '',
        parking_lot_id: '',
        date: '',
        time_slot: '',
        duration: '60',
        start_time: null,
        end_time: null,
        remarks: ''
      },
      // 表单验证规则
      rules: {
        parking_lot_id: [
          { required: true, message: '请选择停车场', trigger: 'change' }
        ],
        vehicle_id: [
          { required: true, message: '请选择车辆', trigger: 'change' }
        ],
        date: [
          { required: true, message: '请选择预约日期', trigger: 'change' }
        ],
        time_slot: [
          { required: true, message: '请选择时间段', trigger: 'change' }
        ],
        duration: [
          { required: true, message: '请选择预约时长', trigger: 'change' }
        ]
      },
      // 日期选择器配置
      datePickerOptions: {
        disabledDate(time) {
          // 禁用今天之前的日期和7天后的日期
          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > Date.now() + 7 * 24 * 3600 * 1000
        }
      },
      // 可用的时间段
      availableTimeSlots: [],
      // 是否显示预约信息摘要
      showSummary: false,
      // 提交加载状态
      submitLoading: false,
      
      // 停车场列表
      parkingLots: [],
      // 车辆列表
      vehicles: [],
      
      // 预约列表
      reservations: [],
      // 加载状态
      loading: false,
      // 总数
      total: 0,
      // 查询参数
      listQuery: {
        page: 1,
        per_page: 5
      }
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'name'
    ])
  },
  created() {
    // 初始化用户ID
    this.form.user_id = this.userId
    
    // 获取停车场列表
    this.fetchParkingLots()
    
    // 获取用户车辆列表
    this.fetchUserVehicles()
    
    // 获取用户预约列表
    if (this.showList) {
      this.fetchReservations()
    }
    
    // 生成时间段选项
    this.generateTimeSlots()
  },
  methods: {
    // 获取停车场列表
    async fetchParkingLots() {
      try {
        const response = await getParkingLots()
        if (response.data && response.data.items) {
          this.parkingLots = response.data.items
        }
      } catch (error) {
        console.error('获取停车场列表失败:', error)
        this.$message.error('获取停车场列表失败')
      }
    },
    
    // 获取用户车辆列表
    async fetchUserVehicles() {
      try {
        const response = await getUserVehicles(this.userId)
        if (response.data && response.data.items) {
          this.vehicles = response.data.items
        }
      } catch (error) {
        console.error('获取用户车辆列表失败:', error)
        this.$message.error('获取用户车辆列表失败')
      }
    },
    
    // 获取用户预约列表
    async fetchReservations() {
      if (!this.showList) return
      
      this.loading = true
      try {
        const params = {
          ...this.listQuery
        }
        
        const response = await getUserChargingReservations(this.userId, params)
        if (response.data) {
          this.reservations = response.data.items || []
          this.total = response.data.total || 0
        }
      } catch (error) {
        console.error('获取用户预约列表失败:', error)
        this.$message.error('获取用户预约列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 刷新预约列表
    refreshReservations() {
      this.fetchReservations()
    },
    
    // 打开预约对话框
    openReservationDialog() {
      this.dialogVisible = true
      
      // 如果没有车辆，提示用户先添加车辆
      if (this.vehicles.length === 0) {
        this.$message.warning('您还没有添加车辆，请先添加车辆')
        this.dialogVisible = false
        return
      }
      
      // 如果没有停车场，提示管理员先添加停车场
      if (this.parkingLots.length === 0) {
        this.$message.warning('系统中没有可用的停车场，请联系管理员')
        this.dialogVisible = false
        return
      }
      
      // 默认选择第一个车辆和停车场
      if (this.vehicles.length > 0 && !this.form.vehicle_id) {
        this.form.vehicle_id = this.vehicles[0].id
      }
      
      if (this.parkingLots.length > 0 && !this.form.parking_lot_id) {
        this.form.parking_lot_id = this.parkingLots[0].id
      }
      
      // 默认选择今天
      if (!this.form.date) {
        this.form.date = new Date()
      }
      
      // 生成时间段选项
      this.generateTimeSlots()
    },
    
    // 生成时间段选项
    generateTimeSlots() {
      const slots = []
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const selectedDate = this.form.date ? new Date(this.form.date) : today
      const isToday = selectedDate.getTime() === today.getTime()
      
      // 时间段：8:00 - 20:00，每半小时一个时间段
      for (let hour = 8; hour < 20; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const slotTime = new Date(selectedDate)
          slotTime.setHours(hour, minute, 0, 0)
          
          // 如果是今天，且时间已过，则禁用该时间段
          const disabled = isToday && slotTime <= now
          
          slots.push({
            label: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            value: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            time: slotTime,
            disabled
          })
        }
      }
      
      this.availableTimeSlots = slots
      
      // 如果当前选择的时间段被禁用，则清空选择
      if (this.form.time_slot) {
        const selectedSlot = this.availableTimeSlots.find(slot => slot.value === this.form.time_slot)
        if (selectedSlot && selectedSlot.disabled) {
          this.form.time_slot = ''
        }
      }
      
      // 如果是今天，默认选择当前时间之后的第一个可用时间段
      if (isToday && !this.form.time_slot) {
        const availableSlot = this.availableTimeSlots.find(slot => !slot.disabled)
        if (availableSlot) {
          this.form.time_slot = availableSlot.value
          this.handleTimeSlotChange()
        }
      }
    },
    
    // 处理停车场变更
    handleParkingLotChange() {
      // 可以在这里添加获取停车场充电车位数量等逻辑
    },
    
    // 处理日期变更
    handleDateChange() {
      // 重新生成时间段选项
      this.generateTimeSlots()
      
      // 更新预约时间
      this.updateReservationTime()
    },
    
    // 处理时间段变更
    handleTimeSlotChange() {
      // 更新预约时间
      this.updateReservationTime()
    },
    
    // 处理时长变更
    handleDurationChange() {
      // 更新预约时间
      this.updateReservationTime()
    },
    
    // 更新预约时间
    updateReservationTime() {
      if (!this.form.date || !this.form.time_slot || !this.form.duration) {
        this.showSummary = false
        return
      }
      
      // 解析时间段
      const [hours, minutes] = this.form.time_slot.split(':').map(Number)
      
      // 创建开始时间
      const startTime = new Date(this.form.date)
      startTime.setHours(hours, minutes, 0, 0)
      
      // 创建结束时间
      const endTime = new Date(startTime.getTime() + parseInt(this.form.duration) * 60 * 1000)
      
      // 更新表单数据
      this.form.start_time = startTime
      this.form.end_time = endTime
      
      // 显示预约信息摘要
      this.showSummary = true
    },
    
    // 提交表单
    submitForm() {
      this.$refs.form.validate(async valid => {
        if (!valid) {
          return false
        }
        
        // 验证预约时间
        if (!this.form.start_time || !this.form.end_time) {
          this.$message.error('请选择有效的预约时间')
          return
        }
        
        // 确认预约
        try {
          this.submitLoading = true
          
          // 准备提交数据
          const data = {
            user_id: this.form.user_id,
            vehicle_id: this.form.vehicle_id,
            parking_lot_id: this.form.parking_lot_id,
            start_time: this.form.start_time.toISOString(),
            end_time: this.form.end_time.toISOString(),
            remarks: this.form.remarks
          }
          
          // 创建预约
          const response = await createChargingReservation(data)
          
          this.$message.success('充电预约创建成功')
          this.dialogVisible = false
          this.resetForm()
          
          // 刷新预约列表
          this.fetchReservations()
          
          // 触发成功事件
          this.$emit('success', response.data)
        } catch (error) {
          console.error('创建充电预约失败:', error)
          if (error.response && error.response.data && error.response.data.message) {
            this.$message.error(error.response.data.message)
          } else {
            this.$message.error('创建充电预约失败')
          }
        } finally {
          this.submitLoading = false
        }
      })
    },
    
    // 取消预约
    async cancelReservation(reservation) {
      try {
        await this.$confirm(`确认取消预约？<br><br>停车场：${this.getParkingLotName(reservation)}<br>预约时间：${this.formatDateTime(reservation.start_time)}`, '取消预约', {
          confirmButtonText: '确认取消',
          cancelButtonText: '返回',
          type: 'warning',
          dangerouslyUseHTMLString: true
        })
        
        // 取消预约
        await cancelChargingReservation(reservation.id)
        
        this.$message.success('预约已取消')
        
        // 刷新预约列表
        this.fetchReservations()
      } catch (error) {
        if (error === 'cancel') {
          return
        }
        console.error('取消预约失败:', error)
        this.$message.error('取消预约失败')
      }
    },
    
    // 重置表单
    resetForm() {
      this.form = {
        user_id: this.userId,
        vehicle_id: this.vehicles.length > 0 ? this.vehicles[0].id : '',
        parking_lot_id: this.parkingLots.length > 0 ? this.parkingLots[0].id : '',
        date: new Date(),
        time_slot: '',
        duration: '60',
        start_time: null,
        end_time: null,
        remarks: ''
      }
      
      this.showSummary = false
      
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
        this.generateTimeSlots()
      })
    },
    
    // 处理分页大小变化
    handleSizeChange(size) {
      this.listQuery.per_page = size
      this.fetchReservations()
    },
    
    // 处理页码变化
    handleCurrentChange(page) {
      this.listQuery.page = page
      this.fetchReservations()
    },
    
    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return ''
      
      const date = new Date(dateTimeStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    
    // 格式化日期
    formatDate(dateTimeStr) {
      if (!dateTimeStr) return ''
      
      const date = new Date(dateTimeStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    },
    
    // 格式化时间
    formatTime(dateTimeStr) {
      if (!dateTimeStr) return ''
      
      const date = new Date(dateTimeStr)
      return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    
    // 获取时长文本
    getDurationText(duration) {
      const minutes = parseInt(duration)
      if (minutes < 60) {
        return `${minutes}分钟`
      } else {
        const hours = Math.floor(minutes / 60)
        const remainingMinutes = minutes % 60
        if (remainingMinutes === 0) {
          return `${hours}小时`
        } else {
          return `${hours}小时${remainingMinutes}分钟`
        }
      }
    },
    
    // 获取车辆信息
    getVehicleInfo(reservation) {
      if (!reservation) return ''
      
      if (reservation.vehicle) {
        return `${reservation.vehicle.number} (${reservation.vehicle.brand} ${reservation.vehicle.color})`
      }
      
      return `车辆ID: ${reservation.vehicle_id}`
    },
    
    // 获取停车场名称
    getParkingLotName(reservation) {
      if (!reservation) return ''
      
      if (reservation.parking_lot) {
        return reservation.parking_lot.name
      }
      
      return `停车场ID: ${reservation.parking_lot_id}`
    },
    
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'primary',  // 待使用
        1: 'success',  // 已使用
        2: 'info',     // 已取消
        3: 'danger'    // 已过期
      }
      return statusMap[status] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-reservation-component {
  // 预约对话框
  ::v-deep .charging-reservation-dialog {
    .el-dialog__header {
      padding: 15px 20px;
      background-color: #f0f9ff;
      border-bottom: 1px solid #ebeef5;
      
      .el-dialog__title {
        font-weight: 600;
        color: #4A9BFF;
      }
    }
    
    .el-dialog__body {
      padding: 20px;
    }
    
    .el-dialog__footer {
      padding: 15px 20px;
      border-top: 1px solid #ebeef5;
      background-color: #f5f7fa;
    }
  }
  
  // 预约信息摘要
  .reservation-summary {
    margin-top: 20px;
    background-color: #f5f7fa;
    border-radius: 8px;
    padding: 15px;
    
    .summary-title {
      font-weight: 600;
      margin-bottom: 10px;
      color: #303133;
    }
    
    .summary-content {
      .summary-item {
        display: flex;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          width: 100px;
          color: #606266;
        }
        
        .value {
          flex: 1;
          font-weight: 500;
        }
      }
    }
  }
  
  // 预约列表
  .reservations-list {
    margin-top: 20px;
    
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      .header-title {
        font-size: 16px;
        font-weight: 600;
        display: flex;
        align-items: center;
        
        i {
          margin-right: 8px;
          color: #409EFF;
        }
      }
    }
    
    // 预约卡片
    .reservation-cards {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
      
      .reservation-card {
        border-radius: 8px;
        transition: all 0.3s;
        
        &.status-pending {
          border-left: 4px solid #409EFF;
        }
        
        &.status-used {
          border-left: 4px solid #67C23A;
        }
        
        &.status-canceled {
          border-left: 4px solid #909399;
        }
        
        &.status-expired {
          border-left: 4px solid #F56C6C;
        }
        
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
          
          .card-title {
            display: flex;
            align-items: center;
            
            .parking-lot {
              font-weight: 600;
              margin-right: 10px;
            }
          }
          
          .card-time {
            color: #909399;
            font-size: 13px;
          }
        }
        
        .card-content {
          margin-bottom: 15px;
          
          .info-row {
            display: flex;
            margin-bottom: 8px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .info-label {
              width: 80px;
              color: #606266;
              font-size: 13px;
            }
            
            .info-value {
              flex: 1;
              font-size: 13px;
            }
          }
        }
        
        .card-actions {
          text-align: right;
        }
      }
    }
    
    // 分页
    .pagination-container {
      text-align: right;
      margin-top: 15px;
    }
  }
}
</style>
