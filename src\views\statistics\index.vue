<template>
  <div class="app-container">
    <!-- 使用权限包装器组件，只允许管理员访问 -->
    <permission-wrapper :allowed-roles="['admin']" message="您没有权限访问停车统计数据，此功能仅对管理员开放">
      <el-card class="filter-container">
        <div class="filter-item">
          <el-select v-model="filterParams.parkingLotId" placeholder="选择停车场" clearable @change="handleFilterChange">
            <el-option
              v-for="item in parkingLots"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>

          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleDateChange"
          />

          <el-button type="primary" icon="el-icon-download" @click="handleExport">导出报表</el-button>
        </div>
      </el-card>

      <parking-statistics
        :is-admin="isAdmin"
        :user-id="userId"
        ref="parkingStats"
      />

      <el-card class="detailed-stats">
        <div slot="header" class="card-header">
          <span><i class="el-icon-s-data"></i> 详细统计数据</span>
        </div>

        <el-tabs v-model="activeTab">
          <el-tab-pane label="停车记录统计" name="records">
            <el-table
              v-loading="loading"
              :data="recordsData"
              border
              style="width: 100%"
            >
              <el-table-column prop="date" label="日期" width="120" />
              <el-table-column prop="total" label="总记录数" width="100" />
              <el-table-column prop="completed" label="已完成" width="100" />
              <el-table-column prop="active" label="进行中" width="100" />
              <el-table-column prop="avgDuration" label="平均停车时长" width="150">
                <template slot-scope="scope">
                  {{ formatDuration(scope.row.avgDuration) }}
                </template>
              </el-table-column>
              <el-table-column prop="peakHour" label="高峰时段" width="120" />
              <el-table-column prop="utilization" label="使用率" width="100">
                <template slot-scope="scope">
                  {{ scope.row.utilization }}%
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="停车场统计" name="parkingLots">
            <el-table
              v-loading="loading"
              :data="parkingLotsData"
              border
              style="width: 100%"
            >
              <el-table-column prop="name" label="停车场名称" />
              <el-table-column prop="totalSpaces" label="总车位数" width="100" />
              <el-table-column prop="occupiedSpaces" label="已占用" width="100" />
              <el-table-column prop="availableSpaces" label="可用" width="100" />
              <el-table-column prop="utilizationRate" label="使用率" width="100">
                <template slot-scope="scope">
                  <el-progress
                    :percentage="scope.row.utilizationRate"
                    :color="getUtilizationColor(scope.row.utilizationRate)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="totalRecords" label="停车记录数" width="120" />
              <el-table-column prop="todayRecords" label="今日记录" width="100" />
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="车辆类型统计" name="vehicleTypes">
            <el-table
              v-loading="loading"
              :data="vehicleTypesData"
              border
              style="width: 100%"
            >
              <el-table-column prop="type" label="车辆类型" />
              <el-table-column prop="count" label="数量" width="100" />
              <el-table-column prop="percentage" label="占比" width="100">
                <template slot-scope="scope">
                  {{ scope.row.percentage }}%
                </template>
              </el-table-column>
              <el-table-column prop="avgDuration" label="平均停车时长" width="150">
                <template slot-scope="scope">
                  {{ formatDuration(scope.row.avgDuration) }}
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </permission-wrapper>
  </div>
</template>

<script>
import ParkingStatistics from '@/components/ParkingStatistics'
import PermissionWrapper from '@/components/PermissionWrapper'
import { getParkingStats, exportParkingRecords } from '@/api/statistics'
import { getParkingLots } from '@/api/parking-lot'
import request from '@/utils/request'

export default {
  name: 'Statistics',
  components: {
    ParkingStatistics,
    PermissionWrapper
  },
  data() {
    return {
      loading: false,
      isAdmin: false,
      userId: null,
      activeTab: 'records',
      dateRange: [],
      filterParams: {
        parkingLotId: null,
        startDate: null,
        endDate: null
      },
      parkingLots: [],
      recordsData: [],
      parkingLotsData: [],
      vehicleTypesData: []
    }
  },
  created() {
    // 获取用户信息
    this.isAdmin = this.$store.getters.roles.includes('admin')
    this.userId = this.$store.getters.userId

    // 只有管理员才执行数据获取
    if (this.isAdmin) {
      // 获取停车场列表
      this.fetchParkingLots()

      // 获取详细统计数据
      this.fetchDetailedStats()
    }
  },
  methods: {
    fetchParkingLots() {
      console.log('statistics/index.vue 中调用 fetchParkingLots')

      // 使用直接请求，避免依赖特定的API路径
      request({
        url: '/api/parkinglots',
        method: 'get'
      }).then(response => {
        console.log('停车场列表响应 (parkinglots):', response)
        if (response.code === 20000 && response.data && response.data.items) {
          this.parkingLots = response.data.items
        } else if (response.data && response.data.items) {
          this.parkingLots = response.data.items
        } else {
          // 如果第一个API失败，尝试备用API
          console.log('尝试备用API路径')
          this.fetchParkingLotsBackup()
        }
      }).catch(error => {
        console.error('获取停车场列表失败 (parkinglots):', error)
        // 尝试备用API
        this.fetchParkingLotsBackup()
      })
    },

    // 备用API路径
    fetchParkingLotsBackup() {
      request({
        url: '/api/parking-lots',
        method: 'get'
      }).then(response => {
        console.log('停车场列表响应 (parking-lots):', response)
        if (response.code === 20000 && response.data && response.data.items) {
          this.parkingLots = response.data.items
        } else if (response.data && response.data.items) {
          this.parkingLots = response.data.items
        } else {
          console.error('两个API路径都无法获取停车场列表')
          this.$message.error('获取停车场列表失败')

          // 使用模拟数据作为最后的备用方案
          this.parkingLots = [
            { id: 1, name: '北门停车场', total_spaces: 20, occupied_spaces: 10 },
            { id: 2, name: '东区停车场', total_spaces: 30, occupied_spaces: 15 },
            { id: 3, name: '科研楼停车场', total_spaces: 15, occupied_spaces: 5 }
          ]
        }
      }).catch(error => {
        console.error('获取停车场列表失败 (parking-lots):', error)
        this.$message.error('获取停车场列表失败')

        // 使用模拟数据作为最后的备用方案
        this.parkingLots = [
          { id: 1, name: '北门停车场', total_spaces: 20, occupied_spaces: 10 },
          { id: 2, name: '东区停车场', total_spaces: 30, occupied_spaces: 15 },
          { id: 3, name: '科研楼停车场', total_spaces: 15, occupied_spaces: 5 }
        ]
      })
    },
    fetchDetailedStats() {
      this.loading = true
      console.log('获取详细统计数据')

      // 构建查询参数
      const params = {
        ...this.filterParams,
        _t: new Date().getTime() // 添加时间戳防止缓存
      }

      console.log('统计数据请求参数:', params)

      getParkingStats(params)
        .then(response => {
          console.log('获取详细统计数据成功:', response)

          if (response.code === 20000 && response.data) {
            // 处理详细统计数据
            this.processDetailedStats(response.data)
          } else {
            console.warn('获取详细统计数据返回异常:', response)
            this.$message.error('获取详细统计数据失败')
          }
        })
        .catch(error => {
          console.error('获取详细统计数据失败:', error)
          this.$message.error('获取详细统计数据失败: ' + (error.message || '未知错误'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    processDetailedStats(data) {
      console.log('处理详细统计数据:', data)

      // 确保 overview 数据存在
      if (!data.overview) {
        data.overview = {
          total_records: 0,
          active_records: 0,
          completed_records: 0,
          abnormal_records: 0,
          today_records: 0,
          avg_duration_hours: 0
        }
      }

      // 处理停车记录统计数据
      if (data.daily_stats && data.daily_stats.length > 0) {
        // 获取总体数据
        const totalRecords = data.overview.total_records || 0
        const activeRecords = data.overview.active_records || 0
        const completedRecords = data.overview.completed_records || 0

        // 计算比例，用于估算每天的完成和进行中记录数
        const completedRatio = totalRecords > 0 ? completedRecords / totalRecords : 0.9
        const activeRatio = totalRecords > 0 ? activeRecords / totalRecords : 0.1

        this.recordsData = data.daily_stats.map(item => {
          // 如果后端已经提供了 completed 和 active 字段，直接使用
          // 否则根据总体比例估算
          const completed = item.completed !== undefined ? item.completed : Math.round(item.count * completedRatio)
          const active = item.active !== undefined ? item.active : Math.round(item.count * activeRatio)

          // 确保 completed + active = count
          const adjustedCompleted = item.count > 0 ?
            Math.min(completed, item.count) :
            completed
          const adjustedActive = item.count > 0 ?
            Math.min(active, item.count - adjustedCompleted) :
            active

          return {
            date: item.date,
            total: item.count || 0,
            completed: adjustedCompleted,
            active: adjustedActive,
            // 使用后端提供的平均停车时长，如果没有则使用默认值
            avgDuration: data.overview.avg_duration_hours || 2,
            peakHour: data.peak_hour || '暂无数据',
            // 使用实际的停车场使用率，如果没有则使用默认值
            utilization: data.parking_lots && data.parking_lots.length > 0 ?
                        Math.round(data.parking_lots.reduce((sum, lot) => sum + (lot.utilization_rate || 0), 0) / data.parking_lots.length) :
                        50
          }
        })
      } else {
        this.recordsData = []
      }

      // 处理停车场统计数据
      if (data.parking_lots && data.parking_lots.length > 0) {
        this.parkingLotsData = data.parking_lots.map(item => {
          // 确保所有必要的字段都存在
          const totalSpaces = item.total_spaces || 0
          const occupiedSpaces = item.occupied_spaces || 0
          const availableSpaces = Math.max(0, totalSpaces - occupiedSpaces)
          const utilizationRate = totalSpaces > 0 ?
            Math.round((occupiedSpaces / totalSpaces) * 100) :
            item.utilization_rate || 0

          return {
            name: item.name || '未命名停车场',
            totalSpaces: totalSpaces,
            occupiedSpaces: occupiedSpaces,
            availableSpaces: availableSpaces,
            utilizationRate: utilizationRate,
            totalRecords: item.total_records || 0,
            todayRecords: item.today_records || 0
          }
        })
      } else {
        this.parkingLotsData = []
      }

      // 处理车辆类型统计数据
      if (data.vehicle_types && data.vehicle_types.length > 0) {
        // 计算总车辆数
        const totalVehicles = data.vehicle_types.reduce((sum, item) => sum + (item.count || 0), 0)

        this.vehicleTypesData = data.vehicle_types.map(item => {
          const count = item.count || 0
          return {
            type: item.type || '未知类型',
            count: count,
            percentage: totalVehicles > 0 ? Math.round((count / totalVehicles) * 100) : 0,
            // 使用后端提供的平均停车时长，如果没有则使用默认值
            avgDuration: data.overview.avg_duration_hours || 2
          }
        })
      } else {
        this.vehicleTypesData = []
      }

      // 打印处理后的数据，用于调试
      console.log('处理后的数据:', {
        recordsData: this.recordsData,
        parkingLotsData: this.parkingLotsData,
        vehicleTypesData: this.vehicleTypesData
      })
    },
    handleFilterChange() {
      // 刷新统计数据
      this.fetchDetailedStats()

      // 刷新图表
      if (this.$refs.parkingStats) {
        this.$refs.parkingStats.fetchData()
      }
    },
    handleDateChange(val) {
      if (val) {
        this.filterParams.startDate = val[0]
        this.filterParams.endDate = val[1]
      } else {
        this.filterParams.startDate = null
        this.filterParams.endDate = null
      }

      this.handleFilterChange()
    },
    handleExport() {
      // 构建导出参数
      const params = { ...this.filterParams }

      // 调用导出API
      exportParkingRecords(params)
        .then(response => {
          // 创建Blob对象
          const blob = new Blob([response], { type: 'application/vnd.ms-excel' })

          // 创建下载链接
          const link = document.createElement('a')
          link.href = URL.createObjectURL(blob)

          // 设置文件名
          const now = new Date()
          const fileName = `停车记录报表_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}.xlsx`
          link.download = fileName

          // 触发下载
          link.click()

          // 释放URL对象
          URL.revokeObjectURL(link.href)

          this.$message.success('报表导出成功')
        })
        .catch(error => {
          console.error('报表导出失败:', error)
          this.$message.error('报表导出失败: ' + (error.message || '未知错误'))
        })
    },
    formatDuration(hours) {
      const h = Math.floor(hours)
      const m = Math.round((hours - h) * 60)

      if (h > 0) {
        return `${h}小时${m > 0 ? ` ${m}分钟` : ''}`
      } else {
        return `${m}分钟`
      }
    },
    getUtilizationColor(rate) {
      if (rate < 30) {
        return '#67C23A' // 绿色，使用率低
      } else if (rate < 70) {
        return '#E6A23C' // 黄色，使用率中等
      } else {
        return '#F56C6C' // 红色，使用率高
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-container {
    margin-bottom: 20px;

    .filter-item {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }
  }

  .detailed-stats {
    margin-top: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
