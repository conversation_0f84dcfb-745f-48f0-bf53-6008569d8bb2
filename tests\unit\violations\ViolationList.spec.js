import { shallowMount, createLocalVue } from '@vue/test-utils'
import ElementUI from 'element-ui'
import ViolationList from '@/views/violations/components/ViolationList.vue'
import Pagination from '@/components/Pagination'

const localVue = createLocalVue()
localVue.use(ElementUI)
localVue.component('pagination', Pagination)

describe('ViolationList.vue', () => {
  let wrapper
  const mockFetchList = jest.fn().mockResolvedValue({
    data: {
      items: [
        {
          id: 1,
          bike_number: 'TEST001',
          violation_type: '违规停车',
          location: '教学楼A区',
          violation_time: '2023-05-01T10:00:00',
          status: 0,
          status_text: '未处理',
          recorder_name: '保安1'
        },
        {
          id: 2,
          bike_number: 'TEST002',
          violation_type: '占用消防通道',
          location: '图书馆前',
          violation_time: '2023-05-02T14:30:00',
          status: 1,
          status_text: '已处理',
          recorder_name: '保安2'
        }
      ],
      total: 2
    }
  })

  beforeEach(() => {
    wrapper = shallowMount(ViolationList, {
      localVue,
      propsData: {
        fetchListMethod: mockFetchList,
        showSearch: true,
        showDateRange: true,
        showCreateButton: false,
        showRecorder: true,
        canAppeal: true,
        canHandle: false
      },
      stubs: {
        'el-table': true,
        'el-table-column': true,
        'el-select': true,
        'el-option': true,
        'el-date-picker': true,
        'el-input': true,
        'el-button': true,
        'el-tag': true,
        'el-empty': true,
        'pagination': true
      }
    })
  })

  afterEach(() => {
    wrapper.destroy()
    jest.clearAllMocks()
  })

  it('初始化时应该调用fetchList方法', () => {
    expect(mockFetchList).toHaveBeenCalledTimes(1)
  })

  it('handleFilter方法应该重置页码并调用getList', async () => {
    wrapper.vm.listQuery.page = 2
    await wrapper.vm.handleFilter()
    expect(wrapper.vm.listQuery.page).toBe(1)
    expect(mockFetchList).toHaveBeenCalledTimes(2)
  })

  it('handleDateRangeChange方法应该正确设置日期范围', () => {
    const dateRange = ['2023-01-01', '2023-01-31']
    wrapper.vm.handleDateRangeChange(dateRange)
    expect(wrapper.vm.listQuery.start_date).toBe('2023-01-01')
    expect(wrapper.vm.listQuery.end_date).toBe('2023-01-31')

    wrapper.vm.handleDateRangeChange(null)
    expect(wrapper.vm.listQuery.start_date).toBeUndefined()
    expect(wrapper.vm.listQuery.end_date).toBeUndefined()
  })

  it('getStatusType方法应该返回正确的状态类型', () => {
    expect(wrapper.vm.getStatusType(0)).toBe('info')
    expect(wrapper.vm.getStatusType(1)).toBe('success')
    expect(wrapper.vm.getStatusType(2)).toBe('warning')
    expect(wrapper.vm.getStatusType(3)).toBe('danger')
    expect(wrapper.vm.getStatusType(999)).toBe('info') // 默认值
  })

  it('getViolationTypeTag方法应该返回正确的违规类型标签', () => {
    expect(wrapper.vm.getViolationTypeTag('违规停车')).toBe('danger')
    expect(wrapper.vm.getViolationTypeTag('占用消防通道')).toBe('danger')
    expect(wrapper.vm.getViolationTypeTag('占用无障碍通道')).toBe('warning')
    expect(wrapper.vm.getViolationTypeTag('超时停车')).toBe('warning')
    expect(wrapper.vm.getViolationTypeTag('未知类型')).toBe('info') // 默认值
  })

  it('formatDateTime方法应该正确格式化日期时间', () => {
    const date = new Date('2023-05-01T10:00:00')
    expect(wrapper.vm.formatDateTime(date)).toMatch(/2023-05-01 10:00/)
    expect(wrapper.vm.formatDateTime('')).toBe('')
  })

  it('tableRowClassName方法应该返回正确的行类名', () => {
    expect(wrapper.vm.tableRowClassName({ row: { status: 2 } })).toBe('warning-row')
    expect(wrapper.vm.tableRowClassName({ row: { status: 3 } })).toBe('success-row')
    expect(wrapper.vm.tableRowClassName({ row: { status: 0 } })).toBe('')
  })

  it('getStatusCount方法应该返回指定状态的记录数量', () => {
    wrapper.vm.list = [
      { status: 0 },
      { status: 0 },
      { status: 1 },
      { status: 2 },
      { status: 3 }
    ]
    expect(wrapper.vm.getStatusCount(0)).toBe(2)
    expect(wrapper.vm.getStatusCount(1)).toBe(1)
    expect(wrapper.vm.getStatusCount(2)).toBe(1)
    expect(wrapper.vm.getStatusCount(3)).toBe(1)
    expect(wrapper.vm.getStatusCount(999)).toBe(0)
  })

  it('应该正确触发事件', async () => {
    const row = { id: 1 }
    
    await wrapper.vm.handleDetail(row)
    expect(wrapper.emitted().detail).toBeTruthy()
    expect(wrapper.emitted().detail[0]).toEqual([row])
    
    await wrapper.vm.handleAppeal(row)
    expect(wrapper.emitted().appeal).toBeTruthy()
    expect(wrapper.emitted().appeal[0]).toEqual([row])
    
    await wrapper.vm.handleProcess(row)
    expect(wrapper.emitted().process).toBeTruthy()
    expect(wrapper.emitted().process[0]).toEqual([row])
    
    await wrapper.vm.handleCreate()
    expect(wrapper.emitted().create).toBeTruthy()
  })
})
