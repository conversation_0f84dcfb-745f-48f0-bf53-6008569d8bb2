from app import db
from passlib.hash import pbkdf2_sha256 as sha256
from marshmallow_sqlalchemy import SQLAlchemyAutoSchema
from marshmallow import fields


class Players(db.Model):
    __tablename__ = 'players'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    username = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(120), nullable=False)
    
    # 添加与Users表的关联字段 - 设置为外键
    user_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=True, comment='对应users表的u_id')
    
    # 使用字符串形式的关系定义，避免循环导入
    user = db.relationship('Users', backref=db.backref('player_account', uselist=False))
    
    def __init__(self, username, password, user_id=None):
        self.username = username
        self.password = password
        self.user_id = user_id

    def create(self):
        db.session.add(self)
        db.session.commit()
        return self

    @classmethod
    def find_by_username(cls, username):
        return cls.query.filter_by(username=username).first()

    #生成密码
    @staticmethod
    def generate_hash(password):
        return sha256.hash(password)

    # 验证密码
    @staticmethod
    def verify_hash(password, hash):
        print(f"尝试验证密码: password={password}, hash={hash[:20]}...")
        try:
            result = sha256.verify(password, hash)
            print(f"验证结果: {result}")
            return result
        except Exception as e:
            print(f"密码验证出错: {str(e)}")
            return False
        
    # 获取详细信息（包括关联的用户信息）
    def get_details(self, include_user=False):
        player_data = {
            'id': self.id,
            'username': self.username,
            'user_id': self.user_id
        }
        
        if include_user and self.user:
            # 使用字典格式而不是schema
            player_data['user'] = {
                'u_id': self.user.u_id,
                'u_name': self.user.u_name,
                'u_role': self.user.u_role,
                'u_belong': self.user.u_belong,
                'u_phone': self.user.u_phone
            }
            
        return player_data
