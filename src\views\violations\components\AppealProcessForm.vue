<template>
  <div class="appeal-process-form">
    <el-dialog
      title="处理申诉"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :append-to-body="true"
      :destroy-on-close="false"
      custom-class="appeal-process-dialog"
      @open="onDialogOpen"
      @close="onDialogClose"
    >
      <div class="appeal-info">
        <div class="appeal-header">
          <div class="appeal-id">
            <span>申诉编号: #{{ appeal.id }}</span>
            <el-tag v-if="appeal.status !== undefined" size="small" :type="getStatusType(appeal.status)">
              {{ getStatusText(appeal.status) }}
            </el-tag>
          </div>
          <div class="appeal-time">
            <i class="el-icon-time"></i>
            <span>{{ formatDateTime(appeal.created_at) }}</span>
          </div>
        </div>

        <div class="appeal-user">
          <i class="el-icon-user"></i>
          <span>{{ appeal.user_name || 'admin' }}</span>
          <i class="el-icon-view"></i>
          <span>违规ID: {{ appeal.violation_id || '-' }}</span>
        </div>

        <!-- 违规记录信息 -->
        <div class="violation-info">
          <div class="info-title">违规记录信息</div>

          <div v-if="loadingViolation" class="loading-text">
            <i class="el-icon-loading"></i> 加载违规记录信息...
          </div>

          <div v-else-if="violationDetail" class="info-content">
            <div class="info-item">
              <span class="info-label">车牌号:</span>
              <span class="info-value">{{ violationDetail.bike_number || '未知' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">违规类型:</span>
              <span class="info-value">{{ violationDetail.violation_type || '未知' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">违规地点:</span>
              <span class="info-value">{{ violationDetail.location || '未知' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">违规时间:</span>
              <span class="info-value">{{ formatDateTime(violationDetail.violation_time) || '未知' }}</span>
            </div>
          </div>

          <div v-else class="info-content">
            <div class="info-item">
              <span class="info-label">车牌号:</span>
              <span class="info-value">未知</span>
            </div>
            <div class="info-item">
              <span class="info-label">违规类型:</span>
              <span class="info-value">未知</span>
            </div>
            <div class="info-item">
              <span class="info-label">违规地点:</span>
              <span class="info-value">未知</span>
            </div>
            <div class="info-item">
              <span class="info-label">违规时间:</span>
              <span class="info-value">未知</span>
            </div>
          </div>
        </div>

        <div class="appeal-reason">
          <div class="reason-title">申诉理由</div>
          <div class="reason-content">
            {{ appeal.reason || '' }}
          </div>
        </div>
      </div>

      <el-divider content-position="center">处理决定</el-divider>

      <el-form ref="form" :model="form" :rules="rules" label-position="left" class="process-form">
        <el-form-item label="处理结果" prop="status" :required="true" label-width="80px">
          <div class="result-selection-box">
            <div class="radio-group-container">
              <el-radio v-model="form.status" :label="AppealStatus.APPROVED" class="custom-radio">
                <div class="radio-content">
                  <div class="radio-icon-wrapper success">
                    <i class="el-icon-check"></i>
                  </div>
                  <div class="radio-text">
                    <span class="radio-label">{{ AppealStatus.TEXT_APPROVED }}</span>
                    <span class="radio-desc">撤销违规记录</span>
                  </div>
                </div>
              </el-radio>
              <el-radio v-model="form.status" :label="AppealStatus.REJECTED" class="custom-radio">
                <div class="radio-content">
                  <div class="radio-icon-wrapper danger">
                    <i class="el-icon-close"></i>
                  </div>
                  <div class="radio-text">
                    <span class="radio-label">{{ AppealStatus.TEXT_REJECTED }}</span>
                    <span class="radio-desc">维持违规记录</span>
                  </div>
                </div>
              </el-radio>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="处理意见" prop="comment" :required="true" label-width="80px">
          <el-input
            v-model="form.comment"
            type="textarea"
            :rows="4"
            :placeholder="form.status === AppealStatus.APPROVED ? '请输入通过申诉的原因' : '请输入未通过申诉的原因'"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>


      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" plain>
          <i class="el-icon-close"></i> 取消
        </el-button>
        <el-button
          :type="form.status === AppealStatus.APPROVED ? 'success' : 'danger'"
          @click="submitForm"
          :loading="submitting"
        >
          <i :class="form.status === AppealStatus.APPROVED ? 'el-icon-check' : 'el-icon-close'"></i>
          {{ form.status === AppealStatus.APPROVED ? '通过申诉' : '拒绝申诉' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
import { getViolationDetail } from '@/api/violations'

export default {
  name: 'AppealProcessForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    appeal: {
      type: Object,
      required: true
    },
    submitMethod: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        status: 1, // AppealStatus.APPROVED
        comment: ''
      },
      rules: {
        status: [
          { required: true, message: '请选择处理结果', trigger: 'change' }
        ],
        comment: [
          { required: true, message: '请输入处理意见', trigger: 'blur' },
          { min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur' }
        ]
      },
      submitting: false,
      violationDetail: null,
      loadingViolation: false,
      // 在组件内部定义AppealStatus常量
      AppealStatus: {
        PENDING: 0,      // 待审核
        APPROVED: 1,     // 已通过
        REJECTED: 2,     // 未通过
        TEXT_PENDING: '待审核',
        TEXT_APPROVED: '已通过',
        TEXT_REJECTED: '未通过',
        TYPE_PENDING: 'warning',
        TYPE_APPROVED: 'success',
        TYPE_REJECTED: 'danger',
        ICON_PENDING: 'el-icon-time',
        ICON_APPROVED: 'el-icon-circle-check',
        ICON_REJECTED: 'el-icon-circle-close',
        getText(status) {
          const textMap = {
            0: '待审核',
            1: '已通过',
            2: '未通过'
          }
          return textMap[status] || '未知状态'
        },
        getType(status) {
          const typeMap = {
            0: 'warning',
            1: 'success',
            2: 'danger'
          }
          return typeMap[status] || 'info'
        },
        getIcon(status) {
          const iconMap = {
            0: 'el-icon-time',
            1: 'el-icon-circle-check',
            2: 'el-icon-circle-close'
          }
          return iconMap[status] || 'el-icon-info'
        }
      }
    }
  },
  watch: {
    visible(val) {
      if (this.dialogVisible !== val) {
        this.dialogVisible = val
      }
    },
    dialogVisible(val) {
      if (this.visible !== val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    formatDateTime(time) {
      if (!time) return ''
      return parseTime(new Date(time), '{y}-{m}-{d} {h}:{i}')
    },
    getStatusType(status) {
      return this.AppealStatus.getType(status)
    },
    getStatusText(status) {
      return this.AppealStatus.getText(status)
    },
    onDialogOpen() {
      this.$nextTick(() => {
        // 输出调试信息
        console.log('申诉对话框打开，申诉数据:', this.appeal)

        // 重置表单
        this.form = {
          status: this.AppealStatus.APPROVED,
          comment: ''
        }
        // 如果是编辑模式，填充表单数据
        if (this.appeal && this.appeal.status > 0) {
          this.form.status = this.appeal.status
          this.form.comment = this.appeal.comment || ''
        }

        // 获取违规记录详情
        if (this.appeal && this.appeal.violation_id) {
          console.log('开始获取违规记录详情，违规ID:', this.appeal.violation_id)
          this.loadViolationDetail(this.appeal.violation_id)
        } else {
          console.warn('没有找到违规ID，无法获取违规记录详情')
          this.violationDetail = null
        }
      })
    },

    // 获取违规记录详情
    loadViolationDetail(violationId) {
      if (!violationId) return

      this.loadingViolation = true
      this.violationDetail = null

      getViolationDetail(violationId)
        .then(response => {
          console.log('获取到违规记录详情:', response.data)

          // 处理API返回的数据格式
          // 如果response.data是对象且包含violation字段，使用violation字段
          if (response.data && response.data.violation) {
            this.violationDetail = response.data.violation
          }
          // 如果response.data本身就是违规记录对象，直接使用
          else if (response.data && response.data.id) {
            this.violationDetail = response.data
          }
          // 如果没有获取到有效数据，创建一个默认对象
          else {
            this.violationDetail = {
              bike_number: '未知',
              violation_type: '未知',
              location: '未知',
              violation_time: null
            }
          }
        })
        .catch(error => {
          console.error('获取违规记录详情失败:', error)
          this.$message.error('获取违规记录详情失败')

          // 创建一个默认对象，以便UI显示
          this.violationDetail = {
            bike_number: '未知',
            violation_type: '未知',
            location: '未知',
            violation_time: null
          }
        })
        .finally(() => {
          this.loadingViolation = false
        })
    },
    onDialogClose() {
      this.$refs.form && this.$refs.form.resetFields()
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (!valid) return

        this.submitting = true

        this.submitMethod(this.appeal.id, this.form)
          .then(response => {
            this.$notify({
              title: '成功',
              message: this.form.status === this.AppealStatus.APPROVED ? '申诉已通过，相关违规记录已撤销' : '申诉未通过，相关违规记录已维持',
              type: 'success',
              duration: 2000
            })
            this.dialogVisible = false
            this.$emit('success')
          })
          .catch(error => {
            console.error('处理申诉失败:', error)
            this.$message.error(error.message || '处理失败，请重试')
          })
          .finally(() => {
            this.submitting = false
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.appeal-process-form {
  transition: all 0.3s ease;
}

.appeal-process-dialog {
  ::v-deep .el-dialog {
    transition: all 0.3s ease;
    border-radius: 4px;
    overflow: hidden;
  }

  ::v-deep .el-dialog__header {
    background-color: #f5f7fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e6e6e6;
  }

  ::v-deep .el-dialog__title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }

  ::v-deep .el-dialog__body {
    padding: 0;
  }

  ::v-deep .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #e6e6e6;
    text-align: right;
    background-color: #f9f9f9;
  }

  ::v-deep .el-button {
    padding: 9px 20px;
    border-radius: 4px;
    font-size: 14px;

    &.el-button--success {
      background-color: #67C23A;
      border-color: #67C23A;
    }

    &.el-button--danger {
      background-color: #F56C6C;
      border-color: #F56C6C;
    }
  }
}

.appeal-info {
  padding: 20px;
  background-color: #fff;

  .appeal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .appeal-id {
      display: flex;
      align-items: center;

      span {
        margin-right: 10px;
        font-weight: 500;
        color: #333;
      }
    }

    .appeal-time {
      color: #909399;
      font-size: 14px;

      i {
        margin-right: 5px;
      }
    }
  }

  .appeal-user {
    margin-bottom: 15px;
    color: #606266;
    font-size: 14px;

    i {
      margin-right: 5px;
      color: #909399;

      &.el-icon-view {
        margin-left: 15px;
      }
    }
  }

  .violation-info {
    margin-top: 15px;
    margin-bottom: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    border: 1px solid #ebeef5;

    .info-title {
      font-weight: 500;
      margin-bottom: 12px;
      color: #606266;
      font-size: 14px;
    }

    .info-content {
      display: flex;
      flex-wrap: wrap;
    }

    .info-item {
      width: 50%;
      margin-bottom: 8px;
      font-size: 14px;
      line-height: 1.6;

      .info-label {
        color: #909399;
        margin-right: 5px;
      }

      .info-value {
        color: #303133;
        font-weight: 500;
      }
    }

    .loading-text {
      color: #909399;
      text-align: center;
      padding: 10px 0;
      font-size: 14px;

      i {
        margin-right: 5px;
      }
    }
  }

  .appeal-reason {
    margin-top: 15px;

    .reason-title {
      font-weight: 500;
      margin-bottom: 10px;
      color: #606266;
      font-size: 14px;
    }

    .reason-content {
      background-color: #f5f7fa;
      padding: 15px;
      border-radius: 4px;
      color: #606266;
      line-height: 1.6;
      min-height: 60px;
      font-size: 14px;
    }
  }
}

.el-divider {
  margin: 0;
}

.process-form {
  padding: 20px;

  .result-selection-box {
    border: 1px solid #F56C6C;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    width: 100%;
  }

  .radio-group-container {
    display: flex;
    width: 100%;
    justify-content: space-between;

    .custom-radio {
      margin: 0;
      padding: 0;
      width: 48%;

      .radio-content {
        display: flex;
        align-items: center;
        padding: 5px 0;
      }

      .radio-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;

        &.success {
          color: #67C23A;
        }

        &.danger {
          color: #F56C6C;
        }

        i {
          font-size: 16px;
        }
      }

      .radio-text {
        display: flex;
        flex-direction: column;
      }

      .radio-label {
        font-size: 14px;
        font-weight: 500;
        line-height: 1.4;
      }

      .radio-desc {
        color: #909399;
        font-size: 12px;
        line-height: 1.4;
      }
    }
  }



  ::v-deep .el-form-item__label {
    color: #606266;

    &::before {
      content: '';
    }
  }

  ::v-deep .el-form-item.is-required .el-form-item__label:before {
    content: '* ';
    color: #F56C6C;
    margin-right: 2px;
  }

  ::v-deep .el-textarea__inner {
    border-color: #DCDFE6;

    &:focus {
      border-color: #409EFF;
    }
  }

  ::v-deep .el-radio__inner {
    border-color: #DCDFE6;
  }

  ::v-deep .el-radio__input.is-checked .el-radio__inner {
    border-color: #409EFF;
    background: #409EFF;
  }

  ::v-deep .el-radio {
    margin-right: 0;
  }
}
</style>
