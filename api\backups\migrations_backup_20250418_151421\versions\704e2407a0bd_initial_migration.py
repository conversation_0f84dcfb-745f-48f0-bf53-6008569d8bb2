"""initial migration

Revision ID: 704e2407a0bd
Revises: 
Create Date: 2025-04-15 14:02:55.106303

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '704e2407a0bd'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('players', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.create_unique_constraint(None, ['username'])

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('u_email', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('avatar', sa.String(length=255), nullable=True))
        batch_op.add_column(sa.Column('version', sa.Integer(), nullable=True))
        batch_op.alter_column('u_id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.create_unique_constraint(None, ['u_name'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')
        batch_op.alter_column('u_id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)
        batch_op.drop_column('version')
        batch_op.drop_column('avatar')
        batch_op.drop_column('u_email')

    with op.batch_alter_table('players', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    # ### end Alembic commands ###
