"""
添加状态变更日志表的迁移脚本
"""
import os
import sqlite3
from datetime import datetime

def upgrade():
    """升级数据库，添加状态变更日志表"""
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'sys.db')
    print(f"数据库路径: {db_path}")
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查表是否已存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='status_change_logs'")
        if cursor.fetchone():
            print("状态变更日志表已存在，无需创建")
            return
        
        # 创建状态变更日志表
        cursor.execute("""
        CREATE TABLE status_change_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            entity_type VARCHAR(20) NOT NULL,
            entity_id INTEGER NOT NULL,
            old_status INTEGER NOT NULL,
            new_status INTEGER NOT NULL,
            operator_id INTEGER NOT NULL,
            change_time TIMESTAMP NOT NULL,
            related_entities TEXT,
            FOREIGN KEY (operator_id) REFERENCES users (u_id)
        )
        """)
        
        # 创建索引
        cursor.execute("CREATE INDEX idx_status_change_logs_entity ON status_change_logs (entity_type, entity_id)")
        cursor.execute("CREATE INDEX idx_status_change_logs_operator ON status_change_logs (operator_id)")
        
        # 提交事务
        conn.commit()
        print("状态变更日志表创建成功")
        
    except Exception as e:
        conn.rollback()
        print(f"创建状态变更日志表失败: {str(e)}")
        raise
    finally:
        cursor.close()
        conn.close()

def downgrade():
    """降级数据库，删除状态变更日志表"""
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'sys.db')
    print(f"数据库路径: {db_path}")
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='status_change_logs'")
        if not cursor.fetchone():
            print("状态变更日志表不存在，无需删除")
            return
        
        # 删除表
        cursor.execute("DROP TABLE status_change_logs")
        
        # 提交事务
        conn.commit()
        print("状态变更日志表删除成功")
        
    except Exception as e:
        conn.rollback()
        print(f"删除状态变更日志表失败: {str(e)}")
        raise
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    print("开始执行数据库迁移...")
    upgrade()
    print("数据库迁移完成")
