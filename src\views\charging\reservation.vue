<template>
  <div class="app-container charging-reservation-page">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="page-header-card">
          <div class="page-header">
            <div class="page-title">
              <i class="el-icon-lightning"></i>
              <span>充电预约中心</span>
            </div>
            <div class="page-actions">
              <el-button type="primary" @click="openReservationDialog">新增预约</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <!-- 左侧充电状态和操作区域 -->
      <el-col :span="16">
        <!-- 充电状态卡片 -->
        <el-card class="status-card" v-loading="activeChargingLoading">
          <div slot="header" class="clearfix">
            <span><i class="el-icon-lightning"></i> 充电状态</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshActiveCharging">刷新</el-button>
          </div>

          <div v-if="activeCharging" class="active-charging">
            <div class="charging-header">
              <div class="charging-title">
                <i class="el-icon-lightning"></i>
                <span>正在充电</span>
              </div>
              <el-tag type="success">{{ activeCharging.status_text }}</el-tag>
            </div>

            <div class="charging-info">
              <div class="info-row">
                <span class="info-label">车辆信息</span>
                <span class="info-value">{{ activeCharging.vehicle ? `${activeCharging.vehicle.number} (${activeCharging.vehicle.brand} ${activeCharging.vehicle.color})` : `车辆ID: ${activeCharging.vehicle_id}` }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">停车场</span>
                <span class="info-value">{{ activeCharging.parking_lot ? activeCharging.parking_lot.name : `停车场ID: ${activeCharging.parking_lot_id}` }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">车位编号</span>
                <span class="info-value">{{ activeCharging.parking_space ? activeCharging.parking_space.space_number : `车位ID: ${activeCharging.parking_space_id}` }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">开始时间</span>
                <span class="info-value">{{ formatDateTime(activeCharging.start_time) }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">已充电时长</span>
                <span class="info-value">{{ activeCharging.duration_text }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">预计费用</span>
                <span class="info-value fee">{{ chargingFee !== null ? `¥${chargingFee}` : '计算中...' }}</span>
              </div>
            </div>

            <div class="charging-actions">
              <el-button type="danger" @click="handleEndCharging" :loading="endChargingLoading">结束充电</el-button>
            </div>
          </div>

          <div v-else class="no-charging">
            <el-empty description="当前没有进行中的充电记录">
              <template #description>
                <p>当前没有进行中的充电记录</p>
                <p class="sub-desc">您可以在停车时选择充电车位并开启充电功能</p>
              </template>
              <el-button type="primary" @click="goToParkingCenter">前往停车中心</el-button>
            </el-empty>
          </div>
        </el-card>

        <!-- 充电记录卡片 -->
        <el-card class="records-card" v-loading="chargingRecordsLoading">
          <div slot="header" class="clearfix">
            <span><i class="el-icon-tickets"></i> 充电记录</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshChargingRecords">刷新</el-button>
          </div>

          <el-table
            v-if="chargingRecords.length > 0"
            :data="chargingRecords"
            style="width: 100%"
            border
            stripe
          >
            <el-table-column
              prop="id"
              label="ID"
              width="60"
              align="center"
            />
            <el-table-column
              label="车辆信息"
              min-width="180"
            >
              <template slot-scope="scope">
                {{ scope.row.vehicle ? `${scope.row.vehicle.number} (${scope.row.vehicle.brand} ${scope.row.vehicle.color})` : `车辆ID: ${scope.row.vehicle_id}` }}
              </template>
            </el-table-column>
            <el-table-column
              label="停车场"
              min-width="120"
            >
              <template slot-scope="scope">
                {{ scope.row.parking_lot ? scope.row.parking_lot.name : `停车场ID: ${scope.row.parking_lot_id}` }}
              </template>
            </el-table-column>
            <el-table-column
              label="开始时间"
              min-width="180"
            >
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.start_time) }}
              </template>
            </el-table-column>
            <el-table-column
              label="结束时间"
              min-width="180"
            >
              <template slot-scope="scope">
                {{ scope.row.end_time ? formatDateTime(scope.row.end_time) : '进行中' }}
              </template>
            </el-table-column>
            <el-table-column
              label="时长"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.duration_text }}
              </template>
            </el-table-column>
            <el-table-column
              label="费用"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.fee ? `¥${scope.row.fee}` : '-' }}
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ scope.row.status_text }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>

          <el-empty description="暂无充电记录" />

          <div class="pagination-container" v-if="total > 0">
            <el-pagination
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="listQuery.page"
              :page-sizes="[5, 10, 20]"
              :page-size="listQuery.per_page"
              layout="total, sizes, prev, pager, next"
              :total="total"
            />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧充电信息和统计区域 -->
      <el-col :span="8">
        <!-- 充电预约卡片 -->
        <el-card class="reservation-card">
          <div slot="header" class="clearfix">
            <span><i class="el-icon-date"></i> 充电预约</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="openReservationDialog">新增预约</el-button>
          </div>

          <charging-reservation :show-list="true" @success="handleReservationSuccess" />
        </el-card>

        <!-- 充电服务介绍卡片 -->
        <el-card class="intro-card">
          <div slot="header" class="clearfix">
            <span><i class="el-icon-info"></i> 充电服务说明</span>
          </div>

          <div class="service-intro">
            <h4>如何使用充电服务</h4>
            <ol>
              <li>在停车时选择充电车位类型</li>
              <li>勾选"我需要使用充电功能"选项</li>
              <li>完成停车后，系统会自动创建充电记录</li>
              <li>充电完成后，在此页面点击"结束充电"</li>
            </ol>

            <h4>计费说明</h4>
            <p>充电费用根据充电时长自动计算，计费标准如下：</p>
            <ul>
              <li>基础费率：1元/小时</li>
              <li>高峰时段（8:00-22:00）：1.5元/小时</li>
              <li>最低收费：0.5元</li>
            </ul>

            <h4>注意事项</h4>
            <ul>
              <li>充电功能仅在充电车位可用</li>
              <li>请在充电完成后及时结束充电</li>
              <li>如有问题，请联系管理员</li>
            </ul>
          </div>
        </el-card>

        <!-- 充电通知卡片 -->
        <el-card class="notification-card">
          <div slot="header" class="clearfix">
            <span><i class="el-icon-bell"></i> 充电通知</span>
          </div>

          <charging-notification
            :notifications="notifications"
            @click="handleNotificationClick"
            @read="handleNotificationRead"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 结束充电对话框 -->
    <el-dialog
      title="结束充电"
      :visible.sync="endChargingDialogVisible"
      width="400px"
    >
      <div v-if="activeCharging">
        <div class="end-charging-info">
          <div class="info-item">
            <span class="label">车辆信息：</span>
            <span class="value">{{ activeCharging.vehicle ? `${activeCharging.vehicle.number} (${activeCharging.vehicle.brand} ${activeCharging.vehicle.color})` : `车辆ID: ${activeCharging.vehicle_id}` }}</span>
          </div>
          <div class="info-item">
            <span class="label">开始时间：</span>
            <span class="value">{{ formatDateTime(activeCharging.start_time) }}</span>
          </div>
          <div class="info-item">
            <span class="label">充电时长：</span>
            <span class="value">{{ activeCharging.duration_text }}</span>
          </div>
          <div class="info-item">
            <span class="label">预计费用：</span>
            <span class="value fee">{{ chargingFee !== null ? `¥${chargingFee}` : '计算中...' }}</span>
          </div>
        </div>

        <div class="end-charging-confirm">
          <p>确认结束充电？费用将自动计算。</p>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="endChargingDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmEndCharging" :loading="endChargingLoading">确认结束</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getChargingRecords, getUserChargingRecords, getChargingRecord, endChargingRecord, calculateChargingFee, getChargingStats } from '@/api/charging'
import ChargingReservation from '@/components/ChargingReservation'
import ChargingNotification from '@/components/ChargingNotification'

export default {
  name: 'ChargingReservationPage',
  components: {
    ChargingReservation,
    ChargingNotification
  },
  data() {
    return {
      // 充电记录列表
      chargingRecords: [],
      total: 0,
      listQuery: {
        page: 1,
        per_page: 10
      },
      chargingRecordsLoading: false,

      // 活跃充电记录
      activeCharging: null,
      activeChargingLoading: false,
      chargingFee: null,

      // 结束充电
      endChargingDialogVisible: false,
      endChargingLoading: false,

      // 定时器
      timer: null,

      // 通知列表
      notifications: []
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'roles',
      'name'
    ])
  },
  created() {
    this.fetchActiveCharging()
    this.fetchChargingRecords()

    // 生成模拟通知
    this.generateMockNotifications()

    // 设置定时刷新
    this.timer = setInterval(() => {
      if (this.activeCharging) {
        this.fetchActiveCharging()
      }
    }, 60000) // 每分钟刷新一次
  },
  beforeDestroy() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    // 打开预约对话框
    openReservationDialog() {
      // 获取ChargingReservation组件实例
      const reservationComponent = this.$children.find(child => child.$options.name === 'ChargingReservation')
      if (reservationComponent) {
        reservationComponent.openReservationDialog()
      }
    },

    // 获取活跃充电记录
    async fetchActiveCharging() {
      this.activeChargingLoading = true
      try {
        const response = await getUserChargingRecords(this.userId, { status: 0 })
        if (response.data && response.data.items && response.data.items.length > 0) {
          this.activeCharging = response.data.items[0]
          this.calculateFee(this.activeCharging.id)
        } else {
          this.activeCharging = null
          this.chargingFee = null
        }
      } catch (error) {
        console.error('获取活跃充电记录失败:', error)
        this.$message.error('获取活跃充电记录失败')
      } finally {
        this.activeChargingLoading = false
      }
    },

    // 获取充电记录列表
    async fetchChargingRecords() {
      this.chargingRecordsLoading = true
      try {
        const params = {
          ...this.listQuery,
          user_id: this.userId
        }

        const response = await getChargingRecords(params)
        if (response.data) {
          this.chargingRecords = response.data.items || []
          this.total = response.data.total || 0
        }
      } catch (error) {
        console.error('获取充电记录列表失败:', error)
        this.$message.error('获取充电记录列表失败')
      } finally {
        this.chargingRecordsLoading = false
      }
    },

    // 计算充电费用
    async calculateFee(recordId) {
      try {
        const response = await calculateChargingFee(recordId)
        if (response.data) {
          this.chargingFee = response.data.fee
        }
      } catch (error) {
        console.error('计算充电费用失败:', error)
        this.chargingFee = null
      }
    },

    // 处理结束充电
    handleEndCharging() {
      if (!this.activeCharging) {
        this.$message.warning('没有进行中的充电记录')
        return
      }

      this.endChargingDialogVisible = true
    },

    // 确认结束充电
    async confirmEndCharging() {
      if (!this.activeCharging) {
        this.$message.warning('没有进行中的充电记录')
        this.endChargingDialogVisible = false
        return
      }

      this.endChargingLoading = true
      try {
        const data = {
          fee: this.chargingFee
        }

        await endChargingRecord(this.activeCharging.id, data)
        this.$message.success('充电已结束')
        this.endChargingDialogVisible = false

        // 刷新数据
        this.fetchActiveCharging()
        this.fetchChargingRecords()
      } catch (error) {
        console.error('结束充电失败:', error)
        this.$message.error('结束充电失败: ' + (error.message || '未知错误'))
      } finally {
        this.endChargingLoading = false
      }
    },

    // 生成模拟通知
    generateMockNotifications() {
      const now = new Date()
      this.notifications = [
        {
          id: 1,
          title: '充电即将完成',
          message: '您的车辆充电即将完成，请及时结束充电并移动车辆',
          type: 'charging',
          time: new Date(now.getTime() - 10 * 60 * 1000), // 10分钟前
          read: false
        },
        {
          id: 2,
          title: '预约提醒',
          message: '您有一个充电预约将在30分钟后开始，请提前到达指定车位',
          type: 'reservation',
          time: new Date(now.getTime() - 2 * 60 * 60 * 1000), // 2小时前
          read: false
        },
        {
          id: 3,
          title: '充电完成',
          message: '您的车辆已完成充电，费用已自动结算',
          type: 'complete',
          time: new Date(now.getTime() - 5 * 60 * 60 * 1000), // 5小时前
          read: true
        },
        {
          id: 4,
          title: '充电异常',
          message: '检测到您的充电过程中出现异常，请联系管理员处理',
          type: 'warning',
          time: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000), // 1天前
          read: true
        },
        {
          id: 5,
          title: '系统维护通知',
          message: '系统将于今晚22:00-23:00进行维护，期间充电服务可能受到影响',
          type: 'info',
          time: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000), // 2天前
          read: true
        }
      ]
    },

    // 处理通知点击
    handleNotificationClick(notification) {
      console.log('点击通知:', notification)

      // 根据通知类型执行不同操作
      switch (notification.type) {
        case 'reservation':
          // 滚动到充电预约卡片
          this.$nextTick(() => {
            const reservationCard = document.querySelector('.reservation-card')
            if (reservationCard) {
              reservationCard.scrollIntoView({ behavior: 'smooth' })
            }
          })
          break
        case 'charging':
        case 'complete':
        case 'warning':
          // 如果有活跃充电记录，滚动到充电状态卡片
          if (this.activeCharging) {
            this.$nextTick(() => {
              const statusCard = document.querySelector('.status-card')
              if (statusCard) {
                statusCard.scrollIntoView({ behavior: 'smooth' })
              }
            })
          }
          break
        default:
          // 默认不执行任何操作
          break
      }
    },

    // 处理通知已读
    handleNotificationRead(notification) {
      console.log('标记通知为已读:', notification)
      // 在实际应用中，这里应该调用API将通知标记为已读
    },

    // 处理预约成功
    handleReservationSuccess(data) {
      this.$message.success('充电预约创建成功')
      // 可以在这里添加其他逻辑，比如刷新预约列表等
    },

    // 前往停车中心
    goToParkingCenter() {
      this.$router.push('/profile/myparking')
    },

    // 刷新活跃充电记录
    refreshActiveCharging() {
      this.fetchActiveCharging()
    },

    // 刷新充电记录列表
    refreshChargingRecords() {
      this.fetchChargingRecords()
    },

    // 处理分页大小变化
    handleSizeChange(size) {
      this.listQuery.per_page = size
      this.fetchChargingRecords()
    },

    // 处理页码变化
    handleCurrentChange(page) {
      this.listQuery.page = page
      this.fetchChargingRecords()
    },

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return ''

      const date = new Date(dateTimeStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'primary',  // 进行中
        1: 'success',  // 已完成
        2: 'danger'    // 异常
      }
      return statusMap[status] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-reservation-page {
  .page-header-card {
    margin-bottom: 20px;

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .page-title {
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;

        i {
          margin-right: 10px;
          color: #409EFF;
        }
      }
    }
  }

  .el-card {
    margin-bottom: 20px;
    border-radius: 8px;

    .el-card__header {
      padding: 15px 20px;
      font-weight: 600;

      i {
        margin-right: 5px;
        color: #409EFF;
      }
    }
  }

  // 充电状态卡片
  .status-card {
    .active-charging {
      .charging-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .charging-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 600;

          i {
            margin-right: 8px;
            color: #409EFF;
          }
        }
      }

      .charging-info {
        background-color: #f5f7fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;

        .info-row {
          display: flex;
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }

          .info-label {
            width: 100px;
            color: #606266;
          }

          .info-value {
            flex: 1;
            font-weight: 500;

            &.fee {
              color: #F56C6C;
              font-weight: 600;
            }
          }
        }
      }

      .charging-actions {
        text-align: right;
      }
    }

    .no-charging {
      padding: 30px 0;

      .sub-desc {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
      }
    }
  }

  // 充电记录卡片
  .records-card {
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }

  // 充电预约卡片
  .reservation-card {
    margin-bottom: 20px;

    .reservation-tips {
      margin-top: 15px;
      padding: 10px;
      background-color: #f0f9eb;
      border-radius: 4px;
      font-size: 13px;
      color: #67C23A;

      p {
        display: flex;
        align-items: center;

        i {
          margin-right: 5px;
        }
      }
    }
  }

  // 充电服务介绍卡片
  .intro-card {
    .service-intro {
      h4 {
        margin-top: 15px;
        margin-bottom: 10px;
        font-size: 15px;
        color: #303133;

        &:first-child {
          margin-top: 0;
        }
      }

      ol, ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 5px;
          font-size: 14px;
          color: #606266;
        }
      }

      p {
        margin: 10px 0;
        font-size: 14px;
        color: #606266;
      }
    }
  }

  // 充电通知卡片
  .notification-card {
    margin-bottom: 20px;

    ::v-deep .notification-list {
      max-height: 400px;
      overflow-y: auto;

      .notification-item {
        &:last-child {
          border-bottom: none;
        }
      }
    }
  }

  // 结束充电对话框
  .end-charging-info {
    background-color: #f5f7fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;

    .info-item {
      display: flex;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 100px;
        color: #606266;
      }

      .value {
        flex: 1;
        font-weight: 500;

        &.fee {
          color: #F56C6C;
          font-weight: 600;
        }
      }
    }
  }

  .end-charging-confirm {
    text-align: center;
    margin: 20px 0;
    color: #606266;
  }
}
</style>
