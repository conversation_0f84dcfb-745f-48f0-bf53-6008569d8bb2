from marshmallow import fields
from app.players.models import Players
from app import marshmallow, db
from marshmallow_sqlalchemy import SQLAlchemyAutoSchema

class PlayerSchema(SQLAlchemyAutoSchema):
    class Meta:
        model = Players
        sqla_session = db.session

    id = fields.Integer(dump_only=True)
    username = fields.String(required=True)
    password = fields.String(required=True)
