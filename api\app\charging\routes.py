from flask import request, current_app, g, Response
from app import db
from app.charging import charging_bp
from app.charging.models import ChargingRecord, Charging<PERSON>eservation, ChargingFault, ChargingException
from datetime import datetime, timedelta
from app.parkinglots.models import ParkingSpace, ParkingLot
from app.parking_records.models import ParkingRecord
from app.bikes.models import Bikes
from app.utils.response import api_response
from app.utils.auth import jwt_required, admin_required
from app.utils.vehicle_status import check_vehicle_for_operation, is_vehicle_disabled
from app.parkinglots.utils import generate_space_number
import random
import csv
import io
import traceback
import logging

# 获取充电记录列表
@charging_bp.route('/charging-records', methods=['GET'])
@jwt_required()
def get_charging_records():
    """获取充电记录列表，支持分页、排序和筛选"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        status = request.args.get('status', None, type=int)
        user_id = request.args.get('user_id', None, type=int)
        vehicle_id = request.args.get('vehicle_id', None, type=int)
        vehicle_number = request.args.get('vehicle_number', None)
        parking_lot_id = request.args.get('parking_lot_id', None, type=int)

        # 构建查询
        query = ChargingRecord.query

        # 应用筛选条件
        if status is not None:
            query = query.filter(ChargingRecord.status == status)
        if user_id is not None:
            query = query.filter(ChargingRecord.user_id == user_id)
        if vehicle_id is not None:
            query = query.filter(ChargingRecord.vehicle_id == vehicle_id)
        if vehicle_number is not None and vehicle_number.strip():
            # 导入车辆模型
            from app.bikes.models import Bikes
            # 先查找匹配车牌号的车辆
            vehicle_ids = [v.b_id for v in Bikes.query.filter(Bikes.b_num.like(f'%{vehicle_number}%')).all()]
            if vehicle_ids:
                query = query.filter(ChargingRecord.vehicle_id.in_(vehicle_ids))
            else:
                # 如果没有找到匹配的车辆，返回空结果
                query = query.filter(ChargingRecord.id < 0)  # 一个不可能满足的条件
        if parking_lot_id is not None:
            query = query.filter(ChargingRecord.parking_lot_id == parking_lot_id)

        # 获取排序参数
        sort_field = request.args.get('sort_field', 'id')
        sort_order = request.args.get('sort_order', 'asc')

        # 根据排序参数设置排序方式
        if sort_field == 'id':
            if sort_order == 'asc':
                query = query.order_by(ChargingRecord.id.asc())
            else:
                query = query.order_by(ChargingRecord.id.desc())
        elif sort_field == 'created_at':
            if sort_order == 'asc':
                query = query.order_by(ChargingRecord.created_at.asc())
            else:
                query = query.order_by(ChargingRecord.created_at.desc())
        elif sort_field == 'start_time':
            if sort_order == 'asc':
                query = query.order_by(ChargingRecord.start_time.asc())
            else:
                query = query.order_by(ChargingRecord.start_time.desc())
        elif sort_field == 'end_time':
            if sort_order == 'asc':
                query = query.order_by(ChargingRecord.end_time.asc())
            else:
                query = query.order_by(ChargingRecord.end_time.desc())
        else:
            # 默认按id升序排序
            query = query.order_by(ChargingRecord.id.asc())

        # 执行分页查询
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)

        # 格式化结果
        records = []
        for record in pagination.items:
            records.append(record.get_details(include_relations=True))

        # 返回结果
        return api_response(
            data={
                'items': records,
                'total': pagination.total,
                'page': page,
                'per_page': per_page,
                'pages': pagination.pages
            },
            message="获取充电记录列表成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取充电记录列表失败: {str(e)}")
        return api_response(message=f"获取充电记录列表失败: {str(e)}", status="error", code=500)

# 获取单个充电记录
@charging_bp.route('/charging-records/<int:record_id>', methods=['GET'])
@jwt_required()
def get_charging_record(record_id):
    """获取单个充电记录详情"""
    try:
        record = ChargingRecord.query.get(record_id)
        if not record:
            return api_response(message="充电记录不存在", status="error", code=404)

        return api_response(
            data=record.get_details(include_relations=True),
            message="获取充电记录成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取充电记录失败: {str(e)}")
        return api_response(message=f"获取充电记录失败: {str(e)}", status="error", code=500)

# 创建充电记录
@charging_bp.route('/charging-records', methods=['POST'])
@jwt_required()
def create_charging_record():
    """创建新的充电记录"""
    try:
        data = request.get_json()

        # 验证必要字段
        required_fields = ['parking_record_id', 'vehicle_id', 'user_id', 'parking_lot_id', 'parking_space_id']
        for field in required_fields:
            if field not in data:
                return api_response(message=f"缺少必要字段: {field}", status="error", code=400)

        # 验证停车记录是否存在
        parking_record = ParkingRecord.query.get(data['parking_record_id'])
        if not parking_record:
            return api_response(message="停车记录不存在", status="error", code=404)

        # 验证停车记录是否处于进行中状态
        if parking_record.status != 0:
            return api_response(message="停车记录不是进行中状态，无法创建充电记录", status="error", code=400)

        # 验证车位是否是充电车位
        parking_space = ParkingSpace.query.get(data['parking_space_id'])
        if not parking_space:
            return api_response(message="车位不存在", status="error", code=404)

        if parking_space.type != 3:  # 3表示充电车位
            return api_response(message="只能在充电车位使用充电功能", status="error", code=400)

        # 验证车位是否被当前车辆占用
        if parking_space.current_vehicle_id != data['vehicle_id']:
            return api_response(message="车位未被当前车辆占用，无法创建充电记录", status="error", code=400)

        # 检查车辆是否被禁用
        is_disabled, reason, _ = is_vehicle_disabled(data['vehicle_id'])
        if is_disabled:
            return api_response(
                message=f"车辆已被禁用，无法充电。{reason}",
                status="error",
                code=403
            )

        # 检查车辆是否可用于充电操作
        can_operate, operation_reason = check_vehicle_for_operation(data['vehicle_id'], "charging")
        if not can_operate:
            return api_response(
                message=operation_reason,
                status="error",
                code=403
            )

        # 检查是否已有进行中的充电记录
        existing_record = ChargingRecord.query.filter_by(
            parking_record_id=data['parking_record_id'],
            status=0  # 进行中
        ).first()

        if existing_record:
            return api_response(message="已存在进行中的充电记录", status="error", code=400)

        # 创建充电记录
        new_record = ChargingRecord(
            parking_record_id=data['parking_record_id'],
            vehicle_id=data['vehicle_id'],
            user_id=data['user_id'],
            parking_lot_id=data['parking_lot_id'],
            parking_space_id=data['parking_space_id']
        )

        # 如果有备注，添加备注
        if 'remarks' in data:
            new_record.remarks = data['remarks']

        # 保存记录
        created_record = new_record.create()
        if not created_record:
            return api_response(message="创建充电记录失败", status="error", code=500)

        return api_response(
            data=created_record.get_details(include_relations=True),
            message="创建充电记录成功",
            code=201
        )
    except Exception as e:
        current_app.logger.error(f"创建充电记录失败: {str(e)}")
        return api_response(message=f"创建充电记录失败: {str(e)}", status="error", code=500)

# 结束充电记录
@charging_bp.route('/charging-records/<int:record_id>/end', methods=['PUT'])
@jwt_required()
def end_charging_record(record_id):
    """结束充电记录"""
    try:
        record = ChargingRecord.query.get(record_id)
        if not record:
            return api_response(message="充电记录不存在", status="error", code=404)

        # 验证充电记录是否处于进行中状态
        if record.status != 0:
            return api_response(message="充电记录不是进行中状态，无法结束", status="error", code=400)

        data = request.get_json()

        # 获取备注
        remarks = data.get('remarks')

        # 结束充电记录
        success = record.end_charging(remarks=remarks)
        if not success:
            return api_response(message="结束充电记录失败", status="error", code=500)

        return api_response(
            data=record.get_details(include_relations=True),
            message="结束充电记录成功"
        )
    except Exception as e:
        current_app.logger.error(f"结束充电记录失败: {str(e)}")
        return api_response(message=f"结束充电记录失败: {str(e)}", status="error", code=500)

# 获取用户的充电记录
@charging_bp.route('/users/<int:user_id>/charging-records', methods=['GET'])
@jwt_required()
def get_user_charging_records(user_id):
    """获取指定用户的充电记录"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        status = request.args.get('status', None, type=int)

        # 构建查询
        query = ChargingRecord.query.filter_by(user_id=user_id)

        # 应用状态筛选
        if status is not None:
            query = query.filter(ChargingRecord.status == status)

        # 获取排序参数
        sort_field = request.args.get('sort_field', 'id')
        sort_order = request.args.get('sort_order', 'asc')

        # 根据排序参数设置排序方式
        if sort_field == 'id':
            if sort_order == 'asc':
                query = query.order_by(ChargingRecord.id.asc())
            else:
                query = query.order_by(ChargingRecord.id.desc())
        elif sort_field == 'created_at':
            if sort_order == 'asc':
                query = query.order_by(ChargingRecord.created_at.asc())
            else:
                query = query.order_by(ChargingRecord.created_at.desc())
        else:
            # 默认按id升序排序
            query = query.order_by(ChargingRecord.id.asc())

        # 执行分页查询
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)

        # 格式化结果
        records = []
        for record in pagination.items:
            records.append(record.get_details(include_relations=True))

        # 返回结果
        return api_response(
            data={
                'items': records,
                'total': pagination.total,
                'page': page,
                'per_page': per_page,
                'pages': pagination.pages
            },
            message="获取用户充电记录成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取用户充电记录失败: {str(e)}")
        return api_response(message=f"获取用户充电记录失败: {str(e)}", status="error", code=500)

# 获取车辆的充电记录
@charging_bp.route('/vehicles/<int:vehicle_id>/charging-records', methods=['GET'])
@jwt_required()
def get_vehicle_charging_records(vehicle_id):
    """获取指定车辆的充电记录"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        status = request.args.get('status', None, type=int)

        # 构建查询
        query = ChargingRecord.query.filter_by(vehicle_id=vehicle_id)

        # 应用状态筛选
        if status is not None:
            query = query.filter(ChargingRecord.status == status)

        # 获取排序参数
        sort_field = request.args.get('sort_field', 'id')
        sort_order = request.args.get('sort_order', 'asc')

        # 根据排序参数设置排序方式
        if sort_field == 'id':
            if sort_order == 'asc':
                query = query.order_by(ChargingRecord.id.asc())
            else:
                query = query.order_by(ChargingRecord.id.desc())
        elif sort_field == 'created_at':
            if sort_order == 'asc':
                query = query.order_by(ChargingRecord.created_at.asc())
            else:
                query = query.order_by(ChargingRecord.created_at.desc())
        else:
            # 默认按id升序排序
            query = query.order_by(ChargingRecord.id.asc())

        # 执行分页查询
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)

        # 格式化结果
        records = []
        for record in pagination.items:
            records.append(record.get_details(include_relations=True))

        # 返回结果
        return api_response(
            data={
                'items': records,
                'total': pagination.total,
                'page': page,
                'per_page': per_page,
                'pages': pagination.pages
            },
            message="获取车辆充电记录成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取车辆充电记录失败: {str(e)}")
        return api_response(message=f"获取车辆充电记录失败: {str(e)}", status="error", code=500)



# 获取充电统计数据
@charging_bp.route('/charging-stats', methods=['GET'])
@jwt_required()
def get_charging_stats():
    """获取充电统计数据"""
    try:
        # 获取查询参数
        parking_lot_id = request.args.get('parking_lot_id', None, type=int)

        # 获取充电车位总数
        from app.parkinglots.models import ParkingSpace
        spaces_query = ParkingSpace.query.filter_by(type=3)  # 充电车位
        if parking_lot_id:
            spaces_query = spaces_query.filter_by(parking_lot_id=parking_lot_id)
        total_spaces = spaces_query.count()

        # 获取正在充电的记录数
        active_query = ChargingRecord.query.filter_by(status=0)  # 进行中
        if parking_lot_id:
            active_query = active_query.filter_by(parking_lot_id=parking_lot_id)
        active_charging = active_query.count()

        # 获取今日充电次数
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_query = ChargingRecord.query.filter(ChargingRecord.start_time >= today_start)
        if parking_lot_id:
            today_query = today_query.filter_by(parking_lot_id=parking_lot_id)

        today_count = today_query.count()

        return api_response(
            data={
                'total_spaces': total_spaces,
                'active_charging': active_charging,
                'today_count': today_count
            },
            message="获取充电统计数据成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取充电统计数据失败: {str(e)}")
        return api_response(message=f"获取充电统计数据失败: {str(e)}", status="error", code=500)

# 获取每日充电统计数据
@charging_bp.route('/charging-daily-stats', methods=['GET'])
@jwt_required()
def get_charging_daily_stats():
    """获取每日充电统计数据"""
    try:
        # 获取查询参数
        parking_lot_id = request.args.get('parking_lot_id', None, type=int)
        date_range = request.args.get('date_range', None)

        # 根据date_range确定日期范围
        end_date_obj = datetime.now()
        end_date = end_date_obj.strftime('%Y-%m-%d')

        if date_range == 'day':
            start_date_obj = end_date_obj.replace(hour=0, minute=0, second=0, microsecond=0)
            start_date = start_date_obj.strftime('%Y-%m-%d')
        elif date_range == 'week':
            # 获取过去7天的数据，包括今天
            start_date_obj = (end_date_obj - timedelta(days=6)).replace(hour=0, minute=0, second=0, microsecond=0)
            start_date = start_date_obj.strftime('%Y-%m-%d')
        elif date_range == 'month':
            # 获取过去30天的数据，包括今天
            start_date_obj = (end_date_obj - timedelta(days=29)).replace(hour=0, minute=0, second=0, microsecond=0)
            start_date = start_date_obj.strftime('%Y-%m-%d')
        else:
            # 如果没有指定date_range，使用start_date和end_date参数
            start_date = request.args.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
            end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))

        current_app.logger.info(f"获取每日充电统计数据，参数: date_range={date_range}, start_date={start_date}, end_date={end_date}, parking_lot_id={parking_lot_id}")

        # 转换为datetime对象
        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
        end_datetime = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')

        # 检查是否有当前日期范围内的数据，如果没有，则使用数据库中的最近日期范围
        query = ChargingRecord.query
        if parking_lot_id:
            query = query.filter_by(parking_lot_id=parking_lot_id)

        # 检查当前日期范围内是否有数据
        current_range_count = query.filter(
            ChargingRecord.start_time >= start_datetime,
            ChargingRecord.start_time <= end_datetime
        ).count()

        # 如果当前日期范围内没有数据，则查找数据库中的最近记录
        if current_range_count == 0:
            # 获取最早和最晚的记录日期
            earliest_record = query.order_by(ChargingRecord.start_time.asc()).first()
            latest_record = query.order_by(ChargingRecord.start_time.desc()).first()

            if earliest_record and latest_record:
                # 使用数据库中的日期范围
                earliest_date = earliest_record.start_time.replace(hour=0, minute=0, second=0, microsecond=0)
                latest_date = latest_record.start_time.replace(hour=23, minute=59, second=59, microsecond=999999)

                # 如果最早和最晚的记录日期相差超过7天，则只取最近7天
                if (latest_date - earliest_date).days > 7:
                    start_datetime = (latest_date - timedelta(days=6)).replace(hour=0, minute=0, second=0, microsecond=0)
                else:
                    start_datetime = earliest_date

                end_datetime = latest_date

                current_app.logger.info(f"当前日期范围内没有数据，使用数据库中的日期范围: {start_datetime} - {end_datetime}")

        # 构建查询
        query = ChargingRecord.query.filter(
            ChargingRecord.start_time >= start_datetime,
            ChargingRecord.start_time <= end_datetime
        )
        if parking_lot_id:
            query = query.filter_by(parking_lot_id=parking_lot_id)

        # 获取所有记录
        records = query.all()

        # 按日期分组统计
        daily_stats = {}

        # 首先初始化日期范围内的所有日期，确保即使没有数据也会返回完整的日期范围
        current_date = start_datetime
        while current_date <= end_datetime:
            date_str = current_date.strftime('%Y-%m-%d')
            daily_stats[date_str] = {'count': 0, 'income': 0}
            current_date += timedelta(days=1)

        # 统计实际记录
        for record in records:
            date_str = record.start_time.strftime('%Y-%m-%d')
            if date_str in daily_stats:  # 只统计日期范围内的记录
                daily_stats[date_str]['count'] += 1
                # 检查record.fee是否存在且不为None
                if record.status == 1 and hasattr(record, 'fee') and record.fee is not None:
                    daily_stats[date_str]['income'] += record.fee

        # 转换为列表格式
        result = []
        for date_str, stats in daily_stats.items():
            result.append({
                'date': date_str,
                'count': stats['count'],
                'income': round(stats['income'], 2)
            })

        # 按日期排序
        result.sort(key=lambda x: x['date'])

        # 记录返回的数据，便于调试
        current_app.logger.info(f"返回的每日充电统计数据: {result}")

        return api_response(
            data=result,
            message="获取每日充电统计数据成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取每日充电统计数据失败: {str(e)}")
        return api_response(message=f"获取每日充电统计数据失败: {str(e)}", status="error", code=500)

# 获取充电时长分布数据
@charging_bp.route('/charging-duration-distribution', methods=['GET'])
@jwt_required()
def get_charging_duration_distribution():
    """获取充电时长分布数据"""
    try:
        # 获取查询参数
        parking_lot_id = request.args.get('parking_lot_id', None, type=int)
        start_date = request.args.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))

        # 转换为datetime对象
        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
        end_datetime = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')

        # 构建查询
        query = ChargingRecord.query.filter(
            ChargingRecord.start_time >= start_datetime,
            ChargingRecord.start_time <= end_datetime,
            ChargingRecord.status == 1  # 已完成
        )
        if parking_lot_id:
            query = query.filter_by(parking_lot_id=parking_lot_id)

        # 获取所有记录
        records = query.all()

        # 定义时长区间
        duration_ranges = [
            {'range': '0-30分钟', 'min': 0, 'max': 30, 'count': 0},
            {'range': '30分钟-1小时', 'min': 30, 'max': 60, 'count': 0},
            {'range': '1-2小时', 'min': 60, 'max': 120, 'count': 0},
            {'range': '2-3小时', 'min': 120, 'max': 180, 'count': 0},
            {'range': '3-4小时', 'min': 180, 'max': 240, 'count': 0},
            {'range': '4小时以上', 'min': 240, 'max': float('inf'), 'count': 0}
        ]

        # 统计时长分布
        for record in records:
            if record.duration is not None:
                for range_info in duration_ranges:
                    if range_info['min'] <= record.duration < range_info['max']:
                        range_info['count'] += 1
                        break

        # 提取结果
        result = [{'range': r['range'], 'count': r['count']} for r in duration_ranges]

        return api_response(
            data=result,
            message="获取充电时长分布数据成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取充电时长分布数据失败: {str(e)}")
        return api_response(message=f"获取充电时长分布数据失败: {str(e)}", status="error", code=500)

# 获取停车场充电统计数据
@charging_bp.route('/parking-lot-charging-stats', methods=['GET'])
@jwt_required()
def get_parking_lot_charging_stats():
    """获取停车场充电统计数据"""
    try:
        # 获取查询参数
        start_date = request.args.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))

        # 转换为datetime对象
        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
        end_datetime = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')

        # 获取所有停车场
        parking_lots = ParkingLot.query.all()

        # 统计每个停车场的充电情况
        result = []
        for lot in parking_lots:
            # 获取充电车位数
            charging_spaces = ParkingSpace.query.filter_by(parking_lot_id=lot.id, type=3).count()

            # 获取正在充电的记录数
            active_charging = ChargingRecord.query.filter_by(
                parking_lot_id=lot.id,
                status=0  # 进行中
            ).count()

            # 获取今日充电次数
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_count = ChargingRecord.query.filter(
                ChargingRecord.parking_lot_id == lot.id,
                ChargingRecord.start_time >= today_start
            ).count()



            # 计算使用率
            usage_rate = round((active_charging / charging_spaces * 100) if charging_spaces > 0 else 0, 2)

            result.append({
                'id': lot.id,
                'name': lot.name,
                'charging_spaces': charging_spaces,
                'active_charging': active_charging,
                'today_count': today_count,
                'usage_rate': usage_rate
            })

        return api_response(
            data=result,
            message="获取停车场充电统计数据成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取停车场充电统计数据失败: {str(e)}")
        return api_response(message=f"获取停车场充电统计数据失败: {str(e)}", status="error", code=500)

# 获取充电预约列表
@charging_bp.route('/charging-reservations', methods=['GET'])
@charging_bp.route('/api/charging-reservations', methods=['GET'])  # 添加前端使用的路径
@jwt_required()
def get_charging_reservations():
    """获取充电预约列表，支持分页、排序和筛选"""
    try:
        user_id = g.user.u_id

        # 获取用户的充电预约
        reservations = ChargingReservation.query.filter_by(user_id=user_id).all()

        # 检查预约状态并获取详细信息
        reservations_data = []
        for reservation in reservations:
            # 检查预约是否已过期
            reservation.check_expired()

            # 获取详细信息，包括关联数据
            reservation_data = reservation.get_details(include_relations=True)

            # 确保vehicle对象存在
            if 'vehicle_id' in reservation_data:
                # 获取车辆信息
                vehicle = Bikes.query.get(reservation_data['vehicle_id'])
                if vehicle:
                    reservation_data['vehicle'] = {
                        'number': vehicle.b_num,
                        'brand': vehicle.brand,
                        'color': vehicle.color
                    }
                else:
                    reservation_data['vehicle'] = {
                        'number': '未知车牌',
                        'brand': '未知品牌',
                        'color': '未知颜色'
                    }

            # 获取停车场信息
            if 'parking_lot_id' in reservation_data:
                parking_lot = ParkingLot.query.get(reservation_data['parking_lot_id'])
                if parking_lot:
                    reservation_data['parking_lot'] = {
                        'name': parking_lot.name,
                        'address': parking_lot.address
                    }
                else:
                    reservation_data['parking_lot'] = {
                        'name': '未知停车场',
                        'address': '未知地址'
                    }

            reservations_data.append(reservation_data)

        return api_response(data=reservations_data)
    except Exception as e:
        current_app.logger.error(f"获取充电预约列表失败: {str(e)}")
        return api_response(message=f"获取充电预约列表失败: {str(e)}", status="error", code=500)

# 获取单个充电预约
@charging_bp.route('/charging-reservations/<int:reservation_id>', methods=['GET'])
@jwt_required()
def get_charging_reservation(reservation_id):
    """获取单个充电预约详情"""
    try:
        reservation = ChargingReservation.query.get(reservation_id)
        if not reservation:
            return api_response(message="充电预约不存在", status="error", code=404)

        # 检查预约是否已过期
        reservation.check_expired()

        return api_response(
            data=reservation.get_details(include_relations=True),
            message="获取充电预约成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取充电预约失败: {str(e)}")
        return api_response(message=f"获取充电预约失败: {str(e)}", status="error", code=500)

# 创建充电预约
@charging_bp.route('/charging-reservations', methods=['POST'])
@charging_bp.route('/api/charging-reservations', methods=['POST'])  # 添加前端使用的路径
@jwt_required()
def create_charging_reservation():
    """创建新的充电预约"""
    try:
        data = request.get_json()
        user_id = g.user.u_id

        # 添加用户ID
        data['user_id'] = user_id

        # 验证必要字段
        required_fields = ['vehicle_id', 'parking_lot_id', 'timeRange']
        for field in required_fields:
            if field not in data:
                return api_response(message=f"缺少必要字段: {field}", status="error", code=400)

        # 处理时间范围
        if 'timeRange' in data and len(data['timeRange']) == 2:
            data['start_time'] = data['timeRange'][0]
            data['end_time'] = data['timeRange'][1]
        else:
            return api_response(message="时间范围格式不正确", status="error", code=400)

        # 验证时间格式
        try:
            start_time = datetime.fromisoformat(data['start_time'].replace('Z', '+00:00'))
            end_time = datetime.fromisoformat(data['end_time'].replace('Z', '+00:00'))
        except ValueError:
            return api_response(message="时间格式不正确，请使用ISO 8601格式", status="error", code=400)

        # 验证车辆是否属于当前用户
        vehicle = Bikes.query.filter_by(b_id=data['vehicle_id'], belong_to=user_id).first()
        if not vehicle:
            return api_response(message="车辆不存在或不属于当前用户", status="error", code=400)

        # 验证时间范围
        if start_time >= end_time:
            return api_response(message="结束时间必须晚于开始时间", status="error", code=400)

        # 验证预约时长不超过3小时
        if (end_time - start_time).total_seconds() > 3 * 60 * 60:
            return api_response(message="预约时长不能超过3小时", status="error", code=400)

        # 验证预约时间不能早于当前时间
        if start_time < datetime.now():
            return api_response(message="预约开始时间不能早于当前时间", status="error", code=400)

        # 验证预约时间不能超过7天后
        if start_time > datetime.now() + timedelta(days=7):
            return api_response(message="预约时间不能超过7天后", status="error", code=400)

        # 检查用户是否已有冲突的预约
        if ChargingReservation.check_conflict(data['user_id'], start_time, end_time):
            return api_response(message="您在该时间段内已有预约", status="error", code=400)

        # 创建预约
        new_reservation = ChargingReservation(
            user_id=data['user_id'],
            vehicle_id=data['vehicle_id'],
            parking_lot_id=data['parking_lot_id'],
            start_time=start_time,
            end_time=end_time,
            remarks=data.get('remarks')
        )

        # 保存预约
        created_reservation = new_reservation.create()
        if not created_reservation:
            return api_response(message="创建充电预约失败", status="error", code=500)

        return api_response(
            data=created_reservation.get_details(include_relations=True),
            message="创建充电预约成功",
            code=201
        )
    except Exception as e:
        current_app.logger.error(f"创建充电预约失败: {str(e)}")
        return api_response(message=f"创建充电预约失败: {str(e)}", status="error", code=500)

# 取消充电预约
@charging_bp.route('/charging-reservations/<int:reservation_id>/cancel', methods=['PUT'])
@charging_bp.route('/api/charging-reservations/<int:reservation_id>/cancel', methods=['POST'])  # 添加前端使用的路径和方法
@jwt_required()
def cancel_charging_reservation(reservation_id):
    """取消充电预约"""
    try:
        user_id = g.user.u_id

        # 获取预约
        reservation = ChargingReservation.query.get(reservation_id)
        if not reservation:
            return api_response(message="充电预约不存在", status="error", code=404)

        # 验证预约是否属于当前用户
        if reservation.user_id != user_id:
            return api_response(message="无权取消该充电预约", status="error", code=403)

        # 验证预约状态
        if reservation.status != 0:
            return api_response(message="只能取消待使用的预约", status="error", code=400)

        # 取消预约
        success = reservation.cancel()
        if not success:
            return api_response(message="取消充电预约失败", status="error", code=500)

        return api_response(
            data=reservation.get_details(include_relations=True),
            message="取消充电预约成功"
        )
    except Exception as e:
        current_app.logger.error(f"取消充电预约失败: {str(e)}")
        return api_response(message=f"取消充电预约失败: {str(e)}", status="error", code=500)

# 标记充电预约为已使用
@charging_bp.route('/charging-reservations/<int:reservation_id>/use', methods=['PUT'])
@jwt_required()
def use_charging_reservation(reservation_id):
    """标记充电预约为已使用"""
    try:
        reservation = ChargingReservation.query.get(reservation_id)
        if not reservation:
            return api_response(message="充电预约不存在", status="error", code=404)

        # 验证预约状态
        if reservation.status != 0:
            return api_response(message="只能使用待使用的预约", status="error", code=400)

        # 标记为已使用
        success = reservation.mark_as_used()
        if not success:
            return api_response(message="标记充电预约为已使用失败", status="error", code=500)

        return api_response(
            data=reservation.get_details(include_relations=True),
            message="标记充电预约为已使用成功"
        )
    except Exception as e:
        current_app.logger.error(f"标记充电预约为已使用失败: {str(e)}")
        return api_response(message=f"标记充电预约为已使用失败: {str(e)}", status="error", code=500)

# 获取用户的充电预约
@charging_bp.route('/users/<int:user_id>/charging-reservations', methods=['GET'])
@jwt_required()
def get_user_charging_reservations(user_id):
    """获取指定用户的充电预约"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        status = request.args.get('status', None, type=int)

        # 构建查询
        query = ChargingReservation.query.filter_by(user_id=user_id)

        # 应用状态筛选
        if status is not None:
            query = query.filter(ChargingReservation.status == status)

        # 默认按创建时间降序排序
        query = query.order_by(ChargingReservation.created_at.desc())

        # 执行分页查询
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)

        # 格式化结果
        reservations = []
        for reservation in pagination.items:
            # 检查预约是否已过期
            reservation.check_expired()
            reservations.append(reservation.get_details(include_relations=True))

        # 返回结果
        return api_response(
            data={
                'items': reservations,
                'total': pagination.total,
                'page': page,
                'per_page': per_page,
                'pages': pagination.pages
            },
            message="获取用户充电预约成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取用户充电预约失败: {str(e)}")
        return api_response(message=f"获取用户充电预约失败: {str(e)}", status="error", code=500)

# 获取按小时分布的充电记录数据
@charging_bp.route('/charging-hourly-stats', methods=['GET'])
@jwt_required()
def get_charging_hourly_stats():
    """获取按小时分布的充电记录数据"""
    try:
        # 获取查询参数
        parking_lot_id = request.args.get('parking_lot_id', None, type=int)
        date_range = request.args.get('date_range', 'week')  # 默认获取一周的数据

        # 根据date_range确定日期范围
        end_date = datetime.now()
        if date_range == 'day':
            start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
        elif date_range == 'week':
            # 获取过去7天的数据，包括今天
            start_date = (end_date - timedelta(days=6)).replace(hour=0, minute=0, second=0, microsecond=0)
        elif date_range == 'month':
            # 获取过去30天的数据，包括今天
            start_date = (end_date - timedelta(days=29)).replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            # 自定义日期范围
            start_date_str = request.args.get('start_date')
            end_date_str = request.args.get('end_date')

            if not start_date_str or not end_date_str:
                # 如果没有提供自定义日期范围，默认使用一周
                current_app.logger.warning("未提供自定义日期范围参数，使用默认一周范围")
                start_date = (end_date - timedelta(days=6)).replace(hour=0, minute=0, second=0, microsecond=0)
            else:
                try:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
                    end_date = datetime.strptime(end_date_str + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                except ValueError as e:
                    current_app.logger.error(f"日期格式错误: {str(e)}")
                    return api_response(message="日期格式错误，请使用YYYY-MM-DD格式", status="error", code=400)

        current_app.logger.info(f"获取按小时分布的充电记录数据，参数: date_range={date_range}, start_date={start_date}, end_date={end_date}, parking_lot_id={parking_lot_id}")

        # 创建基本查询
        query = ChargingRecord.query
        if parking_lot_id:
            query = query.filter_by(parking_lot_id=parking_lot_id)

        # 检查当前日期范围内是否有数据
        current_range_count = query.filter(
            ChargingRecord.start_time >= start_date,
            ChargingRecord.start_time <= end_date
        ).count()

        # 如果当前日期范围内没有数据，则查找数据库中的最近记录
        if current_range_count == 0:
            # 获取最早和最晚的记录日期
            earliest_record = query.order_by(ChargingRecord.start_time.asc()).first()
            latest_record = query.order_by(ChargingRecord.start_time.desc()).first()

            if earliest_record and latest_record:
                # 使用数据库中的日期范围
                earliest_date = earliest_record.start_time.replace(hour=0, minute=0, second=0, microsecond=0)
                latest_date = latest_record.start_time.replace(hour=23, minute=59, second=59, microsecond=999999)

                # 如果最早和最晚的记录日期相差超过7天，则只取最近7天
                if (latest_date - earliest_date).days > 7:
                    start_date = (latest_date - timedelta(days=6)).replace(hour=0, minute=0, second=0, microsecond=0)
                else:
                    start_date = earliest_date

                end_date = latest_date

                current_app.logger.info(f"当前日期范围内没有数据，使用数据库中的日期范围: {start_date} - {end_date}")

        # 构建查询
        query = query.filter(
            ChargingRecord.start_time >= start_date,
            ChargingRecord.start_time <= end_date
        )

        if parking_lot_id:
            query = query.filter_by(parking_lot_id=parking_lot_id)

        # 获取所有记录
        records = query.all()
        current_app.logger.info(f"查询到 {len(records)} 条充电记录")

        # 初始化24小时的计数器
        hourly_stats = [{'hour': i, 'count': 0} for i in range(24)]

        # 统计每个小时的充电开始次数
        for record in records:
            hour = record.start_time.hour
            hourly_stats[hour]['count'] += 1

        current_app.logger.info(f"返回的按小时分布的充电记录数据: {hourly_stats}")

        return api_response(
            data=hourly_stats,
            message="获取按小时分布的充电记录数据成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取按小时分布的充电记录数据失败: {str(e)}")
        return api_response(message=f"获取按小时分布的充电记录数据失败: {str(e)}", status="error", code=500)

# 获取充电统计数据（管理员版本）
@charging_bp.route('/admin/charging-stats', methods=['GET'])
@jwt_required()
@admin_required
def get_admin_charging_stats():
    """获取充电统计数据（仅管理员）"""
    try:
        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # 如果没有提供日期范围，默认为最近7天
        if not start_date:
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')

        # 转换为datetime对象
        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
        end_datetime = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')

        # 获取充电车位总数
        from app.parkinglots.models import ParkingSpace
        total_charging_spaces = ParkingSpace.query.filter_by(type=3).count()

        # 获取正在充电的记录数
        active_charging_count = ChargingRecord.query.filter_by(status=0).count()

        # 获取今日充电次数
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_charging_count = ChargingRecord.query.filter(
            ChargingRecord.start_time >= today_start
        ).count()



        # 获取日期范围内的每日充电次数和收入
        daily_stats = []
        current_date = start_datetime
        while current_date <= end_datetime:
            next_date = current_date + timedelta(days=1)

            # 查询当日充电次数
            daily_count = ChargingRecord.query.filter(
                ChargingRecord.start_time >= current_date,
                ChargingRecord.start_time < next_date
            ).count()



            # 添加到统计数据
            daily_stats.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'count': daily_count
            })

            # 移动到下一天
            current_date = next_date

        # 获取充电时长分布
        duration_stats = {
            '1h': 0,  # 1小时内
            '1-3h': 0,  # 1-3小时
            '3-6h': 0,  # 3-6小时
            '6h+': 0   # 6小时以上
        }

        # 查询所有已完成的充电记录
        completed_records = ChargingRecord.query.filter_by(status=1).all()
        for record in completed_records:
            duration_minutes = record.calculate_duration()
            duration_hours = duration_minutes / 60

            if duration_hours <= 1:
                duration_stats['1h'] += 1
            elif duration_hours <= 3:
                duration_stats['1-3h'] += 1
            elif duration_hours <= 6:
                duration_stats['3-6h'] += 1
            else:
                duration_stats['6h+'] += 1

        # 返回统计数据
        return api_response(
            data={
                'total_charging_spaces': total_charging_spaces,
                'active_charging_count': active_charging_count,
                'today_charging_count': today_charging_count,
                'daily_stats': daily_stats,
                'duration_stats': duration_stats
            },
            message="获取充电统计数据成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取充电统计数据失败: {str(e)}")
        return api_response(message=f"获取充电统计数据失败: {str(e)}", status="error", code=500)

# 根据策略计算充电费用
@charging_bp.route('/charging-price-strategies/calculate', methods=['POST'])
@jwt_required()
def calculate_charging_fee_by_strategy():
    """根据策略计算充电费用"""
    try:
        data = request.get_json()

        # 验证必要字段
        required_fields = ['strategy_id', 'start_time', 'end_time']
        for field in required_fields:
            if field not in data:
                return api_response(message=f"缺少必要字段: {field}", status="error", code=400)

        # 获取策略
        strategy = ChargingPriceStrategy.query.get(data['strategy_id'])
        if not strategy:
            return api_response(message="充电价格策略不存在", status="error", code=404)

        # 解析时间
        try:
            start_time = datetime.fromisoformat(data['start_time'].replace('Z', '+00:00'))
            end_time = datetime.fromisoformat(data['end_time'].replace('Z', '+00:00'))
        except ValueError:
            return api_response(message="时间格式无效", status="error", code=400)

        # 验证时间
        if end_time <= start_time:
            return api_response(message="结束时间必须大于开始时间", status="error", code=400)

        # 计算费用
        fee = strategy.calculate_fee(start_time, end_time)

        # 计算时长（分钟）
        duration_minutes = (end_time - start_time).total_seconds() / 60

        # 格式化时长
        hours = int(duration_minutes / 60)
        minutes = int(duration_minutes % 60)
        duration_formatted = f"{hours}小时{minutes}分钟" if hours > 0 else f"{minutes}分钟"

        return api_response(
            data={
                'strategy_id': strategy.id,
                'strategy_name': strategy.name,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration_minutes,
                'duration_formatted': duration_formatted,
                'fee': fee
            },
            message="计算充电费用成功"
        )
    except Exception as e:
        current_app.logger.error(f"计算充电费用失败: {str(e)}")
        return api_response(message=f"计算充电费用失败: {str(e)}", status="error", code=500)

# 定义主要的充电车位列表获取函数
def get_charging_spaces_main():
    """获取充电车位列表的主要实现函数，支持分页、排序和筛选"""
    try:
        # 记录请求开始
        current_app.logger.info("开始处理获取充电车位列表请求")

        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 获取筛选参数
        status_param = request.args.get('status')
        parking_lot_id_param = request.args.get('parking_lot_id')
        type_param = request.args.get('type', '3')  # 默认只获取充电车位

        # 记录请求参数
        current_app.logger.info(f"请求参数: page={page}, per_page={per_page}, status={status_param}, parking_lot_id={parking_lot_id_param}, type={type_param}")

        # 导入模型
        from app.parkinglots.models import ParkingSpace, ParkingLot

        # 使用最简单的方式获取所有充电车位
        try:
            # 创建基本查询
            all_spaces = []

            # 使用原始SQL查询，避免ORM问题
            from app import db
            from sqlalchemy import text

            # 构建SQL查询
            sql = "SELECT * FROM parking_spaces WHERE type = 3"
            params = {}

            # 添加筛选条件
            if status_param:
                try:
                    status = int(status_param)
                    sql += " AND status = :status"
                    params['status'] = status
                except ValueError:
                    pass

            if parking_lot_id_param:
                try:
                    lot_id = int(parking_lot_id_param)
                    sql += " AND parking_lot_id = :lot_id"
                    params['lot_id'] = lot_id
                except ValueError:
                    pass

            # 添加排序
            sql += " ORDER BY id ASC"

            # 执行查询
            current_app.logger.info(f"执行SQL: {sql} 参数: {params}")
            result = db.session.execute(text(sql), params)

            # 获取所有记录
            rows = result.fetchall()
            current_app.logger.info(f"查询返回 {len(rows)} 条记录")

            # 计算分页
            total = len(rows)
            start_idx = (page - 1) * per_page
            end_idx = min(start_idx + per_page, total)

            # 获取当前页的记录
            page_rows = rows[start_idx:end_idx] if start_idx < total else []

            # 处理结果
            spaces = []
            for row in page_rows:
                # 将行转换为字典
                space_dict = dict(row._mapping)

                # 添加文本描述
                type_map = {0: '普通车位', 1: '普通车位', 2: '残疾人车位', 3: '充电车位'}
                status_map = {0: '空闲', 1: '已占用', 2: '故障', 3: '维修中', 4: '禁用'}

                space_dict['type_text'] = type_map.get(space_dict.get('type'), '未知类型')
                space_dict['status_text'] = status_map.get(space_dict.get('status'), '未知状态')

                # 处理日期时间
                for key in ['created_at', 'updated_at', 'last_maintenance_time']:
                    if key in space_dict and space_dict[key] is not None:
                        try:
                            space_dict[key] = space_dict[key].isoformat()
                        except:
                            space_dict[key] = str(space_dict[key])

                spaces.append(space_dict)

            # 返回结果
            return api_response(
                data={
                    'items': spaces,
                    'total': total,
                    'page': page,
                    'per_page': per_page,
                    'pages': (total + per_page - 1) // per_page if per_page > 0 else 1
                },
                message="获取充电车位列表成功"
            )

        except Exception as db_error:
            # 记录错误
            import traceback
            error_traceback = traceback.format_exc()
            current_app.logger.error(f"数据库查询失败: {str(db_error)}\n{error_traceback}")

            # 返回空结果
            return api_response(
                data={
                    'items': [],
                    'total': 0,
                    'page': page,
                    'per_page': per_page,
                    'pages': 0
                },
                message="获取充电车位列表成功（空结果）"
            )

    except Exception as e:
        # 记录详细错误信息
        import traceback
        error_traceback = traceback.format_exc()
        current_app.logger.error(f"获取充电车位列表失败: {str(e)}\n{error_traceback}")

        # 返回空结果而不是错误，避免前端崩溃
        return api_response(
            data={
                'items': [],
                'total': 0,
                'page': 1,
                'per_page': 20,
                'pages': 0
            },
            message="获取充电车位列表成功（空结果）"
        )

# 获取充电车位列表 - 原始路由（重定向到新路由）
@charging_bp.route('/charging-spaces', methods=['GET'])
@jwt_required()
def get_charging_spaces():
    """获取充电车位列表 - 重定向到新路由"""
    # 记录请求信息
    current_app.logger.info("收到旧API请求，重定向到新API: /api/charging/spaces")

    # 调用主函数
    return get_charging_spaces_main()

# 获取充电车位列表 - 新的主要路由
@charging_bp.route('/charging/spaces', methods=['GET'])
@jwt_required()
def get_charging_spaces_alternative():
    """获取充电车位列表的主要路由，支持分页、排序和筛选"""
    # 记录请求信息
    current_app.logger.info("收到主API请求: /api/charging/spaces")

    # 调用主函数
    return get_charging_spaces_main()

# 创建充电车位
@charging_bp.route('/charging-spaces', methods=['POST'])
@jwt_required()
@admin_required
def create_charging_space():
    """创建新的充电车位（仅管理员）"""
    try:
        data = request.get_json()

        # 验证必要字段
        required_fields = ['parking_lot_id', 'power']
        for field in required_fields:
            if field not in data:
                return api_response(message=f"缺少必要字段: {field}", status="error", code=400)

        # 验证停车场是否存在
        from app.parkinglots.models import ParkingLot, ParkingSpace
        parking_lot = ParkingLot.query.get(data['parking_lot_id'])
        if not parking_lot:
            return api_response(message="停车场不存在", status="error", code=404)

        # 使用辅助函数生成车位号
        space_number = generate_space_number(data['parking_lot_id'], 3)  # 3=充电车位

        # 创建充电车位
        try:
            # 检查ParkingSpace模型是否支持power和remarks参数
            import inspect
            signature = inspect.signature(ParkingSpace.__init__)
            params = signature.parameters

            # 根据模型支持的参数创建充电车位
            if 'power' in params and 'remarks' in params:
                # 完整版本的模型
                new_space = ParkingSpace(
                    parking_lot_id=data['parking_lot_id'],
                    space_number=space_number,
                    type=3,  # 充电车位类型为3
                    status=data.get('status', 0),  # 默认空闲
                    power=data.get('power', 7),  # 默认7kW
                    remarks=data.get('remarks')
                )
            elif 'power' in params:
                # 支持power但不支持remarks的版本
                new_space = ParkingSpace(
                    parking_lot_id=data['parking_lot_id'],
                    space_number=space_number,
                    type=3,  # 充电车位类型为3
                    status=data.get('status', 0),  # 默认空闲
                    power=data.get('power', 7)  # 默认7kW
                )
                # 尝试手动设置remarks
                if hasattr(new_space, 'remarks') and data.get('remarks'):
                    new_space.remarks = data.get('remarks')
            else:
                # 基础版本的模型
                new_space = ParkingSpace(
                    parking_lot_id=data['parking_lot_id'],
                    space_number=space_number,
                    type=3,  # 充电车位类型为3
                    status=data.get('status', 0)  # 默认空闲
                )
                # 尝试手动设置power和remarks
                if hasattr(new_space, 'power'):
                    new_space.power = data.get('power', 7)
                if hasattr(new_space, 'remarks') and data.get('remarks'):
                    new_space.remarks = data.get('remarks')
        except Exception as model_error:
            current_app.logger.error(f"创建充电车位对象失败: {str(model_error)}")
            # 使用最基本的参数创建
            new_space = ParkingSpace(
                parking_lot_id=data['parking_lot_id'],
                space_number=space_number,
                type=3,  # 充电车位类型为3
                status=data.get('status', 0)  # 默认空闲
            )

        # 保存车位
        db.session.add(new_space)
        db.session.commit()

        # 更新停车场总车位数
        parking_lot.total_spaces += 1
        db.session.commit()

        # 安全地获取车位详情
        try:
            space_details = new_space.get_details()
        except Exception as detail_error:
            current_app.logger.error(f"获取新创建车位详情失败: {str(detail_error)}")
            # 手动构建车位信息
            type_map = {0: '普通车位', 1: '普通车位', 2: '残疾人车位', 3: '充电车位'}
            status_map = {0: '空闲', 1: '已占用', 2: '故障', 3: '维修中', 4: '禁用'}

            # 安全地获取日期时间字符串
            def safe_isoformat(dt):
                try:
                    return dt.isoformat() if dt else None
                except Exception:
                    return None

            space_details = {
                'id': new_space.id,
                'parking_lot_id': new_space.parking_lot_id,
                'space_number': new_space.space_number,
                'type': new_space.type,
                'type_text': type_map.get(new_space.type, '未知类型'),
                'status': new_space.status,
                'status_text': status_map.get(new_space.status, '未知状态'),
                'current_vehicle_id': getattr(new_space, 'current_vehicle_id', None),
                'power': getattr(new_space, 'power', None),
                'charging_type': getattr(new_space, 'charging_type', None),
                'created_at': safe_isoformat(getattr(new_space, 'created_at', None)),
                'updated_at': safe_isoformat(getattr(new_space, 'updated_at', None))
            }

        return api_response(
            data=space_details,
            message="创建充电车位成功",
            code=201
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建充电车位失败: {str(e)}")
        return api_response(message=f"创建充电车位失败: {str(e)}", status="error", code=500)

# 更新充电车位
@charging_bp.route('/charging-spaces/<int:space_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_charging_space(space_id):
    """更新充电车位（仅管理员）"""
    try:
        # 记录请求信息，便于调试
        current_app.logger.info(f"更新充电车位请求，ID: {space_id}")
        data = request.get_json()
        current_app.logger.info(f"请求数据: {data}")

        from app.parkinglots.models import ParkingSpace
        space = ParkingSpace.query.get(space_id)
        if not space:
            current_app.logger.warning(f"充电车位不存在，ID: {space_id}")
            return api_response(message="充电车位不存在", status="error", code=404)

        # 验证是否为充电车位
        if space.type != 3:
            current_app.logger.warning(f"非充电车位，类型: {space.type}，ID: {space_id}")
            return api_response(message="只能更新充电车位", status="error", code=400)

        # 记录更新前的状态
        current_app.logger.info(f"更新前状态: {space.get_details()}")

        # 更新字段
        if 'space_number' in data:
            # 验证车位编号是否已存在
            existing_space = ParkingSpace.query.filter(
                ParkingSpace.parking_lot_id == space.parking_lot_id,
                ParkingSpace.space_number == data['space_number'],
                ParkingSpace.id != space_id
            ).first()
            if existing_space:
                current_app.logger.warning(f"车位编号已存在: {data['space_number']}")
                return api_response(message="车位编号已存在", status="error", code=400)

            current_app.logger.info(f"更新车位编号: {space.space_number} -> {data['space_number']}")
            space.space_number = data['space_number']

        if 'status' in data:
            current_app.logger.info(f"更新状态: {space.status} -> {data['status']}")
            space.status = data['status']

        if 'power' in data:
            current_app.logger.info(f"更新充电功率: {space.power} -> {data['power']}")
            space.power = data['power']

        if 'remarks' in data:
            current_app.logger.info(f"更新备注: {space.remarks} -> {data['remarks']}")
            space.remarks = data['remarks']

        # 更新最近维护时间（如果有）
        if 'last_maintenance_time' in data and data['last_maintenance_time']:
            try:
                # 尝试解析日期时间字符串
                if isinstance(data['last_maintenance_time'], str):
                    # 移除可能的 'Z' 后缀并添加时区信息
                    maintenance_time_str = data['last_maintenance_time'].replace('Z', '+00:00')
                    space.last_maintenance_time = datetime.fromisoformat(maintenance_time_str)
                else:
                    space.last_maintenance_time = data['last_maintenance_time']

                current_app.logger.info(f"更新最近维护时间: {space.last_maintenance_time}")
            except Exception as date_error:
                current_app.logger.error(f"解析维护时间失败: {str(date_error)}, 值: {data['last_maintenance_time']}")
                # 继续执行，不因日期解析错误而中断整个更新

        # 保存更改
        space.updated_at = datetime.now()  # 确保更新时间被设置
        db.session.commit()

        # 记录更新后的状态
        current_app.logger.info(f"更新成功，更新后状态: {space.get_details()}")

        return api_response(
            data=space.get_details(),
            message="更新充电车位成功"
        )
    except Exception as e:
        db.session.rollback()
        # 记录详细错误信息
        import traceback
        error_traceback = traceback.format_exc()
        current_app.logger.error(f"更新充电车位失败: {str(e)}\n{error_traceback}")

        # 返回友好的错误消息
        return api_response(
            message="更新充电车位失败，请联系管理员检查服务器日志",
            status="error",
            code=500
        )

# 删除充电车位
@charging_bp.route('/charging-spaces/<int:space_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_charging_space(space_id):
    """删除充电车位（仅管理员）"""
    try:
        from app.parkinglots.models import ParkingSpace
        space = ParkingSpace.query.get(space_id)
        if not space:
            return api_response(message="充电车位不存在", status="error", code=404)

        # 验证是否为充电车位
        if space.type != 3:
            return api_response(message="只能删除充电车位", status="error", code=400)

        # 验证车位是否被占用
        if space.status == 1:
            return api_response(message="车位正在被占用，无法删除", status="error", code=400)

        # 获取停车场
        from app.parkinglots.models import ParkingLot
        parking_lot = ParkingLot.query.get(space.parking_lot_id)

        # 删除车位
        db.session.delete(space)

        # 更新停车场总车位数
        if parking_lot:
            parking_lot.total_spaces -= 1

        db.session.commit()

        return api_response(
            message="删除充电车位成功"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除充电车位失败: {str(e)}")
        return api_response(message=f"删除充电车位失败: {str(e)}", status="error", code=500)

# 维护充电车位
@charging_bp.route('/charging-spaces/<int:space_id>/maintain', methods=['PUT'])
@jwt_required()
@admin_required
def maintain_charging_space(space_id):
    """维护充电车位（仅管理员）"""
    try:
        # 记录请求信息，便于调试
        current_app.logger.info(f"维护充电车位请求，ID: {space_id}")
        data = request.get_json()
        current_app.logger.info(f"请求数据: {data}")

        from app.parkinglots.models import ParkingSpace
        space = ParkingSpace.query.get(space_id)
        if not space:
            current_app.logger.warning(f"充电车位不存在，ID: {space_id}")
            return api_response(message="充电车位不存在", status="error", code=404)

        # 验证是否为充电车位
        if space.type != 3:
            current_app.logger.warning(f"非充电车位，类型: {space.type}，ID: {space_id}")
            return api_response(message="只能维护充电车位", status="error", code=400)

        # 记录更新前的状态
        current_app.logger.info(f"维护前状态: {space.get_details()}")

        # 验证必要字段
        required_fields = ['maintenance_type', 'maintenance_staff', 'maintenance_time', 'status_after', 'maintenance_content']
        for field in required_fields:
            if field not in data:
                current_app.logger.warning(f"缺少必要字段: {field}")
                return api_response(message=f"缺少必要字段: {field}", status="error", code=400)

        # 更新车位状态
        current_app.logger.info(f"更新状态: {space.status} -> {data['status_after']}")
        space.status = data['status_after']

        # 更新最近维护时间
        try:
            # 尝试解析日期时间字符串
            if isinstance(data['maintenance_time'], str):
                # 移除可能的 'Z' 后缀并添加时区信息
                maintenance_time_str = data['maintenance_time'].replace('Z', '+00:00')
                space.last_maintenance_time = datetime.fromisoformat(maintenance_time_str)
            else:
                space.last_maintenance_time = data['maintenance_time']

            current_app.logger.info(f"更新最近维护时间: {space.last_maintenance_time}")
        except Exception as date_error:
            current_app.logger.error(f"解析维护时间失败: {str(date_error)}, 值: {data['maintenance_time']}")
            # 使用当前时间作为备选
            space.last_maintenance_time = datetime.now()
            current_app.logger.info(f"使用当前时间作为维护时间: {space.last_maintenance_time}")

        # 更新备注
        current_app.logger.info(f"更新备注: {space.remarks} -> {data.get('maintenance_content')}")
        space.remarks = data.get('maintenance_content')

        # 保存更改
        space.updated_at = datetime.now()  # 确保更新时间被设置
        db.session.commit()

        # 记录更新后的状态
        current_app.logger.info(f"维护成功，更新后状态: {space.get_details()}")

        return api_response(
            data=space.get_details(),
            message="维护充电车位成功"
        )
    except Exception as e:
        db.session.rollback()
        # 记录详细错误信息
        import traceback
        error_traceback = traceback.format_exc()
        current_app.logger.error(f"维护充电车位失败: {str(e)}\n{error_traceback}")

        # 返回友好的错误消息
        return api_response(
            message="维护充电车位失败，请联系管理员检查服务器日志",
            status="error",
            code=500
        )

# 获取充电故障列表
@charging_bp.route('/charging-faults', methods=['GET'])
@jwt_required()
def get_charging_faults():
    """获取充电故障列表，支持分页、排序和筛选"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        status = request.args.get('status')
        parking_lot_id = request.args.get('parking_lot_id')
        space_id = request.args.get('space_id')
        fault_type = request.args.get('fault_type')

        # 记录请求参数
        current_app.logger.info(f"获取充电故障列表，参数: page={page}, per_page={per_page}, status={status}, parking_lot_id={parking_lot_id}, space_id={space_id}, fault_type={fault_type}")

        # 构建查询
        query = ChargingFault.query

        # 应用筛选条件
        if status is not None and status != '':
            try:
                status_int = int(status)
                query = query.filter_by(status=status_int)
            except (ValueError, TypeError):
                current_app.logger.warning(f"无效的状态值: {status}")

        if parking_lot_id is not None and parking_lot_id != '':
            try:
                parking_lot_id_int = int(parking_lot_id)
                query = query.filter_by(parking_lot_id=parking_lot_id_int)
            except (ValueError, TypeError):
                current_app.logger.warning(f"无效的停车场ID: {parking_lot_id}")

        if space_id is not None and space_id != '':
            try:
                space_id_int = int(space_id)
                query = query.filter_by(space_id=space_id_int)
            except (ValueError, TypeError):
                current_app.logger.warning(f"无效的车位ID: {space_id}")

        if fault_type is not None and fault_type != '':
            query = query.filter_by(fault_type=fault_type)

        # 获取排序参数
        sort_field = request.args.get('sort_field', 'id')
        sort_order = request.args.get('sort_order', 'asc')

        # 记录排序参数
        current_app.logger.info(f"排序参数: field={sort_field}, order={sort_order}")

        # 根据排序参数设置排序方式
        if sort_field == 'id':
            if sort_order.lower() == 'asc':
                query = query.order_by(ChargingFault.id.asc())
            else:
                query = query.order_by(ChargingFault.id.desc())
        elif sort_field == 'report_time':
            if sort_order.lower() == 'asc':
                query = query.order_by(ChargingFault.report_time.asc())
            else:
                query = query.order_by(ChargingFault.report_time.desc())
        else:
            # 默认按ID升序排序
            query = query.order_by(ChargingFault.id.asc())

        try:
            # 执行分页查询
            pagination = query.paginate(page=page, per_page=per_page, error_out=False)

            # 格式化结果
            paginated_faults = []
            for fault in pagination.items:
                try:
                    fault_details = fault.get_details()
                    paginated_faults.append(fault_details)
                except Exception as detail_error:
                    current_app.logger.error(f"获取故障详情失败，ID: {fault.id}, 错误: {str(detail_error)}")
                    # 添加简化版的故障信息
                    paginated_faults.append({
                        'id': fault.id,
                        'parking_lot_id': fault.parking_lot_id,
                        'space_id': fault.space_id,
                        'fault_type': fault.fault_type,
                        'status': fault.status,
                        'report_time': fault.report_time.isoformat() if fault.report_time else None
                    })

            # 获取总数
            total = pagination.total
        except Exception as pagination_error:
            current_app.logger.error(f"分页查询失败: {str(pagination_error)}")
            # 如果分页失败，尝试获取所有记录并手动分页
            all_faults = query.all()
            total = len(all_faults)

            start_idx = (page - 1) * per_page
            end_idx = min(start_idx + per_page, total)

            paginated_faults = []
            for fault in all_faults[start_idx:end_idx]:
                try:
                    fault_details = fault.get_details()
                    paginated_faults.append(fault_details)
                except Exception as detail_error:
                    current_app.logger.error(f"获取故障详情失败，ID: {fault.id}, 错误: {str(detail_error)}")
                    # 添加简化版的故障信息
                    paginated_faults.append({
                        'id': fault.id,
                        'parking_lot_id': fault.parking_lot_id,
                        'space_id': fault.space_id,
                        'fault_type': fault.fault_type,
                        'status': fault.status,
                        'report_time': fault.report_time.isoformat() if fault.report_time else None
                    })

        # 记录结果
        current_app.logger.info(f"获取充电故障列表成功，总数: {total}, 当前页数据: {len(paginated_faults)}")

        # 返回结果
        return api_response(
            data={
                'items': paginated_faults,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page
            },
            message="获取充电故障列表成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取充电故障列表失败: {str(e)}")
        return api_response(message=f"获取充电故障列表失败: {str(e)}", status="error", code=500)

# 获取有充电车位的停车场列表
@charging_bp.route('/charging/parking-lots', methods=['GET'])
@jwt_required()
def get_charging_parking_lots():
    """获取有充电车位的停车场列表"""
    try:
        # 获取所有停车场
        parking_lots = ParkingLot.query.filter_by(status=1).all()

        # 获取每个停车场的详细信息，包括充电车位统计
        parking_lots_data = [lot.get_details() for lot in parking_lots]

        return api_response(data=parking_lots_data)
    except Exception as e:
        current_app.logger.error(f"获取充电停车场列表失败: {str(e)}")
        return api_response(message=f"获取充电停车场列表失败: {str(e)}", status="error", code=500)

# 获取进行中的充电记录
@charging_bp.route('/charging/records/active', methods=['GET'])
@jwt_required()
def get_active_charging_records():
    """获取进行中的充电记录"""
    try:
        user_id = g.user.u_id

        # 获取用户进行中的充电记录
        active_records = ChargingRecord.query.filter_by(
            user_id=user_id,
            status=0  # 进行中
        ).all()

        # 获取详细信息
        records_data = [record.get_details(include_relations=True) for record in active_records]

        return api_response(data=records_data)
    except Exception as e:
        current_app.logger.error(f"获取进行中的充电记录失败: {str(e)}")
        return api_response(message=f"获取进行中的充电记录失败: {str(e)}", status="error", code=500)

# 获取历史充电记录
@charging_bp.route('/charging/records/history', methods=['GET'])
@jwt_required()
def get_history_charging_records():
    """获取历史充电记录"""
    try:
        user_id = g.user.u_id
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        license_plate = request.args.get('license', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        # 构建查询
        query = ChargingRecord.query.filter_by(
            user_id=user_id,
            status=1  # 已完成
        )

        # 按车牌号筛选
        if license_plate:
            # 获取匹配车牌号的车辆ID
            vehicles = Bikes.query.filter(Bikes.b_num.like(f'%{license_plate}%')).all()
            vehicle_ids = [vehicle.b_id for vehicle in vehicles]
            if vehicle_ids:
                query = query.filter(ChargingRecord.vehicle_id.in_(vehicle_ids))
            else:
                # 没有匹配的车辆，返回空结果
                return api_response(data={'items': [], 'total': 0})

        # 按日期范围筛选
        if start_date:
            start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(ChargingRecord.start_time >= start_datetime)

        if end_date:
            end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
            # 设置为当天的23:59:59
            end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
            query = query.filter(ChargingRecord.start_time <= end_datetime)

        # 获取排序参数
        sort_field = request.args.get('sort_field', 'id')
        sort_order = request.args.get('sort_order', 'asc')

        # 打印排序参数，便于调试
        current_app.logger.info(f"排序参数: field={sort_field}, order={sort_order}")
        print(f"排序参数: field={sort_field}, order={sort_order}")

        # 根据排序参数设置排序方式
        print(f"应用排序: field={sort_field}, order={sort_order}")

        # 强制清除之前的排序
        query = query.order_by(None)

        # 使用更直接的方式应用排序
        if sort_field == 'start_time':
            # 使用字符串格式的排序表达式
            if sort_order.lower() == 'asc':
                query = query.order_by(db.text('start_time ASC'))
            else:
                query = query.order_by(db.text('start_time DESC'))
        elif sort_field == 'end_time':
            if sort_order.lower() == 'asc':
                query = query.order_by(db.text('end_time ASC'))
            else:
                query = query.order_by(db.text('end_time DESC'))
        else:
            # 默认按ID排序
            if sort_order.lower() == 'asc':
                query = query.order_by(db.text('id ASC'))
            else:
                query = query.order_by(db.text('id DESC'))

        # 打印最终的SQL查询
        print(f"SQL查询: {query}")
        try:
            sql_str = str(query.statement.compile(compile_kwargs={'literal_binds': True}))
            print(f"SQL查询字符串: {sql_str}")
        except Exception as e:
            print(f"无法打印SQL查询字符串: {str(e)}")

        # 打印排序后的前几条记录ID，验证排序是否生效
        try:
            sample_records = query.limit(5).all()
            sample_ids = [record.id for record in sample_records]
            sample_times = []
            if sort_field == 'start_time':
                sample_times = [record.start_time for record in sample_records]
            elif sort_field == 'end_time':
                sample_times = [record.end_time for record in sample_records]
            print(f"排序后的前5条记录ID: {sample_ids}")
            print(f"排序后的前5条记录时间: {sample_times}")
        except Exception as e:
            print(f"无法获取排序后的样本记录: {str(e)}")

        # 打印查询结果数量
        total = query.count()
        print(f"查询结果总数: {total}")

        # 应用分页
        records = query.offset((page - 1) * limit).limit(limit).all()

        # 打印返回的记录ID，验证排序是否生效
        record_ids = [record.id for record in records]
        print(f"返回的记录ID: {record_ids}")

        # 打印返回的记录时间，验证排序是否生效
        if sort_field == 'start_time':
            record_times = [record.start_time.isoformat() if record.start_time else None for record in records]
            print(f"返回的记录开始时间: {record_times}")
        elif sort_field == 'end_time':
            record_times = [record.end_time.isoformat() if record.end_time else None for record in records]
            print(f"返回的记录结束时间: {record_times}")

        # 获取详细信息
        records_data = [record.get_details(include_relations=True) for record in records]

        return api_response(data={'items': records_data, 'total': total})
    except Exception as e:
        current_app.logger.error(f"获取历史充电记录失败: {str(e)}")
        return api_response(message=f"获取历史充电记录失败: {str(e)}", status="error", code=500)

# 开始充电
@charging_bp.route('/charging/start', methods=['POST'])
@jwt_required()
def start_charging():
    """开始充电"""
    try:
        data = request.get_json()
        user_id = g.user.u_id

        # 验证必要参数
        required_fields = ['vehicle_id', 'parking_lot_id', 'parking_space_id']
        for field in required_fields:
            if field not in data:
                return api_response(message=f"缺少必要参数: {field}", status="error", code=400)

        vehicle_id = data['vehicle_id']
        parking_lot_id = data['parking_lot_id']
        parking_space_id = data['parking_space_id']

        # 验证车辆是否属于当前用户
        vehicle = Bikes.query.filter_by(b_id=vehicle_id, belong_to=user_id).first()
        if not vehicle:
            return api_response(message="车辆不存在或不属于当前用户", status="error", code=400)

        # 验证车辆是否已有进行中的充电记录
        existing_charging = ChargingRecord.query.filter_by(
            vehicle_id=vehicle_id,
            status=0  # 进行中
        ).first()

        if existing_charging:
            return api_response(message="该车辆已有进行中的充电记录", status="error", code=400)

        # 检查车辆是否被禁用
        is_disabled, reason, _ = is_vehicle_disabled(vehicle_id)
        if is_disabled:
            return api_response(
                message=f"车辆已被禁用，无法充电。{reason}",
                status="error",
                code=403
            )

        # 检查车辆是否可用于充电操作
        can_operate, operation_reason = check_vehicle_for_operation(vehicle_id, "charging")
        if not can_operate:
            return api_response(
                message=operation_reason,
                status="error",
                code=403
            )

        # 验证停车场是否存在
        parking_lot = ParkingLot.query.get(parking_lot_id)
        if not parking_lot:
            return api_response(message="停车场不存在", status="error", code=400)

        # 验证车位是否存在且可用
        parking_space = ParkingSpace.query.get(parking_space_id)
        if not parking_space:
            return api_response(message="车位不存在", status="error", code=400)

        if parking_space.status != 0:  # 不是空闲状态
            return api_response(message="车位不可用", status="error", code=400)

        if parking_space.type != 3:  # 不是充电车位
            return api_response(message="该车位不是充电车位", status="error", code=400)

        # 查找或创建停车记录
        parking_record = ParkingRecord.query.filter_by(
            vehicle_id=vehicle_id,
            status=0  # 进行中
        ).first()

        if not parking_record:
            # 创建新的停车记录
            parking_record = ParkingRecord(
                vehicle_id=vehicle_id,
                user_id=user_id,
                parking_lot_id=parking_lot_id,
                parking_space_id=parking_space_id
            )
            db.session.add(parking_record)
            db.session.flush()  # 获取ID

        # 创建充电记录
        charging_record = ChargingRecord(
            parking_record_id=parking_record.id,
            vehicle_id=vehicle_id,
            user_id=user_id,
            parking_lot_id=parking_lot_id,
            parking_space_id=parking_space_id,
            power=parking_space.power
        )

        # 使用当前时间作为开始时间
        from datetime import datetime
        current_time = datetime.now()
        charging_record.start_time = current_time
        print(f"设置充电开始时间为当前时间: {charging_record.start_time}")

        # 设置备注（如果有）
        if 'remarks' in data:
            charging_record.remarks = data['remarks']

        # 更新车位状态
        parking_space.status = 1  # 已占用
        parking_space.current_vehicle_id = vehicle_id

        db.session.add(charging_record)
        db.session.commit()

        return api_response(
            message="充电开始成功",
            data=charging_record.get_details(include_relations=True)
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"开始充电失败: {str(e)}")
        return api_response(message=f"开始充电失败: {str(e)}", status="error", code=500)

# 结束充电
@charging_bp.route('/charging/end/<int:record_id>', methods=['POST'])
@jwt_required()
def end_charging(record_id):
    """结束充电"""
    try:
        user_id = g.user.u_id

        # 获取充电记录
        charging_record = ChargingRecord.query.get(record_id)
        if not charging_record:
            return api_response(message="充电记录不存在", status="error", code=404)

        # 验证记录是否属于当前用户
        if charging_record.user_id != user_id:
            return api_response(message="无权操作此充电记录", status="error", code=403)

        # 验证记录是否处于进行中状态
        if charging_record.status != 0:
            return api_response(message="该充电记录不是进行中状态", status="error", code=400)

        # 获取车位
        parking_space = ParkingSpace.query.get(charging_record.parking_space_id)
        if parking_space:
            # 更新车位状态
            parking_space.status = 0  # 空闲
            parking_space.current_vehicle_id = None

        # 更新充电记录
        # 使用当前时间作为结束时间
        current_time = datetime.now()
        print(f"开始时间: {charging_record.start_time}, 结束时间: {current_time}")

        charging_record.end_time = current_time
        charging_record.status = 1  # 已完成

        # 计算实际充电时长
        duration = charging_record.calculate_duration()
        charging_record.duration = duration
        print(f"计算的充电时长: {charging_record.duration}分钟")

        db.session.commit()

        return api_response(
            message="充电结束成功",
            data=charging_record.get_details(include_relations=True)
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"结束充电失败: {str(e)}")
        return api_response(message=f"结束充电失败: {str(e)}", status="error", code=500)

# 获取充电故障详情
@charging_bp.route('/charging-faults/<int:fault_id>', methods=['GET'])
@jwt_required()
def get_charging_fault_detail(fault_id):
    """获取充电故障详情"""
    try:
        current_app.logger.info(f"获取充电故障详情，ID: {fault_id}")

        # 从数据库中查询充电故障记录
        fault_record = ChargingFault.query.get(fault_id)

        if not fault_record:
            return api_response(message="充电故障记录不存在", status="error", code=404)

        # 获取详细信息
        fault = fault_record.get_details(include_relations=True)

        current_app.logger.info(f"获取充电故障详情成功，ID: {fault_id}")

        return api_response(
            data=fault,
            message="获取充电故障详情成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取充电故障详情失败: {str(e)}")
        return api_response(message=f"获取充电故障详情失败: {str(e)}", status="error", code=500)

# 创建充电故障
@charging_bp.route('/charging-faults', methods=['POST'])
@jwt_required()
def create_charging_fault():
    """创建新的充电故障"""
    try:
        data = request.get_json()
        current_app.logger.info(f"创建充电故障，数据: {data}")

        # 验证数据类型
        try:
            # 确保 parking_lot_id 和 space_id 是整数
            if 'parking_lot_id' in data and not isinstance(data['parking_lot_id'], int):
                try:
                    data['parking_lot_id'] = int(data['parking_lot_id'])
                    current_app.logger.info(f"已将 parking_lot_id 转换为整数: {data['parking_lot_id']}")
                except (ValueError, TypeError) as e:
                    current_app.logger.error(f"无法将 parking_lot_id 转换为整数: {data['parking_lot_id']}, 错误: {str(e)}")
                    return api_response(message=f"parking_lot_id 必须是整数: {data['parking_lot_id']}", status="error", code=400)

            if 'space_id' in data and not isinstance(data['space_id'], int):
                try:
                    data['space_id'] = int(data['space_id'])
                    current_app.logger.info(f"已将 space_id 转换为整数: {data['space_id']}")
                except (ValueError, TypeError) as e:
                    current_app.logger.error(f"无法将 space_id 转换为整数: {data['space_id']}, 错误: {str(e)}")
                    return api_response(message=f"space_id 必须是整数: {data['space_id']}", status="error", code=400)
        except Exception as type_error:
            current_app.logger.error(f"验证数据类型时出错: {str(type_error)}")
            import traceback
            current_app.logger.error(traceback.format_exc())
            return api_response(message=f"验证数据类型时出错: {str(type_error)}", status="error", code=400)

        # 验证必要字段
        required_fields = ['parking_lot_id', 'space_id', 'fault_type', 'severity', 'reporter_name', 'fault_description']
        for field in required_fields:
            if field not in data:
                current_app.logger.error(f"缺少必要字段: {field}")
                return api_response(message=f"缺少必要字段: {field}", status="error", code=400)

        # 验证停车场是否存在
        from app.parkinglots.models import ParkingLot
        parking_lot = ParkingLot.query.get(data['parking_lot_id'])
        if not parking_lot:
            current_app.logger.error(f"停车场不存在，ID: {data['parking_lot_id']}")
            return api_response(message=f"停车场不存在，ID: {data['parking_lot_id']}", status="error", code=404)

        # 验证车位是否存在
        from app.parkinglots.models import ParkingSpace
        space = ParkingSpace.query.get(data['space_id'])
        if not space:
            current_app.logger.error(f"车位不存在，ID: {data['space_id']}")
            return api_response(message=f"车位不存在，ID: {data['space_id']}", status="error", code=404)

        # 创建故障记录
        try:
            current_app.logger.info(f"开始创建故障记录，参数: parking_lot_id={data['parking_lot_id']}({type(data['parking_lot_id'])}), space_id={data['space_id']}({type(data['space_id'])})")

            new_fault = ChargingFault(
                parking_lot_id=data['parking_lot_id'],
                space_id=data['space_id'],
                fault_type=data['fault_type'],
                severity=data['severity'],
                reporter_name=data['reporter_name'],
                fault_description=data['fault_description'],
                reporter_phone=data.get('reporter_phone')
            )

            current_app.logger.info(f"故障记录对象创建成功，准备保存到数据库")

            # 保存到数据库
            try:
                created_fault = new_fault.create()
                current_app.logger.info(f"故障记录保存成功，ID: {created_fault.id}")
            except Exception as create_error:
                import traceback
                error_traceback = traceback.format_exc()
                current_app.logger.error(f"保存故障记录失败: {str(create_error)}\n{error_traceback}")
                return api_response(message=f"创建充电故障失败: {str(create_error)}", status="error", code=500)

            if not created_fault:
                current_app.logger.error("创建充电故障失败，返回值为空")
                return api_response(message="创建充电故障失败，返回值为空", status="error", code=500)
        except Exception as fault_error:
            import traceback
            error_traceback = traceback.format_exc()
            current_app.logger.error(f"创建故障记录对象失败: {str(fault_error)}\n{error_traceback}")
            return api_response(message=f"创建故障记录对象失败: {str(fault_error)}", status="error", code=500)

        current_app.logger.info(f"创建充电故障成功，ID: {created_fault.id}")

        # 自动创建对应的充电异常记录
        try:
            # 查找与该车位相关的最新充电记录
            from app.charging.models import ChargingRecord
            charging_record = ChargingRecord.query.filter_by(
                parking_space_id=data['space_id'],
                status=0  # 进行中的充电记录
            ).order_by(ChargingRecord.start_time.desc()).first()

            # 如果没有找到进行中的充电记录，尝试查找最近的充电记录
            if not charging_record:
                charging_record = ChargingRecord.query.filter_by(
                    parking_space_id=data['space_id']
                ).order_by(ChargingRecord.start_time.desc()).first()

            # 准备创建充电异常所需的通用信息
            exception_type = data['fault_type']
            current_app.logger.info(f"使用故障类型作为异常类型: {exception_type}")

            # 创建异常描述
            description = f"充电故障报修：{data['fault_description']}（报修人：{data['reporter_name']}）"

            # 获取故障严重程度信息
            severity_info = ""
            if data['severity'] == 'low':
                severity_info = "轻微"
            elif data['severity'] == 'medium':
                severity_info = "一般"
            elif data['severity'] == 'high':
                severity_info = "严重"
            elif data['severity'] == 'critical':
                severity_info = "紧急"

            # 添加严重程度到描述
            description += f"，严重程度：{severity_info}"

            # 添加车位信息到描述
            from app.parkinglots.models import ParkingSpace
            space = ParkingSpace.query.get(data['space_id'])
            if space:
                description += f"，车位号：{space.space_number}"

            # 如果找到了充电记录，使用它创建充电异常
            if charging_record:
                # 创建充电异常记录
                try:
                    from app.charging.models import ChargingException

                    # 确保 charging_record.id 是整数
                    charging_record_id = charging_record.id
                    if not isinstance(charging_record_id, int):
                        try:
                            charging_record_id = int(charging_record_id)
                            current_app.logger.info(f"已将 charging_record_id 转换为整数: {charging_record_id}")
                        except (ValueError, TypeError) as e:
                            current_app.logger.error(f"无法将 charging_record_id 转换为整数: {charging_record_id}, 错误: {str(e)}")
                            raise ValueError(f"charging_record_id 必须是整数: {charging_record_id}")

                    current_app.logger.info(f"准备创建充电异常记录，charging_record_id={charging_record_id}({type(charging_record_id)})")

                    new_exception = ChargingException(
                        exception_type=exception_type,
                        description=description,
                        charging_record_id=charging_record_id,
                        fault_id=created_fault.id,
                        space_id=data['space_id'],
                        parking_lot_id=data['parking_lot_id']
                    )

                    current_app.logger.info(f"充电异常对象创建成功，准备保存到数据库")

                    # 尝试添加关联故障ID的字段（如果模型支持）
                    try:
                        if hasattr(new_exception, 'fault_id'):
                            new_exception.fault_id = created_fault.id
                            current_app.logger.info(f"设置异常记录的故障ID: {created_fault.id}")
                    except Exception as attr_error:
                        current_app.logger.warning(f"设置异常记录的故障ID失败: {str(attr_error)}")

                    try:
                        created_exception = new_exception.create()
                        current_app.logger.info(f"自动创建充电异常成功，ID: {created_exception.id}，关联故障ID: {created_fault.id}")

                        # 更新故障记录，添加关联的异常ID
                        try:
                            if hasattr(created_fault, 'exception_id'):
                                created_fault.exception_id = created_exception.id
                                db.session.commit()
                                current_app.logger.info(f"更新故障记录，添加关联的异常ID: {created_exception.id}")
                        except Exception as update_error:
                            current_app.logger.warning(f"更新故障记录的异常ID失败: {str(update_error)}")
                    except Exception as create_error:
                        import traceback
                        error_traceback = traceback.format_exc()
                        current_app.logger.error(f"保存充电异常记录失败: {str(create_error)}\n{error_traceback}")
                        # 不抛出异常，继续执行
                except Exception as exception_error:
                    import traceback
                    error_traceback = traceback.format_exc()
                    current_app.logger.error(f"创建充电异常记录失败: {str(exception_error)}\n{error_traceback}")
                    # 不抛出异常，继续执行
            else:
                # 查找是否有与该车位相关的已存在的异常记录
                try:
                    # 查找与该车位相关的所有充电记录
                    all_charging_records = ChargingRecord.query.filter_by(
                        parking_space_id=data['space_id']
                    ).all()

                    # 获取这些充电记录的ID列表
                    charging_record_ids = [record.id for record in all_charging_records]

                    if charging_record_ids:
                        # 查找与这些充电记录关联的异常记录
                        existing_exceptions = ChargingException.query.filter(
                            ChargingException.charging_record_id.in_(charging_record_ids)
                        ).all()

                        if existing_exceptions:
                            current_app.logger.info(f"找到与车位 {data['space_id']} 相关的已存在异常记录: {len(existing_exceptions)} 条")

                            # 更新第一个异常记录的状态为已处理
                            for exception in existing_exceptions:
                                if exception.status == 0:  # 未处理状态
                                    exception.status = 1  # 已处理
                                    exception.processor = data['reporter_name']
                                    exception.process_time = datetime.now()
                                    exception.process_result = f"由新故障报修(ID:{created_fault.id})自动处理: {data['fault_description']}"
                                    exception.updated_at = datetime.now()

                                    # 更新故障记录，添加关联的异常ID
                                    try:
                                        if hasattr(created_fault, 'exception_id'):
                                            created_fault.exception_id = exception.id
                                            db.session.commit()
                                            current_app.logger.info(f"更新故障记录，添加关联的异常ID: {exception.id}")
                                    except Exception as update_error:
                                        current_app.logger.warning(f"更新故障记录的异常ID失败: {str(update_error)}")

                                    db.session.commit()
                                    current_app.logger.info(f"自动处理异常记录成功，ID: {exception.id}")
                                    break
                        else:
                            # 没有找到相关的异常记录，但有充电记录，使用第一个充电记录创建异常
                            try:
                                from app.charging.models import ChargingException
                                charging_record_id = charging_record_ids[0]

                                current_app.logger.info(f"未找到相关异常记录，使用充电记录ID {charging_record_id} 创建新的异常记录")

                                new_exception = ChargingException(
                                    exception_type=exception_type,
                                    description=description,
                                    charging_record_id=charging_record_id,
                                    fault_id=created_fault.id,
                                    space_id=data['space_id'],
                                    parking_lot_id=data['parking_lot_id']
                                )

                                created_exception = new_exception.create()
                                current_app.logger.info(f"使用现有充电记录创建充电异常成功，ID: {created_exception.id}")

                                # 更新故障记录，添加关联的异常ID
                                try:
                                    if hasattr(created_fault, 'exception_id'):
                                        created_fault.exception_id = created_exception.id
                                        db.session.commit()
                                except Exception as update_error:
                                    current_app.logger.warning(f"更新故障记录的异常ID失败: {str(update_error)}")
                            except Exception as e:
                                current_app.logger.error(f"使用现有充电记录创建异常记录失败: {str(e)}")
                    else:
                        # 没有找到任何充电记录，创建一个临时充电记录
                        try:
                            current_app.logger.info(f"未找到任何充电记录，创建临时充电记录")

                            # 获取车位所属停车场
                            parking_lot_id = data['parking_lot_id']

                            # 查找一个用户和车辆来创建临时记录
                            from app.users.models import Users
                            from app.bikes.models import Bikes

                            # 尝试获取管理员用户
                            admin_user = Users.query.filter_by(role=2).first()  # 假设角色2是管理员
                            if not admin_user:
                                # 如果没有管理员，获取任意用户
                                admin_user = Users.query.first()

                            # 尝试获取一个车辆
                            vehicle = None
                            if admin_user:
                                # 尝试获取该用户的车辆
                                vehicle = Bikes.query.filter_by(belong_to=admin_user.u_id).first()

                            if not vehicle:
                                # 如果没有找到特定用户的车辆，获取任意车辆
                                vehicle = Bikes.query.first()

                            if admin_user and vehicle:
                                # 创建临时停车记录
                                from app.parking_records.models import ParkingRecord

                                temp_parking_record = ParkingRecord(
                                    vehicle_id=vehicle.b_id,
                                    user_id=admin_user.u_id,
                                    parking_lot_id=parking_lot_id,
                                    parking_space_id=data['space_id']
                                )

                                # 设置备注，标记为临时记录
                                temp_parking_record.remarks = f"临时记录，由故障报修自动创建，故障ID: {created_fault.id}"

                                # 保存停车记录
                                db.session.add(temp_parking_record)
                                db.session.flush()  # 获取ID

                                # 创建临时充电记录
                                temp_charging_record = ChargingRecord(
                                    parking_record_id=temp_parking_record.id,
                                    vehicle_id=vehicle.b_id,
                                    user_id=admin_user.u_id,
                                    parking_lot_id=parking_lot_id,
                                    parking_space_id=data['space_id']
                                )

                                # 设置备注，标记为临时记录
                                temp_charging_record.remarks = f"临时记录，由故障报修自动创建，故障ID: {created_fault.id}"

                                # 保存充电记录
                                db.session.add(temp_charging_record)
                                db.session.flush()  # 获取ID

                                current_app.logger.info(f"临时充电记录创建成功，ID: {temp_charging_record.id}")

                                # 创建充电异常记录
                                new_exception = ChargingException(
                                    exception_type=exception_type,
                                    description=description + "（由系统自动创建的临时记录）",
                                    charging_record_id=temp_charging_record.id,
                                    fault_id=created_fault.id,
                                    space_id=data['space_id'],
                                    parking_lot_id=data['parking_lot_id']
                                )

                                created_exception = new_exception.create()
                                current_app.logger.info(f"使用临时充电记录创建充电异常成功，ID: {created_exception.id}")

                                # 更新故障记录，添加关联的异常ID
                                try:
                                    if hasattr(created_fault, 'exception_id'):
                                        created_fault.exception_id = created_exception.id
                                        db.session.commit()
                                except Exception as update_error:
                                    current_app.logger.warning(f"更新故障记录的异常ID失败: {str(update_error)}")
                            else:
                                current_app.logger.warning("无法找到用户或车辆来创建临时记录")
                        except Exception as e:
                            current_app.logger.error(f"创建临时记录失败: {str(e)}")
                except Exception as e:
                    current_app.logger.warning(f"查找已存在的异常记录失败: {str(e)}")
                    current_app.logger.warning(f"未找到与车位 {data['space_id']} 相关的充电记录，无法创建充电异常")
        except Exception as e:
            current_app.logger.error(f"自动创建充电异常失败: {str(e)}")
            # 不影响故障创建的返回结果

        return api_response(
            data=created_fault.get_details(),
            message="创建充电故障成功",
            code=201
        )
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        current_app.logger.error(f"创建充电故障失败: {str(e)}\n{error_traceback}")
        return api_response(message=f"创建充电故障失败: {str(e)}", status="error", code=500)

# 处理充电故障
@charging_bp.route('/charging-faults/<int:fault_id>/process', methods=['PUT'])
@jwt_required()
@admin_required
def process_charging_fault(fault_id):
    """处理充电故障（仅管理员）"""
    try:
        data = request.get_json()
        current_app.logger.info(f"处理充电故障，ID: {fault_id}，数据: {data}")

        # 验证必要字段
        required_fields = ['processor', 'process_result']
        for field in required_fields:
            if field not in data:
                return api_response(message=f"缺少必要字段: {field}", status="error", code=400)

        # 从数据库中查询充电故障记录
        fault_record = ChargingFault.query.get(fault_id)

        if not fault_record:
            return api_response(message="充电故障记录不存在", status="error", code=404)

        # 检查故障状态
        if fault_record.status != 0:
            return api_response(message="只能处理待处理状态的故障", status="error", code=400)

        # 开始处理故障
        success = fault_record.start_process(data['processor'])
        if not success:
            return api_response(message="开始处理充电故障失败", status="error", code=500)

        # 如果有处理结果，直接完成处理
        if data['process_result']:
            success = fault_record.complete_process(data['process_result'])
            if not success:
                return api_response(message="完成充电故障处理失败", status="error", code=500)

        # 获取更新后的详细信息
        fault = fault_record.get_details()

        current_app.logger.info(f"处理充电故障成功，ID: {fault_id}")

        # 查找与该故障相关的充电异常记录并同步处理
        try:
            # 查找与该故障相关的充电异常记录
            from app.charging.models import ChargingException

            # 首先尝试通过直接关联的fault_id查找
            exceptions = ChargingException.query.filter_by(
                fault_id=fault_id,
                status=0  # 未处理
            ).all()

            # 如果没有找到直接关联的异常，尝试通过车位ID查找
            if not exceptions:
                # 查找与该车位相关的未处理异常
                exceptions = ChargingException.query.filter_by(
                    space_id=fault_record.space_id,
                    status=0  # 未处理
                ).all()

            # 如果仍然没有找到，尝试通过充电记录查找（兼容旧数据）
            if not exceptions:
                # 查找与该车位相关的充电记录
                from app.charging.models import ChargingRecord

                # 查找与该车位相关的充电记录
                charging_records = ChargingRecord.query.filter_by(
                    parking_space_id=fault_record.space_id
                ).all()

                # 获取这些充电记录的ID列表
                charging_record_ids = [record.id for record in charging_records]

                # 查找与这些充电记录关联的未处理的充电异常
                if charging_record_ids:
                    exceptions = ChargingException.query.filter(
                        ChargingException.charging_record_id.in_(charging_record_ids),
                        ChargingException.status == 0  # 未处理
                    ).all()

            # 处理找到的充电异常
            if exceptions:
                for exception in exceptions:
                    exception.status = 1  # 已处理
                    exception.processor = data['processor']
                    exception.process_time = datetime.now()
                    exception.process_result = f"随充电故障(ID:{fault_id})一起处理: {data['process_result']}"
                    exception.updated_at = datetime.now()

                    # 如果异常没有关联故障ID，添加关联
                    if exception.fault_id is None:
                        exception.fault_id = fault_id
                        current_app.logger.info(f"为异常记录(ID:{exception.id})添加故障关联")

                # 提交更改
                db.session.commit()
                current_app.logger.info(f"同步处理了 {len(exceptions)} 条相关充电异常记录")
            else:
                current_app.logger.info(f"未找到与故障(ID:{fault_id})相关的未处理充电异常记录")
        except Exception as e:
            current_app.logger.error(f"同步处理充电异常失败: {str(e)}")
            # 不影响故障处理的返回结果

        return api_response(
            data=fault,
            message="处理充电故障成功"
        )
    except Exception as e:
        current_app.logger.error(f"处理充电故障失败: {str(e)}")
        return api_response(message=f"处理充电故障失败: {str(e)}", status="error", code=500)

# 关闭充电故障
@charging_bp.route('/charging-faults/<int:fault_id>/close', methods=['PUT'])
@jwt_required()
@admin_required
def close_charging_fault(fault_id):
    """关闭充电故障（仅管理员）"""
    try:
        current_app.logger.info(f"关闭充电故障，ID: {fault_id}")

        # 从数据库中查询充电故障记录
        fault_record = ChargingFault.query.get(fault_id)

        if not fault_record:
            return api_response(message="充电故障记录不存在", status="error", code=404)

        # 关闭故障
        success = fault_record.close()
        if not success:
            return api_response(message="关闭充电故障失败", status="error", code=500)

        # 获取更新后的详细信息
        fault = fault_record.get_details()

        current_app.logger.info(f"关闭充电故障成功，ID: {fault_id}")

        return api_response(
            data=fault,
            message="关闭充电故障成功"
        )
    except Exception as e:
        current_app.logger.error(f"关闭充电故障失败: {str(e)}")
        return api_response(message=f"关闭充电故障失败: {str(e)}", status="error", code=500)

# 删除充电故障
@charging_bp.route('/charging-faults/<int:fault_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_charging_fault(fault_id):
    """删除充电故障（仅管理员）"""
    try:
        current_app.logger.info(f"删除充电故障，ID: {fault_id}")

        # 从数据库中查询充电故障记录
        fault_record = ChargingFault.query.get(fault_id)

        if not fault_record:
            return api_response(message="充电故障记录不存在", status="error", code=404)

        # 删除故障记录
        try:
            # 在删除故障记录前，查找与该故障相关的充电异常记录
            try:
                # 查找与该故障相关的充电异常记录
                from app.charging.models import ChargingException

                # 首先尝试通过直接关联的fault_id查找
                related_exceptions = ChargingException.query.filter_by(
                    fault_id=fault_id
                ).all()

                # 记录直接关联的异常ID
                if related_exceptions:
                    exception_ids = [exception.id for exception in related_exceptions]
                    current_app.logger.info(f"找到直接关联故障ID {fault_id} 的充电异常记录: {exception_ids}")

                # 如果没有找到直接关联的异常，尝试通过车位ID查找
                if not related_exceptions:
                    # 查找与该车位相关的异常
                    space_related_exceptions = ChargingException.query.filter_by(
                        space_id=fault_record.space_id
                    ).all()

                    if space_related_exceptions:
                        related_exceptions = space_related_exceptions
                        exception_ids = [exception.id for exception in related_exceptions]
                        current_app.logger.info(f"找到与车位ID {fault_record.space_id} 相关的充电异常记录: {exception_ids}")

                # 如果仍然没有找到，尝试通过充电记录查找（兼容旧数据）
                if not related_exceptions:
                    # 查找与该车位相关的充电记录
                    from app.charging.models import ChargingRecord

                    # 查找与该车位相关的充电记录
                    charging_records = ChargingRecord.query.filter_by(
                        parking_space_id=fault_record.space_id
                    ).all()

                    # 获取这些充电记录的ID列表
                    charging_record_ids = [record.id for record in charging_records]

                    # 查找与这些充电记录关联的充电异常
                    if charging_record_ids:
                        record_related_exceptions = ChargingException.query.filter(
                            ChargingException.charging_record_id.in_(charging_record_ids)
                        ).all()

                        if record_related_exceptions:
                            related_exceptions = record_related_exceptions
                            exception_ids = [exception.id for exception in related_exceptions]
                            current_app.logger.info(f"找到与充电记录相关的充电异常记录: {exception_ids}")
            except Exception as e:
                current_app.logger.error(f"查找相关充电异常记录失败: {str(e)}")
                related_exceptions = []

            # 删除故障记录
            db.session.delete(fault_record)

            # 删除相关的充电异常记录
            for exception in related_exceptions:
                try:
                    db.session.delete(exception)
                    current_app.logger.info(f"删除相关充电异常记录，ID: {exception.id}")
                except Exception as e:
                    current_app.logger.error(f"删除充电异常记录失败，ID: {exception.id}, 错误: {str(e)}")

            # 提交事务
            db.session.commit()
            current_app.logger.info(f"删除充电故障成功，ID: {fault_id}")
            return api_response(
                data={'id': fault_id},
                message="删除充电故障成功"
            )
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除充电故障数据库操作失败: {str(e)}")
            return api_response(message="删除充电故障失败，数据库错误", status="error", code=500)
    except Exception as e:
        current_app.logger.error(f"删除充电故障失败: {str(e)}")
        return api_response(message=f"删除充电故障失败: {str(e)}", status="error", code=500)

# 获取充电异常列表
@charging_bp.route('/charging-exceptions', methods=['GET'])
@jwt_required()
def get_charging_exceptions():
    """获取充电异常列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        type_param = request.args.get('type')  # 异常类型
        handled_param = request.args.get('handled')  # 处理状态
        start_date = request.args.get('start_date')  # 开始日期
        end_date = request.args.get('end_date')  # 结束日期

        # 记录请求参数
        current_app.logger.info(f"获取充电异常列表，参数: page={page}, per_page={per_page}, type={type_param}, handled={handled_param}, start_date={start_date}, end_date={end_date}")

        # 构建查询
        query = ChargingException.query

        # 应用筛选条件
        if type_param:
            query = query.filter_by(exception_type=type_param)

        if handled_param is not None:
            # 将handled转换为status: handled=true -> status=1, handled=false -> status=0
            if handled_param.lower() == 'true':
                query = query.filter_by(status=1)
            elif handled_param.lower() == 'false':
                query = query.filter_by(status=0)

        # 日期筛选
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(ChargingException.time >= start_datetime)
            except ValueError:
                current_app.logger.warning(f"无效的开始日期格式: {start_date}")

        if end_date:
            try:
                end_datetime = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                query = query.filter(ChargingException.time <= end_datetime)
            except ValueError:
                current_app.logger.warning(f"无效的结束日期格式: {end_date}")

        # 获取排序参数
        sort_field = request.args.get('sort_field', 'id')
        sort_order = request.args.get('sort_order', 'asc')

        # 记录排序参数
        current_app.logger.info(f"排序参数: field={sort_field}, order={sort_order}")

        # 根据排序参数设置排序方式
        if sort_field == 'id':
            if sort_order.lower() == 'asc':
                query = query.order_by(ChargingException.id.asc())
            else:
                query = query.order_by(ChargingException.id.desc())
        elif sort_field == 'time':
            if sort_order.lower() == 'asc':
                query = query.order_by(ChargingException.time.asc())
            else:
                query = query.order_by(ChargingException.time.desc())
        elif sort_field == 'process_time':
            # 添加对处理时间的排序支持
            if sort_order.lower() == 'asc':
                # 处理时间为空的记录排在最后
                query = query.order_by(
                    ChargingException.process_time.is_(None).asc(),
                    ChargingException.process_time.asc()
                )
            else:
                # 处理时间为空的记录排在最后
                query = query.order_by(
                    ChargingException.process_time.is_(None).asc(),
                    ChargingException.process_time.desc()
                )
        else:
            # 默认按ID升序排序
            query = query.order_by(ChargingException.id.asc())

        try:
            # 执行分页查询
            pagination = query.paginate(page=page, per_page=per_page, error_out=False)

            # 格式化结果
            paginated_exceptions = []
            for exception in pagination.items:
                try:
                    exception_details = exception.get_details(include_relations=True)
                    paginated_exceptions.append(exception_details)
                except Exception as detail_error:
                    current_app.logger.error(f"获取异常详情失败，ID: {exception.id}, 错误: {str(detail_error)}")
                    # 添加简化版的异常信息
                    paginated_exceptions.append({
                        'id': exception.id,
                        'charging_record_id': exception.charging_record_id,
                        'fault_id': exception.fault_id,
                        'space_id': exception.space_id,
                        'parking_lot_id': exception.parking_lot_id,
                        'type': exception.exception_type,
                        'description': exception.description,
                        'time': exception.time.isoformat() if exception.time else None,
                        'status': exception.status,
                        'handled': exception.status == 1
                    })

            # 获取总数
            total = pagination.total
        except Exception as pagination_error:
            current_app.logger.error(f"分页查询失败: {str(pagination_error)}")
            # 如果分页失败，尝试获取所有记录并手动分页
            all_exceptions = query.all()
            total = len(all_exceptions)

            start_idx = (page - 1) * per_page
            end_idx = min(start_idx + per_page, total)

            paginated_exceptions = []
            for exception in all_exceptions[start_idx:end_idx]:
                try:
                    exception_details = exception.get_details(include_relations=True)
                    paginated_exceptions.append(exception_details)
                except Exception as detail_error:
                    current_app.logger.error(f"获取异常详情失败，ID: {exception.id}, 错误: {str(detail_error)}")
                    # 添加简化版的异常信息
                    paginated_exceptions.append({
                        'id': exception.id,
                        'charging_record_id': exception.charging_record_id,
                        'fault_id': exception.fault_id,
                        'space_id': exception.space_id,
                        'parking_lot_id': exception.parking_lot_id,
                        'type': exception.exception_type,
                        'description': exception.description,
                        'time': exception.time.isoformat() if exception.time else None,
                        'status': exception.status,
                        'handled': exception.status == 1
                    })

        # 记录结果
        current_app.logger.info(f"获取充电异常列表成功，总数: {total}, 当前页数据: {len(paginated_exceptions)}")

        # 返回结果
        return api_response(
            data={
                'items': paginated_exceptions,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page
            },
            message="获取充电异常列表成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取充电异常列表失败: {str(e)}")
        return api_response(message=f"获取充电异常列表失败: {str(e)}", status="error", code=500)

# 获取充电异常统计
@charging_bp.route('/charging-exception-stats', methods=['GET'])
@jwt_required()
@admin_required
def get_charging_exception_stats():
    """获取充电异常统计（仅管理员）"""
    try:
        # 从数据库中查询充电异常记录
        from app.charging.models import ChargingException
        from sqlalchemy import func

        # 获取总数
        total = ChargingException.query.count()

        # 按状态统计
        status_stats = db.session.query(
            ChargingException.status,
            func.count(ChargingException.id)
        ).group_by(ChargingException.status).all()

        # 初始化状态计数
        unprocessed = 0
        processed = 0
        ignored = 0

        # 填充状态计数
        for status, count in status_stats:
            if status == 0:
                unprocessed = count
            elif status == 1:
                processed = count
            elif status == 2:
                ignored = count

        # 按类型统计
        type_stats = db.session.query(
            ChargingException.exception_type,
            func.count(ChargingException.id)
        ).group_by(ChargingException.exception_type).all()

        # 初始化类型计数
        by_type = {
            'connection_lost': 0,
            'power_outage': 0,
            'payment_failure': 0,
            'overheating': 0,
            'other': 0
        }

        # 填充类型计数
        for exception_type, count in type_stats:
            if exception_type in by_type:
                by_type[exception_type] = count

        # 按停车场统计 - 优先使用直接关联的停车场ID
        # 首先获取所有异常记录
        exception_records = ChargingException.query.all()

        # 创建停车场统计字典
        parking_lot_stats = {}

        # 遍历所有异常记录，获取停车场ID
        for exception in exception_records:
            lot_id = None

            # 优先使用直接关联的停车场ID
            if exception.parking_lot_id:
                lot_id = exception.parking_lot_id
            # 如果没有直接关联的停车场ID，尝试通过充电记录获取
            elif exception.charging_record_id:
                charging_record = ChargingRecord.query.get(exception.charging_record_id)
                if charging_record and charging_record.parking_lot_id:
                    lot_id = charging_record.parking_lot_id

            if lot_id:
                if lot_id in parking_lot_stats:
                    parking_lot_stats[lot_id] += 1
                else:
                    parking_lot_stats[lot_id] = 1

        # 获取停车场信息
        by_parking_lot = []
        for lot_id, count in parking_lot_stats.items():
            lot = ParkingLot.query.get(lot_id)
            if lot:
                by_parking_lot.append({
                    'id': lot_id,
                    'name': lot.name,
                    'count': count
                })

        # 计算处理率
        handle_rate = '0%'
        if total > 0:
            handle_rate = f"{round(processed / total * 100)}%"

        # 组装统计数据
        stats = {
            'total': total,
            'unprocessed': unprocessed,
            'processed': processed,
            'ignored': ignored,
            'by_type': by_type,
            'by_parking_lot': by_parking_lot,
            'unhandled': unprocessed,  # 添加前端使用的字段名
            'handled': processed,      # 添加前端使用的字段名
            'handleRate': handle_rate  # 添加处理率字段
        }

        return api_response(
            data=stats,
            message="获取充电异常统计成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取充电异常统计失败: {str(e)}")
        return api_response(message=f"获取充电异常统计失败: {str(e)}", status="error", code=500)

# 删除充电异常
@charging_bp.route('/charging-exceptions/<int:exception_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_charging_exception(exception_id):
    """删除充电异常（仅管理员）"""
    try:
        current_app.logger.info(f"删除充电异常，ID: {exception_id}")

        # 从数据库中查询充电异常记录
        exception_record = ChargingException.query.get(exception_id)

        if not exception_record:
            return api_response(message="充电异常记录不存在", status="error", code=404)

        # 删除异常记录
        try:
            db.session.delete(exception_record)
            db.session.commit()
            current_app.logger.info(f"删除充电异常成功，ID: {exception_id}")
            return api_response(
                data={'id': exception_id},
                message="删除充电异常成功"
            )
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除充电异常数据库操作失败: {str(e)}")
            return api_response(message="删除充电异常失败，数据库错误", status="error", code=500)
    except Exception as e:
        current_app.logger.error(f"删除充电异常失败: {str(e)}")
        return api_response(message=f"删除充电异常失败: {str(e)}", status="error", code=500)

# 处理充电异常
@charging_bp.route('/charging-exceptions/<int:exception_id>/process', methods=['PUT'])
@jwt_required()
@admin_required
def process_charging_exception(exception_id):
    """处理充电异常（仅管理员）"""
    try:
        data = request.get_json()
        current_app.logger.info(f"处理充电异常，ID: {exception_id}，数据: {data}")

        # 验证必要字段
        required_fields = ['processor', 'process_result']
        for field in required_fields:
            if field not in data:
                return api_response(message=f"缺少必要字段: {field}", status="error", code=400)

        # 从数据库中查询充电异常记录
        from app.charging.models import ChargingException
        exception_record = ChargingException.query.get(exception_id)

        if not exception_record:
            return api_response(message="充电异常记录不存在", status="error", code=404)

        # 更新异常记录
        exception_record.status = 1  # 已处理
        exception_record.processor = data['processor']
        exception_record.process_time = datetime.now()
        exception_record.process_result = data['process_result']
        exception_record.updated_at = datetime.now()

        # 保存更改
        db.session.commit()

        # 获取更新后的详细信息
        exception = exception_record.get_details()

        # 查找与该异常相关的充电故障记录并同步处理
        try:
            # 首先尝试通过直接关联的fault_id查找
            from app.charging.models import ChargingFault
            fault = None

            if exception_record.fault_id:
                fault = ChargingFault.query.get(exception_record.fault_id)
                if fault:
                    current_app.logger.info(f"找到直接关联的故障记录，ID: {fault.id}")

                    # 更新故障状态为已完成
                    fault.status = 2  # 已完成
                    fault.processor = data['processor']
                    fault.process_time = datetime.now()
                    fault.process_result = f"随充电异常(ID:{exception_id})一起处理: {data['process_result']}"
                    fault.updated_at = datetime.now()

                    # 提交更改
                    db.session.commit()
                    current_app.logger.info(f"同步处理了直接关联的充电故障记录，ID: {fault.id}")

            # 如果没有直接关联的故障，尝试通过车位ID查找
            if not fault and exception_record.space_id:
                space_id = exception_record.space_id

                # 查找与该车位相关的所有充电故障（不仅限于待处理状态）
                faults = ChargingFault.query.filter_by(
                    space_id=space_id
                ).all()

                current_app.logger.info(f"找到与车位 {space_id} 相关的故障记录: {len(faults)} 条")

                # 处理找到的充电故障
                for fault in faults:
                    current_app.logger.info(f"同步处理充电故障，ID: {fault.id}，当前状态: {fault.status}，关联异常ID: {exception_id}")

                    # 更新故障状态为已完成
                    fault.status = 2  # 已完成
                    fault.processor = data['processor']
                    fault.process_time = datetime.now()
                    fault.process_result = f"随充电异常(ID:{exception_id})一起处理: {data['process_result']}"
                    fault.updated_at = datetime.now()

                # 提交更改
                if faults:
                    db.session.commit()
                    current_app.logger.info(f"同步处理了 {len(faults)} 条相关充电故障记录")

            # 如果仍然没有找到故障，尝试通过充电记录查找（兼容旧数据）
            if not fault and exception_record.charging_record_id:
                # 获取充电记录关联的车位ID
                from app.charging.models import ChargingRecord
                record = ChargingRecord.query.get(exception_record.charging_record_id)

                if record and record.parking_space_id:
                    space_id = record.parking_space_id

                    # 查找与该车位相关的所有充电故障（不仅限于待处理状态）
                    faults = ChargingFault.query.filter_by(
                        space_id=space_id
                    ).all()

                    current_app.logger.info(f"通过充电记录找到与车位 {space_id} 相关的故障记录: {len(faults)} 条")

                    # 处理找到的充电故障
                    for fault in faults:
                        current_app.logger.info(f"同步处理充电故障，ID: {fault.id}，当前状态: {fault.status}，关联异常ID: {exception_id}")

                        # 更新故障状态为已完成
                        fault.status = 2  # 已完成
                        fault.processor = data['processor']
                        fault.process_time = datetime.now()
                        fault.process_result = f"随充电异常(ID:{exception_id})一起处理: {data['process_result']}"
                        fault.updated_at = datetime.now()

                    # 提交更改
                    if faults:
                        db.session.commit()
                        current_app.logger.info(f"同步处理了 {len(faults)} 条相关充电故障记录")

            # 如果仍然没有找到故障，尝试通过异常类型查找
            if not fault:
                # 获取异常类型
                exception_type = exception_record.exception_type
                current_app.logger.info(f"通过其他方式未找到故障记录，尝试通过异常类型 {exception_type} 查找")

                # 查找所有具有相同故障类型的故障记录
                faults = ChargingFault.query.filter_by(
                    fault_type=exception_type,
                    status=0  # 只查找待处理的故障
                ).all()

                current_app.logger.info(f"通过异常类型找到 {len(faults)} 条故障记录")

                # 处理找到的充电故障
                for fault in faults:
                    current_app.logger.info(f"同步处理充电故障，ID: {fault.id}，当前状态: {fault.status}，关联异常ID: {exception_id}")

                    # 更新故障状态为已完成
                    fault.status = 2  # 已完成
                    fault.processor = data['processor']
                    fault.process_time = datetime.now()
                    fault.process_result = f"随充电异常(ID:{exception_id})一起处理: {data['process_result']}"
                    fault.updated_at = datetime.now()

                # 提交更改
                if faults:
                    db.session.commit()
                    current_app.logger.info(f"通过异常类型同步处理了 {len(faults)} 条相关充电故障记录")
        except Exception as e:
            current_app.logger.error(f"同步处理充电故障失败: {str(e)}")
            # 不影响异常处理的返回结果

        return api_response(
            data=exception,
            message="处理充电异常成功"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"处理充电异常失败: {str(e)}")
        return api_response(message=f"处理充电异常失败: {str(e)}", status="error", code=500)

# 删除充电记录
@charging_bp.route('/charging-records/<int:record_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_charging_record(record_id):
    """删除充电记录（仅管理员）"""
    try:
        current_app.logger.info(f"删除充电记录，ID: {record_id}")

        # 从数据库中查询充电记录
        record = ChargingRecord.query.get(record_id)

        if not record:
            return api_response(message="充电记录不存在", status="error", code=404)

        # 检查是否有关联的充电异常记录
        exceptions = ChargingException.query.filter_by(charging_record_id=record_id).all()
        if exceptions:
            # 删除关联的充电异常记录
            for exception in exceptions:
                try:
                    db.session.delete(exception)
                    current_app.logger.info(f"删除关联的充电异常记录，ID: {exception.id}")
                except Exception as e:
                    current_app.logger.error(f"删除充电异常记录失败，ID: {exception.id}, 错误: {str(e)}")
                    db.session.rollback()
                    return api_response(message=f"删除关联的充电异常记录失败: {str(e)}", status="error", code=500)

        # 删除充电记录
        try:
            db.session.delete(record)
            db.session.commit()
            current_app.logger.info(f"删除充电记录成功，ID: {record_id}")
            return api_response(
                data={'id': record_id},
                message="删除充电记录成功"
            )
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除充电记录数据库操作失败: {str(e)}")
            return api_response(message="删除充电记录失败，数据库错误", status="error", code=500)
    except Exception as e:
        current_app.logger.error(f"删除充电记录失败: {str(e)}")
        return api_response(message=f"删除充电记录失败: {str(e)}", status="error", code=500)

# 获取充电记录详情
@charging_bp.route('/charging-records/<int:record_id>/detail', methods=['GET'])
@jwt_required()
def get_charging_record_detail_v3(record_id):
    """获取充电记录详情"""
    try:
        record = ChargingRecord.query.get(record_id)
        if not record:
            return api_response(message="充电记录不存在", status="error", code=404)

        # 获取详细信息，包括关联数据
        record_data = record.get_details(include_relations=True)

        return api_response(
            data=record_data,
            message="获取充电记录详情成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取充电记录详情失败: {str(e)}")
        return api_response(message=f"获取充电记录详情失败: {str(e)}", status="error", code=500)

# 标记充电异常
@charging_bp.route('/charging-records/<int:record_id>/mark-exception', methods=['PUT'])
@jwt_required()
def mark_charging_exception_v3(record_id):
    """标记充电异常"""
    try:
        data = request.get_json()

        # 验证必要参数
        if 'exception_type' not in data:
            return api_response(message="缺少必要参数: exception_type", status="error", code=400)

        # 获取充电记录
        record = ChargingRecord.query.get(record_id)
        if not record:
            return api_response(message="充电记录不存在", status="error", code=404)

        # 标记异常
        record.exception_type = data['exception_type']
        record.exception_remarks = data.get('exception_remarks', '')
        record.exception_time = datetime.now()
        record.exception_status = 0  # 未处理

        db.session.commit()

        return api_response(
            message="标记充电异常成功"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"标记充电异常失败: {str(e)}")
        return api_response(message=f"标记充电异常失败: {str(e)}", status="error", code=500)

# 导出充电记录
@charging_bp.route('/charging-records/export', methods=['GET'])
@jwt_required()
def export_charging_records_v3():
    """导出充电记录"""
    try:
        # 获取查询参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        # 构建查询
        query = ChargingRecord.query

        # 应用日期筛选
        if start_date:
            start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(ChargingRecord.start_time >= start_datetime)

        if end_date:
            end_datetime = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
            query = query.filter(ChargingRecord.start_time <= end_datetime)

        # 获取记录
        records = query.all()

        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)

        # 写入表头
        writer.writerow(['ID', '用户ID', '车辆ID', '车牌号', '停车场', '车位', '开始时间', '结束时间', '充电时长(分钟)', '充电功率(kW)', '费用(元)', '状态'])

        # 写入数据
        for record in records:
            vehicle = Bikes.query.get(record.vehicle_id)
            parking_lot = ParkingLot.query.get(record.parking_lot_id)
            parking_space = ParkingSpace.query.get(record.parking_space_id)

            # 计算充电时长
            start_time = record.start_time
            end_time = record.end_time or datetime.now()
            duration_minutes = int((end_time - start_time).total_seconds() / 60)

            # 获取状态文本
            status_map = {0: '进行中', 1: '已完成', 2: '异常'}
            status_text = status_map.get(record.status, '未知')

            writer.writerow([
                record.id,
                record.user_id,
                record.vehicle_id,
                vehicle.b_num if vehicle else '未知',
                parking_lot.name if parking_lot else '未知',
                parking_space.space_number if parking_space else '未知',
                record.start_time.strftime('%Y-%m-%d %H:%M:%S'),
                record.end_time.strftime('%Y-%m-%d %H:%M:%S') if record.end_time else '',
                duration_minutes,
                record.power,
                record.fee,
                status_text
            ])

        # 获取CSV内容
        csv_content = output.getvalue()
        output.close()

        # 创建响应
        response = Response(
            csv_content,
            mimetype='text/csv',
            headers={
                'Content-Disposition': f'attachment; filename=charging_records_{datetime.now().strftime("%Y%m%d%H%M%S")}.csv'
            }
        )

        return response
    except Exception as e:
        current_app.logger.error(f"导出充电记录失败: {str(e)}")
        return api_response(message=f"导出充电记录失败: {str(e)}", status="error", code=500)

# 获取充电车位列表
@charging_bp.route('/charging/parking-lots/<int:lot_id>/spaces', methods=['GET'])
@jwt_required()
def get_charging_spaces_api(lot_id):
    """获取指定停车场的充电车位列表"""
    try:
        # 记录请求信息，便于调试
        current_app.logger.info(f"获取停车场 {lot_id} 的充电车位列表")

        # 验证停车场是否存在
        parking_lot = ParkingLot.query.get(lot_id)
        if not parking_lot:
            current_app.logger.warning(f"停车场不存在，ID: {lot_id}")
            return api_response(message="停车场不存在", status="error", code=404)

        # 获取充电车位
        spaces = ParkingSpace.query.filter_by(
            parking_lot_id=lot_id,
            type=3  # 充电车位类型
        ).all()

        current_app.logger.info(f"找到 {len(spaces)} 个充电车位")

        # 如果没有充电车位，创建模拟数据
        if not spaces:
            current_app.logger.info(f"停车场 {lot_id} 没有充电车位，创建模拟数据")
            spaces_data = []
            for i in range(1, 11):  # 创建10个模拟充电车位
                space_status = random.choices([0, 1, 2], weights=[0.6, 0.3, 0.1])[0]  # 60%空闲，30%占用，10%维护
                power = 7.0  # 默认充电功率

                space_data = {
                    'id': i + 1000,
                    'parking_lot_id': lot_id,
                    'space_number': f"C-{lot_id}-{i}",  # 使用C前缀，表示充电车位
                    'type': 3,  # 充电车位
                    'type_text': '充电车位',
                    'status': space_status,
                    'status_text': ['空闲', '已占用', '维护中'][space_status],
                    'power': power,
                    'current_vehicle_id': None if space_status == 0 else random.randint(1, 100),
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                }

                # 如果车位被占用，添加车辆信息
                if space_status == 1 and space_data['current_vehicle_id']:
                    space_data['vehicle_info'] = {
                        'id': space_data['current_vehicle_id'],
                        'number': f"模拟车牌{space_data['current_vehicle_id']}",
                        'brand': f"品牌{space_data['current_vehicle_id'] % 5 + 1}",
                        'color': ['红色', '蓝色', '黑色', '白色', '银色'][space_data['current_vehicle_id'] % 5]
                    }

                spaces_data.append(space_data)
        else:
            # 使用真实数据
            current_app.logger.info("使用真实车位数据")
            spaces_data = []
            for space in spaces:
                try:
                    # 安全地获取车位详情
                    space_details = space.get_details()
                    spaces_data.append(space_details)
                except Exception as detail_error:
                    # 记录获取详情错误，但继续处理其他车位
                    current_app.logger.error(f"获取车位 {space.id} 详情失败: {str(detail_error)}")
                    # 添加一个简化版的车位信息
                    spaces_data.append({
                        'id': space.id,
                        'parking_lot_id': space.parking_lot_id,
                        'space_number': space.space_number,
                        'type': space.type,
                        'type_text': '充电车位',
                        'status': space.status,
                        'status_text': ['空闲', '已占用', '维护中'][space.status] if space.status in [0, 1, 2] else '未知',
                        'power': space.power
                    })

        return api_response(data=spaces_data)
    except Exception as e:
        # 记录详细错误信息
        import traceback
        error_traceback = traceback.format_exc()
        current_app.logger.error(f"获取充电车位列表失败: {str(e)}\n{error_traceback}")
        return api_response(message="获取充电车位列表失败，请联系管理员检查服务器日志", status="error", code=500)