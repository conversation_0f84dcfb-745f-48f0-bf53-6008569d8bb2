#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统性能测试脚本
测试内容：
1. 响应速度测试
2. 并发处理能力测试
3. 稳定性测试
"""

import os
import sys
import time
import json
import random
import requests
import threading
import statistics
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

# 测试配置
BASE_URL = "http://127.0.0.1:5000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"
USER_USERNAME = "user"
USER_PASSWORD = "user123"
SECURITY_USERNAME = "security"
SECURITY_PASSWORD = "security123"

# 测试结果存储
results = {
    "response_time": {},
    "concurrency": {},
    "stability": {}
}

def get_token(username, password):
    """获取用户令牌"""
    login_url = f"{BASE_URL}/api/login"
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            return response.json().get("access_token")
        else:
            print(f"登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"登录请求异常: {str(e)}")
        return None

def measure_response_time(url, token=None, params=None, method="GET", data=None):
    """测量API响应时间"""
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    start_time = time.time()
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, params=params)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        return {
            "status_code": response.status_code,
            "response_time": response_time,
            "success": 200 <= response.status_code < 300
        }
    except Exception as e:
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        return {
            "status_code": 0,
            "response_time": response_time,
            "success": False,
            "error": str(e)
        }

def test_page_load_time(token, page_name, url):
    """测试页面加载时间"""
    print(f"测试页面加载时间: {page_name}")
    
    # 执行5次测试取平均值
    times = []
    for i in range(5):
        result = measure_response_time(url, token)
        if result["success"]:
            times.append(result["response_time"])
            print(f"  第{i+1}次测试: {result['response_time']:.2f}ms")
        else:
            print(f"  第{i+1}次测试失败: {result.get('error', '未知错误')}")
    
    if times:
        avg_time = statistics.mean(times)
        results["response_time"][page_name] = {
            "avg_time": avg_time,
            "min_time": min(times),
            "max_time": max(times),
            "times": times
        }
        print(f"  平均加载时间: {avg_time:.2f}ms")
        return avg_time
    else:
        results["response_time"][page_name] = {
            "avg_time": None,
            "min_time": None,
            "max_time": None,
            "times": []
        }
        print("  所有测试均失败")
        return None

def test_api_response_time(token, api_name, url, method="GET", params=None, data=None):
    """测试API响应时间"""
    print(f"测试API响应时间: {api_name}")
    
    # 执行5次测试取平均值
    times = []
    for i in range(5):
        result = measure_response_time(url, token, params, method, data)
        if result["success"]:
            times.append(result["response_time"])
            print(f"  第{i+1}次测试: {result['response_time']:.2f}ms")
        else:
            print(f"  第{i+1}次测试失败: {result.get('error', '未知错误')}")
    
    if times:
        avg_time = statistics.mean(times)
        results["response_time"][api_name] = {
            "avg_time": avg_time,
            "min_time": min(times),
            "max_time": max(times),
            "times": times
        }
        print(f"  平均响应时间: {avg_time:.2f}ms")
        return avg_time
    else:
        results["response_time"][api_name] = {
            "avg_time": None,
            "min_time": None,
            "max_time": None,
            "times": []
        }
        print("  所有测试均失败")
        return None

def run_response_time_tests():
    """运行响应速度测试"""
    print("\n=== 响应速度测试 ===\n")
    
    # 获取管理员令牌
    admin_token = get_token(ADMIN_USERNAME, ADMIN_PASSWORD)
    if not admin_token:
        print("获取管理员令牌失败，无法继续测试")
        return
    
    # 获取普通用户令牌
    user_token = get_token(USER_USERNAME, USER_PASSWORD)
    if not user_token:
        print("获取普通用户令牌失败，部分测试可能无法进行")
    
    # 测试主要页面加载时间
    pages = [
        {"name": "仪表盘", "url": f"{BASE_URL}/api/dashboard/overview", "token": admin_token},
        {"name": "停车场列表", "url": f"{BASE_URL}/api/parkinglots", "token": admin_token},
        {"name": "停车记录", "url": f"{BASE_URL}/api/parking-records", "token": admin_token},
        {"name": "充电记录", "url": f"{BASE_URL}/api/charging/records", "token": admin_token},
        {"name": "违规记录", "url": f"{BASE_URL}/api/violations/records", "token": admin_token},
        {"name": "公告列表", "url": f"{BASE_URL}/api/announcements", "token": admin_token}
    ]
    
    for page in pages:
        test_page_load_time(page["token"], page["name"], page["url"])
    
    # 测试API响应时间
    apis = [
        {"name": "获取停车场列表(分页)", "url": f"{BASE_URL}/api/parkinglots", "method": "GET", "params": {"page": 1, "limit": 10}, "token": admin_token},
        {"name": "获取停车记录(分页)", "url": f"{BASE_URL}/api/parking-records", "method": "GET", "params": {"page": 1, "per_page": 10}, "token": admin_token},
        {"name": "获取充电记录(分页)", "url": f"{BASE_URL}/api/charging/records", "method": "GET", "params": {"page": 1, "per_page": 10}, "token": admin_token},
        {"name": "获取违规记录(分页)", "url": f"{BASE_URL}/api/violations/records", "method": "GET", "params": {"page": 1, "per_page": 10}, "token": admin_token},
        {"name": "获取公告列表(分页)", "url": f"{BASE_URL}/api/announcements", "method": "GET", "params": {"page": 1, "per_page": 10}, "token": admin_token},
        {"name": "获取停车统计", "url": f"{BASE_URL}/api/parking-records/stats", "method": "GET", "params": {"date_range": "week"}, "token": admin_token}
    ]
    
    for api in apis:
        test_api_response_time(api["token"], api["name"], api["url"], api["method"], api.get("params"), api.get("data"))

def concurrent_request(url, token, params=None, method="GET", data=None):
    """执行并发请求"""
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, params=params)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        return {
            "status_code": response.status_code,
            "success": 200 <= response.status_code < 300
        }
    except Exception as e:
        return {
            "status_code": 0,
            "success": False,
            "error": str(e)
        }

def test_concurrency(token, api_name, url, method="GET", params=None, data=None, num_requests=10, max_workers=10):
    """测试并发处理能力"""
    print(f"测试并发处理能力: {api_name} (并发数: {num_requests})")
    
    start_time = time.time()
    success_count = 0
    failure_count = 0
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for _ in range(num_requests):
            futures.append(executor.submit(concurrent_request, url, token, params, method, data))
        
        for future in futures:
            result = future.result()
            if result["success"]:
                success_count += 1
            else:
                failure_count += 1
    
    end_time = time.time()
    total_time = end_time - start_time
    
    results["concurrency"][api_name] = {
        "num_requests": num_requests,
        "success_count": success_count,
        "failure_count": failure_count,
        "total_time": total_time,
        "requests_per_second": num_requests / total_time if total_time > 0 else 0
    }
    
    print(f"  总请求数: {num_requests}")
    print(f"  成功请求数: {success_count}")
    print(f"  失败请求数: {failure_count}")
    print(f"  总耗时: {total_time:.2f}秒")
    print(f"  每秒请求数: {num_requests / total_time:.2f}" if total_time > 0 else "  每秒请求数: 0")
    
    return success_count, failure_count, total_time

def run_concurrency_tests():
    """运行并发处理能力测试"""
    print("\n=== 并发处理能力测试 ===\n")
    
    # 获取管理员令牌
    admin_token = get_token(ADMIN_USERNAME, ADMIN_PASSWORD)
    if not admin_token:
        print("获取管理员令牌失败，无法继续测试")
        return
    
    # 测试高频接口的并发处理能力
    apis = [
        {"name": "获取停车场列表", "url": f"{BASE_URL}/api/parkinglots", "method": "GET", "params": {"page": 1, "limit": 10}, "token": admin_token},
        {"name": "获取公告列表", "url": f"{BASE_URL}/api/announcements", "method": "GET", "params": {"page": 1, "per_page": 10}, "token": admin_token},
        {"name": "获取停车记录", "url": f"{BASE_URL}/api/parking-records", "method": "GET", "params": {"page": 1, "per_page": 10}, "token": admin_token}
    ]
    
    for api in apis:
        test_concurrency(api["token"], api["name"], api["url"], api["method"], api.get("params"), api.get("data"), num_requests=10, max_workers=10)

def export_results_to_excel_format():
    """将测试结果导出为Excel友好的格式"""
    print("\n=== 测试结果（Excel友好格式）===\n")
    
    # 响应速度测试结果
    print("响应速度测试结果")
    print("页面/API名称\t平均响应时间(ms)\t最小响应时间(ms)\t最大响应时间(ms)")
    for name, data in results["response_time"].items():
        avg_time = f"{data['avg_time']:.2f}" if data['avg_time'] is not None else "N/A"
        min_time = f"{data['min_time']:.2f}" if data['min_time'] is not None else "N/A"
        max_time = f"{data['max_time']:.2f}" if data['max_time'] is not None else "N/A"
        print(f"{name}\t{avg_time}\t{min_time}\t{max_time}")
    
    print("\n并发处理能力测试结果")
    print("API名称\t总请求数\t成功请求数\t失败请求数\t总耗时(秒)\t每秒请求数")
    for name, data in results["concurrency"].items():
        rps = f"{data['requests_per_second']:.2f}" if data['requests_per_second'] > 0 else "0"
        print(f"{name}\t{data['num_requests']}\t{data['success_count']}\t{data['failure_count']}\t{data['total_time']:.2f}\t{rps}")

if __name__ == "__main__":
    # 运行响应速度测试
    run_response_time_tests()
    
    # 运行并发处理能力测试
    run_concurrency_tests()
    
    # 导出结果为Excel友好格式
    export_results_to_excel_format()
