<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>违规记录管理</span>
      </div>
      
      <violation-list
        :fetch-list-method="fetchSecurityViolations"
        :show-create-button="true"
        :show-date-range="true"
        :show-recorder="false"
        :show-handler="true"
        :can-appeal="false"
        :can-handle="false"
        @create="handleCreate"
        @detail="handleDetail"
      />
    </el-card>
  </div>
</template>

<script>
import { getSecurityViolations } from '@/api/violations'
import ViolationList from '../components/ViolationList'

export default {
  name: 'SecurityViolationRecords',
  components: {
    ViolationList
  },
  data() {
    return {}
  },
  methods: {
    fetchSecurityViolations(params) {
      return getSecurityViolations(params)
    },
    handleCreate() {
      this.$router.push({ name: 'SecurityViolationCreate' })
    },
    handleDetail(row) {
      this.$router.push({ name: 'SecurityViolationDetail', params: { id: row.id }})
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
</style>
