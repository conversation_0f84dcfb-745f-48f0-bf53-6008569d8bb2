2025/03/26 18:55:00 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/26 18:55:00 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/26 18:55:00 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/26 18:55:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:55:27] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/26 18:55:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:55:27] "POST /api/login HTTP/1.1" 200 -
2025/03/26 18:56:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:56:05] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/26 18:56:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:56:05] "GET /api/bikes HTTP/1.1" 200 -
2025/03/26 18:56:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:56:33] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/03/26 18:56:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:56:33] "[31m[1mGET /api/users/me HTTP/1.1[0m" 422 -
2025/03/26 18:56:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:56:33] "OPTIONS /api/my-bikes HTTP/1.1" 200 -
2025/03/26 18:56:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:56:33] "[31m[1mGET /api/my-bikes HTTP/1.1[0m" 422 -
2025/03/26 18:56:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:56:44] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/26 18:56:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:56:44] "POST /api/login HTTP/1.1" 200 -
2025/03/26 18:57:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:57:07] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/26 18:57:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:57:07] "POST /api/login HTTP/1.1" 200 -
2025/03/26 18:57:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:57:14] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/26 18:57:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:57:14] "GET /api/bikes HTTP/1.1" 200 -
2025/03/26 18:57:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:57:26] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/03/26 18:57:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:57:26] "[31m[1mGET /api/users/me HTTP/1.1[0m" 422 -
2025/03/26 18:57:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:57:26] "OPTIONS /api/my-bikes HTTP/1.1" 200 -
2025/03/26 18:57:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [26/Mar/2025 18:57:26] "[31m[1mGET /api/my-bikes HTTP/1.1[0m" 422 -
