<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>我的违规记录</span>
      </div>

      <violation-list
        :fetch-list-method="fetchUserViolations"
        :show-create-button="false"
        :show-date-range="false"
        :show-recorder="false"
        :show-handler="false"
        :show-statistics="false"
        :can-appeal="true"
        :can-handle="false"
        @detail="handleDetail"
        @appeal="handleAppeal"
      />
    </el-card>
  </div>
</template>

<script>
import { getUserViolations } from '@/api/violations'
import ViolationList from '../components/ViolationList'

export default {
  name: 'UserViolationRecords',
  components: {
    ViolationList
  },
  data() {
    return {}
  },
  methods: {
    fetchUserViolations(params) {
      return getUserViolations(params)
    },
    handleDetail(row) {
      this.$router.push({ name: 'MyViolationDetail', params: { id: row.id }})
    },
    handleAppeal(row) {
      this.$router.push({ name: 'MyViolationAppeal', params: { id: row.id }})
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
</style>
