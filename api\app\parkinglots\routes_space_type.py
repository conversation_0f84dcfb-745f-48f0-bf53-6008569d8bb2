from flask import jsonify, request, current_app
from flask_jwt_extended import jwt_required
from app.utils.validation import admin_required
from app.parkinglots import parkinglots_bp
from app.parkinglots.models import ParkingSpace, ParkingLot
from app import db
from app.parkinglots.utils import TYPE_PREFIX_MAP

# 更新车位类型（仅管理员）
@parkinglots_bp.route('/spaces/<int:space_id>/type', methods=['PUT'])
@jwt_required()
@admin_required
def update_space_type(space_id):
    """更新车位类型（仅管理员）"""
    try:
        # 获取车位
        space = ParkingSpace.query.get(space_id)
        if not space:
            return jsonify({'message': '未找到指定车位'}), 404

        # 获取请求数据
        data = request.get_json()
        if 'type' not in data:
            return jsonify({'message': '缺少类型参数'}), 400

        new_type = data['type']

        # 验证类型值
        if new_type not in [1, 2, 3]:  # 1普通车位，2残疾人车位，3充电车位
            return jsonify({'message': '无效的类型值，必须为 1（普通车位）、2（残疾人车位）或 3（充电车位）'}), 400

        # 检查当前类型和车位号
        old_type = space.type
        old_space_number = space.space_number

        # 更新车位号格式
        # 解析当前车位号
        parts = old_space_number.split('-')
        if len(parts) >= 2:
            # 获取新的类型前缀
            new_prefix = TYPE_PREFIX_MAP.get(new_type, 'N')

            # 如果车位号格式是 {前缀}-{停车场ID}-{序号}，则更新前缀
            if len(parts) >= 3 and parts[1].isdigit():
                # 保留停车场ID和序号部分
                space.space_number = f"{new_prefix}-{parts[1]}-{parts[2]}"
            else:
                # 如果格式不符合，则保留原车位号
                current_app.logger.warning(f"车位号格式不符合标准，保留原车位号: {old_space_number}")
        else:
            # 如果车位号格式不符合要求，记录警告但不修改
            current_app.logger.warning(f"车位号格式不符合标准，无法更新前缀: {old_space_number}")

        # 如果是充电车位，需要设置充电功率和充电类型
        if new_type == 3:  # 充电车位
            # 如果请求中包含充电功率和充电类型，则使用请求中的值
            if 'power' in data:
                space.power = data['power']
            else:
                # 默认设置为3.3kW的标准充
                space.power = 3.3

            if 'charging_type' in data:
                space.charging_type = data['charging_type']
            else:
                # 默认设置为标准充
                space.charging_type = 2
        else:
            # 如果不是充电车位，清除充电功率和充电类型
            space.power = None
            space.charging_type = None

        # 更新车位类型
        space.type = new_type

        # 如果有备注，更新备注
        if 'remarks' in data:
            space.remarks = data['remarks']

        # 保存更改
        db.session.commit()

        # 返回成功响应
        type_map = {1: '普通车位', 2: '残疾人车位', 3: '充电车位'}
        return jsonify({
            'message': f'车位类型已更新为{type_map.get(new_type, "未知")}',
            'space': space.get_details(),
            'old_space_number': old_space_number,
            'new_space_number': space.space_number
        }), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'更新车位类型出错: {str(e)}')
        return jsonify({'message': '更新车位类型失败', 'error': str(e)}), 500

# 批量更新车位类型（仅管理员）
@parkinglots_bp.route('/spaces/batch-update-type', methods=['POST'])
@jwt_required()
@admin_required
def batch_update_space_type():
    """批量更新车位类型（仅管理员）"""
    try:
        # 获取请求数据
        data = request.get_json()
        if 'space_ids' not in data or 'type' not in data:
            return jsonify({'message': '缺少必要参数'}), 400

        space_ids = data['space_ids']
        new_type = data['type']

        # 验证类型值
        if new_type not in [1, 2, 3]:  # 1普通车位，2残疾人车位，3充电车位
            return jsonify({'message': '无效的类型值，必须为 1（普通车位）、2（残疾人车位）或 3（充电车位）'}), 400

        # 检查车位是否存在
        spaces = ParkingSpace.query.filter(ParkingSpace.id.in_(space_ids)).all()
        if len(spaces) != len(space_ids):
            return jsonify({'message': '部分车位不存在'}), 404

        # 更新车位类型
        updated_spaces = []

        for space in spaces:
            old_type = space.type
            old_space_number = space.space_number

            # 更新车位类型
            space.type = new_type

            # 更新车位号格式
            # 解析当前车位号
            parts = old_space_number.split('-')
            if len(parts) >= 2:
                # 获取新的类型前缀
                new_prefix = TYPE_PREFIX_MAP.get(new_type, 'N')

                # 如果车位号格式是 {前缀}-{停车场ID}-{序号}，则更新前缀
                if len(parts) >= 3 and parts[1].isdigit():
                    # 保留停车场ID和序号部分
                    space.space_number = f"{new_prefix}-{parts[1]}-{parts[2]}"
                else:
                    # 如果格式不符合，则保留原车位号
                    current_app.logger.warning(f"车位号格式不符合标准，保留原车位号: {old_space_number}")
            else:
                # 如果车位号格式不符合要求，记录警告但不修改
                current_app.logger.warning(f"车位号格式不符合标准，无法更新前缀: {old_space_number}")

            # 如果是充电车位，需要设置充电功率和充电类型
            if new_type == 3:  # 充电车位
                # 如果请求中包含充电功率和充电类型，则使用请求中的值
                if 'power' in data:
                    space.power = data['power']
                else:
                    # 默认设置为3.3kW的标准充
                    space.power = 3.3

                if 'charging_type' in data:
                    space.charging_type = data['charging_type']
                else:
                    # 默认设置为标准充
                    space.charging_type = 2
            else:
                # 如果不是充电车位，清除充电功率和充电类型
                space.power = None
                space.charging_type = None

            # 如果有备注，更新备注
            if 'remarks' in data:
                space.remarks = data['remarks']

            updated_spaces.append({
                'id': space.id,
                'old_space_number': old_space_number,
                'new_space_number': space.space_number,
                'old_type': old_type,
                'new_type': new_type
            })

        # 提交更改
        db.session.commit()

        # 返回成功响应
        type_map = {1: '普通车位', 2: '残疾人车位', 3: '充电车位'}
        return jsonify({
            'message': f'已成功更新 {len(spaces)} 个车位类型为{type_map.get(new_type, "未知")}',
            'updated_spaces': updated_spaces
        }), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'批量更新车位类型出错: {str(e)}')
        return jsonify({'message': '批量更新车位类型失败', 'error': str(e)}), 500
