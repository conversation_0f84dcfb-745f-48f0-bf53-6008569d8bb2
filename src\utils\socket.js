import io from 'socket.io-client'
import { getToken } from '@/utils/auth'
import store from '@/store'

class SocketService {
  constructor() {
    this.socket = null
    this.connected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.listeners = {}
    this.isLoggingOut = false // 添加登出状态标志
    this.connectionLock = false // 添加连接锁定标志
  }

  // 初始化连接
  connect() {
    // 检查是否正在登出或连接锁定
    if (this.isLoggingOut) {
      console.warn('正在登出过程中，禁止建立WebSocket连接')
      return
    }

    if (this.connectionLock) {
      console.warn('WebSocket连接已锁定，禁止建立新连接')
      return
    }

    // 先检查是否已连接
    if (this.socket && this.connected) {
      console.log('WebSocket已连接，无需重新连接')
      return
    }

    // 如果存在旧的socket实例但未连接，先清理
    if (this.socket && !this.connected) {
      console.log('存在未连接的旧Socket实例，先清理')
      this.disconnect()
    }

    // 检查token是否存在
    const token = getToken()
    if (!token) {
      console.error('未登录，无法建立WebSocket连接')
      return
    }

    // 检查token是否有效格式
    if (token.split('.').length !== 3) {
      console.error('Token格式无效，无法建立WebSocket连接')
      return
    }

    // 设置连接锁定，防止重复连接
    this.connectionLock = true

    // 创建Socket连接
    const socketUrl = process.env.VUE_APP_BASE_API
    console.log(`尝试连接WebSocket: ${socketUrl}`)

    try {
      this.socket = io(socketUrl, {
        query: {
          token: token
        },
        path: '/socket.io',
        transports: ['polling', 'websocket'],  // 先尝试polling，如果失败则回退到websocket
        reconnection: true,  // 启用自动重连
        reconnectionAttempts: 5,  // 最多重连5次
        reconnectionDelay: 1000,  // 重连延迟1秒
        timeout: 20000,  // 增加超时时间
        autoConnect: true
      })
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
      this.socket = null
      this.connectionLock = false // 解除连接锁定
      return
    }

    // 添加连接超时处理
    const connectionTimeout = setTimeout(() => {
      if (!this.connected && this.socket) {
        console.error('WebSocket连接超时')
        this.disconnect()
        this.connectionLock = false // 解除连接锁定
      }
    }, 10000) // 10秒连接超时

    // 连接事件
    this.socket.on('connect', () => {
      console.log('WebSocket连接成功')
      this.connected = true
      this.reconnectAttempts = 0
      this.connectionLock = false // 解除连接锁定

      // 清除连接超时计时器
      clearTimeout(connectionTimeout)

      // 如果正在登出，不要加入任何房间
      if (this.isLoggingOut) {
        console.warn('正在登出过程中，不加入任何房间')
        return
      }

      // 加入用户特定房间
      const userId = store.getters.userId
      if (userId) {
        this.joinRoom(`user_${userId}`)
      }

      // 如果是管理员，加入管理员房间
      if (store.getters.isAdmin) {
        this.joinRoom('admin')
      }
    })

    // 连接响应事件
    this.socket.on('connection_response', (data) => {
      console.log('WebSocket连接响应:', data)
      if (data.authenticated) {
        console.log('WebSocket认证成功')
      } else if (data.error) {
        console.error('WebSocket认证失败:', data.error)
        // 如果是认证失败，可能是token已失效，主动断开连接
        if (data.error.includes('token') || data.error.includes('Token')) {
          console.warn('WebSocket认证失败，可能是token已失效，主动断开连接')

          // 检查是否需要清除token（如果是因为token过期）
          const token = getToken()
          if (token && !this.isLoggingOut) {
            console.warn('检测到可能的token失效，通知store清除token')
            // 通知store清除token
            store.dispatch('user/resetToken').catch(err => {
              console.error('清除token失败:', err)
            })
          }

          // 断开连接并解除锁定
          this.disconnect()
        } else {
          // 其他错误也需要解除连接锁定
          this.connectionLock = false
        }
      } else {
        // 没有错误但也没有认证成功，解除锁定
        this.connectionLock = false
      }
    })

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.error('WebSocket连接错误:', error)
      this.connected = false

      // 清理连接
      this.disconnect() // disconnect方法会解除锁定
    })

    // 断开连接
    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket断开连接:', reason)
      this.connected = false

      // 如果是因为认证问题断开，不要尝试重连
      if (reason === 'io server disconnect' || reason.includes('auth')) {
        console.warn('服务器主动断开连接，可能是认证问题，不尝试重连')
        this.disconnect() // 确保完全清理
      } else {
        // 确保解除连接锁定
        this.connectionLock = false
      }
    })

    // 错误处理
    this.socket.on('error', (data) => {
      console.error('WebSocket错误:', data)

      // 如果是认证错误，主动断开
      if (data && (data.message || data).toString().includes('auth')) {
        console.warn('WebSocket认证错误，主动断开连接')
        this.disconnect() // disconnect方法会解除锁定
      } else {
        // 确保解除连接锁定
        this.connectionLock = false
      }
    })
  }

  // 断开连接
  disconnect() {
    if (this.socket) {
      try {
        // 移除所有监听器
        this.removeAllListeners()

        // 断开连接前先检查连接状态
        if (this.socket.connected) {
          console.log('WebSocket处于连接状态，执行断开操作')
          this.socket.disconnect()
        } else {
          console.log('WebSocket已处于断开状态，无需再次断开')
        }

        // 清理资源
        this.socket = null
        this.connected = false
        this.reconnectAttempts = 0

        console.log('WebSocket已断开连接并清理资源')
      } catch (error) {
        console.error('断开WebSocket连接时发生错误:', error)
        // 确保即使出错也清理资源
        this.socket = null
        this.connected = false
      }
    }

    // 无论如何都解除连接锁定
    this.connectionLock = false
  }

  // 移除所有事件监听器
  removeAllListeners() {
    if (!this.socket) return

    try {
      // 移除所有已注册的监听器
      for (const event in this.listeners) {
        if (this.listeners[event] && this.listeners[event].length > 0) {
          this.listeners[event].forEach(callback => {
            this.socket.off(event, callback)
          })
          console.log(`移除事件 ${event} 的所有监听器`)
        }
      }

      // 移除内置事件监听器
      const builtInEvents = ['connect', 'connection_response', 'connect_error',
                            'disconnect', 'error', 'reconnect_attempt',
                            'reconnect_failed', 'reconnect']

      builtInEvents.forEach(event => {
        this.socket.off(event)
      })

      // 清空监听器记录
      this.listeners = {}
    } catch (error) {
      console.error('移除WebSocket监听器时发生错误:', error)
    }
  }

  // 加入房间
  joinRoom(room) {
    if (!this.socket || !this.connected) {
      console.error('WebSocket未连接，无法加入房间')
      return
    }

    this.socket.emit('join', { room })
    console.log(`加入房间: ${room}`)
  }

  // 离开房间
  leaveRoom(room) {
    if (!this.socket || !this.connected) {
      console.error('WebSocket未连接，无法离开房间')
      return
    }

    this.socket.emit('leave', { room })
    console.log(`离开房间: ${room}`)
  }

  // 订阅停车场更新
  subscribeParkingLot(parkingLotId) {
    if (!this.socket || !this.connected) {
      console.error('WebSocket未连接，无法订阅停车场')
      return
    }

    this.socket.emit('subscribe_parking_lot', { parking_lot_id: parkingLotId })
    console.log(`订阅停车场: ${parkingLotId}`)
  }

  // 取消订阅停车场更新
  unsubscribeParkingLot(parkingLotId) {
    if (!this.socket || !this.connected) {
      console.error('WebSocket未连接，无法取消订阅停车场')
      return
    }

    this.socket.emit('unsubscribe_parking_lot', { parking_lot_id: parkingLotId })
    console.log(`取消订阅停车场: ${parkingLotId}`)
  }

  // 添加事件监听器
  on(event, callback) {
    if (!this.socket) {
      console.error('WebSocket未初始化，无法添加事件监听器')
      return
    }

    // 保存监听器引用，以便后续可以移除
    if (!this.listeners[event]) {
      this.listeners[event] = []
    }
    this.listeners[event].push(callback)

    this.socket.on(event, callback)
    console.log(`添加事件监听器: ${event}`)
  }

  // 移除事件监听器
  off(event, callback) {
    if (!this.socket) {
      console.error('WebSocket未初始化，无法移除事件监听器')
      return
    }

    if (callback && this.listeners[event]) {
      // 移除特定回调
      const index = this.listeners[event].indexOf(callback)
      if (index !== -1) {
        this.listeners[event].splice(index, 1)
        this.socket.off(event, callback)
        console.log(`移除事件监听器: ${event}`)
      }
    } else {
      // 移除所有该事件的监听器
      if (this.listeners[event]) {
        this.listeners[event].forEach(cb => {
          this.socket.off(event, cb)
        })
        this.listeners[event] = []
        console.log(`移除所有${event}事件监听器`)
      }
    }
  }

  // 设置登出状态，防止在登出过程中建立新连接
  setLoggingOut(isLoggingOut) {
    console.log(`设置WebSocket登出状态: ${isLoggingOut}`)
    this.isLoggingOut = isLoggingOut

    // 如果正在登出，锁定连接并确保断开现有连接
    if (isLoggingOut) {
      console.log('登出过程开始，锁定WebSocket连接并断开现有连接')
      this.connectionLock = true

      // 如果当前有连接，立即断开
      if (this.socket && (this.connected || this.socket.connected)) {
        console.log('登出过程中发现活跃连接，主动断开')
        this.disconnect()
      }
    } else {
      console.log('登出过程结束，解除WebSocket连接锁定')
      // 登出完成后，解除连接锁定
      this.connectionLock = false
    }
  }
}

// 创建单例
const socketService = new SocketService()

export default socketService
