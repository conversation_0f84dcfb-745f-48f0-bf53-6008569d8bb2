// cover some element-ui styles
@import './variables.scss';

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

// 优化按钮样式
.el-button {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  border-radius: 4px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }

  // 主要按钮
  &--primary {
    background-color: $primaryColor;
    border-color: $primaryColor;

    &:hover, &:focus {
      background-color: mix(white, $primaryColor, 20%);
      border-color: mix(white, $primaryColor, 20%);
    }

    &:active {
      background-color: mix(black, $primaryColor, 10%);
      border-color: mix(black, $primaryColor, 10%);
    }
  }

  // 成功按钮
  &--success {
    background-color: $successColor;
    border-color: $successColor;

    &:hover, &:focus {
      background-color: mix(white, $successColor, 20%);
      border-color: mix(white, $successColor, 20%);
    }

    &:active {
      background-color: mix(black, $successColor, 10%);
      border-color: mix(black, $successColor, 10%);
    }
  }
}

// 操作按钮组样式
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;

  .el-dropdown {
    margin: 0;
  }

  .el-button--mini {
    padding: 7px 12px;
  }
}

// 表格中的操作列样式
.el-table .operation-column {
  .cell {
    display: flex;
    justify-content: center;
    gap: 8px;
  }
}

// 表格样式
.el-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: $boxShadow;

  th {
    background-color: mix(white, $primaryColor, 90%);
    color: $primaryTextColor;
    font-weight: 600;
  }

  td {
    color: $regularTextColor;
  }

  .el-table__row {
    transition: all 0.3s;

    &:hover {
      background-color: mix(white, $primaryColor, 95%);
    }
  }
}

// 卡片样式
.el-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  margin-bottom: 20px;
  border: none;
  box-shadow: $boxShadow;

  &:hover {
    box-shadow: $hoverShadow;
    transform: translateY(-5px);
  }

  .el-card__header {
    padding: 15px 20px;
    border-bottom: 1px solid $lightBorderColor;
    background-color: mix(white, $primaryColor, 95%);
  }

  .el-card__body {
    padding: 20px;
  }
}

// 表单样式
.el-form {
  .el-form-item__label {
    color: $primaryTextColor;
    font-weight: 500;
  }

  .el-input__inner {
    border-radius: 4px;
    border: 1px solid $borderColor;
    transition: all 0.3s;

    &:hover {
      border-color: mix(white, $primaryColor, 50%);
    }

    &:focus {
      border-color: $primaryColor;
    }
  }

  .el-select .el-input.is-focus .el-input__inner {
    border-color: $primaryColor;
  }
}

// 对话框样式
.el-dialog {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);

  .el-dialog__header {
    padding: 15px 20px;
    background-color: mix(white, $primaryColor, 95%);
    border-bottom: 1px solid $lightBorderColor;
  }

  .el-dialog__title {
    color: $primaryTextColor;
    font-weight: 600;
    font-size: 18px;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid $lightBorderColor;
    background-color: $pageBgColor;
  }
}
