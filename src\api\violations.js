import request from '@/utils/request'

// 获取当前用户的违规记录
export function getUserViolations(params) {
  return request({
    url: '/api/violations/user/records',
    method: 'get',
    params
  })
}

// 获取公开的违规记录
export function getPublicViolations(params) {
  return request({
    url: '/api/violations/public/records',
    method: 'get',
    params
  })
}

// 获取违规记录详情
export function getViolationDetail(id) {
  return request({
    url: `/api/violations/records/${id}`,
    method: 'get'
  })
}

// 提交申诉
export function createAppeal(data) {
  return request({
    url: '/api/violations/appeals',
    method: 'post',
    data
  })
}

// 获取申诉详情
export function getAppealDetail(id) {
  return request({
    url: `/api/violations/appeals/${id}`,
    method: 'get'
  })
}

// 保安：获取自己创建的违规记录
export function getSecurityViolations(params) {
  return request({
    url: '/api/violations/security/records',
    method: 'get',
    params
  })
}

// 保安：创建违规记录
export function createViolation(data) {
  // 添加 user 和 recorder 字段
  const enhancedData = {
    ...data,
    user: data.user_id,
    recorder: data.recorder_id || null // 使用表单中的recorder_id或null
  }

  console.log('发送违规记录数据:', enhancedData)

  return request({
    url: '/api/violations/records',
    method: 'post',
    data: enhancedData
  })
}

// 管理员：获取所有违规记录
export function getAllViolations(params) {
  return request({
    url: '/api/violations/admin/records',
    method: 'get',
    params
  })
}

// 管理员：处理违规记录
export function handleViolation(id, data) {
  return request({
    url: `/api/violations/admin/records/${id}`,
    method: 'put',
    data
  })
}

// 管理员：删除违规记录
export function deleteViolation(id) {
  return request({
    url: `/api/violations/admin/records/${id}`,
    method: 'delete'
  })
}

// 管理员：获取所有申诉
export function getAllAppeals(params) {
  return request({
    url: '/api/violations/admin/appeals',
    method: 'get',
    params
  })
}

// 管理员：处理申诉
export function handleAppeal(id, data) {
  return request({
    url: `/api/violations/admin/appeals/${id}`,
    method: 'put',
    data
  })
}

// 管理员：删除申诉记录
export function deleteAppeal(id) {
  return request({
    url: `/api/violations/admin/appeals/${id}`,
    method: 'delete'
  })
}

// 获取违规统计数据
export function getViolationStatistics(params) {
  return request({
    url: '/api/violations/admin/statistics',
    method: 'get',
    params
  })
}

// 获取违规类型列表
export function getViolationTypes() {
  return request({
    url: '/api/violations/types',
    method: 'get'
  })
}

// 获取违规类型选项
export function getViolationTypeOptions() {
  return request({
    url: '/api/violations/type-options',
    method: 'get'
  })
}

// 管理员：创建违规类型
export function createViolationType(data) {
  return request({
    url: '/api/violations/admin/types',
    method: 'post',
    data
  })
}

// 管理员：更新违规类型
export function updateViolationType(id, data) {
  return request({
    url: `/api/violations/admin/types/${id}`,
    method: 'put',
    data
  })
}

// 管理员：删除违规类型
export function deleteViolationType(id) {
  return request({
    url: `/api/violations/admin/types/${id}`,
    method: 'delete'
  })
}
