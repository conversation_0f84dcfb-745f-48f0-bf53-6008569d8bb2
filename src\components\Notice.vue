<template>
  <div v-if="show" class="notice-wrapper">
    <div class="notice-container">
      <div class="notice-title">
        <i class="el-icon-info" />
        系统提示
        <span class="close-btn" @click="close">
          <i class="el-icon-close" />
        </span>
      </div>
      <div class="notice-content">
        <slot>
          <p>{{ message }}</p>
        </slot>
      </div>
      <div v-if="showFooter" class="notice-footer">
        <el-button size="small" @click="close">知道了</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Notice',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      default: ''
    },
    showFooter: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    close() {
      this.$emit('update:show', false)
    }
  }
}
</script>

<style scoped>
.notice-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.notice-container {
  background-color: #fff;
  border-radius: 4px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.notice-title {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  font-size: 16px;
  font-weight: bold;
  position: relative;
}

.close-btn {
  position: absolute;
  right: 15px;
  top: 15px;
  cursor: pointer;
}

.notice-content {
  padding: 20px;
  line-height: 1.5;
}

.notice-footer {
  padding: 10px 20px;
  text-align: right;
  border-top: 1px solid #eee;
}
</style>
