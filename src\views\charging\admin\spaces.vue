<template>
  <div class="app-container charging-spaces-container">
    <permission-wrapper :allowed-roles="['admin']" message="您没有权限访问充电车位管理数据，此功能仅对管理员开放">
      <el-card class="filter-container">
        <div class="filter-item">
          <el-form :inline="true" :model="listQuery" class="demo-form-inline">
            <el-form-item label="停车场">
              <el-select v-model="listQuery.parking_lot_id" placeholder="选择停车场" clearable @change="handleFilter">
                <el-option
                  v-for="item in parkingLots"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="listQuery.status" placeholder="选择状态" clearable @change="handleFilter">
                <el-option label="空闲" :value="0" />
                <el-option label="占用" :value="1" />
                <el-option label="禁用" :value="4" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleFilter">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

    <div class="parking-lot-tabs">
      <el-tabs v-model="activeParkingLot" @tab-click="handleParkingLotTabClick">
        <el-tab-pane label="全部车位" name="all"></el-tab-pane>
        <el-tab-pane
          v-for="lot in parkingLots"
          :key="lot.id"
          :label="lot.name"
          :name="String(lot.id)"
        ></el-tab-pane>
      </el-tabs>
    </div>

    <div class="list-header">
      <div class="header-title">
        <i class="el-icon-s-grid"></i>
        <span>充电车位列表</span>
      </div>
      <div class="header-actions">
        <el-button type="text" @click="fetchData">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
    </div>

    <div v-loading="listLoading" class="charging-spaces-list">
      <div v-if="activeParkingLot === 'all'" class="parking-lots-summary">
        <el-row :gutter="20">
          <el-col v-for="lot in filteredParkingLots" :key="lot.id" :span="6">
            <el-card class="lot-summary-card" shadow="hover">
              <div class="lot-name">{{ lot.name }}</div>
              <div class="lot-stats">
                <div class="stat-item">
                  <span class="label">总车位:</span>
                  <span class="value">{{ getLotSpacesCount(lot.id) }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">空闲:</span>
                  <span class="value">{{ getLotAvailableSpacesCount(lot.id) }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">占用:</span>
                  <span class="value">{{ getLotOccupiedSpacesCount(lot.id) }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <el-table
        :data="filteredList"
        border
        style="width: 100%"
        :row-class-name="getRowClassName"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
          align="center"
        />
        <el-table-column
          prop="space_number"
          label="车位编号"
          width="120"
          align="center"
        />
        <el-table-column
          label="所属停车场"
          width="150"
          align="center"
        >
          <template slot-scope="scope">
            {{ getParkingLotName(scope.row.parking_lot_id) }}
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="mini">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="power"
          label="充电功率(kW)"
          width="120"
          align="center"
        />

        <el-table-column
          label="最近维护"
          width="180"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.last_maintenance_time">{{ formatDateTime(scope.row.last_maintenance_time) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="remarks"
          label="备注"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="操作"
          width="180"
          align="center"
        >
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="filteredList.length === 0" class="empty-data">
        <el-empty description="暂无充电车位数据"></el-empty>
      </div>
    </div>

    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="listQuery.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="listQuery.per_page"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>

    <!-- 编辑充电车位对话框 -->
    <el-dialog title="编辑充电车位" :visible.sync="dialogFormVisible" width="600px">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="110px">
        <el-form-item label="停车场" prop="parking_lot_id">
          <el-select v-model="temp.parking_lot_id" class="filter-item" placeholder="请选择停车场" @change="handleParkingLotChange">
            <el-option
              v-for="item in parkingLots"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="车位编号" prop="space_number">
          <el-input v-model="temp.space_number" placeholder="请输入车位编号" />
        </el-form-item>
        <el-form-item label="充电功率(kW)" prop="power">
          <el-input-number v-model="temp.power" :min="1" :max="100" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="temp.status" class="filter-item" placeholder="请选择状态">
            <el-option label="空闲" :value="0" />
            <el-option label="占用" :value="1" disabled />
            <el-option label="禁用" :value="4" />
          </el-select>
          <span v-if="temp.status === 1" class="status-tip">
            <i class="el-icon-info"></i> 占用状态由系统自动设置，不可手动选择
          </span>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="temp.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="updateData()">确认</el-button>
      </div>
    </el-dialog>
    </permission-wrapper>
  </div>
</template>

<script>
import { getParkingLots } from '@/api/parkinglot'
import { getVehicleDetail } from '@/api/vehicle'
import { getChargingSpaces, updateChargingSpace, deleteChargingSpace } from '@/api/charging'
import PermissionWrapper from '@/components/PermissionWrapper'

export default {
  name: 'ChargingSpaces',
  components: {
    PermissionWrapper
  },
  data() {
    return {
      list: [],
      filteredList: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        per_page: 20,  // 增加默认每页显示数量
        parking_lot_id: undefined,
        status: undefined
      },
      parkingLots: [],
      activeParkingLot: 'all',  // 当前选中的停车场标签
      filteredParkingLots: [],  // 过滤后的停车场列表
      dialogFormVisible: false,
      dialogStatus: 'update',
      temp: {
        id: undefined,
        parking_lot_id: '',
        space_number: '',
        power: 7,
        status: 0,
        remarks: ''
      },
      rules: {
        parking_lot_id: [{ required: true, message: '请选择停车场', trigger: 'change' }],
        space_number: [{ required: true, message: '请输入车位编号', trigger: 'blur' }],
        power: [{ required: true, message: '请输入充电功率', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      }
    }
  },
  created() {
    this.fetchParkingLots()
    this.fetchData()
  },
  computed: {
    // 根据当前选中的停车场过滤车位列表
    filteredSpacesList() {
      if (this.activeParkingLot === 'all') {
        return this.list
      } else {
        return this.list.filter(item => item.parking_lot_id === parseInt(this.activeParkingLot))
      }
    }
  },
  methods: {
    // 获取停车场列表
    async fetchParkingLots() {
      try {
        const response = await getParkingLots()
        this.parkingLots = response.data.items || []
        this.updateFilteredParkingLots()
      } catch (error) {
        console.error('获取停车场列表失败:', error)
        this.$message.error('获取停车场列表失败')
      }
    },
    // 获取充电车位列表
    async fetchData() {
      this.listLoading = true
      try {
        const params = { ...this.listQuery }
        // 如果选择了特定停车场，添加到查询参数
        if (this.activeParkingLot !== 'all') {
          params.parking_lot_id = parseInt(this.activeParkingLot)
        }

        // 确保请求充电车位类型
        params.type = 3

        // 确保分页参数合理
        if (!params.per_page || params.per_page < 1) {
          params.per_page = 20 // 默认每页20条
        }

        if (!params.page || params.page < 1) {
          params.page = 1 // 默认第1页
        }

        console.log('发送充电车位列表请求，参数:', params)

        try {
          const response = await getChargingSpaces(params)
          console.log('充电车位列表响应:', response)

          // 检查响应格式
          if (response && response.data) {
            if (Array.isArray(response.data.items)) {
              this.list = response.data.items
            } else if (Array.isArray(response.data)) {
              // 兼容直接返回数组的情况
              this.list = response.data
            } else {
              this.list = []
              console.warn('响应数据格式不符合预期:', response.data)
            }

            // 获取总数
            this.total = response.data.total || this.list.length || 0

            console.log(`成功获取 ${this.list.length} 条充电车位数据，总数: ${this.total}`)
          } else {
            this.list = []
            this.total = 0
            console.warn('响应数据格式不符合预期:', response)
          }
        } catch (apiError) {
          console.error('API请求失败:', apiError)

          // API请求失败时，使用空数据
          this.list = []
          this.total = 0

          // 显示友好的错误消息
          this.$message({
            message: '获取充电车位列表失败，将显示空列表',
            type: 'warning',
            duration: 5000
          })
        }

        // 根据筛选条件过滤列表
        this.filterList()

        // 更新过滤后的停车场列表
        this.updateFilteredParkingLots()
      } catch (error) {
        console.error('获取充电车位列表失败:', error)

        // 出错时清空列表
        this.list = []
        this.total = 0

        // 显示友好的错误消息
        this.$message({
          message: '获取充电车位列表失败: ' + (error.message || '未知错误'),
          type: 'error',
          duration: 5000
        })
      } finally {
        this.listLoading = false
      }
    },

    // 根据筛选条件过滤列表
    filterList() {
      this.filteredList = [...this.list]

      // 如果有状态筛选
      if (this.listQuery.status !== undefined) {
        this.filteredList = this.filteredList.filter(item => item.status === this.listQuery.status)
      }

      // 如果在标签页中选择了特定停车场
      if (this.activeParkingLot !== 'all') {
        const parkingLotId = parseInt(this.activeParkingLot)
        this.filteredList = this.filteredList.filter(item => item.parking_lot_id === parkingLotId)
      }
    },

    // 更新过滤后的停车场列表
    updateFilteredParkingLots() {
      if (this.activeParkingLot === 'all') {
        // 获取所有有车位的停车场
        const lotIds = [...new Set(this.list.map(item => item.parking_lot_id))]
        this.filteredParkingLots = this.parkingLots.filter(lot => lotIds.includes(lot.id))
      } else {
        // 只显示选中的停车场
        const lotId = parseInt(this.activeParkingLot)
        this.filteredParkingLots = this.parkingLots.filter(lot => lot.id === lotId)
      }
    },

    // 处理停车场标签点击
    handleParkingLotTabClick() {
      // 重置页码
      this.listQuery.page = 1
      // 重新获取数据
      this.fetchData()
    },

    // 获取指定停车场的车位
    getSpacesByLot(lotId) {
      return this.list.filter(space => space.parking_lot_id === lotId)
    },

    // 获取指定停车场的车位总数
    getLotSpacesCount(lotId) {
      return this.getSpacesByLot(lotId).length
    },

    // 获取指定停车场的空闲车位数
    getLotAvailableSpacesCount(lotId) {
      return this.getSpacesByLot(lotId).filter(space => space.status === 0).length
    },

    // 获取指定停车场的占用车位数
    getLotOccupiedSpacesCount(lotId) {
      return this.getSpacesByLot(lotId).filter(space => space.status === 1).length
    },

    // 获取车位状态对应的CSS类名
    getSpaceStatusClass(status) {
      const statusMap = {
        0: 'status-available',  // 空闲
        1: 'status-occupied',   // 占用
        4: 'status-disabled'    // 禁用
      }
      return statusMap[status] || ''
    },

    // 获取停车场名称
    getParkingLotName(id) {
      const lot = this.parkingLots.find(item => item.id === id)
      return lot ? lot.name : '-'
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'success',  // 空闲
        1: 'warning',  // 占用
        4: 'info'      // 禁用
      }
      return statusMap[status] || 'info'
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '空闲',
        1: '占用',
        4: '禁用'
      }
      return statusMap[status] || '未知'
    },


    // 获取行的类名
    getRowClassName({ row }) {
      return `status-row-${row.status}`
    },
    // 格式化日期时间
    formatDateTime(dateStr) {
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    // 处理筛选
    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    },
    // 处理页面大小变化
    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchData()
    },
    // 处理页码变化
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.fetchData()
    },
    // 重置表单
    resetTemp() {
      this.temp = {
        id: undefined,
        parking_lot_id: '',
        space_number: '',
        power: 7,
        status: 0,
        remarks: ''
      }
    },

    // 处理编辑充电车位
    handleEdit(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    // 处理删除充电车位
    handleDelete(row) {
      this.$confirm('确认删除该充电车位?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteChargingSpace(row.id)
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.fetchData()
        } catch (error) {
          console.error('删除充电车位失败:', error)
          this.$message.error('删除充电车位失败')
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 更新充电车位
    updateData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          try {
            const tempData = Object.assign({}, this.temp)
            const id = tempData.id

            // 确保状态是数字类型
            if (tempData.status !== undefined) {
              tempData.status = Number(tempData.status)
            }

            // 确保车位编号格式正确
            const originalSpace = this.list.find(item => item.id === id)
            if (originalSpace && originalSpace.space_number !== tempData.space_number) {
              // 如果修改了车位编号，确保前缀不变
              const originalPrefix = originalSpace.space_number.split('-')[0]
              if (!tempData.space_number.startsWith(`${originalPrefix}-`)) {
                this.$message.warning('车位编号前缀不可修改，充电车位应以C-开头')
                return
              }
            }

            // 检查是否尝试将车位设置为占用状态
            if (tempData.status === 1) {
              // 如果原始状态也是占用，则不修改状态字段，避免发送到后端
              if (originalSpace && originalSpace.status === 1) {
                console.log('车位已处于占用状态，保持不变')
                delete tempData.status // 从请求数据中移除状态字段
              } else {
                // 如果尝试将非占用状态改为占用，显示错误
                this.$message.warning('占用状态只能由系统自动设置，不能手动修改')
                return
              }
            }

            // 添加日志，查看发送的数据
            console.log('发送更新请求，数据:', tempData)
            console.log('当前车位状态:', tempData.status, '类型:', typeof tempData.status)

            try {
              const response = await updateChargingSpace(id, tempData)
              console.log('更新充电车位响应:', response)
              this.dialogFormVisible = false
              this.$message({
                type: 'success',
                message: '更新成功!'
              })
            } catch (updateError) {
              console.error('更新充电车位详细错误:', updateError)
              throw updateError
            }
            // 延迟刷新数据，确保后端处理完成
            setTimeout(() => {
              this.fetchData()
            }, 500)
          } catch (error) {
            console.error('更新充电车位失败:', error)
            this.$message.error(error.message || '更新充电车位失败')
          }
        }
      })
    },

    // 处理停车场变化
    handleParkingLotChange(parkingLotId) {
      if (!parkingLotId || this.dialogStatus !== 'create') {
        return
      }

      // 查找当前停车场下的充电车位数量，用于生成新的序号
      const existingSpaces = this.list.filter(space =>
        space.parking_lot_id === parkingLotId &&
        space.space_number.startsWith('C-')
      )
      const nextNumber = existingSpaces.length + 1
      this.temp.space_number = `C-${parkingLotId}-${nextNumber}`
    },

    // 生成新的车位编号
    generateSpaceNumber(parkingLotId, type) {
      // 根据类型选择前缀：C-充电车位，N-普通车位，D-残疾人车位
      let prefix = 'N'
      if (type === 3) {
        prefix = 'C'  // 充电车位类型为3
      } else if (type === 2) {
        prefix = 'D'  // 残疾人车位类型为2
      }

      // 查找指定类型的车位
      const existingSpaces = this.list.filter(space =>
        space.parking_lot_id === parkingLotId &&
        space.space_number.startsWith(`${prefix}-`)
      )
      const nextNumber = existingSpaces.length + 1
      return `${prefix}-${parkingLotId}-${nextNumber}`
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-spaces-container {
  .filter-container {
    margin-bottom: 20px;
  }

  .parking-lot-tabs {
    margin-bottom: 20px;
  }

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-title {
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;

      i {
        margin-right: 10px;
        color: #409EFF;
      }
    }
  }

  .charging-spaces-grid {
    .parking-lot-section {
      margin-bottom: 30px;

      .parking-lot-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;

        h3 {
          font-size: 16px;
          margin: 0;
          color: #303133;
        }

        .lot-stats {
          span {
            margin-left: 15px;
            color: #606266;
            font-size: 14px;
          }
        }
      }

      .spaces-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
        margin-bottom: 20px;

        .space-card {
          border-radius: 4px;
          transition: all 0.3s;

          &.status-available {
            border-left: 4px solid #67C23A;
          }

          &.status-occupied {
            border-left: 4px solid #E6A23C;
          }

          &.status-fault {
            border-left: 4px solid #F56C6C;
          }

          &.status-maintenance {
            border-left: 4px solid #909399;
          }

          &.status-disabled {
            border-left: 4px solid #DCDFE6;
            opacity: 0.8;
          }

          .space-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .space-number {
              font-size: 16px;
              font-weight: 600;
              color: #303133;
            }
          }

          .space-info {
            margin-bottom: 15px;

            .info-item {
              display: flex;
              margin-bottom: 8px;
              font-size: 14px;

              .label {
                color: #909399;
                width: 70px;
              }

              .value {
                color: #606266;
                flex: 1;
              }

              &.remarks {
                margin-top: 10px;
                color: #909399;
                font-size: 13px;
              }
            }
          }

          .space-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 15px;

            .el-button {
              margin-left: 10px;
            }
          }
        }
      }
    }

    .empty-data {
      padding: 40px 0;
      text-align: center;
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  // 表格行状态样式
  ::v-deep .status-row-0 {
    background-color: rgba(103, 194, 58, 0.1);
  }

  ::v-deep .status-row-1 {
    background-color: rgba(230, 162, 60, 0.1);
  }

  ::v-deep .status-row-4 {
    background-color: rgba(220, 223, 230, 0.1);
  }

  // 停车场摘要卡片样式
  .parking-lots-summary {
    margin-bottom: 20px;

    .lot-summary-card {
      height: 100%;

      .lot-name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #303133;
      }

      .lot-stats {
        .stat-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;

          .label {
            color: #909399;
          }

          .value {
            font-weight: 600;
            color: #606266;
          }
        }
      }
    }
  }





  // 状态提示样式
  .status-tip {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #E6A23C;

    i {
      margin-right: 4px;
    }
  }
}
</style>
