"""
停车场相关定期任务
用于校验和修复停车场数据一致性
"""
import logging
from app import db
from app.parkinglots.models import ParkingLot, ParkingSpace
from app.parking_records.models import ParkingRecord
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime, timedelta

def check_parking_lot_consistency():
    """
    检查并修复停车场占用数与实际车位状态的一致性
    """
    logging.info("开始执行停车场数据一致性检查...")

    try:
        # 获取所有停车场
        parking_lots = ParkingLot.query.all()
        fixed_count = 0

        for lot in parking_lots:
            # 查询实际占用车位数
            actual_occupied = ParkingSpace.query.filter_by(
                parking_lot_id=lot.id,
                status=1  # 已占用状态
            ).count()

            # 查询实际总车位数
            actual_total = ParkingSpace.query.filter_by(
                parking_lot_id=lot.id
            ).count()

            # 检查是否需要修复
            needs_fix = False

            if lot.occupied_spaces != actual_occupied:
                logging.warning(f"停车场 {lot.name} (ID: {lot.id}) 占用数不一致: 记录值={lot.occupied_spaces}, 实际值={actual_occupied}")
                lot.occupied_spaces = actual_occupied
                needs_fix = True

            if lot.total_spaces != actual_total:
                logging.warning(f"停车场 {lot.name} (ID: {lot.id}) 总车位数不一致: 记录值={lot.total_spaces}, 实际值={actual_total}")
                lot.total_spaces = actual_total
                needs_fix = True

            if needs_fix:
                db.session.add(lot)
                fixed_count += 1

        # 提交所有修复
        if fixed_count > 0:
            db.session.commit()
            logging.info(f"成功修复 {fixed_count} 个停车场的数据一致性问题")
        else:
            logging.info("所有停车场数据一致性检查通过，无需修复")

        return fixed_count

    except SQLAlchemyError as e:
        db.session.rollback()
        logging.error(f"停车场数据一致性检查失败: {str(e)}")
        return -1

def check_parking_space_consistency():
    """
    检查并修复车位状态与停车记录的一致性
    """
    logging.info("开始检查车位状态与停车记录的一致性...")

    try:
        # 查找所有进行中的停车记录
        active_records = ParkingRecord.query.filter_by(status=0).all()
        logging.info(f"找到 {len(active_records)} 条进行中的停车记录")

        fixed_count = 0

        for record in active_records:
            # 获取对应的车位
            space = ParkingSpace.query.get(record.parking_space_id)

            if not space:
                logging.warning(f"停车记录 ID: {record.id} 关联的车位 ID: {record.parking_space_id} 不存在")
                continue

            # 检查车位状态是否与记录一致
            if space.status != 1 or space.current_vehicle_id != record.vehicle_id:
                logging.warning(f"车位状态与停车记录不一致: 记录ID={record.id}, 车位ID={space.id}, 车位状态={space.status}, 车位车辆ID={space.current_vehicle_id}, 记录车辆ID={record.vehicle_id}")

                # 修复车位状态
                space.status = 1  # 已占用
                space.current_vehicle_id = record.vehicle_id
                db.session.add(space)
                fixed_count += 1

        if fixed_count > 0:
            db.session.commit()
            logging.info(f"成功修复 {fixed_count} 个车位状态")

            # 更新所有停车场的占用数
            for lot in ParkingLot.query.all():
                lot.update_occupied_spaces()

            db.session.commit()
            logging.info("已更新所有停车场的占用数")
        else:
            logging.info("所有车位状态与停车记录一致，无需修复")

        return fixed_count

    except SQLAlchemyError as e:
        db.session.rollback()
        logging.error(f"车位状态一致性检查失败: {str(e)}")
        return -1

def check_orphaned_parking_records():
    """
    检查并修复异常的停车记录
    - 查找长时间处于"进行中"状态的记录
    - 查找没有对应车位或停车场的记录
    """
    logging.info("开始检查异常停车记录...")

    try:
        # 查找超过24小时的进行中记录
        one_day_ago = datetime.now() - timedelta(days=1)
        long_records = ParkingRecord.query.filter(
            ParkingRecord.status == 0,  # 进行中
            ParkingRecord.entry_time < one_day_ago
        ).all()

        if long_records:
            logging.warning(f"发现 {len(long_records)} 条超过24小时的进行中停车记录")

            for record in long_records:
                # 标记为异常状态
                record.status = 2  # 异常
                record.remarks = f"{record.remarks or ''}\n系统自动标记: 停车时间超过24小时"
                record.updated_at = datetime.now()

                # 释放车位
                space = ParkingSpace.query.get(record.parking_space_id)
                if space and space.status == 1 and space.current_vehicle_id == record.vehicle_id:
                    space.status = 0  # 空闲
                    space.current_vehicle_id = None
                    db.session.add(space)

                    # 更新停车场占用数
                    lot = ParkingLot.query.get(record.parking_lot_id)
                    if lot:
                        lot.update_occupied_spaces()

                db.session.add(record)

            db.session.commit()
            logging.info(f"成功修复 {len(long_records)} 条异常停车记录")

        # 查找孤立的停车记录（没有对应车位或停车场）
        orphaned_records = ParkingRecord.query.filter(
            ParkingRecord.status == 0  # 进行中
        ).all()

        orphaned_count = 0
        for record in orphaned_records:
            space = ParkingSpace.query.get(record.parking_space_id)
            lot = ParkingLot.query.get(record.parking_lot_id)

            if not space or not lot:
                # 标记为异常状态
                record.status = 2  # 异常
                record.remarks = f"{record.remarks or ''}\n系统自动标记: 关联的车位或停车场不存在"
                record.updated_at = datetime.now()
                db.session.add(record)
                orphaned_count += 1

        if orphaned_count > 0:
            db.session.commit()
            logging.info(f"成功修复 {orphaned_count} 条孤立的停车记录")

        return len(long_records) + orphaned_count

    except SQLAlchemyError as e:
        db.session.rollback()
        logging.error(f"检查异常停车记录失败: {str(e)}")
        return -1

# 如果直接运行此脚本，执行所有检查任务
if __name__ == "__main__":
    from app import create_app

    app = create_app()
    with app.app_context():
        print("=== 开始执行停车场数据一致性检查 ===")

        print("\n1. 检查停车场统计数据...")
        fixed_lots = check_parking_lot_consistency()
        print(f"停车场数据一致性检查完成，修复了 {fixed_lots} 个停车场")

        print("\n2. 检查车位状态与停车记录的一致性...")
        fixed_spaces = check_parking_space_consistency()
        print(f"车位状态一致性检查完成，修复了 {fixed_spaces} 个车位")

        print("\n3. 检查异常停车记录...")
        fixed_records = check_orphaned_parking_records()
        print(f"异常停车记录检查完成，修复了 {fixed_records} 条记录")

        print("\n=== 停车场数据一致性检查完成 ===")
