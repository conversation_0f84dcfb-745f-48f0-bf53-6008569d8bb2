# 用户蓝图
from flask import Blueprint, request
from flask_jwt_extended import jwt_required, get_jwt_identity

from ..utils.responses import response_with
from ..utils import responses as resp
from .models import Users
from .service import get_user_by_id
from .schemas import UserSchema

users_bp = Blueprint('users', __name__)

# 调试：检查蓝图中是否有响应拦截
print("\n检查users_bp蓝图是否有响应拦截器...")
if hasattr(users_bp, 'after_request_funcs'):
    print(f"users_bp.after_request_funcs: {users_bp.after_request_funcs}")
else:
    print("users_bp没有after_request_funcs属性")
    
# 用户相关路由 