"""
密码工具模块 - 提供密码哈希和验证功能
"""
import hashlib
import uuid

def verify_password(password, hashed_password, salt):
    """
    验证密码是否正确
    
    Args:
        password: 用户输入的明文密码
        hashed_password: 数据库中存储的哈希密码
        salt: 用于哈希的盐值
        
    Returns:
        bool: 密码是否正确
    """
    # 使用相同的盐值对输入的密码进行哈希
    hashed = hashlib.sha256(f"{password}{salt}".encode()).hexdigest()
    # 比较哈希结果
    return hashed == hashed_password

def hash_password(password, salt):
    """
    使用盐值对密码进行哈希
    
    Args:
        password: 明文密码
        salt: 盐值
        
    Returns:
        str: 哈希后的密码
    """
    return hashlib.sha256(f"{password}{salt}".encode()).hexdigest()

def get_salt_if_none():
    """
    生成一个随机的盐值
    
    Returns:
        str: 生成的盐值
    """
    return str(uuid.uuid4())
