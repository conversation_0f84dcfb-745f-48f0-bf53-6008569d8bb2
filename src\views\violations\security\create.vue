<template>
  <div class="app-container">
    <permission-wrapper :allowed-roles="['admin', 'security']" message="您没有权限访问违规记录录入页面，此功能仅对管理员和保安开放">
      <div class="page-header">
        <el-button icon="el-icon-back" @click="goBack">返回</el-button>
        <h2>录入违规记录</h2>
      </div>

    <!-- 提示框 -->
    <el-card class="violation-tips-card">
      <div slot="header" class="clearfix">
        <span class="tips-title">录入违规记录前的重要提示</span>
      </div>
      <div class="tips-content">
        <p class="tips-description">在录入违规记录前，请先在系统中查询违规车辆的相关信息，包括车主用户ID、车牌号等。请使用下方的查询模块获取车辆和车主信息。</p>
      </div>
    </el-card>

    <!-- 车辆与车主信息查询模块 -->
    <vehicle-owner-search @copy-data="handleCopyData" ref="vehicleSearch" />

    <violation-form
      :is-edit="false"
      :submit-method="createViolation"
      @success="handleSuccess"
      ref="violationForm"
    />
    </permission-wrapper>
  </div>
</template>

<script>
import { createViolation } from '@/api/violations'
import ViolationForm from '../components/ViolationForm'
import VehicleOwnerSearch from '@/components/VehicleOwnerSearch'
import PermissionWrapper from '@/components/PermissionWrapper'

export default {
  name: 'SecurityViolationCreate',
  components: {
    ViolationForm,
    VehicleOwnerSearch,
    PermissionWrapper
  },
  data() {
    return {}
  },
  methods: {
    createViolation(data) {
      return createViolation(data)
    },
    handleSuccess(data) {
      this.$message.success('违规记录创建成功')

      // 检查数据中是否包含有效的ID
      if (data && data.id) {
        // 跳转到详情页面，便于上传更多证据
        console.log('跳转到详情页面，ID:', data.id)
        this.$router.push({ name: 'SecurityViolationDetail', params: { id: data.id }})
      } else {
        console.warn('无法跳转到详情页面，因为缺少有效的ID:', data)
        // 返回列表页面
        this.$router.push({ name: 'SecurityViolationRecords' })
      }
    },
    goBack() {
      this.$router.push({ name: 'SecurityViolationRecords' })
    },
    // 处理复制数据到表单
    handleCopyData(data) {
      // 将车辆和车主信息填入到违规表单中
      if (this.$refs.violationForm && this.$refs.violationForm.setFormData) {
        this.$refs.violationForm.setFormData({
          bike_number: data.bikeNumber,
          user_id: data.userId,
          user: data.user, // 添加 user 字段
          bike_id: data.bikeId // 添加 bike_id 字段
        })
      } else {
        // 如果表单组件没有提供设置数据的方法，则显示提示
        this.$message.info(`请将车牌号 ${data.bikeNumber} 和车主ID ${data.userId} 填入表单`)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .page-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin-left: 15px;
      margin-bottom: 0;
    }
  }
}

.violation-tips-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-left: 4px solid #409EFF;

  .tips-title {
    font-size: 16px;
    font-weight: bold;
    color: #409EFF;
  }

  .tips-content {
    padding: 10px 0;

    .tips-description {
      margin-bottom: 15px;
      line-height: 1.6;
      color: #606266;
    }
  }
}
</style>
