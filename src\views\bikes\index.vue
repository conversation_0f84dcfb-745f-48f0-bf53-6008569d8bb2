<template>
  <div class="app-container">
    <permission-wrapper :allowed-roles="['admin']" message="您没有权限访问车辆管理数据，请联系管理员获取权限。">
    <!-- 用户信息面板 -->
    <el-card v-if="userId" class="user-info-card" style="margin-bottom: 15px;">
      <div class="user-info-header">
        <i class="el-icon-user" />
        <span style="margin-left: 8px;">当前登录用户</span>
      </div>
      <div class="user-info-content">
        <div class="user-info-item">
          <span class="item-label">用户ID:</span>
          <span class="item-value">{{ userId }}</span>
        </div>
        <div class="user-info-item">
          <span class="item-label">角色:</span>
          <span class="item-value">{{ (roles || []).join(', ') || '普通用户' }}</span>
        </div>
        <div class="user-info-item">
          <span class="item-label">管理车辆:</span>
          <span class="item-value">{{ list.length || 0 }}台</span>
        </div>
        <div class="user-info-item">
          <span class="item-label">状态统计:</span>
          <span class="item-value">
            <el-tag size="mini" type="success">正常: {{ availableBikesCount }}</el-tag>
            <el-tag size="mini" type="danger" style="margin-left: 5px;">停用: {{ unavailableBikesCount }}</el-tag>
          </span>
        </div>
      </div>
      <div v-if="isAdmin" class="user-tip">
        <i class="el-icon-info" style="color: #409EFF;" />
        <span>您是管理员用户，可以管理所有车辆信息</span>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-header">
            <div class="stat-icon-container" style="background-color: rgba(64, 158, 255, 0.08);">
              <i class="el-icon-bicycle" style="color: #409EFF;" />
            </div>
            <span class="stat-title">车辆总数</span>
          </div>
          <div class="stat-value">{{ bikeStats.total }}台</div>
          <div class="stat-footer">
            <el-button type="text" size="mini" @click="updateStats">
              <i class="el-icon-refresh-right" /> 刷新统计
            </el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-header">
            <div class="stat-icon-container" style="background-color: rgba(103, 194, 58, 0.08);">
              <i class="el-icon-success" style="color: #67C23A;" />
            </div>
            <span class="stat-title">可用车辆</span>
          </div>
          <div class="stat-value">{{ bikeStats.available }}台</div>
          <div v-if="bikeStats.total > 0" class="stat-footer">
            <span class="stat-percent">{{ Math.round((bikeStats.available / bikeStats.total) * 100) }}%</span>
            <el-progress :percentage="Math.round((bikeStats.available / bikeStats.total) * 100)" :color="'#67C23A'" :show-text="false" :stroke-width="4" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-header">
            <div class="stat-icon-container" style="background-color: rgba(245, 108, 108, 0.08);">
              <i class="el-icon-error" style="color: #F56C6C;" />
            </div>
            <span class="stat-title">停用车辆</span>
          </div>
          <div class="stat-value">{{ bikeStats.unavailable }}台</div>
          <div v-if="bikeStats.total > 0" class="stat-footer">
            <span class="stat-percent">{{ Math.round((bikeStats.unavailable / bikeStats.total) * 100) }}%</span>
            <el-progress :percentage="Math.round((bikeStats.unavailable / bikeStats.total) * 100)" :color="'#F56C6C'" :show-text="false" :stroke-width="4" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-header">
            <div class="stat-icon-container" style="background-color: rgba(230, 162, 60, 0.08);">
              <i class="el-icon-data-line" style="color: #E6A23C;" />
            </div>
            <span class="stat-title">品牌数量</span>
          </div>
          <div class="stat-value">{{ Object.keys(bikeStats.by_brand || {}).length }}种</div>
          <div class="stat-footer">
            <!-- 保持空白以维持高度一致性 -->
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row v-if="showCharts" :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="12">
        <el-card shadow="hover">
          <div slot="header">
            <span>按品牌分布</span>
            <div style="float: right;">
              <el-radio-group v-model="brandChartType" size="mini" @change="refreshBrandChart">
                <el-radio-button label="pie">饼图</el-radio-button>
                <el-radio-button label="bar">柱状图</el-radio-button>
              </el-radio-group>
              <el-tooltip content="导出图表为图片" placement="top">
                <el-button
                  style="margin-left: 10px; padding: 3px 0"
                  type="text"
                  icon="el-icon-download"
                  @click="exportChart('brandChart')"
                />
              </el-tooltip>
              <el-button style="margin-left: 10px; padding: 3px 0" type="text" @click="showCharts = false">
                <i class="el-icon-close" />
              </el-button>
            </div>
          </div>
          <div ref="brandChartRef" style="height: 250px;" />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <div slot="header">
            <span>按类型分布</span>
            <div style="float: right;">
              <el-radio-group v-model="typeChartType" size="mini" @change="refreshTypeChart">
                <el-radio-button label="bar">柱状图</el-radio-button>
                <el-radio-button label="pie">饼图</el-radio-button>
              </el-radio-group>
              <el-tooltip content="导出图表为图片" placement="top">
                <el-button
                  style="margin-left: 10px; padding: 3px 0"
                  type="text"
                  icon="el-icon-download"
                  @click="exportChart('typeChart')"
                />
              </el-tooltip>
            </div>
          </div>
          <div ref="typeChartRef" style="height: 250px;" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 更多图表 -->
    <el-row v-if="showCharts && showAdvancedCharts" :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="12">
        <el-card shadow="hover">
          <div slot="header">
            <span>按颜色分布</span>
            <div style="float: right;">
              <el-radio-group v-model="colorChartType" size="mini" @change="refreshColorChart">
                <el-radio-button label="pie">饼图</el-radio-button>
                <el-radio-button label="bar">柱状图</el-radio-button>
              </el-radio-group>
              <el-tooltip content="导出图表为图片" placement="top">
                <el-button
                  style="margin-left: 10px; padding: 3px 0"
                  type="text"
                  icon="el-icon-download"
                  @click="exportChart('colorChart')"
                />
              </el-tooltip>
            </div>
          </div>
          <div ref="colorChartRef" style="height: 250px;" />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <div slot="header">
            <span>车辆状态分布</span>
            <div style="float: right;">
              <el-tooltip content="导出图表为图片" placement="top">
                <el-button
                  style="margin-left: 10px; padding: 3px 0"
                  type="text"
                  icon="el-icon-download"
                  @click="exportChart('statusChart')"
                />
              </el-tooltip>
            </div>
          </div>
          <div ref="statusChartRef" style="height: 250px;" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 顶部操作栏 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.bike_number"
        placeholder="车牌号"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.status" placeholder="状态" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.brand" placeholder="品牌" clearable class="filter-item" style="width: 150px">
        <el-option v-for="brand in brandOptions" :key="brand" :label="brand" :value="brand" />
      </el-select>
      <el-select v-model="listQuery.type" placeholder="类型" clearable class="filter-item" style="width: 150px">
        <el-option v-for="type in typeOptions" :key="type" :label="type" :value="type" />
      </el-select>
      <!-- 管理员可以按用户ID筛选 -->
      <el-input
        v-if="isAdmin"
        v-model="listQuery.user_id"
        placeholder="用户ID"
        style="width: 120px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        添加
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="success"
        icon="el-icon-refresh"
        :loading="loading"
        @click="fetchData"
      >
        刷新
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="warning"
        icon="el-icon-data-analysis"
        @click="toggleChartsView"
      >
        {{ chartButtonText }}
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="info"
        icon="el-icon-document"
        @click="exportBikes"
      >
        导出
      </el-button>
    </div>

    <!-- 提示消息 -->
    <el-alert
      v-if="showAlert"
      :title="alertMessage"
      :type="alertType"
      show-icon
      @close="showAlert = false"
    />

    <!-- 数据加载状态 -->
    <div v-if="loading" class="loading-indicator">
      <el-progress :percentage="loadingProgress" :status="loadingProgress < 100 ? '' : (loadingStatusType === 'exception' ? 'exception' : 'success')" :stroke-width="10" />
      <div class="loading-text">{{ loadingText }}</div>
    </div>

    <!-- 车辆列表 -->
    <el-table
      v-loading="loading"
      :data="paginatedList"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 15px;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />

      <el-table-column label="车牌号" align="center" width="120">
        <template slot-scope="scope">
          <div style="display: flex; align-items: center; justify-content: center;">
            <el-tag size="medium" :type="scope.row.bike_number.startsWith('临时车牌') ? 'warning' : ''">
              <router-link :to="'/bikes/' + scope.row.b_id" class="link-type">
                {{ scope.row.bike_number }}
              </router-link>
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column v-if="isAdmin" label="所属用户" align="center" width="100">
        <template slot-scope="scope">
          {{ scope.row.user_id }}
        </template>
      </el-table-column>

      <el-table-column label="品牌" align="center" width="120">
        <template slot-scope="scope">
          {{ scope.row.brand }}
        </template>
      </el-table-column>

      <el-table-column label="颜色" align="center" width="100">
        <template slot-scope="scope">
          <span
            class="color-dot"
            :style="{
              backgroundColor: getColorCode(scope.row.color),
              borderColor: getColorBorder(scope.row.color)
            }"
          />
          {{ scope.row.color }}
        </template>
      </el-table-column>

      <el-table-column label="类型" align="center" width="100">
        <template slot-scope="scope">
          {{ scope.row.type }}
        </template>
      </el-table-column>

      <el-table-column label="状态" width="150" align="center">
        <template slot-scope="scope">
          <div class="status-container">
            <el-tag :type="scope.row.status === 'available' ? 'success' : 'danger'">
              {{ scope.row.status === 'available' ? '正常' : '停用' }}
            </el-tag>
            <vehicle-disable-status
              :vehicle-id="scope.row.b_id"
              :show-when-enabled="false"
              :show-actions="false"
              @enabled="handleVehicleEnabled"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column v-if="showAdvancedColumns" label="注册时间" align="center" width="180">
        <template slot-scope="scope">
          {{ formatTime(scope.row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="250" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleEdit(scope.row)"
          >编辑</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
          >删除</el-button>
          <el-button
            v-if="scope.row.status === '废弃'"
            size="mini"
            type="success"
            @click="handleEnableVehicle(scope.row)"
          >解除禁用</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 页面底部功能区 -->
    <div class="bottom-action" style="margin-top: 15px; display: flex; justify-content: space-between; align-items: center;">
      <!-- 批量操作 -->
      <div>
        <el-checkbox v-model="showAdvancedColumns" style="margin-right: 15px;">显示高级信息</el-checkbox>
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          :disabled="selectedBikes.length === 0"
          @click="handleBatchDisable"
        >
          批量停用 ({{ selectedBikes.length }})
        </el-button>
      </div>

      <div class="pagination-container">
        <el-pagination
          v-if="filteredList.length > pageSize"
          background
          :current-page.sync="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="filteredList.length"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加/编辑车辆对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" @close="handleDialogClose">
      <div v-if="dialogType === 'create'" class="dialog-tip">
        <i class="el-icon-info" style="color: #409EFF;" />
        <span>添加的车辆将与您的账户关联</span>
      </div>

      <el-form ref="bikeForm" :model="bikeForm" :rules="bikeFormRules" label-width="100px">
        <!-- 隐藏所属用户字段，自动使用当前登录用户ID -->
        <input v-model="bikeForm.belong_to" type="hidden">

        <el-form-item label="车牌号" prop="bike_number">
          <el-input v-model="bikeForm.bike_number" placeholder="请输入车牌号，如：京A12345" />
          <div class="form-tip">标准车牌格式：省份+字母+5-6位字母数字组合，如：京A12345</div>
        </el-form-item>

        <el-form-item label="品牌" prop="brand">
          <el-select v-model="bikeForm.brand" placeholder="请选择品牌" style="width: 100%;">
            <el-option v-for="item in brandOptions" :key="item" :label="item" :value="item" />
            <el-option label="其他品牌" value="其他品牌" />
          </el-select>
          <div class="form-tip">如果未找到合适品牌，请选择"其他品牌"</div>
        </el-form-item>

        <el-form-item label="颜色" prop="color">
          <el-select v-model="bikeForm.color" placeholder="请选择颜色" style="width: 100%;">
            <el-option v-for="item in colorOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-input v-model="bikeForm.type" placeholder="请输入车辆类型" style="width: 100%;" />
          <div class="form-tip">可以输入数字和字母的组合</div>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select v-model="bikeForm.status" placeholder="请选择状态">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>

    <!-- 数据同步状态指示器 -->
    <div v-if="lastSyncTime" class="sync-status">
      <el-tag size="small" type="success">
        <i class="el-icon-success" /> 数据已同步
      </el-tag>
      <span class="sync-time">{{ formatLastSyncTime() }}</span>
    </div>
    </permission-wrapper>
  </div>
</template>

<script>
import { getBikes, createBike, updateBike, deleteBike, batchUpdateBikeStatus, getBikeStats, exportBikesToCSV } from '@/api/bike'
import { checkVehicleDisableStatus, disableVehicle, enableVehicle } from '@/api/vehicle'
import { mapGetters } from 'vuex'
import * as echarts from 'echarts'
import PermissionWrapper from '@/components/PermissionWrapper'
import VehicleDisableStatus from '@/components/VehicleDisableStatus'

export default {
  name: 'BikeManagement',
  components: {
    PermissionWrapper,
    VehicleDisableStatus
  },
  data() {
    // 车牌号验证函数 - 直接使用规则对象中的验证
    /* const validatePlateNumber = (rule, value, callback) => {
      if (!value || value.trim() === '') {
        return callback(new Error('请输入车牌号'))
      }
      if (value.length > 20) {
        return callback(new Error('车牌号不能超过20个字符'))
      }
      if (value.length < 2) {
        return callback(new Error('车牌号至少需要2个字符'))
      }

      // 检查车牌号是否已存在
      const existingBike = this.list.find(bike =>
        bike.bike_number.toLowerCase() === value.toLowerCase() &&
        bike.b_id !== this.bikeForm.b_id
      )
      if (existingBike) {
        return callback(new Error('该车牌号已存在，请使用其他车牌号'))
      }

      // 标准车牌格式验证（可选）
      if (!value.startsWith('临时车牌-') && !value.startsWith('未知车牌')) {
        const pattern = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]{1}[A-Z]{1}[A-Z0-9]{4,5}[A-Z0-9挂学警港澳]{1}$/
        if (!pattern.test(value)) {
          // 不符合标准格式，给出警告但不阻止提交
          this.$message({
            message: '车牌号格式不符合标准，建议使用"省份+字母+数字"格式，如：京A12345',
            type: 'warning',
            duration: 5000
          })
        }
      }
      callback()
    } */

    return {
      listLoading: false,
      list: [],
      total: 0,
      listQuery: {
        page: 1,
        limit: 10,
        bike_number: '',
        status: undefined,
        brand: '',
        type: '',
        user_id: undefined
      },
      showAdvancedColumns: false, // 是否显示高级列
      dialogVisible: false,
      dialogType: 'create',
      dialogTitle: '添加车辆',
      bikeForm: {
        b_id: undefined,
        bike_number: '',
        brand: '',
        color: '',
        type: '',
        status: 'available',
        belong_to: undefined
      },
      // 颜色选项
      colorOptions: [
        '黑色', '白色', '红色', '蓝色', '黄色',
        '绿色', '灰色', '银色', '金色', '棕色',
        '紫色', '粉色', '橙色', '未知颜色'
      ],
      // 品牌选项
      brandOptions: [
        '雅迪', '爱玛', '台铃', '小刀', '立马',
        '绿源', '新日', '飞鸽', '美利达', '捷安特',
        '阿米尼', '未知品牌'
      ],
      // 类型选项
      typeOptions: [
        '普通型号', '踏板车', '山地车', '公路车',
        '折叠车', '轻便型', '豪华型', '复古型'
      ],
      bikeFormRules: {
        bike_number: [
          { required: true, message: '请输入车牌号', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        brand: [
          { required: true, message: '请输入品牌', trigger: 'blur' }
        ],
        color: [
          { required: true, message: '请输入颜色', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请输入类型', trigger: 'blur' }
        ]
      },
      deleteDialogVisible: false,
      enableDialogVisible: false,
      currentBikeId: null,
      alertType: '',
      alertMessage: '',
      showAlert: false,
      currentPage: 1,
      pageSize: 10,
      loading: false,
      loadingProgress: 0,
      loadingStatusType: '', // 有效值：'', 'success', 'exception'（不要使用其他值）
      loadingText: '正在加载数据...',
      lastSyncTime: null,
      dataChangeFlag: false,
      selectedBikes: [], // 选中的车辆
      statusOptions: [
        { label: '正常', value: 'available' },
        { label: '停用', value: 'unavailable' }
      ],
      showCharts: false,
      brandChart: null,
      typeChart: null,
      colorChart: null,
      statusChart: null,
      brandChartType: 'pie',
      typeChartType: 'bar',
      colorChartType: 'pie',
      statusChartType: 'pie',
      showAdvancedCharts: false,
      bikeStats: {
        total: 0,
        available: 0,
        unavailable: 0,
        by_brand: {},
        by_type: {},
        by_color: {}
      },
      chartButtonText: '显示图表'
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'roles'
    ]),
    isAdmin() {
      return this.roles && Array.isArray(this.roles) && this.roles.includes('admin')
    },
    filteredList() {
      let result = this.list

      // 按车牌号筛选
      if (this.listQuery.bike_number) {
        const searchText = this.listQuery.bike_number.toLowerCase()
        result = result.filter(bike => {
          return bike.bike_number && bike.bike_number.toLowerCase().includes(searchText)
        })
      }

      // 按状态筛选 - 如果选择了特定状态才过滤
      if (this.listQuery.status && this.listQuery.status !== '') {
        result = result.filter(bike => {
          return bike.status === this.listQuery.status
        })
      }

      // 按品牌筛选
      if (this.listQuery.brand && this.listQuery.brand !== '') {
        result = result.filter(bike => {
          return bike.brand === this.listQuery.brand
        })
      }

      // 按类型筛选
      if (this.listQuery.type && this.listQuery.type !== '') {
        result = result.filter(bike => {
          return bike.type === this.listQuery.type
        })
      }

      // 按用户ID筛选 - 仅当管理员使用用户ID过滤时
      if (this.isAdmin && this.listQuery.user_id && this.listQuery.user_id !== '') {
        const userId = parseInt(this.listQuery.user_id, 10)
        if (!isNaN(userId)) {
          result = result.filter(bike => {
            return bike.user_id === userId
          })
        }
      }

      // 按照所属用户id增序排序
      result.sort((a, b) => {
        const userIdA = parseInt(a.user_id, 10) || 0
        const userIdB = parseInt(b.user_id, 10) || 0
        return userIdA - userIdB
      })

      return result
    },
    paginatedList() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredList.slice(start, end)
    },
    userRoleDisplay() {
      return Array.isArray(this.roles) ? this.roles.join(', ') : '普通用户'
    },
    availableBikesCount() {
      return this.list.filter(bike => bike && bike.status === 'available').length
    },
    unavailableBikesCount() {
      return this.list.filter(bike => bike && bike.status === 'unavailable').length
    }
  },
  watch: {
    // 监听列表数据变化，自动更新统计
    list: {
      handler() {
        this.generateStatsFromLocalData()
      },
      deep: true
    }
  },
  created() {
    console.log('正在加载车辆管理页面，当前用户ID:', this.userId)
    this.fetchData()
    this.fetchBikeStats()

    // 第一次加载时显示指导提示
    if (!localStorage.getItem('bike_page_visited')) {
      this.$notify({
        title: '欢迎使用车辆管理',
        message: '您可以在此页面添加、编辑或删除您的车辆信息。点击"添加"按钮开始。',
        type: 'info',
        duration: 8000,
        position: 'top-right'
      })
      localStorage.setItem('bike_page_visited', 'true')
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.brandChart) {
      this.brandChart.dispose()
    }
    if (this.typeChart) {
      this.typeChart.dispose()
    }
    if (this.colorChart) {
      this.colorChart.dispose()
    }
    if (this.statusChart) {
      this.statusChart.dispose()
    }
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true
        this.loadingStatusType = ''
        this.loadingProgress = 0
        this.loadingText = '正在获取车辆数据...'

        // 构建查询参数
        const params = {}

        // 注意：后端已经实现了权限控制
        // 普通用户只能看到自己的车辆，管理员可以看到所有车辆
        // 如果是管理员且指定了用户ID过滤，则添加用户ID参数
        if (this.isAdmin && this.listQuery.user_id) {
          params.user_id = this.listQuery.user_id
        }

        // 添加其他查询参数
        if (this.listQuery.bike_number) {
          params.bike_number = this.listQuery.bike_number
        }
        if (this.listQuery.status) {
          params.status = this.listQuery.status
        }
        if (this.listQuery.brand) {
          params.brand = this.listQuery.brand
        }
        if (this.listQuery.type) {
          params.type = this.listQuery.type
        }

        const response = await getBikes(params)
        console.log('获取车辆列表响应:', response)

        if (response && response.status === 'success') {
          // 处理新的响应格式
          if (response.data && Array.isArray(response.data)) {
            this.list = response.data.map(bike => this.formatBikeData(bike)).filter(Boolean)
          } else if (response.data && response.data.bikes && Array.isArray(response.data.bikes)) {
            // 处理旧的响应格式
            this.list = response.data.bikes.map(bike => this.formatBikeData(bike)).filter(Boolean)
          } else {
            this.list = []
            console.warn('未找到有效的车辆数据')
          }
          this.loadingStatusType = 'success'
        } else if (response && response.data && response.data.bikes && Array.isArray(response.data.bikes)) {
          // 处理旧的响应格式
          this.list = response.data.bikes.map(bike => this.formatBikeData(bike)).filter(Boolean)
          this.loadingStatusType = 'success'
        } else {
          this.list = []
          console.warn('未找到有效的车辆数据')
          this.loadingStatusType = ''
        }

        // 更新同步时间
        this.lastSyncTime = Date.now()
        this.loadingProgress = 100
      } catch (error) {
        console.error('获取车辆列表失败:', error)
        this.showAlertMessage('error', '获取车辆列表失败')
        this.list = []
        this.loadingStatusType = 'exception'
        this.loadingProgress = 100
      } finally {
        // 延迟隐藏加载状态，让用户看到完成进度
        setTimeout(() => {
          this.loading = false
        }, 500)
      }
    },
    formatBikeData(bike) {
      if (!bike) {
        console.warn('formatBikeData: 接收到空数据')
        return null
      }

      console.log('开始格式化车辆数据:', bike)

      // 处理API返回的嵌套数据结构
      if (bike.data && !bike.b_id && !bike.id) {
        console.log('检测到嵌套数据结构，提取内部数据', bike)
        bike = bike.data
      }

      // 处理服务器返回的message和status字段
      if (bike.message && bike.status && !bike.b_id && !bike.id) {
        console.log('检测到服务器响应格式，尝试提取data字段', bike)
        if (bike.data) {
          bike = bike.data
        } else {
          console.warn('服务器响应中未找到有效的车辆数据', bike)
          return null
        }
      }

      // 处理bike字段包含真实数据的情况
      if (bike.bike && typeof bike.bike === 'object') {
        console.log('检测到bike字段包含车辆数据', bike.bike)
        bike = bike.bike
      }

      // 统一使用b_id作为主键
      const bikeId = bike.b_id || bike.id
      if (!bikeId && bikeId !== 0) {
        console.warn('车辆数据缺少有效ID:', bike)
        return null
      }

      // 统一状态值转换
      const status = this.convertStatusToFrontend(bike.status)

      // 确保所有必要字段都有默认值
      const formattedBike = {
        id: bikeId,
        b_id: bikeId,
        bike_number: bike.bike_number || bike.b_num || '未知车牌',
        user_id: bike.user_id || bike.belong_to || this.userId || 0,
        brand: bike.brand || '未知品牌',
        color: bike.color || '未知颜色',
        type: bike.type || bike.b_type || '普通型号',
        status: status || 'available',
        created_at: bike.created_at || new Date().toISOString(),
        updated_at: bike.updated_at || new Date().toISOString()
      }

      console.log('格式化后的车辆数据:', formattedBike)

      // 验证车牌号
      if (!formattedBike.bike_number || formattedBike.bike_number === '未知车牌') {
        console.warn('车辆数据缺少车牌号，使用默认值', formattedBike)
      }

      return formattedBike
    },
    convertStatusToBackend(status) {
      const statusMap = {
        'available': '可用',
        'unavailable': '废弃',
        '正常': '可用',
        '停用': '废弃'
      }
      return statusMap[status] || '可用'
    },
    convertStatusToFrontend(status) {
      const statusMap = {
        '可用': 'available',
        '废弃': 'unavailable',
        '停用': 'unavailable',
        'available': 'available',
        'unavailable': 'unavailable',
        '正常': 'available'
      }
      return statusMap[status] || 'available'
    },
    getStatusText(status) {
      const statusMap = {
        'available': '正常',
        'unavailable': '停用',
        '可用': '正常',
        '废弃': '停用',
        '正常': '正常',
        '停用': '停用'
      }
      return statusMap[status] || '未知'
    },
    resetForm() {
      const currentUserId = this.userId ? parseInt(this.userId, 10) : null
      if (!currentUserId || isNaN(currentUserId)) {
        this.$message.warning('无法获取有效的用户ID，请确保已正确登录')
      }

      this.bikeForm = {
        b_id: undefined,
        bike_number: '',
        brand: this.brandOptions[0] || '', // 设置默认品牌
        color: this.colorOptions[0] || '', // 设置默认颜色
        type: this.typeOptions[0] || '', // 设置默认类型
        status: 'available',
        belong_to: undefined
      }
      console.log(`表单已重置，设置所属用户ID: ${this.bikeForm.belong_to}`)
    },
    handleCreate() {
      this.dialogType = 'create'
      this.resetForm()
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogType = 'edit'
      this.bikeForm = {
        id: row.b_id,
        b_id: row.b_id,
        bike_number: row.bike_number,
        brand: row.brand,
        color: row.color,
        type: row.type,
        status: row.status,
        belong_to: row.user_id
      }
      this.dialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确定要删除该车辆吗？此操作不可恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteBike(row.b_id).then(response => {
          this.showAlertMessage('success', '删除成功')
          this.fetchData()
          // 更新统计信息
          this.updateStats()
        }).catch(error => {
          console.error('删除车辆失败:', error)
          this.showAlertMessage('error', '删除失败: ' + (error.message || '未知错误'))
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleEnable(row) {
      this.currentBikeId = row.b_id
      this.enableDialogVisible = true
    },
    async confirmDelete() {
      try {
        this.loading = true
        this.loadingStatusType = ''
        this.loadingProgress = 0

        // 修改为更新状态而不是删除
        if (this.currentBikeId) {
          const bikeToUpdate = this.list.find(item => item.b_id === this.currentBikeId)
          if (bikeToUpdate) {
            // 准备更新数据，将状态改为停用而不是删除
            const updateData = {
              b_num: bikeToUpdate.bike_number,
              brand: bikeToUpdate.brand,
              color: bikeToUpdate.color,
              b_type: bikeToUpdate.type,
              status: '停用'
            }

            // 如果是管理员且车辆属于其他用户，则添加用户ID参数
            // 后端已经实现了权限控制，普通用户只能操作自己的车辆
            if (this.isAdmin && bikeToUpdate.user_id && bikeToUpdate.user_id !== this.userId) {
              updateData.belong_to = bikeToUpdate.user_id
            }

            console.log('准备停用车辆，ID:', this.currentBikeId, '数据:', updateData)

            // 调用更新API
            const response = await updateBike(this.currentBikeId, updateData)
            console.log('停用车辆响应:', response)

            // 无论响应格式如何，都尝试提取并更新数据
            if (response && (response.code === 20000 || response.status === 'success')) {
              // 更新本地数据
              const index = this.list.findIndex(item => item.b_id === this.currentBikeId)
              if (index !== -1) {
                this.$set(this.list[index], 'status', 'unavailable')
                // 直接更新本地统计数据
                this.updateStats()
              }
              this.$message.success('车辆已停用')
              this.loadingStatusType = 'success'
            } else {
              this.loadingStatusType = 'exception'
              throw new Error('停用车辆失败：服务器响应异常')
            }
          }
        }

        this.deleteDialogVisible = false

        // 立即刷新数据列表
        await this.fetchData()
        // 更新统计信息
        this.updateStats()
      } catch (error) {
        console.error('停用车辆失败:', error)
        this.$message.error('停用车辆失败: ' + (error.message || '未知错误'))
        this.loadingStatusType = 'exception'
      } finally {
        this.loadingProgress = 100

        // 延迟后隐藏加载状态
        setTimeout(() => {
          this.loading = false
        }, 500)
      }
    },
    async confirmEnable() {
      try {
        this.loading = true
        this.loadingStatusType = ''
        this.loadingProgress = 0

        if (this.currentBikeId) {
          const bikeToUpdate = this.list.find(item => item.b_id === this.currentBikeId)
          if (bikeToUpdate) {
            // 准备更新数据，将状态改为可用
            const updateData = {
              b_num: bikeToUpdate.bike_number,
              brand: bikeToUpdate.brand,
              color: bikeToUpdate.color,
              b_type: bikeToUpdate.type,
              status: '可用'
            }

            // 如果是管理员且车辆属于其他用户，则添加用户ID参数
            // 后端已经实现了权限控制，普通用户只能操作自己的车辆
            if (this.isAdmin && bikeToUpdate.user_id && bikeToUpdate.user_id !== this.userId) {
              updateData.belong_to = bikeToUpdate.user_id
            }

            console.log('准备启用车辆，ID:', this.currentBikeId, '数据:', updateData)

            // 调用更新API
            const response = await updateBike(this.currentBikeId, updateData)
            console.log('启用车辆响应:', response)

            // 无论响应格式如何，都尝试提取并更新数据
            if (response && (response.code === 20000 || response.status === 'success')) {
              // 更新本地数据
              const index = this.list.findIndex(item => item.b_id === this.currentBikeId)
              if (index !== -1) {
                this.$set(this.list[index], 'status', 'available')
                // 直接更新本地统计数据
                this.updateStats()
              }
              this.$message.success('车辆已启用')
              this.loadingStatusType = 'success'
            } else {
              this.loadingStatusType = 'exception'
              throw new Error('启用车辆失败：服务器响应异常')
            }
          }
        }

        this.enableDialogVisible = false

        // 立即刷新数据列表
        await this.fetchData()
        // 更新统计信息
        this.updateStats()
      } catch (error) {
        console.error('启用车辆失败:', error)
        this.$message.error('启用车辆失败: ' + (error.message || '未知错误'))
        this.loadingStatusType = 'exception'
      } finally {
        this.loadingProgress = 100

        // 延迟后隐藏加载状态
        setTimeout(() => {
          this.loading = false
        }, 500)
      }
    },
    async submitForm() {
      this.$refs.bikeForm.validate(async valid => {
        if (valid) {
          try {
            this.loading = true
            this.loadingStatusType = ''
            this.loadingProgress = 0

            // 准备提交的数据
            const formData = {
              bike_number: this.bikeForm.bike_number,
              brand: this.bikeForm.brand,
              color: this.bikeForm.color,
              type: this.bikeForm.type,
              status: this.convertStatusToBackend(this.bikeForm.status) // 转换状态值为后端格式
            }

            // 如果是管理员且指定了用户ID，则添加用户ID参数
            // 后端已经实现了权限控制，普通用户只能为自己创建车辆
            if (this.isAdmin && this.bikeForm.belong_to) {
              formData.user_id = this.bikeForm.belong_to
            }

            console.log('准备提交表单数据:', formData)

            let response
            if (this.dialogType === 'create') {
              // 创建新车辆
              response = await createBike(formData)
            } else {
              // 更新现有车辆
              response = await updateBike(this.bikeForm.b_id, formData)
            }

            console.log('表单提交响应:', response)

            // 检查是否成功，包括检查响应中是否包含错误信息
            const isSuccess = response &&
                            (response.code === 20000 || response.status === 'success') &&
                            (!response.message || !response.message.includes('失败'))

            if (isSuccess) {
              const message = this.dialogType === 'create' ? '车辆添加成功' : '车辆信息更新成功'
              this.$message.success(message)

              // 关闭对话框并刷新数据
              this.dialogVisible = false
              this.fetchData()
              // 更新统计信息
              this.updateStats()
              this.loadingStatusType = 'success'
            } else {
              throw new Error(response?.message || '操作失败')
            }
          } catch (error) {
            console.error('表单提交失败:', error)
            // 检查错误信息是否包含"成功"，如果包含则表示操作实际上是成功的
            if (error.message && error.message.includes('成功')) {
              const message = this.dialogType === 'create' ? '车辆添加成功' : '车辆信息更新成功'
              this.$message.success(message)

              // 关闭对话框并刷新数据
              this.dialogVisible = false
              this.fetchData()
              // 更新统计信息
              this.updateStats()
              this.loadingStatusType = 'success'
            } else {
              this.$message.error(error.message || '操作失败，请重试')
              this.loadingStatusType = 'exception'
            }
          } finally {
            this.loadingProgress = 100

            // 延迟隐藏加载状态
            setTimeout(() => {
              this.loading = false
            }, 500)
          }
        } else {
          console.log('表单验证失败')
          this.$message.warning('请完善表单信息')
          return false
        }
      })
    },
    showAlertMessage(type, message) {
      this.alertType = type
      this.alertMessage = message
      this.showAlert = true

      // 根据消息类型设置不同的关闭时间
      const duration = type === 'error' ? 8000 : 5000

      setTimeout(() => {
        this.showAlert = false
      }, duration)
    },
    handleDialogClose() {
      this.resetForm()
    },
    handleFilter() {
      // 搜索功能使用计算属性过滤
      console.log('正在搜索车牌号:', this.listQuery.bike_number)
      console.log('正在筛选状态:', this.listQuery.status)
      this.currentPage = 1 // 重置到第一页

      // 确保在过滤时更新统计数据
      this.$nextTick(() => {
        this.generateStatsFromLocalData()
      })
    },
    handleCurrentChange(page) {
      this.currentPage = page
    },
    // 格式化时间显示
    formatTime(timestamp) {
      if (!timestamp) return '未知时间'

      try {
        const date = new Date(timestamp)
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } catch (e) {
        return timestamp
      }
    },
    // 获取颜色对应的CSS颜色代码
    getColorCode(color) {
      const colorMap = {
        '黑色': '#000000',
        '白色': '#FFFFFF',
        '红色': '#FF0000',
        '蓝色': '#0000FF',
        '黄色': '#FFFF00',
        '绿色': '#00FF00',
        '灰色': '#808080',
        '银色': '#C0C0C0',
        '金色': '#FFD700',
        '棕色': '#A52A2A',
        '紫色': '#800080',
        '粉色': '#FFC0CB',
        '橙色': '#FFA500',
        '未知颜色': '#EEEEEE'
      }
      return colorMap[color] || '#EEEEEE'
    },
    // 获取颜色边框颜色
    getColorBorder(color) {
      return color === '白色' || color === '银色' || color === '未知颜色' ? '#CCCCCC' : 'transparent'
    },
    formatLastSyncTime() {
      if (!this.lastSyncTime) return ''

      const now = Date.now()
      const diff = now - this.lastSyncTime

      if (diff < 60000) { // 小于1分钟
        return '刚刚'
      } else if (diff < 3600000) { // 小于1小时
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 小于1天
        return `${Math.floor(diff / 3600000)}小时前`
      } else {
        return `${Math.floor(diff / 86400000)}天前`
      }
    },
    // 获取统计数据
    fetchBikeStats() {
      getBikeStats().then(response => {
        if (response.data && response.data.stats) {
          this.bikeStats = response.data.stats
          this.$nextTick(() => {
            if (this.showCharts) {
              this.initBrandChart()
              this.initTypeChart()
            }
          })
        } else {
          // 如果返回数据格式不正确，使用本地数据计算
          this.generateStatsFromLocalData()
        }
      }).catch(error => {
        console.error('获取车辆统计数据失败:', error)
        // 如果API不存在或失败，则根据当前表格数据计算统计信息
        this.generateStatsFromLocalData()
      })
    },

    // 更新本地数据后刷新统计信息
    updateStats() {
      // 先使用本地数据更新统计数据
      this.generateStatsFromLocalData()

      // 然后尝试从服务器获取最新统计数据
      this.fetchBikeStats()
    },

    // 从本地数据生成统计信息
    generateStatsFromLocalData() {
      console.log('使用本地数据生成统计信息')

      // 初始化统计对象
      const stats = {
        total: this.list.length,
        available: 0,
        unavailable: 0,
        by_brand: {},
        by_type: {},
        by_color: {}
      }

      // 遍历当前列表数据
      this.list.forEach(bike => {
        // 统计状态
        if (bike.status === 'available') {
          stats.available++
        } else if (bike.status === 'unavailable') {
          stats.unavailable++
        }

        // 统计品牌
        const brand = bike.brand || '未知品牌'
        stats.by_brand[brand] = (stats.by_brand[brand] || 0) + 1

        // 统计类型
        const type = bike.type || '未知类型'
        stats.by_type[type] = (stats.by_type[type] || 0) + 1

        // 统计颜色
        const color = bike.color || '未知颜色'
        stats.by_color[color] = (stats.by_color[color] || 0) + 1
      })

      // 更新统计数据
      this.bikeStats = stats

      // 如果需要显示图表，则重新初始化
      this.$nextTick(() => {
        if (this.showCharts) {
          this.initBrandChart()
          this.initTypeChart()

          if (this.showAdvancedCharts) {
            this.initColorChart()
            this.initStatusChart()
          }
        }
      })
    },

    // 初始化品牌分布图表
    initBrandChart() {
      if (!this.$refs.brandChartRef) return

      if (this.brandChart) {
        this.brandChart.dispose()
      }

      const brandData = Object.entries(this.bikeStats.by_brand || {}).map(([name, value]) => ({
        name,
        value
      }))

      this.brandChart = echarts.init(this.$refs.brandChartRef)

      // 根据当前选择的图表类型设置不同的配置
      if (this.brandChartType === 'pie') {
        // 饼图配置
        this.brandChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c}台 ({d}%)'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            data: brandData.map(item => item.name)
          },
          series: [
            {
              name: '品牌分布',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: brandData
            }
          ]
        })
      } else {
        // 柱状图配置
        const sortedData = [...brandData].sort((a, b) => b.value - a.value)
        this.brandChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: sortedData.map(item => item.name),
            axisLabel: {
              interval: 0,
              rotate: 30
            }
          },
          yAxis: {
            type: 'value',
            name: '数量'
          },
          series: [
            {
              name: '车辆数量',
              type: 'bar',
              data: sortedData.map(item => item.value),
              itemStyle: {
                color: function(params) {
                  const colorList = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#B983FF']
                  return colorList[params.dataIndex % colorList.length]
                }
              }
            }
          ]
        })
      }
    },

    // 初始化类型分布图表
    initTypeChart() {
      if (!this.$refs.typeChartRef) return

      if (this.typeChart) {
        this.typeChart.dispose()
      }

      const typeData = Object.entries(this.bikeStats.by_type || {}).map(([name, value]) => ({
        name,
        value
      }))

      this.typeChart = echarts.init(this.$refs.typeChartRef)

      // 根据当前选择的图表类型设置不同的配置
      if (this.typeChartType === 'bar') {
        // 柱状图配置
        const sortedData = [...typeData].sort((a, b) => b.value - a.value)
        this.typeChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            name: '数量'
          },
          yAxis: {
            type: 'category',
            data: sortedData.map(item => item.name),
            axisLabel: {
              interval: 0
            }
          },
          series: [
            {
              name: '数量',
              type: 'bar',
              data: sortedData.map(item => item.value),
              itemStyle: {
                color: function(params) {
                  const colorList = ['#67C23A', '#409EFF', '#E6A23C', '#F56C6C', '#909399']
                  return colorList[params.dataIndex % colorList.length]
                }
              }
            }
          ]
        })
      } else {
        // 饼图配置
        this.typeChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c}台 ({d}%)'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            data: typeData.map(item => item.name)
          },
          series: [
            {
              name: '类型分布',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: typeData
            }
          ]
        })
      }
    },

    // 初始化颜色分布图表
    initColorChart() {
      if (!this.$refs.colorChartRef) return

      if (this.colorChart) {
        this.colorChart.dispose()
      }

      const colorData = Object.entries(this.bikeStats.by_color || {}).map(([name, value]) => ({
        name,
        value,
        itemStyle: {
          color: this.getColorCode(name)
        }
      }))

      this.colorChart = echarts.init(this.$refs.colorChartRef)

      if (this.colorChartType === 'pie') {
        // 饼图配置
        this.colorChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c}台 ({d}%)'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            data: colorData.map(item => item.name)
          },
          series: [
            {
              name: '颜色分布',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: colorData
            }
          ]
        })
      } else {
        // 柱状图配置
        const sortedData = [...colorData].sort((a, b) => b.value - a.value)
        this.colorChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: sortedData.map(item => item.name),
            axisLabel: {
              interval: 0,
              rotate: 30
            }
          },
          yAxis: {
            type: 'value',
            name: '数量'
          },
          series: [
            {
              name: '车辆数量',
              type: 'bar',
              data: sortedData
            }
          ]
        })
      }
    },

    // 初始化状态分布图表
    initStatusChart() {
      if (!this.$refs.statusChartRef) return

      if (this.statusChart) {
        this.statusChart.dispose()
      }

      const statusData = [
        { name: '正常', value: this.bikeStats.available, itemStyle: { color: '#67C23A' }},
        { name: '停用', value: this.bikeStats.unavailable, itemStyle: { color: '#F56C6C' }}
      ]

      this.statusChart = echarts.init(this.$refs.statusChartRef)

      // 饼图
      this.statusChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}台 ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: statusData.map(item => item.name)
        },
        series: [
          {
            name: '状态分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              formatter: '{b}: {c}台 ({d}%)'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            data: statusData
          }
        ]
      })
    },

    // 处理窗口大小改变
    handleResize() {
      if (this.brandChart) {
        this.brandChart.resize()
      }
      if (this.typeChart) {
        this.typeChart.resize()
      }
      if (this.colorChart) {
        this.colorChart.resize()
      }
      if (this.statusChart) {
        this.statusChart.resize()
      }
    },

    // 导出车辆数据
    exportBikes() {
      this.loading = true
      this.loadingText = '正在导出数据...'

      exportBikesToCSV(this.listQuery).then(response => {
        const blob = new Blob([response.data], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const fileName = '车辆数据_' + new Date().toISOString().slice(0, 10) + '.csv'

        if (window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveBlob(blob, fileName)
        } else {
          const url = window.URL.createObjectURL(blob)
          link.href = url
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        }

        this.loading = false
        this.showAlert = true
        this.alertType = 'success'
        this.alertMessage = '导出成功'
      }).catch(error => {
        console.error('导出车辆数据失败:', error)
        this.loading = false
        this.showAlert = true
        this.alertType = 'error'
        this.alertMessage = '导出失败: ' + (error.message || '未知错误')
      })
    },

    // 批量更新车辆状态
    async handleBatchDisable() {
      if (this.selectedBikes.length === 0) {
        this.$message.warning('请先选择要停用的车辆')
        return
      }

      try {
        this.$confirm(`确定要停用选中的 ${this.selectedBikes.length} 辆车吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          this.loading = true
          this.loadingStatusType = ''
          this.loadingProgress = 0
          this.loadingText = '正在批量更新车辆状态...'

          // 获取所有选中车辆的ID
          const bikeIds = this.selectedBikes.map(bike => bike.b_id)

          try {
            // 首先更新车辆状态为停用
            const response = await batchUpdateBikeStatus(bikeIds, '废弃')

            if (response && (response.code === 20000 || response.status === 'success')) {
              this.$message.success(`成功停用 ${bikeIds.length} 辆车`)

              // 更新本地数据状态
              this.selectedBikes.forEach(selectedBike => {
                const index = this.list.findIndex(item => item.b_id === selectedBike.b_id)
                if (index !== -1) {
                  this.$set(this.list[index], 'status', 'unavailable')
                }
              })

              // 对每个车辆调用禁用API，记录禁用原因
              const disablePromises = bikeIds.map(bikeId => {
                return disableVehicle(bikeId, {
                  reason: '批量禁用操作'
                }).catch(err => {
                  console.warn(`车辆 ${bikeId} 禁用记录创建失败:`, err)
                  return null
                })
              })

              // 等待所有禁用操作完成
              await Promise.all(disablePromises)

              // 更新统计信息
              this.updateStats()

              // 清空选择
              if (this.$refs.table) {
                this.$refs.table.clearSelection()
              }
              this.selectedBikes = []

              this.loadingStatusType = 'success'
              this.loadingProgress = 100
            } else {
              throw new Error(response?.message || '批量停用失败')
            }
          } catch (error) {
            console.error('批量停用失败:', error)
            this.$message.error('批量停用失败: ' + (error.message || '未知错误'))
            this.loadingStatusType = 'exception'
            this.loadingProgress = 100
          } finally {
            setTimeout(() => {
              this.loading = false
            }, 500)
          }
        }).catch(() => {
          this.$message.info('已取消批量停用操作')
        })
      } catch (error) {
        console.error('批量停用错误:', error)
        this.$message.error('操作失败，请重试')
      }
    },

    // 处理解除车辆禁用
    handleEnableVehicle(bike) {
      this.$confirm(`确定要解除车辆 ${bike.bike_number} 的禁用状态吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enableVehicle(bike.b_id)
          .then(response => {
            this.$message.success('解除禁用成功')
            this.fetchData()
          })
          .catch(error => {
            console.error('解除禁用失败:', error)
            this.$message.error('解除禁用失败: ' + (error.message || '未知错误'))
          })
      }).catch(() => {
        this.$message.info('已取消解除禁用操作')
      })
    },

    // 处理车辆禁用状态解除
    handleVehicleEnabled() {
      this.fetchData()
    },

    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedBikes = selection
      console.log('已选择车辆数量:', this.selectedBikes.length)
    },
    refreshBrandChart() {
      this.initBrandChart()
    },
    refreshTypeChart() {
      this.initTypeChart()
    },
    refreshColorChart() {
      this.initColorChart()
    },
    refreshStatusChart() {
      this.initStatusChart()
    },
    // 切换高级图表显示
    toggleAdvancedCharts() {
      this.showAdvancedCharts = !this.showAdvancedCharts

      if (this.showAdvancedCharts && this.showCharts) {
        this.$nextTick(() => {
          this.initColorChart()
          this.initStatusChart()
        })
      }
    },
    toggleChartsView() {
      this.showCharts = !this.showCharts
      this.chartButtonText = this.showCharts ? '隐藏图表' : '显示图表'
    },
    exportChart(chartType) {
      // 确定要导出的图表
      let chart = null
      let chartName = ''

      switch (chartType) {
        case 'brandChart':
          chart = this.brandChart
          chartName = '品牌分布图'
          break
        case 'typeChart':
          chart = this.typeChart
          chartName = '类型分布图'
          break
        case 'colorChart':
          chart = this.colorChart
          chartName = '颜色分布图'
          break
        case 'statusChart':
          chart = this.statusChart
          chartName = '状态分布图'
          break
        default:
          this.$message.error('未找到指定图表')
          return
      }

      if (!chart) {
        this.$message.error('图表尚未初始化')
        return
      }

      try {
        // 获取图表的base64数据URL
        const dataURL = chart.getDataURL({
          type: 'png',
          pixelRatio: 2, // 使用更高的像素比获得更好的质量
          backgroundColor: '#fff'
        })

        // 创建下载链接
        const link = document.createElement('a')
        link.download = `${chartName}_${new Date().toISOString().slice(0, 10)}.png`
        link.href = dataURL

        // 触发下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        this.$message.success('图表导出成功')
      } catch (error) {
        console.error('导出图表失败:', error)
        this.$message.error('导出图表失败: ' + (error.message || '未知错误'))
      }
    }
  }
}
</script>

<style lang="scss">
@import '@/styles/vehicle-management.scss';
.filter-container {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.loading-indicator {
  margin: 15px 0;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.loading-text {
  margin-top: 5px;
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.delete-dialog-content {
  margin: 20px 0;
  text-align: center;
}

.enable-dialog-content {
  margin: 20px 0;
  text-align: center;
}

.pagination-container {
  margin-top: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  margin-top: 5px;
  padding-left: 2px;
}

.color-dot {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
  border: 1px solid transparent;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
}

.bottom-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.user-info {
  margin-top: 15px;
}

.user-info-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  overflow: hidden;

  &:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
    transform: translateY(-3px);
  }
}

.user-info-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  color: #409EFF;
}

.user-info-content {
  display: flex;
  flex-wrap: wrap;
}

.user-info-item {
  margin-right: 20px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.item-label {
  color: #606266;
  margin-right: 5px;
}

.item-value {
  font-weight: bold;
}

.user-tip {
  margin-top: 15px;
  padding: 10px 15px;
  background-color: rgba(#409EFF, 0.1);
  border-radius: 8px;
  color: #606266;
  font-size: 14px;
  display: flex;
  align-items: center;

  i {
    margin-right: 8px;
    color: #409EFF;
    font-size: 16px;
  }
}

.dialog-tip {
  margin-bottom: 15px;
  padding: 12px 15px;
  background-color: rgba(#409EFF, 0.1);
  border-radius: 8px;
  color: #606266;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.dialog-tip i {
  margin-right: 8px;
  color: #409EFF;
  font-size: 16px;
}

.sync-status {
  display: inline-flex;
  align-items: center;
  margin-left: 15px;
}

.sync-time {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

/* 添加表单变更提示样式 */
.form-changed-tip {
  margin-top: 15px;
  padding: 12px 15px;
  background-color: rgba(#67C23A, 0.1);
  border-radius: 8px;
  color: #67c23a;
  font-size: 14px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 数据同步提示样式 */
.data-sync-tip {
  padding: 15px;
  margin: 15px 0;
  background-color: rgba(#409EFF, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.data-sync-tip i {
  margin-right: 10px;
  font-size: 18px;
  color: #409EFF;
}

.link-type {
  color: #409EFF;
  text-decoration: none;
}

.link-type:hover {
  text-decoration: underline;
}

.stat-card {
  border-radius: 8px;
  height: 100%;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 15px;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border: 1px solid #ebeef5;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  border-color: #dcdfe6;
}

.stat-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 5px 0;
}

.stat-icon-container {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.stat-icon-container i {
  font-size: 24px;
}

.stat-title {
  margin-left: 12px;
  font-weight: 600;
  color: #606266;
  font-size: 16px;
}

.stat-value {
  font-size: 30px;
  font-weight: 600;
  margin: 15px 0;
  color: #303133;
  flex-grow: 1;
  letter-spacing: 0.5px;
}

.stat-footer {
  margin-top: auto;
  padding: 10px 0;
  min-height: 40px;
}

.stat-percent {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
  display: block;
  font-weight: 500;
}

.el-progress {
  margin-top: 8px;
}

.stat-footer .el-button {
  padding: 5px 0;
  font-size: 13px;
  font-weight: 500;
}

.status-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
</style>
