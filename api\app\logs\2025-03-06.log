2025/03/06 22:52:11 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/06 22:52:11 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/03/06 22:52:11 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/06 22:52:14 root __init__.py[121] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/06 22:52:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:52:14] "[33mGET / HTTP/1.1[0m" 404 -
2025/03/06 22:52:14 root __init__.py[121] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/06 22:52:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:52:14] "[33mGET / HTTP/1.1[0m" 404 -
2025/03/06 22:52:14 root __init__.py[121] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/06 22:52:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:52:14] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025/03/06 22:52:51 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/06 22:52:51 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/03/06 22:52:51 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/06 22:52:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:52:54] "GET / HTTP/1.1" 200 -
2025/03/06 22:52:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:52:54] "GET /static/css/app.949a0224.css HTTP/1.1" 200 -
2025/03/06 22:52:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:52:54] "GET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1" 200 -
2025/03/06 22:52:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:52:54] "GET /static/css/chunk-libs.3dfb7769.css HTTP/1.1" 200 -
2025/03/06 22:52:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:52:54] "GET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1" 200 -
2025/03/06 22:52:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:52:55] "GET /static/js/app.a7289db6.js HTTP/1.1" 200 -
2025/03/06 22:52:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:52:55] "GET /static/js/chunk-libs.4a5831c0.js HTTP/1.1" 200 -
2025/03/06 22:52:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:52:55] "GET /static/css/chunk-a8539586.94702ff7.css HTTP/1.1" 200 -
2025/03/06 22:52:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:52:55] "GET /static/js/chunk-a8539586.06bed4b6.js HTTP/1.1" 200 -
2025/03/06 22:53:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:53:06] "GET /static/fonts/element-icons.535877f5.woff HTTP/1.1" 200 -
2025/03/06 22:53:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:53:06] "GET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1" 200 -
2025/03/06 22:53:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:53:06] "GET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1" 200 -
2025/03/06 22:53:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:53:10] "GET /?imageView2/1/w/80/h/80 HTTP/1.1" 200 -
2025/03/06 22:53:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:53:58] "[36mGET /?imageView2/1/w/80/h/80 HTTP/1.1[0m" 304 -
2025/03/06 22:54:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:54:16] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/06 22:54:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:54:16] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/06 22:54:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:54:35] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/06 22:54:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:54:35] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/06 22:57:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:57:04] "OPTIONS /users/login HTTP/1.1" 200 -
2025/03/06 22:57:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:57:04] "[31m[1mPOST /users/login HTTP/1.1[0m" 405 -
2025/03/06 22:57:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:57:52] "OPTIONS /users/login HTTP/1.1" 200 -
2025/03/06 22:57:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:57:52] "[31m[1mPOST /users/login HTTP/1.1[0m" 405 -
2025/03/06 22:58:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:58:09] "OPTIONS /users/login HTTP/1.1" 200 -
2025/03/06 22:58:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:58:09] "[31m[1mPOST /users/login HTTP/1.1[0m" 405 -
2025/03/06 22:59:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:59:03] "OPTIONS /users/login HTTP/1.1" 200 -
2025/03/06 22:59:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:59:03] "[31m[1mPOST /users/login HTTP/1.1[0m" 405 -
2025/03/06 22:59:13 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/06 22:59:13 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/03/06 22:59:13 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/06 22:59:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:59:17] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/06 22:59:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:59:17] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/06 22:59:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:59:28] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/06 22:59:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 22:59:28] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/06 23:00:59 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/06 23:00:59 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/03/06 23:00:59 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/06 23:01:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:01:05] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/06 23:01:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:01:05] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/06 23:01:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:01:24] "OPTIONS /users/login HTTP/1.1" 200 -
2025/03/06 23:01:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:01:25] "[31m[1mPOST /users/login HTTP/1.1[0m" 405 -
2025/03/06 23:05:28 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/06 23:05:28 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/03/06 23:05:28 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/06 23:05:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:05:32] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/06 23:05:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:05:32] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/06 23:06:45 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/06 23:06:45 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/03/06 23:06:45 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/06 23:06:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:06:51] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/06 23:06:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:06:51] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/06 23:07:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:07:12] "OPTIONS /api/api/users/login HTTP/1.1" 200 -
2025/03/06 23:07:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:07:12] "[31m[1mPOST /api/api/users/login HTTP/1.1[0m" 405 -
2025/03/06 23:10:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:10:04] "OPTIONS /api/api/users/login HTTP/1.1" 200 -
2025/03/06 23:10:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:10:04] "[31m[1mPOST /api/api/users/login HTTP/1.1[0m" 405 -
2025/03/06 23:13:49 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/06 23:13:49 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/03/06 23:13:49 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/06 23:14:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:14:02] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/06 23:14:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:14:02] "[31m[1mPOST /api/login HTTP/1.1[0m" 405 -
2025/03/06 23:14:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:14:32] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/06 23:14:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:14:32] "[31m[1mPOST /api/login HTTP/1.1[0m" 405 -
2025/03/06 23:15:10 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/06 23:15:10 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/03/06 23:15:10 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/06 23:15:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:15:16] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/06 23:15:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:15:16] "[31m[1mPOST /api/login HTTP/1.1[0m" 405 -
2025/03/06 23:18:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:18:36] "OPTIONS /login HTTP/1.1" 200 -
2025/03/06 23:18:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:18:36] "[31m[1mPOST /login HTTP/1.1[0m" 405 -
2025/03/06 23:18:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:18:49] "OPTIONS /login HTTP/1.1" 200 -
2025/03/06 23:18:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:18:49] "[31m[1mPOST /login HTTP/1.1[0m" 405 -
2025/03/06 23:18:53 root __init__.py[120] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/06 23:18:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:18:53] "[33mGET /login HTTP/1.1[0m" 404 -
2025/03/06 23:22:37 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/06 23:22:37 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/03/06 23:22:37 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/06 23:22:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:22:41] "OPTIONS /login HTTP/1.1" 200 -
2025/03/06 23:22:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:22:41] "[31m[1mPOST /login HTTP/1.1[0m" 405 -
2025/03/06 23:23:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:23:01] "OPTIONS /login HTTP/1.1" 200 -
2025/03/06 23:23:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:23:01] "[31m[1mPOST /login HTTP/1.1[0m" 405 -
2025/03/06 23:23:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:23:07] "OPTIONS /login HTTP/1.1" 200 -
2025/03/06 23:23:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:23:07] "[31m[1mPOST /login HTTP/1.1[0m" 405 -
2025/03/06 23:24:03 root __init__.py[120] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/06 23:24:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Mar/2025 23:24:03] "[33mGET /login HTTP/1.1[0m" 404 -
