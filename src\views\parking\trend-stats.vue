<template>
  <div class="app-container">
    <div class="dashboard-header">
      <h2>停车场趋势分析</h2>
      <div>
        <el-button type="primary" icon="el-icon-refresh" @click="fetchData">刷新数据</el-button>
      </div>
    </div>



    <!-- 统计概览卡片 -->
    <el-row v-loading="loading" :gutter="20" class="stat-cards">
      <el-col :xs="12" :sm="8" :md="6" :lg="4" v-for="(item, index) in overviewItems" :key="index">
        <el-card shadow="hover" class="stat-card" :body-style="{ padding: '15px' }">
          <div class="card-content">
            <div class="card-icon" :style="{ backgroundColor: item.color }">
              <i :class="item.icon"></i>
            </div>
            <div class="card-data">
              <div class="card-value">{{ item.value }}</div>
              <div class="card-label">{{ item.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 停车记录趋势图 -->
    <el-card v-loading="loading" shadow="hover" class="chart-card">
      <div slot="header" class="clearfix">
        <span>停车记录趋势</span>
        <el-radio-group v-model="trendChartType" size="mini" style="float: right;">
          <el-radio-button label="daily">日视图</el-radio-button>
          <el-radio-button label="weekly">周视图</el-radio-button>
          <el-radio-button label="monthly">月视图</el-radio-button>
        </el-radio-group>
      </div>
      <div class="chart-container">
        <div ref="trendChart" class="chart"></div>
      </div>
    </el-card>



    <!-- 高峰时段分析 -->
    <el-card v-loading="loading" shadow="hover" class="chart-card">
      <div slot="header" class="clearfix">
        <span>高峰时段分析</span>
      </div>
      <div class="chart-container">
        <div ref="peakHoursChart" class="chart"></div>
      </div>
    </el-card>


  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import request from '@/utils/request'
// 导入完整的echarts库，避免模块导入问题
import * as echarts from 'echarts'

export default {
  name: 'ParkingTrendStats',
  data() {
    return {
      loading: true,
      stats: {
        overview: {
          total_records: 0,
          active_records: 0,
          completed_records: 0,
          abnormal_records: 0,
          today_records: 0
        },
        parking_lots: [],
        vehicle_types: [],
        daily_stats: [],
        duration_stats: [],
        peak_hour: null
      },
      trendChartType: 'daily',
      charts: {
        trendChart: null,
        peakHoursChart: null
      }
    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ]),
    isAdmin() {
      return this.roles.includes('admin')
    },
    overviewItems() {
      const overview = this.stats.overview || {}

      return [
        {
          label: '总停车记录',
          value: overview.total_records || 0,
          icon: 'el-icon-s-data',
          color: '#409EFF'
        },
        {
          label: '进行中记录',
          value: overview.active_records || 0,
          icon: 'el-icon-time',
          color: '#67C23A'
        },
        {
          label: '已完成记录',
          value: overview.completed_records || 0,
          icon: 'el-icon-check',
          color: '#E6A23C'
        },
        {
          label: '今日停车',
          value: overview.today_records || 0,
          icon: 'el-icon-date',
          color: '#F56C6C'
        },
        {
          label: '异常记录',
          value: overview.abnormal_records || 0,
          icon: 'el-icon-warning',
          color: '#9B59B6'
        }
      ]
    }
  },
  watch: {
    trendChartType() {
      this.renderTrendChart()
    }
  },
  created() {
    // 允许管理员和保安访问
    if (!this.roles.includes('admin') && !this.roles.includes('security')) {
      this.$message.error('您没有权限访问此页面')
      this.$router.push('/')
      return
    }
    this.fetchData()
  },
  mounted() {
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts)
    this.disposeCharts()
  },
  methods: {
    fetchData() {
      this.loading = true
      console.log('开始获取停车统计数据')

      // 构建查询参数
      const params = {
        date_range: 'week', // 默认获取一周的数据
        _t: new Date().getTime() // 添加时间戳防止缓存
      }

      request({
        url: '/api/parking-records/stats',
        method: 'get',
        params: params
      }).then(response => {
        console.log('获取停车统计数据成功:', response)

        let responseData = response
        if (response.data) {
          responseData = response.data
        }

        // 检查数据完整性并设置默认值
        this.ensureDataIntegrity(responseData)

        // 更新统计数据
        this.stats = responseData
        this.loading = false

        // 渲染图表
        this.$nextTick(() => {
          this.initCharts()
        })
      }).catch(error => {
        console.error('获取停车统计数据失败', error)
        this.$message.error('获取停车统计数据失败')
        this.loading = false
      })
    },

    // 确保数据完整性
    ensureDataIntegrity(data) {
      // 确保 overview 数据存在
      if (!data.overview) {
        data.overview = {
          total_records: 0,
          active_records: 0,
          completed_records: 0,
          abnormal_records: 0,
          today_records: 0
        }
      }

      // 确保 daily_stats 数据存在
      if (!data.daily_stats || !Array.isArray(data.daily_stats)) {
        data.daily_stats = []
      }

      // 确保 vehicle_types 数据存在
      if (!data.vehicle_types || !Array.isArray(data.vehicle_types)) {
        data.vehicle_types = []
      }

      // 确保 parking_lots 数据存在
      if (!data.parking_lots || !Array.isArray(data.parking_lots)) {
        data.parking_lots = []
      }

      // 确保 duration_stats 数据存在
      if (!data.duration_stats || !Array.isArray(data.duration_stats)) {
        data.duration_stats = []
      }
    },



    // 初始化所有图表
    initCharts() {
      this.initTrendChart()
      this.initPeakHoursChart()
    },

    // 初始化停车记录趋势图
    initTrendChart() {
      if (this.charts.trendChart) {
        this.charts.trendChart.dispose()
      }
      this.charts.trendChart = echarts.init(this.$refs.trendChart)
      this.renderTrendChart()
    },

    // 渲染停车记录趋势图
    renderTrendChart() {
      if (!this.charts.trendChart) return

      const dailyStats = this.stats.daily_stats || []

      // 检查是否有数据
      if (!dailyStats || dailyStats.length === 0) {
        console.warn('没有每日趋势数据')
        // 设置空数据图表
        this.charts.trendChart.setOption({
          title: {
            text: '暂无停车记录数据',
            left: 'center',
            top: 'center'
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              type: 'line',
              data: []
            }
          ]
        })
        return
      }

      // 检查数据是否全为零
      const hasNonZeroData = dailyStats.some(item =>
        (item.count && item.count > 0) ||
        (item.completed && item.completed > 0) ||
        (item.active && item.active > 0)
      )

      if (!hasNonZeroData) {
        console.warn('所有停车记录数据都为零')
        // 设置全零数据图表
        const dates = dailyStats.map(item => {
          const date = new Date(item.date)
          return `${date.getMonth() + 1}/${date.getDate()}`
        })

        this.charts.trendChart.setOption({
          title: {
            text: '停车记录趋势',
            left: 'center',
            subtext: '当前时间范围内无停车记录数据'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            data: ['总记录', '已完成', '进行中'],
            bottom: 10
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: dates,
            axisLabel: {
              rotate: 45
            }
          },
          yAxis: {
            type: 'value',
            name: '停车记录数',
            minInterval: 1
          },
          series: [
            {
              name: '总记录',
              type: 'line',
              data: Array(dates.length).fill(0),
              smooth: true,
              symbol: 'circle',
              symbolSize: 8,
              itemStyle: {
                color: '#409EFF'
              },
              lineStyle: {
                width: 3
              }
            },
            {
              name: '已完成',
              type: 'bar',
              data: Array(dates.length).fill(0),
              itemStyle: {
                color: '#67C23A'
              },
              stack: 'status'
            },
            {
              name: '进行中',
              type: 'bar',
              data: Array(dates.length).fill(0),
              itemStyle: {
                color: '#E6A23C'
              },
              stack: 'status'
            }
          ]
        })
        return
      }

      // 根据视图类型处理数据
      let chartData = []
      let xAxisData = []

      if (this.trendChartType === 'daily') {
        // 日视图：直接使用每日数据
        chartData = dailyStats
        xAxisData = dailyStats.map(item => {
          const date = new Date(item.date)
          return `${date.getMonth() + 1}/${date.getDate()}`
        })
      } else if (this.trendChartType === 'weekly') {
        // 周视图：按周聚合数据
        // 这里简化处理，实际应用中可能需要更复杂的逻辑
        const weeklyData = {}
        dailyStats.forEach(item => {
          const date = new Date(item.date)
          const weekNum = Math.floor(date.getDate() / 7) + 1
          const weekKey = `第${weekNum}周`

          if (!weeklyData[weekKey]) {
            weeklyData[weekKey] = { count: 0, completed: 0, active: 0 }
          }

          weeklyData[weekKey].count += item.count || 0
          weeklyData[weekKey].completed += item.completed || 0
          weeklyData[weekKey].active += item.active || 0
        })

        xAxisData = Object.keys(weeklyData)
        chartData = xAxisData.map(key => ({
          date: key,
          ...weeklyData[key]
        }))
      } else if (this.trendChartType === 'monthly') {
        // 月视图：按月聚合数据
        const monthlyData = {}
        dailyStats.forEach(item => {
          const date = new Date(item.date)
          const monthKey = `${date.getMonth() + 1}月`

          if (!monthlyData[monthKey]) {
            monthlyData[monthKey] = { count: 0, completed: 0, active: 0 }
          }

          monthlyData[monthKey].count += item.count || 0
          monthlyData[monthKey].completed += item.completed || 0
          monthlyData[monthKey].active += item.active || 0
        })

        xAxisData = Object.keys(monthlyData)
        chartData = xAxisData.map(key => ({
          date: key,
          ...monthlyData[key]
        }))
      }

      // 准备图表数据
      const totalCounts = chartData.map(item => item.count || 0)
      const completedCounts = chartData.map(item => item.completed || 0)
      const activeCounts = chartData.map(item => item.active || 0)

      // 添加调试日志
      console.log('趋势图表数据:', {
        xAxisData,
        totalCounts,
        completedCounts,
        activeCounts
      })

      // 设置图表选项
      const option = {
        title: {
          text: '停车记录趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['总记录', '已完成', '进行中'],
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          name: '停车记录数',
          minInterval: 1
        },
        series: [
          {
            name: '总记录',
            type: 'line',
            data: totalCounts,
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#409EFF'
            },
            lineStyle: {
              width: 3
            }
          },
          {
            name: '已完成',
            type: 'bar',
            data: completedCounts,
            itemStyle: {
              color: '#67C23A'
            },
            stack: 'status'
          },
          {
            name: '进行中',
            type: 'bar',
            data: activeCounts,
            itemStyle: {
              color: '#E6A23C'
            },
            stack: 'status'
          }
        ]
      }

      this.charts.trendChart.setOption(option)
    },



    // 初始化高峰时段分析图
    initPeakHoursChart() {
      if (this.charts.peakHoursChart) {
        this.charts.peakHoursChart.dispose()
      }
      this.charts.peakHoursChart = echarts.init(this.$refs.peakHoursChart)

      // 获取后端提供的高峰时段数据
      const peakHour = this.stats.peak_hour || null

      // 构建查询参数，获取停车统计数据
      const params = {
        date_range: 'week',
        _t: new Date().getTime() // 添加时间戳防止缓存
      }

      // 请求按小时分布的停车记录数据
      request({
        url: '/api/parking-records/stats',
        method: 'get',
        params: params
      }).then(response => {
        console.log('获取停车统计数据成功:', response)

        let hourData = []
        // 使用默认的24小时数据结构
        hourData = Array.from({ length: 24 }, (_, i) => ({
          hour: i,
          count: 0
        }))

        // 如果有hourly_stats数据，使用实际的每小时数据
        if (response.data && response.data.hourly_stats && Array.isArray(response.data.hourly_stats)) {
          response.data.hourly_stats.forEach(hourStat => {
            if (hourStat.hour >= 0 && hourStat.hour < 24) {
              hourData[hourStat.hour].count = hourStat.count
            }
          })
        }
        // 如果没有hourly_stats数据，但有peak_hour数据，则使用旧的逻辑
        else if (response.data && response.data.peak_hour) {
          const peakHour = response.data.peak_hour
          const hourMatch = peakHour.match(/^(\d+):/)
          if (hourMatch && hourMatch[1]) {
            const peakHourNum = parseInt(hourMatch[1])
            if (peakHourNum >= 0 && peakHourNum < 24) {
              // 设置一个较高的值以突出显示
              hourData[peakHourNum].count = 100
            }
          }
        }

        // 准备图表数据
        const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`)
        const counts = Array(24).fill(0)

        // 填充实际数据
        hourData.forEach(item => {
          if (item.hour >= 0 && item.hour < 24) {
            counts[item.hour] = item.count
          }
        })

        // 找出高峰时段
        const maxCount = Math.max(...counts)
        const peakHours = hours.filter((_, i) => counts[i] > maxCount * 0.7)

        // 设置图表选项
        const option = {
          title: {
            text: '停车高峰时段分析',
            left: 'center',
            subtext: response.data && response.data.peak_hour ? `高峰时段: ${response.data.peak_hour}` : '暂无高峰时段数据'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: hours,
            axisLabel: {
              interval: 1,
              rotate: 45
            }
          },
          yAxis: {
            type: 'value',
            name: '停车记录数',
            minInterval: 1
          },
          series: [
            {
              name: '停车记录',
              type: 'bar',
              data: counts,
              itemStyle: {
                color: params => {
                  const value = params.value
                  return value > maxCount * 0.7 ? '#F56C6C' : '#409EFF'
                }
              },
              markLine: {
                data: [
                  { type: 'average', name: '平均值' }
                ]
              }
            }
          ]
        }

        this.charts.peakHoursChart.setOption(option)
      }).catch(error => {
        console.error('获取停车统计数据失败', error)

        // 如果API请求失败，使用已有的peak_hour数据
        const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`)
        const counts = Array(24).fill(0)

        // 如果有peak_hour数据，在对应时段增加计数
        if (peakHour) {
          const hourMatch = peakHour.match(/^(\d+):/)
          if (hourMatch && hourMatch[1]) {
            const peakHourNum = parseInt(hourMatch[1])
            if (peakHourNum >= 0 && peakHourNum < 24) {
              counts[peakHourNum] = 100 // 设置一个较高的值以突出显示
            }
          }
        }

        const option = {
          title: {
            text: '停车高峰时段分析',
            left: 'center',
            subtext: peakHour ? `高峰时段: ${peakHour}` : '暂无高峰时段数据'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: hours,
            axisLabel: {
              interval: 1,
              rotate: 45
            }
          },
          yAxis: {
            type: 'value',
            name: '停车记录数',
            minInterval: 1
          },
          series: [
            {
              name: '停车记录',
              type: 'bar',
              data: counts,
              itemStyle: {
                color: params => {
                  if (peakHour) {
                    const hourMatch = peakHour.match(/^(\d+):/)
                    if (hourMatch && hourMatch[1]) {
                      const peakHourNum = parseInt(hourMatch[1])
                      return params.dataIndex === peakHourNum ? '#F56C6C' : '#409EFF'
                    }
                  }
                  return '#409EFF'
                }
              },
              markLine: {
                data: [
                  { type: 'average', name: '平均值' }
                ]
              }
            }
          ]
        }

        this.charts.peakHoursChart.setOption(option)
      })
    },



    // 获取车辆类型名称
    getVehicleTypeName(type) {
      const typeMap = {
        'car': '小汽车',
        'suv': 'SUV',
        'truck': '卡车',
        'motorcycle': '摩托车',
        'bicycle': '自行车',
        'electric': '电动车'
      }
      return typeMap[type] || type
    },

    // 调整图表大小
    resizeCharts() {
      Object.values(this.charts).forEach(chart => {
        chart && chart.resize()
      })
    },

    // 销毁图表
    disposeCharts() {
      Object.values(this.charts).forEach(chart => {
        chart && chart.dispose()
      })
    },


  }
}
</script>

<style lang="scss" scoped>
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    font-size: 20px;
  }
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.stat-cards {
  margin-bottom: 20px;
}

.stat-card {
  margin-bottom: 15px;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .card-content {
    display: flex;
    align-items: center;

    .card-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        color: white;
        font-size: 20px;
      }
    }

    .card-data {
      flex: 1;

      .card-value {
        font-size: 24px;
        font-weight: bold;
        line-height: 1.2;
      }

      .card-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

// 响应式调整
@media (max-width: 768px) {
  .chart-container {
    height: 300px;
  }

  .stat-card {
    .card-content {
      .card-data {
        .card-value {
          font-size: 20px;
        }
      }
    }
  }
}
</style>
