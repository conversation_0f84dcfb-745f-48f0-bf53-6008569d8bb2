<template>
  <div class="login-container">
    <!-- 系统标题和校徽 -->
    <div class="system-header">
      <div class="logo-container">
        <img src="/cumt_logo.jpg" alt="校徽" class="school-logo">
      </div>
      <h1 class="system-title">校园电动车管理系统</h1>
    </div>

    <el-form v-if="!showRegister" ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on" label-position="left">
      <div class="title-container">
        <h3 class="title">系统登录</h3>
      </div>

      <el-form-item prop="username">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
        <el-input
          ref="username"
          v-model="loginForm.username"
          placeholder="用户名"
          name="username"
          type="text"
          tabindex="1"
          auto-complete="on"
        />
      </el-form-item>

      <el-form-item prop="password">
        <span class="svg-container">
          <svg-icon icon-class="password" />
        </span>
        <el-input
          :key="passwordType"
          ref="password"
          v-model="loginForm.password"
          :type="passwordType"
          placeholder="密码"
          name="password"
          tabindex="2"
          auto-complete="on"
          @keyup.enter.native="handleLogin"
        />
        <span class="show-pwd" @click="showPwd">
          <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
        </span>
      </el-form-item>

      <el-button :loading="loading" type="primary" style="width:100%;margin-bottom:30px;" @click.native.prevent="handleLogin">登录</el-button>

      <div class="tips">
        <span style="margin-right:20px;">还没有账号？</span>
        <el-button type="text" @click="toggleForm">注册</el-button>
      </div>
    </el-form>

    <!-- 注册表单 -->
    <el-form v-if="showRegister" ref="registerForm" :model="registerForm" :rules="registerRules" class="login-form" auto-complete="off" label-position="left">
      <div class="title-container">
        <h3 class="title">用户注册</h3>
      </div>

      <el-form-item prop="username">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
        <el-input
          ref="registerUsername"
          v-model="registerForm.username"
          placeholder="用户名"
          name="username"
          type="text"
          tabindex="1"
          auto-complete="off"
        />
      </el-form-item>

      <el-form-item prop="password">
        <span class="svg-container">
          <svg-icon icon-class="password" />
        </span>
        <el-input
          ref="registerPassword"
          v-model="registerForm.password"
          :type="passwordType"
          placeholder="密码（不少于6位）"
          name="password"
          tabindex="2"
          auto-complete="off"
        />
        <span class="show-pwd" @click="showPwd">
          <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
        </span>
      </el-form-item>

      <el-form-item prop="confirmPassword">
        <span class="svg-container">
          <svg-icon icon-class="password" />
        </span>
        <el-input
          ref="confirmPassword"
          v-model="registerForm.confirmPassword"
          :type="passwordType"
          placeholder="确认密码"
          name="confirmPassword"
          tabindex="3"
          auto-complete="off"
        />
      </el-form-item>

      <el-form-item prop="belong" label="学院">
        <el-input
          v-model="registerForm.belong"
          placeholder="学院（可选）"
          name="belong"
          tabindex="4"
        />
      </el-form-item>

      <el-form-item prop="phone" label="电话">
        <el-input
          v-model="registerForm.phone"
          placeholder="电话（可选）"
          name="phone"
          tabindex="5"
        />
      </el-form-item>

      <el-button :loading="registerLoading" type="primary" style="width:100%;margin-bottom:30px;" @click.native.prevent="handleRegister">注册</el-button>

      <div class="tips">
        <span style="margin-right:20px;">已有账号？</span>
        <el-button type="text" @click="toggleForm">返回登录</el-button>
      </div>
    </el-form>

    <!-- 系统说明提示 -->
    <div class="system-tips">
      <el-button type="text" @click="showSystemTips = true">系统账号说明</el-button>
    </div>

    <!-- 系统说明弹窗 -->
    <Notice :show.sync="showSystemTips" :show-footer="true">
      <div class="tips-content">
        <h3>系统账号说明</h3>
        <el-divider></el-divider>
        <p><b>默认账号:</b></p>
        <ul>
          <li>管理员账号: admin / 111111</li>
          <li>普通用户账号: aba / 111111</li>
        </ul>
        <p><b>自行注册:</b></p>
        <ul>
          <li>请记住您注册时设置的密码</li>
          <li>每个用户名只能注册一次</li>
          <li>注册完成后，使用注册的用户名和密码登录</li>
        </ul>
        <p><b>若遇到问题:</b></p>
        <ul>
          <li>确保您的输入正确</li>
          <li>尝试清除浏览器缓存后再试</li>
          <li>如果仍有问题，请联系系统管理员</li>
        </ul>
        <div class="tips-footer">
          <small>© 2025 中国矿业大学 - 校园电动车管理系统</small>
        </div>
      </div>
    </Notice>
  </div>
</template>

<script>
import { validUsername } from '@/utils/validate'
import axios from 'axios'
import Notice from '@/components/Notice'

export default {
  name: 'Login',
  components: {
    Notice
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error('请输入有效的用户名'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('密码不能少于6位'))
      } else {
        callback()
      }
    }
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.registerForm.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    const validatePhone = (rule, value, callback) => {
      if (value && !/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error('请输入正确的手机号码'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: ''
      },
      registerForm: {
        username: '',
        password: '',
        confirmPassword: '',
        belong: '',
        phone: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      registerRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { validator: validateUsername, trigger: 'blur' },
          { validator: this.checkUsernameExists, trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ],
        phone: [
          { validator: validatePhone, trigger: 'blur' }
        ]
      },
      loading: false,
      registerLoading: false,
      passwordType: 'password',
      redirect: undefined,
      showRegister: false, // 控制显示登录还是注册表单
      checkingUsername: false, // 是否正在检查用户名
      showSystemTips: false
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    // 如果URL中包含username参数，自动填充用户名
    const urlParams = new URLSearchParams(window.location.search)
    const username = urlParams.get('username')
    if (username) {
      this.loginForm.username = username
      // 聚焦到密码输入框
      this.$nextTick(() => {
        this.$refs.password && this.$refs.password.focus()
      })
    }
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        if (!this.showRegister) {
          this.$refs.password.focus()
        }
      })
    },
    // 切换登录/注册表单
    toggleForm() {
      this.showRegister = !this.showRegister
      this.$nextTick(() => {
        if (this.showRegister) {
          this.registerForm = {
            username: '',
            password: '',
            confirmPassword: '',
            belong: '',
            phone: ''
          }
        } else {
          this.loginForm = {
            username: '',
            password: ''
          }
        }
      })
    },
    // 处理登录
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          // 将表单数据映射到后端期望的格式
          const loginData = {
            username: this.loginForm.username,
            password: this.loginForm.password
          }

          console.log('提交登录数据:', {
            username: loginData.username,
            password: loginData.password ? '******' : undefined
          })

          this.$store.dispatch('user/login', loginData)
            .then(() => {
              console.log('登录成功，检查用户信息状态')
              console.log('登录后用户名:', this.$store.getters.name)
              console.log('登录后用户ID:', this.$store.getters.userId)
              console.log('登录后localStorage用户名:', localStorage.getItem('userName'))

              this.$message.success('登录成功')

              // 记录最近登录的用户名到本地存储，方便下次使用
              try {
                localStorage.setItem('last_login_username', loginData.username)
              } catch (e) {
                console.warn('保存用户名到本地存储失败:', e)
              }

              // 确保登录成功后跳转到首页，不刷新页面
              this.$router.push({ path: this.redirect || '/' })
              this.loading = false
            })
            .catch(error => {
              this.loading = false

              // 错误处理 - 确保errorMsg是字符串
              let errorMsg = ''
              if (typeof error === 'string') {
                errorMsg = error
              } else if (error && error.message) {
                errorMsg = error.message
              } else if (error && error.toString) {
                errorMsg = error.toString()
              } else {
                errorMsg = '登录失败，请检查用户名和密码'
              }

              console.log('登录错误:', errorMsg)

              // 用户名不存在的提示
              if (errorMsg.includes && (errorMsg.includes('用户不存在') || errorMsg.includes('404'))) {
                this.$message({
                  message: '该用户名未注册，请先注册账号',
                  type: 'warning',
                  duration: 5000,
                  showClose: true
                })

                // 询问是否要注册
                this.$confirm('该用户名未注册，是否立即注册?', '提示', {
                  confirmButtonText: '去注册',
                  cancelButtonText: '取消',
                  type: 'info'
                }).then(() => {
                  // 用户选择去注册
                  this.showRegister = true
                  this.registerForm.username = this.loginForm.username
                }).catch(() => {
                  // 用户取消
                })
              } else if (errorMsg.includes && (errorMsg.includes('密码不正确') || errorMsg.includes('401'))) {
                // 密码错误提示
                this.$message.error('密码不正确，请重新输入')
                // 清空密码
                this.loginForm.password = ''
                // 聚焦密码输入框
                this.$nextTick(() => {
                  this.$refs.password.focus()
                })
              } else {
                // 其他错误
                this.$message.error(errorMsg)

                // 显示更详细的错误提示
                this.$notify({
                  title: '登录失败',
                  message: '请确保您输入了正确的账号和密码。注册账号后请使用您注册时设置的密码登录，不是默认的111111。',
                  type: 'warning',
                  duration: 10000,
                  showClose: true
                })
              }
            })
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    },
    // 处理注册
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          this.registerLoading = true
          this.$store.dispatch('user/register', {
            username: this.registerForm.username,
            password: this.registerForm.password,
            confirmPassword: this.registerForm.confirmPassword,
            belong: this.registerForm.belong,
            phone: this.registerForm.phone
          }).then(() => {
            this.registerLoading = false
            this.$message({
              message: '注册成功，您现在可以登录',
              type: 'success'
            })
            // 注册成功后自动切换到登录表单
            this.showRegister = false
            // 填充登录表单的用户名
            this.loginForm.username = this.registerForm.username
            // 清空注册表单
            this.registerForm = {
              username: '',
              password: '',
              confirmPassword: '',
              belong: '',
              phone: ''
            }
          }).catch(error => {
            console.error('注册失败:', error)
            this.registerLoading = false
            const message = error?.data?.message || '注册失败，请重试'
            this.$message({
              message,
              type: 'error'
            })
          })
        } else {
          console.log('注册表单验证失败')
          return false
        }
      })
    },
    // 检查用户名是否存在
    checkUsernameExists(rule, value, callback) {
      // 如果用户名为空，跳过检查
      if (!value) {
        callback()
        return
      }

      // 避免重复检查
      if (this.checkingUsername) {
        callback()
        return
      }

      this.checkingUsername = true

      // 发送请求检查用户名是否已存在
      axios.get(`${process.env.VUE_APP_BASE_API}/api/users`)
        .then(response => {
          this.checkingUsername = false

          if (response.data && response.data.users) {
            const users = response.data.users
            const userExists = users.some(user => user.u_name === value)

            if (userExists) {
              callback(new Error('该用户名已被占用，请尝试其他用户名'))
            } else {
              callback()
            }
          } else {
            // 数据格式不对，跳过检查
            callback()
          }
        })
        .catch(() => {
          this.checkingUsername = false
          // 发生错误，跳过检查
          callback()
        })
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg:#f5f5f5; /* 灰白色背景 */
$form_bg:#ffffff; /* 表单白色背景 */
$text_color:#333; /* 文本颜色 */
$primary_color:#1a5bac; /* 主色调 - 蓝色 */
$cursor: #333;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $text_color;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $form_bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid #dcdfe6;
    background: $form_bg;
    border-radius: 5px;
    color: $text_color;
  }

  /* 注册表单样式调整 */
  .el-form-item__label {
    color: $text_color;
    padding-left: 15px;
  }

  /* 按钮样式 */
  .el-button--primary {
    background-color: $primary_color;
    border-color: $primary_color;

    &:hover, &:focus {
      background-color: lighten($primary_color, 10%);
      border-color: lighten($primary_color, 10%);
    }
  }
}
</style>

<style lang="scss" scoped>
$bg:#f5f5f5; /* 灰白色背景 */
$dark_gray:#666;
$text_color:#333;
$primary_color:#1a5bac; /* 主色调 - 蓝色 */

.login-container {
  min-height: 100vh;
  width: 100%;
  background-color: $bg;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-top: 120px;

  /* 系统标题和校徽样式 */
  .system-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;

    .logo-container {
      width: 300px;
      height: 100px;
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
      align-items: center;

      .school-logo {
        width: 100%;
        height: 100%;
        object-fit: contain;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
      }
    }

    .system-title {
      font-size: 30px;
      color: $primary_color;
      font-weight: bold;
      text-align: center;
      margin: 0;
      letter-spacing: 2px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }

  .login-form {
    position: relative;
    width: 400px;
    max-width: 100%;
    padding: 30px;
    margin: 0 auto;
    overflow: hidden;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .tips {
    font-size: 14px;
    color: $dark_gray;
    margin-bottom: 10px;
    text-align: center;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $primary_color;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .system-tips {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    text-align: center;

    .el-button--text {
      color: $primary_color;
      font-size: 14px;

      &:hover {
        color: lighten($primary_color, 10%);
      }
    }
  }

  /* 系统说明弹窗样式 */
  :deep(.tips-content) {
    h3 {
      color: $primary_color;
      text-align: center;
      font-size: 20px;
      margin-top: 0;
    }

    ul {
      padding-left: 20px;
    }

    .tips-footer {
      margin-top: 20px;
      width: 100%;
      text-align: center;
      color: $dark_gray;
    }
  }

  .tips-content {
    text-align: left;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #409EFF;
    }

    ul {
      padding-left: 20px;
      margin-bottom: 15px;
    }

    li {
      margin-bottom: 5px;
    }
  }
}
</style>
