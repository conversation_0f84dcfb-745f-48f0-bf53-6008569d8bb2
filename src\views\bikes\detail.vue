<template>
  <div class="app-container">
    <!-- 返回按钮 -->
    <div class="back-button">
      <el-button icon="el-icon-back" @click="goBack">返回列表</el-button>
    </div>

    <!-- 加载状态 -->
    <div v-loading="loading" class="loading-container">
      <!-- 车辆详情卡片 -->
      <el-card v-if="bikeData" class="bike-detail-card">
        <div slot="header" class="card-header">
          <span>车辆详情</span>
          <div class="header-actions">
            <el-button
              type="primary"
              size="small"
              icon="el-icon-edit"
              @click="handleEdit"
            >
              编辑
            </el-button>
          </div>
        </div>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="车牌号">
            <el-tag :type="bikeData.bike_number.startsWith('临时车牌') ? 'warning' : ''">
              {{ bikeData.bike_number }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="所属用户">
            {{ bikeData.user_id }}
          </el-descriptions-item>

          <el-descriptions-item label="品牌">
            {{ bikeData.brand }}
          </el-descriptions-item>

          <el-descriptions-item label="颜色">
            <div class="color-display">
              <span
                class="color-dot"
                :style="{
                  backgroundColor: getColorCode(bikeData.color),
                  borderColor: getColorBorder(bikeData.color)
                }"
              />
              {{ bikeData.color }}
            </div>
          </el-descriptions-item>

          <el-descriptions-item label="类型">
            {{ bikeData.type }}
          </el-descriptions-item>

          <el-descriptions-item label="状态">
            <div class="status-container">
              <el-tag :type="bikeData.status === 'available' ? 'success' : 'danger'">
                {{ getStatusText(bikeData.status) }}
              </el-tag>
              <vehicle-disable-status
                :vehicle-id="bikeData.b_id"
                :disable-info="disableInfo"
                :show-when-enabled="false"
                :show-actions="true"
                @enabled="handleVehicleEnabled"
              />
            </div>
          </el-descriptions-item>

          <el-descriptions-item label="注册时间">
            {{ formatTime(bikeData.created_at) }}
          </el-descriptions-item>

          <el-descriptions-item label="最后更新">
            {{ formatTime(bikeData.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 操作历史记录 -->
        <div v-if="showHistory" class="operation-history">
          <h3>操作历史</h3>
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :timestamp="formatTime(activity.timestamp)"
              :type="activity.type"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>

      <!-- 无数据提示 -->
      <el-empty v-else description="未找到车辆信息" />
    </div>

    <!-- 编辑对话框 -->
    <el-dialog
      title="编辑车辆信息"
      :visible.sync="dialogVisible"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form ref="bikeForm" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="车牌号" prop="bike_number">
          <el-input v-model="form.bike_number" placeholder="请输入车牌号" />
        </el-form-item>

        <el-form-item label="品牌" prop="brand">
          <el-select v-model="form.brand" placeholder="请选择品牌" style="width: 100%;">
            <el-option v-for="item in brandOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item label="颜色" prop="color">
          <el-select v-model="form.color" placeholder="请选择颜色" style="width: 100%;">
            <el-option v-for="item in colorOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%;">
            <el-option v-for="item in typeOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="available">正常</el-radio>
            <el-radio label="unavailable">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBikeById, updateBike } from '@/api/bike'
import { getUserInfo } from '@/api/user'
import { checkVehicleDisableStatus, enableVehicle } from '@/api/vehicle'
import VehicleDisableStatus from '@/components/VehicleDisableStatus'
import { mapGetters } from 'vuex'

export default {
  name: 'BikeDetail',
  components: {
    VehicleDisableStatus
  },
  data() {
    return {
      loading: false,
      bikeData: null,
      dialogVisible: false,
      showHistory: false,
      activities: [],
      disableInfo: null,
      form: {
        bike_number: '',
        brand: '',
        color: '',
        type: '',
        status: 'available',
        user_id: ''
      },
      // 颜色选项
      colorOptions: [
        '黑色', '白色', '红色', '蓝色', '黄色',
        '绿色', '灰色', '银色', '金色', '棕色',
        '紫色', '粉色', '橙色', '未知颜色'
      ],
      // 品牌选项
      brandOptions: [
        '雅迪', '爱玛', '台铃', '小刀', '立马',
        '绿源', '新日', '飞鸽', '美利达', '捷安特',
        '阿米尼', '未知品牌'
      ],
      // 类型选项
      typeOptions: [
        '普通型号', '踏板车', '山地车', '公路车',
        '折叠车', '轻便型', '豪华型', '复古型'
      ],
      rules: {
        bike_number: [
          { required: true, message: '请输入车牌号', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        brand: [{ required: true, message: '请选择品牌', trigger: 'change' }],
        color: [{ required: true, message: '请选择颜色', trigger: 'change' }],
        type: [{ required: true, message: '请选择类型', trigger: 'change' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'roles'
    ])
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      const bikeId = this.$route.params.id
      console.log('获取车辆详情，ID:', bikeId)

      try {
        // 并行获取车辆详情和禁用状态
        const [bikeResponse, disableResponse] = await Promise.all([
          getBikeById(bikeId),
          checkVehicleDisableStatus(bikeId).catch(err => {
            console.error('获取车辆禁用状态失败:', err)
            return { data: null }
          })
        ])

        console.log('获取车辆详情成功，响应:', bikeResponse)

        // 处理禁用状态信息
        if (disableResponse && disableResponse.data) {
          this.disableInfo = disableResponse.data
          console.log('获取车辆禁用状态成功:', this.disableInfo)
        }

        // 确保响应中包含车辆数据
        if (bikeResponse.data && bikeResponse.data.bike) {
          this.bikeData = bikeResponse.data.bike
        } else if (bikeResponse.data) {
          // 如果响应中没有bike字段，但有data字段，尝试提取车辆信息
          const bikeData = bikeResponse.data
          if (bikeData.b_id || bikeData.id) {
            this.bikeData = bikeData
          } else {
            throw new Error('获取车辆详情失败，响应格式不正确')
          }
        } else {
          throw new Error('获取车辆详情失败，响应格式不正确')
        }

        // 处理表单数据
        this.form = {
          bike_number: this.bikeData.bike_number || this.bikeData.b_id,
          user_id: this.bikeData.user_id,
          brand: this.bikeData.brand,
          color: this.bikeData.color,
          type: this.bikeData.type,
          status: this.bikeData.status
        }

        // 获取用户信息
        if (this.bikeData.user_id) {
          try {
            const userResponse = await getUserInfo(this.bikeData.user_id)
            if (userResponse.data && userResponse.data.user) {
              this.userInfo = userResponse.data.user
            }
          } catch (error) {
            console.error('获取用户信息失败:', error)
          }
        }

        // 生成简单的操作历史记录
        this.generateSimpleHistory()
      } catch (error) {
        console.error('获取车辆详情失败:', error)
        this.$message.error('获取车辆详情失败: ' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    formatBikeData(bike) {
      console.log('格式化车辆数据，原始数据:', bike)

      // 确保返回标准格式的数据
      return {
        id: bike.b_id || bike.id,
        b_id: bike.b_id || bike.id,
        bike_number: bike.bike_number || bike.b_num || '未知车牌',
        user_id: bike.user_id || bike.belong_to,
        brand: bike.brand || '未知品牌',
        color: bike.color || '未知颜色',
        type: bike.type || bike.b_type || '普通型号',
        status: bike.status || 'available',
        created_at: bike.created_at,
        updated_at: bike.updated_at
      }
    },
    initFormData() {
      if (this.bikeData) {
        this.form = {
          bike_number: this.bikeData.bike_number || this.bikeData.b_id,
          user_id: this.bikeData.user_id,
          brand: this.bikeData.brand,
          color: this.bikeData.color,
          type: this.bikeData.type,
          status: this.bikeData.status
        }
      }
    },
    getStatusText(status) {
      const statusMap = {
        'available': '正常',
        'unavailable': '停用'
      }
      return statusMap[status] || '未知'
    },
    getColorCode(color) {
      const colorMap = {
        '黑色': '#000000',
        '白色': '#FFFFFF',
        '红色': '#FF0000',
        '蓝色': '#0000FF',
        '黄色': '#FFFF00',
        '绿色': '#00FF00',
        '灰色': '#808080',
        '银色': '#C0C0C0',
        '金色': '#FFD700',
        '棕色': '#A52A2A',
        '紫色': '#800080',
        '粉色': '#FFC0CB',
        '橙色': '#FFA500',
        '未知颜色': '#EEEEEE'
      }
      return colorMap[color] || '#EEEEEE'
    },
    getColorBorder(color) {
      return color === '白色' || color === '银色' || color === '未知颜色' ? '#CCCCCC' : 'transparent'
    },
    formatTime(timestamp) {
      if (!timestamp) return '未知时间'
      try {
        const date = new Date(timestamp)
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } catch (e) {
        return timestamp
      }
    },
    handleEdit() {
      console.log('点击编辑按钮，打开编辑对话框')
      this.dialogVisible = true
    },
    handleDialogClose() {
      console.log('对话框关闭，重置表单数据')
      this.initFormData()
    },
    async submitForm() {
      this.$refs.bikeForm.validate(async valid => {
        if (valid) {
          try {
            this.loading = true
            const bikeId = this.bikeData.b_id
            console.log('提交表单，车辆ID:', bikeId, '表单数据:', this.form)

            const response = await updateBike(bikeId, this.form)
            console.log('更新车辆成功，响应:', response)

            if (response.data && response.data.bike) {
              this.bikeData = this.formatBikeData(response.data.bike)
              this.$message.success('更新成功')
              this.dialogVisible = false
            } else {
              this.$message.error('更新失败，响应格式不正确')
            }
          } catch (error) {
            console.error('更新车辆失败:', error)
            this.$message.error('更新车辆失败: ' + (error.message || '未知错误'))
          } finally {
            this.loading = false
          }
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    },
    goBack() {
      console.log('返回车辆列表页面')
      this.$router.push('/bikes')
    },

    // 处理车辆禁用状态解除
    handleVehicleEnabled() {
      console.log('车辆禁用状态已解除，刷新数据')
      this.fetchData()
    },

    // 生成简单的操作历史记录
    generateSimpleHistory() {
      // 创建日期和更新日期
      const createdDate = this.bikeData.created_at ? new Date(this.bikeData.created_at) : new Date()
      const updatedDate = this.bikeData.updated_at ? new Date(this.bikeData.updated_at) : null

      // 初始化活动数组
      this.activities = [
        {
          content: '创建了车辆记录',
          timestamp: createdDate,
          type: 'primary'
        }
      ]

      // 如果有更新时间且与创建时间不同
      if (updatedDate && createdDate.getTime() !== updatedDate.getTime()) {
        this.activities.unshift({
          content: '更新了车辆信息',
          timestamp: updatedDate,
          type: 'warning'
        })
      }

      // 显示历史记录
      this.showHistory = true
    }
  }
}
</script>

<style lang="scss">
@import '@/styles/vehicle-management.scss';
.app-container {
  @extend .vehicle-detail;
}

.bike-detail-card {
  @extend .detail-card;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #EBEEF5;
  background-color: rgba(#409EFF, 0.05);
}

.header-actions {
  display: flex;
  gap: 10px;
}

.color-display {
  display: flex;
  align-items: center;
}

.color-dot {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
  border: 1px solid transparent;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
}

.status-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.loading-container {
  min-height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
