<template>
  <div class="app-container">
    <div class="page-header">
      <el-button icon="el-icon-back" @click="goBack">返回</el-button>
    </div>

    <div v-loading="loading">
      <violation-detail
        v-if="!loading && violationDetail.id"
        :violation-detail="violationDetail"
        :appeals="appeals"
        :evidences="evidences"
        :can-edit="false"
        :can-upload="false"
        :can-appeal="false"
        :can-handle-appeal="false"
        @appeal="handleAppeal"
        @refresh="fetchData"
      />
    </div>
  </div>
</template>

<script>
import { getViolationDetail } from '@/api/violations'
import ViolationDetail from '../components/ViolationDetail'

export default {
  name: 'UserViolationDetail',
  components: {
    ViolationDetail
  },
  data() {
    return {
      loading: true,
      violationDetail: {},
      appeals: [],
      evidences: []
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      const id = this.$route.params.id

      getViolationDetail(id)
        .then(response => {
          this.violationDetail = response.data.violation
          this.appeals = response.data.appeals
          this.evidences = response.data.evidences
          this.loading = false
        })
        .catch(error => {
          console.error(error)
          this.$message.error('获取违规详情失败')
          this.loading = false
        })
    },
    handleAppeal() {
      this.$router.push({ name: 'MyViolationAppeal', params: { id: this.violationDetail.id }})
    },
    goBack() {
      this.$router.push({ name: 'MyViolations' })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;
  }
}
</style>
