2025/03/02 00:00:05 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/02 00:00:05 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/02 00:00:05 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 00:00:07 root __init__.py[114] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/02 00:00:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:00:07] "[33mGET / HTTP/1.1[0m" 404 -
2025/03/02 00:00:08 root __init__.py[114] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/02 00:00:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:00:08] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025/03/02 00:02:13 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/02 00:02:13 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/02 00:02:13 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 00:02:15 root __init__.py[114] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/02 00:02:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:02:15] "[33mGET / HTTP/1.1[0m" 404 -
2025/03/02 00:03:20 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/03/02 00:03:20 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/02 00:03:20 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 00:03:21 root __init__.py[114] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/02 00:03:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:03:21] "[33mGET / HTTP/1.1[0m" 404 -
2025/03/02 00:05:44 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/02 00:05:44 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/02 00:05:44 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 00:05:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:05:46] "GET / HTTP/1.1" 200 -
2025/03/02 00:06:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:06:17] "GET /users HTTP/1.1" 200 -
2025/03/02 00:06:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:06:27] "[33mGET /users/1 HTTP/1.1[0m" 404 -
2025/03/02 00:06:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:06:33] "[33mGET /users/1 HTTP/1.1[0m" 404 -
2025/03/02 00:07:08 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/02 00:07:08 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/02 00:07:08 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 00:07:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:07:10] "GET / HTTP/1.1" 200 -
2025/03/02 00:07:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:07:13] "GET /users HTTP/1.1" 200 -
2025/03/02 00:07:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:07:16] "GET /users/1 HTTP/1.1" 200 -
2025/03/02 00:09:09 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/02 00:09:09 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/02 00:09:09 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 00:09:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:09:52] "GET /users HTTP/1.1" 200 -
2025/03/02 00:10:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:10:11] "[31m[1mPOST /users HTTP/1.1[0m" 415 -
2025/03/02 00:10:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:10:20] "GET /users HTTP/1.1" 200 -
2025/03/02 00:11:08 root __init__.py[131] bad_request() ERROR: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025/03/02 00:11:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:11:08] "[31m[1mPOST /users HTTP/1.1[0m" 400 -
2025/03/02 00:14:33 root __init__.py[131] bad_request() ERROR: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025/03/02 00:14:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:14:33] "[31m[1mPOST /users HTTP/1.1[0m" 400 -
2025/03/02 00:15:08 flask_api app.py[875] log_exception() ERROR: Exception on /users [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\api\app\users\routes.py", line 46, in create_user
    new_user = user_schema.load(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_pwd': ['Missing data for required field.'], 'u_role': ['Missing data for required field.'], 'u_belong': ['Missing data for required field.'], 'u_phone': ['Missing data for required field.']}
2025/03/02 00:15:08 root __init__.py[126] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/02 00:15:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:15:08] "[35m[1mPOST /users HTTP/1.1[0m" 500 -
2025/03/02 00:15:53 flask_api app.py[875] log_exception() ERROR: Exception on /users [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\api\app\users\routes.py", line 46, in create_user
    new_user = user_schema.load(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_belong': ['Missing data for required field.'], 'u_phone': ['Missing data for required field.']}
2025/03/02 00:15:53 root __init__.py[126] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/02 00:15:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:15:53] "[35m[1mPOST /users HTTP/1.1[0m" 500 -
2025/03/02 00:17:43 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/02 00:17:43 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/02 00:17:43 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 00:17:46 flask_api app.py[875] log_exception() ERROR: Exception on /users [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\api\app\users\routes.py", line 48, in create_user
    user_instance = Users(**new_user)
TypeError: app.users.models.Users() argument after ** must be a mapping, not Users
2025/03/02 00:17:46 root __init__.py[126] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/02 00:17:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:17:46] "[35m[1mPOST /users HTTP/1.1[0m" 500 -
2025/03/02 00:19:49 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/02 00:19:49 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/02 00:19:49 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 00:19:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:19:51] "[35m[1mPOST /users HTTP/1.1[0m" 201 -
2025/03/02 00:23:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:23:17] "GET /users HTTP/1.1" 200 -
2025/03/02 00:23:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:23:34] "[35m[1mPOST /users HTTP/1.1[0m" 201 -
2025/03/02 00:23:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:23:42] "GET /users HTTP/1.1" 200 -
2025/03/02 00:23:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:23:51] "GET /users/2 HTTP/1.1" 200 -
2025/03/02 00:24:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 00:24:16] "PUT /users/2 HTTP/1.1" 200 -
2025/03/02 15:21:22 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/02 15:21:47 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/02 15:21:47 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025/03/02 15:21:47 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 15:21:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:21:54] "GET / HTTP/1.1" 200 -
2025/03/02 15:21:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:21:54] "GET /static/css/app.949a0224.css HTTP/1.1" 200 -
2025/03/02 15:21:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:21:54] "GET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1" 200 -
2025/03/02 15:21:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:21:54] "GET /static/js/app.a7289db6.js HTTP/1.1" 200 -
2025/03/02 15:21:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:21:54] "GET /static/js/chunk-libs.4a5831c0.js HTTP/1.1" 200 -
2025/03/02 15:21:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:21:54] "GET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1" 200 -
2025/03/02 15:21:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:21:54] "GET /static/css/chunk-libs.3dfb7769.css HTTP/1.1" 200 -
2025/03/02 15:21:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:21:54] "GET /static/css/chunk-a8539586.94702ff7.css HTTP/1.1" 200 -
2025/03/02 15:21:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:21:54] "GET /static/js/chunk-a8539586.06bed4b6.js HTTP/1.1" 200 -
2025/03/02 15:21:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:21:57] "GET /static/fonts/element-icons.535877f5.woff HTTP/1.1" 200 -
2025/03/02 15:21:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:21:57] "GET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1" 200 -
2025/03/02 15:21:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:21:57] "GET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1" 200 -
2025/03/02 15:22:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:22:43] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/02 15:22:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:22:43] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/02 15:22:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:22:49] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/02 15:22:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:22:49] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/02 15:23:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:23:26] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/02 15:23:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:23:26] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/02 15:23:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:23:29] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/02 15:23:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:23:30] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/02 15:24:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:24:06] "GET /?imageView2/1/w/80/h/80 HTTP/1.1" 200 -
2025/03/02 15:24:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 15:24:19] "[36mGET /?imageView2/1/w/80/h/80 HTTP/1.1[0m" 304 -
2025/03/02 16:21:57 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/02 16:21:57 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025/03/02 16:21:57 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 16:22:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:00] "[36mGET / HTTP/1.1[0m" 304 -
2025/03/02 16:22:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:00] "[36mGET /static/css/app.949a0224.css HTTP/1.1[0m" 304 -
2025/03/02 16:22:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:00] "[36mGET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1[0m" 304 -
2025/03/02 16:22:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:00] "[36mGET /static/css/chunk-libs.3dfb7769.css HTTP/1.1[0m" 304 -
2025/03/02 16:22:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:00] "[36mGET /static/js/app.a7289db6.js HTTP/1.1[0m" 304 -
2025/03/02 16:22:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:00] "[36mGET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1[0m" 304 -
2025/03/02 16:22:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:00] "[36mGET /static/js/chunk-libs.4a5831c0.js HTTP/1.1[0m" 304 -
2025/03/02 16:22:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:00] "[36mGET /static/css/chunk-a8539586.94702ff7.css HTTP/1.1[0m" 304 -
2025/03/02 16:22:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:00] "[36mGET /static/js/chunk-a8539586.06bed4b6.js HTTP/1.1[0m" 304 -
2025/03/02 16:22:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:01] "[36mGET /static/fonts/element-icons.535877f5.woff HTTP/1.1[0m" 304 -
2025/03/02 16:22:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:01] "[36mGET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1[0m" 304 -
2025/03/02 16:22:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:01] "[36mGET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1[0m" 304 -
2025/03/02 16:22:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:07] "GET /static/css/chunk-31dba1a5.c58e968f.css HTTP/1.1" 200 -
2025/03/02 16:22:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:22:07] "GET /static/js/chunk-31dba1a5.b0b4da8e.js HTTP/1.1" 200 -
2025/03/02 16:36:38 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/02 16:36:39 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025/03/02 16:36:39 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 16:36:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:36:43] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/02 16:36:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:36:43] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/02 16:39:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:39:26] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/02 16:39:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:39:26] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/02 16:39:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:39:31] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/02 16:39:41 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/02 16:39:41 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025/03/02 16:39:41 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 16:39:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:39:43] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/02 16:39:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:39:43] "[31m[1mPOST /api/users/login HTTP/1.1[0m" 405 -
2025/03/02 16:43:37 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/02 16:43:37 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025/03/02 16:43:37 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/02 16:43:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:43:39] "[36mGET / HTTP/1.1[0m" 304 -
2025/03/02 16:43:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:43:39] "[36mGET /static/css/app.949a0224.css HTTP/1.1[0m" 304 -
2025/03/02 16:43:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:43:39] "[36mGET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1[0m" 304 -
2025/03/02 16:43:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:43:39] "[36mGET /static/css/chunk-libs.3dfb7769.css HTTP/1.1[0m" 304 -
2025/03/02 16:43:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:43:39] "[36mGET /static/js/app.a7289db6.js HTTP/1.1[0m" 304 -
2025/03/02 16:43:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:43:39] "[36mGET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1[0m" 304 -
2025/03/02 16:43:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:43:39] "[36mGET /static/js/chunk-libs.4a5831c0.js HTTP/1.1[0m" 304 -
2025/03/02 16:43:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:43:40] "[36mGET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1[0m" 304 -
2025/03/02 16:43:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:43:40] "[36mGET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1[0m" 304 -
2025/03/02 16:43:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:43:40] "[36mGET /static/fonts/element-icons.535877f5.woff HTTP/1.1[0m" 304 -
2025/03/02 16:43:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:43:42] "OPTIONS /api/users/login HTTP/1.1" 200 -
2025/03/02 16:43:42 flask_api app.py[875] log_exception() ERROR: Exception on /api/users/login [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: players

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 38, in authenticate_Players
    current_player = Players.find_by_username(data['username'])
  File "D:\try\try\vue-admin-template\api\app\players\models.py", line 23, in find_by_username
    return cls.query.filter_by(username=username).first()
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 2754, in first
    return self.limit(1)._iter().first()  # type: ignore
           ~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 2853, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
        params,
        ^^^^^^^
        execution_options={"_sa_orm_load_options": self.load_options},
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: players
[SQL: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('admin', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/03/02 16:43:42 root __init__.py[126] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/02 16:43:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [02/Mar/2025 16:43:42] "[35m[1mPOST /api/users/login HTTP/1.1[0m" 500 -
