<template>
  <div class="vehicle-disable-status">
    <el-popover
      placement="top"
      width="300"
      trigger="hover"
      v-if="isDisabled"
    >
      <div class="disable-details">
        <div class="disable-reason">
          <h4>禁用原因</h4>
          <p>{{ disableReason }}</p>
        </div>
        <div class="disable-time" v-if="disableTime">
          <h4>禁用时间</h4>
          <p>{{ formatDateTime(disableTime) }}</p>
        </div>
        <div class="disable-actions" v-if="showActions && canEnable">
          <el-button
            type="primary"
            size="mini"
            @click="handleEnable"
            :loading="enabling"
          >
            解除禁用
          </el-button>
        </div>
      </div>
      <el-tag slot="reference" type="danger" size="mini">
        <i class="el-icon-warning-outline"></i> {{ VehicleStatus.TEXT_DISABLED }}
      </el-tag>
    </el-popover>
    <el-tag v-else-if="showWhenEnabled" type="success" size="mini">
      <i class="el-icon-check"></i> {{ VehicleStatus.TEXT_AVAILABLE }}
    </el-tag>
  </div>
</template>

<script>
import { checkVehicleDisableStatus, enableVehicle } from '@/api/vehicle'
import { formatDateTime } from '@/utils/index'
import { VehicleStatus } from '@/utils/constants'

export default {
  name: 'VehicleDisableStatus',
  props: {
    vehicleId: {
      type: [Number, String],
      required: true
    },
    disableInfo: {
      type: Object,
      default: null
    },
    showWhenEnabled: {
      type: Boolean,
      default: false
    },
    showActions: {
      type: Boolean,
      default: false
    },
    refreshOnEnable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isDisabled: false,
      disableReason: '',
      disableTime: null,
      disableRecord: null,
      loading: false,
      enabling: false
    }
  },
  computed: {
    canEnable() {
      // 检查用户是否有权限解除禁用（管理员）
      return this.$store.getters.roles.includes('admin')
    }
  },
  created() {
    if (this.disableInfo) {
      // 如果已经提供了禁用信息，直接使用
      this.updateDisableStatus(this.disableInfo)
    } else {
      // 否则，从服务器获取禁用状态
      this.fetchDisableStatus()
    }
  },
  methods: {
    formatDateTime,
    async fetchDisableStatus() {
      if (!this.vehicleId) return

      this.loading = true
      try {
        const response = await checkVehicleDisableStatus(this.vehicleId)
        if (response && response.data) {
          this.updateDisableStatus(response.data)
        }
      } catch (error) {
        console.error('获取车辆禁用状态失败:', error)
        this.$message.error('获取车辆禁用状态失败')
      } finally {
        this.loading = false
      }
    },
    updateDisableStatus(data) {
      this.isDisabled = data.is_disabled || false
      this.disableReason = data.reason || '未知原因'
      this.disableTime = data.disable_start_time || null
      this.disableRecord = data.disable_record || null
    },
    async handleEnable() {
      if (!this.canEnable) {
        this.$message.warning('您没有权限解除车辆禁用状态')
        return
      }

      this.enabling = true
      try {
        const response = await enableVehicle(this.vehicleId)
        if (response && response.status === 'success') {
          this.$message.success('车辆禁用状态已解除')
          this.isDisabled = false
          this.disableReason = ''
          this.disableTime = null
          this.disableRecord = null

          // 通知父组件
          this.$emit('enabled', this.vehicleId)

          // 如果需要刷新页面
          if (this.refreshOnEnable) {
            this.$nextTick(() => {
              location.reload()
            })
          }
        } else {
          this.$message.error(response.message || '解除车辆禁用状态失败')
        }
      } catch (error) {
        console.error('解除车辆禁用状态失败:', error)
        this.$message.error('解除车辆禁用状态失败')
      } finally {
        this.enabling = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.vehicle-disable-status {
  display: inline-block;
}

.disable-details {
  padding: 5px;

  h4 {
    margin: 5px 0;
    font-size: 14px;
    color: #303133;
  }

  p {
    margin: 5px 0 10px;
    font-size: 13px;
    color: #606266;
    word-break: break-all;
  }

  .disable-actions {
    margin-top: 10px;
    text-align: right;
  }
}
</style>
