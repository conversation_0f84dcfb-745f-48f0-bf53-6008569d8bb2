<template>
  <div class="app-container">
    <div class="page-header">
      <el-button icon="el-icon-back" @click="goBack">返回</el-button>
    </div>

    <div v-loading="loading">
      <appeal-detail-card
        v-if="!loading && appeal.id"
        :appeal="appeal"
        :evidences="evidences"
      />
    </div>
  </div>
</template>

<script>
import { getAppealDetail } from '@/api/violations'
import AppealDetailCard from '../components/AppealDetailCard'

export default {
  name: 'UserAppealDetail',
  components: {
    AppealDetailCard
  },
  data() {
    return {
      loading: true,
      appeal: {},
      evidences: []
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      const id = this.$route.params.id

      getAppealDetail(id)
        .then(response => {
          this.appeal = response.data.appeal
          this.evidences = response.data.evidences
          this.loading = false
        })
        .catch(error => {
          console.error(error)
          this.$message.error('获取申诉详情失败')
          this.loading = false
        })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;
  }
}
</style>
