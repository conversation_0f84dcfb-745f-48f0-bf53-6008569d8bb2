import request from '@/utils/request'

// 获取用户可见的公告列表
export function getUserAnnouncements(params) {
  return request({
    url: '/api/announcements/user',
    method: 'get',
    params
  })
}

export function getAnnouncements(params) {
  return request({
    url: '/api/announcements',
    method: 'get',
    params
  })
}

export function getAnnouncement(id) {
  return request({
    url: `/api/announcements/${id}`,
    method: 'get'
  })
}

export function createAnnouncement(data) {
  return request({
    url: '/api/announcements',
    method: 'post',
    data
  })
}

export function updateAnnouncement(id, data) {
  return request({
    url: `/api/announcements/${id}`,
    method: 'put',
    data
  })
}

export function deleteAnnouncement(id) {
  return request({
    url: `/api/announcements/${id}`,
    method: 'delete'
  })
}