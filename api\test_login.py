#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

# 测试登录API
login_data = {
    'username': 'admin',
    'password': 'admin123'
}

try:
    response = requests.post('http://127.0.0.1:5000/api/login', 
                           json=login_data,
                           headers={'Content-Type': 'application/json'})
    
    print('=== 登录API响应 ===')
    print(f'状态码: {response.status_code}')
    print(f'响应内容: {response.text}')
    
    if response.status_code == 200:
        data = response.json()
        print('\n=== 解析后的JSON ===')
        print(json.dumps(data, indent=2, ensure_ascii=False))
        
        # 检查用户信息
        if 'data' in data and 'user' in data['data']:
            user = data['data']['user']
            print(f'\n=== 用户信息 ===')
            print(f'用户ID: {user.get("u_id")}')
            print(f'用户名: {user.get("u_name")}')
            print(f'角色: {user.get("u_role")}')
            
except Exception as e:
    print(f'请求失败: {e}')
