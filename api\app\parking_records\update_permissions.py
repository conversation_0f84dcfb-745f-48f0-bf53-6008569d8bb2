"""
更新停车记录权限的脚本
用于修改停车记录创建和结束的权限检查
"""
from app import create_app
from app.parking_records.routes import create_parking_record, end_parking_record
from functools import wraps
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.utils.auth_helpers import get_current_user_id, is_admin
from flask import request, current_app
from sqlalchemy import and_
from sqlalchemy.exc import SQLAlchemyError
from app.utils.response import api_response
from app.parking_records.models import ParkingRecord
from app import db
import logging

def update_permissions():
    """更新停车记录权限检查"""
    app = create_app()
    
    with app.app_context():
        # 备份原始函数
        original_create = create_parking_record
        original_end = end_parking_record
        
        # 修改创建停车记录函数，移除车辆所有权检查
        @wraps(original_create)
        def new_create_parking_record():
            """创建停车记录（开始停车）- 修改版"""
            try:
                # 获取当前用户ID
                user_id = get_current_user_id()
                if not user_id:
                    return api_response(message="未登录或无法获取用户信息", status="error", code=401)

                data = request.get_json()

                # 验证必要字段
                required_fields = ['vehicle_id', 'parking_lot_id', 'parking_space_id']
                for field in required_fields:
                    if field not in data:
                        return api_response(message=f"缺少必要字段: {field}", status="error", code=400)

                # 检查是否已有进行中的记录
                existing_record = ParkingRecord.query.filter_by(
                    vehicle_id=data['vehicle_id'],
                    status=0  # 进行中
                ).first()

                if existing_record:
                    return api_response(
                        message="此车辆已有进行中的停车记录",
                        status="error",
                        code=400
                    )

                # 检查车位是否可用
                from app.parkinglots.models import ParkingSpace
                space = ParkingSpace.query.get(data['parking_space_id'])
                if not space:
                    return api_response(message="车位不存在", status="error", code=404)

                if space.status != 0:  # 非空闲状态
                    return api_response(message="车位已被占用或维护中", status="error", code=400)

                # 检查车辆是否存在
                from app.bikes.models import Bikes
                vehicle = Bikes.query.get(data['vehicle_id'])
                if not vehicle:
                    return api_response(message="车辆不存在", status="error", code=404)

                # 移除车辆所有权检查，允许所有车辆停车
                # 根据用户角色设置不同的权限
                user_roles = get_jwt_identity().get('roles', [])
                is_admin_user = 'admin' in user_roles
                is_security = 'security' in user_roles
                
                # 如果是管理员或保安，可以操作所有车辆
                # 如果是普通用户，只能操作自己的车辆
                if not (is_admin_user or is_security) and vehicle.belong_to != user_id:
                    return api_response(message="没有操作此车辆的权限", status="error", code=403)

                try:
                    # 使用事务锁定车位，防止并发问题
                    # 再次检查车位状态，使用锁定查询
                    from app.parkinglots.models import ParkingSpace
                    space = ParkingSpace.query.filter(
                        and_(
                            ParkingSpace.id == data['parking_space_id'],
                            ParkingSpace.status == 0  # 必须是空闲状态
                        )
                    ).with_for_update().first()

                    if not space:
                        return api_response(message="车位不存在或已被占用", status="error", code=409)

                    # 创建记录
                    record = ParkingRecord(
                        vehicle_id=data['vehicle_id'],
                        user_id=user_id,
                        parking_lot_id=data['parking_lot_id'],
                        parking_space_id=data['parking_space_id']
                    )

                    # 如果有备注信息，添加到记录中
                    if 'notes' in data and data['notes']:
                        record.remarks = data['notes']

                    # 调用创建方法，其中包含了更新车位状态的逻辑
                    record = record.create()

                    # 记录日志
                    current_app.logger.info(f"用户 {user_id} 成功创建停车记录 ID: {record.id}, 车辆ID: {record.vehicle_id}, 车位ID: {record.parking_space_id}")

                    # 获取记录详情
                    record_details = record.get_details(include_relations=True)

                    return api_response(
                        message="停车记录创建成功",
                        status="success",
                        data=record_details
                    )

                except ValueError as ve:
                    # 特定的业务验证错误
                    db.session.rollback()
                    current_app.logger.warning(f"创建停车记录验证失败: {str(ve)}")
                    return api_response(message=str(ve), status="error", code=400)

                except SQLAlchemyError as se:
                    # 数据库错误
                    db.session.rollback()
                    current_app.logger.error(f"创建停车记录数据库错误: {str(se)}")
                    return api_response(message="数据库操作失败，请重试", status="error", code=500)

            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"创建停车记录未知错误: {str(e)}")
                return api_response(message=f"创建停车记录失败: {str(e)}", status="error", code=500)
        
        # 替换原始函数
        create_parking_record.__wrapped__ = new_create_parking_record
        
        print("已更新停车记录权限检查")

if __name__ == "__main__":
    update_permissions()
