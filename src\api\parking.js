import request from '@/utils/request'

// 获取停车记录列表
export function getParkingRecords(params) {
  return request({
    url: '/api/parking-records',
    method: 'get',
    params
  })
}

// 获取单个停车记录
export function getParkingRecord(id) {
  return request({
    url: `/api/parking-records/${id}`,
    method: 'get'
  })
}

// 创建停车记录
export function createParkingRecord(data) {
  // 增强数据验证
  const errors = []

  // 验证必填字段
  if (!data.vehicle_id) {
    errors.push('车辆ID不能为空')
  }

  if (!data.parking_lot_id) {
    errors.push('停车场ID不能为空')
  }

  if (!data.parking_space_id) {
    errors.push('车位ID不能为空')
  }

  // 验证用户ID
  if (!data.user_id) {
    errors.push('用户ID不能为空')
  }

  // 验证字段类型
  if (data.vehicle_id && isNaN(parseInt(data.vehicle_id))) {
    errors.push('车辆ID必须是数字')
  }

  if (data.parking_lot_id && isNaN(parseInt(data.parking_lot_id))) {
    errors.push('停车场ID必须是数字')
  }

  if (data.parking_space_id && isNaN(parseInt(data.parking_space_id))) {
    errors.push('车位ID必须是数字')
  }

  if (data.user_id && isNaN(parseInt(data.user_id))) {
    errors.push('用户ID必须是数字')
  }

  // 如果有错误，返回错误信息
  if (errors.length > 0) {
    return Promise.reject(new Error(errors.join('\n')))
  }

  // 准备数据，确保字段名与数据库一致
  const requestData = {
    ...data,
    // 确保使用数据库字段名
    entry_time: data.entry_time || data.start_time || new Date().toISOString(),
    status: data.status !== undefined ? data.status : 0, // 默认为进行中
    created_at: data.created_at || new Date().toISOString(),
    updated_at: data.updated_at || new Date().toISOString(),
    remarks: data.remarks || data.notes || ''
  }

  return request({
    url: '/api/parking-records',
    method: 'post',
    data: requestData
  })
  .then(response => {
    // 统一响应格式
    if (response && !response.code) {
      return {
        code: 20000,
        data: response,
        message: '停车记录创建成功'
      }
    }
    return response
  })
  .catch(error => {
    console.error('创建停车记录失败:', error)
    return Promise.reject(error)
  })
}

// 结束停车记录
export function endParkingRecord(id, remarks) {
  // 增强数据验证
  const errors = []

  // 验证ID
  if (!id) {
    errors.push('停车记录ID不能为空')
  } else if (isNaN(parseInt(id))) {
    errors.push('停车记录ID必须是数字')
  }

  // 如果有错误，返回错误信息
  if (errors.length > 0) {
    return Promise.reject(new Error(errors.join('\n')))
  }

  // 准备数据，确保字段名与数据库一致
  const requestData = {
    remarks: remarks || '',
    // 不需要发送exit_time和status，后端会自动设置
    // 这样可以确保使用后端的时间，避免前端时间与后端时间不一致
    _t: new Date().getTime() // 添加时间戳防止缓存
  }

  return request({
    url: `/api/parking-records/${id}/end`,
    method: 'put',
    data: requestData
  })
  .then(response => {
    // 统一响应格式
    if (response && !response.code) {
      return {
        code: 20000,
        data: response,
        message: '停车记录已结束'
      }
    }
    return response
  })
  .catch(error => {
    console.error('结束停车记录失败:', error)
    return Promise.reject(error)
  })
}

// 获取用户的停车记录
export function getUserParkingRecords(params) {
  // 处理参数，确保一致性
  const queryParams = { ...params }

  // 如果有状态参数，确保是数字类型
  if (queryParams.status !== undefined && queryParams.status !== '') {
    queryParams.status = parseInt(queryParams.status)
  }

  // 确定请求URL
  let url = '/api/parking-records'

  // 如果不是管理员，使用用户特定的路径
  if (!queryParams.isAdmin) {
    url = '/api/parking-records/user'
  }

  // 删除不需要的参数
  if (queryParams.isAdmin) {
    delete queryParams.isAdmin
  }

  // 添加请求标识，防止重复处理
  queryParams._parking_request_id = Date.now() + Math.random().toString(36).substring(2, 10)

  console.log('发送停车记录请求:', url, queryParams)

  // 直接返回请求，让request.js中的拦截器处理响应
  return request({
    url: url,
    method: 'get',
    params: queryParams
  })
}

// 获取车辆的停车记录
export function getVehicleParkingRecords(vehicleId, params) {
  return request({
    url: `/api/parking-records/vehicle/${vehicleId}`,
    method: 'get',
    params
  })
}

// 获取车辆的当前停车记录（进行中）
export function getParkingRecordByVehicle(vehicleId, status = 0) {
  return request({
    url: `/api/parking-records/vehicle/${vehicleId}`,
    method: 'get',
    params: { status }
  })
}

// 检查车辆是否有进行中的停车记录
export function checkVehicleActiveParking(vehicleId) {
  if (!vehicleId) {
    return Promise.reject(new Error('车辆ID不能为空'))
  }

  // 直接使用新的API端点
  return request({
    url: '/api/parking-records/check-active',
    method: 'get',
    params: { vehicle_id: vehicleId }
  })
  .then(response => {
    // 如果响应已经是标准格式，直接返回
    if (response && response.code === 20000) {
      return response
    }

    // 如果响应中包含 hasActiveParking 字段
    if (response && response.hasActiveParking !== undefined) {
      return {
        code: 20000,
        data: {
          hasActiveParking: response.hasActiveParking,
          record: response.record || null
        },
        message: response.hasActiveParking ? '该车辆有进行中的停车记录' : '该车辆没有进行中的停车记录'
      }
    }

    // 如果响应中包含 data 字段
    if (response && response.data) {
      if (response.data.hasActiveParking !== undefined) {
        return {
          code: 20000,
          data: response.data,
          message: response.message || (response.data.hasActiveParking ? '该车辆有进行中的停车记录' : '该车辆没有进行中的停车记录')
        }
      }
    }

    // 默认处理
    return {
      code: 20000,
      data: {
        hasActiveParking: false,
        record: null
      },
      message: '无法确定车辆停车状态，默认为无进行中的停车记录'
    }
  })
  .catch(error => {
    console.error('检查车辆停车状态失败:', error)
    // 出错时默认返回没有进行中的停车记录
    return {
      code: 20000,
      data: {
        hasActiveParking: false,
        record: null
      },
      message: '检查失败，默认为无进行中的停车记录'
    }
  })
}

// 获取停车场的停车记录
export function getParkingLotRecords(parkingLotId, params) {
  return request({
    url: `/api/parking-records/parking-lot/${parkingLotId}`,
    method: 'get',
    params
  })
}

// 获取停车记录统计
export function getParkingStats(params) {
  return request({
    url: '/api/parking-records/stats',
    method: 'get',
    params
  })
}

// 导出停车记录
export function exportParkingRecords(params) {
  return request({
    url: '/api/parking-records/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取所有可用于停车的车辆（不包括已经在停车中的车辆）
export function getAvailableVehiclesForParking(params) {
  return request({
    url: '/api/bikes',
    method: 'get',
    params: { ...params, status: 'available' }
  })
}
