import request from '@/utils/request'

// 获取停车场列表
export function getParkingLots(params) {
  // 添加调试日志
  console.log('调用 parkinglot.js 中的 getParkingLots API，参数:', params)

  return request({
    url: '/api/parkinglots',  // 使用 /api/parkinglots 路径，与后端路由匹配
    method: 'get',
    params
  }).catch(error => {
    console.error('获取停车场列表失败 (/api/parkinglots):', error)

    // 如果第一个API失败，尝试备用API
    console.log('尝试备用API路径 /api/parking-lots')
    return request({
      url: '/api/parking-lots',
      method: 'get',
      params
    })
  })
}

// 获取单个停车场信息
export function getParkingLot(id) {
  return request({
    url: `/api/parkinglots/${id}`,
    method: 'get'
  })
}

// 创建停车场
export function createParkingLot(data) {
  return request({
    url: '/api/parkinglots',
    method: 'post',
    data
  })
}

// 更新停车场信息
export function updateParkingLot(id, data) {
  return request({
    url: `/api/parkinglots/${id}`,
    method: 'put',
    data
  })
}

// 删除停车场
export function deleteParkingLot(id) {
  return request({
    url: `/api/parkinglots/${id}`,
    method: 'delete'
  })
}

// 获取停车场车位列表
export function getParkingSpaces(parkingLotId, params) {
  return request({
    url: `/api/parkinglots/${parkingLotId}/spaces`,
    method: 'get',
    params
  })
}

// 获取单个车位信息
export function getParkingSpace(spaceId) {
  return request({
    url: `/api/parking-spaces/${spaceId}`,
    method: 'get'
  })
}

// 创建车位
export function createParkingSpace(parkingLotId, data) {
  return request({
    url: `/api/parkinglots/${parkingLotId}/spaces`,
    method: 'post',
    data
  })
}

// 更新车位信息
export function updateParkingSpace(spaceId, data) {
  return request({
    url: `/api/parking-spaces/${spaceId}`,
    method: 'put',
    data
  })
}

// 删除车位
export function deleteParkingSpace(spaceId) {
  return request({
    url: `/api/parking-spaces/${spaceId}`,
    method: 'delete'
  })
}

// 批量创建车位
export function batchCreateParkingSpaces(parkingLotId, data) {
  return request({
    url: `/api/parkinglots/${parkingLotId}/batch-spaces`,
    method: 'post',
    data
  })
}

// 获取停车场统计信息
export function getParkingLotStats(parkingLotId) {
  return request({
    url: `/api/parkinglots/${parkingLotId}/stats`,
    method: 'get'
  })
}

// 更新车位状态
export function updateParkingSpaceStatus(spaceId, status) {
  return request({
    url: `/api/spaces/${spaceId}/status`,
    method: 'put',
    data: { status }
  })
}

// 批量更新车位状态
export function batchUpdateParkingSpaceStatus(spaceIds, status) {
  return request({
    url: '/api/spaces/batch-update',
    method: 'post',
    data: { space_ids: spaceIds, status }
  })
}

// 获取停车场统计信息
export function getParkingLotsStats() {
  return request({
    url: '/api/parkinglots/stats',
    method: 'get'
  })
}

// 触发停车场数据一致性检查
export function checkParkingLotsConsistency() {
  return request({
    url: '/api/parkinglots/check-consistency',
    method: 'post'
  })
}

// 更新车位类型
export function updateSpaceType(spaceId, type, options = {}) {
  const data = { type, ...options }
  return request({
    url: `/api/spaces/${spaceId}/type`,
    method: 'put',
    data
  })
}

// 批量更新车位类型
export function batchUpdateSpaceTypes(spaceIds, type, options = {}) {
  const data = {
    space_ids: spaceIds,
    type,
    ...options
  }
  return request({
    url: '/api/spaces/batch-update-type',
    method: 'post',
    data
  })
}