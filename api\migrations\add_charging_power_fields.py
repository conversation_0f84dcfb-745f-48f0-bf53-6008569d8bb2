"""
添加充电功率和类型字段的迁移脚本
"""
import os
import sys
import random
from datetime import datetime

# 添加父目录到系统路径
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, parent_dir)

# 导入应用和模型
from app import create_app, db
from app.charging.models import ChargingRecord
from app.parkinglots.models import ParkingSpace

# 创建应用实例
app = create_app()

def upgrade():
    """升级数据库，添加充电功率和类型字段"""
    with app.app_context():
        # 使用原生SQL添加字段
        import sqlite3

        print("开始添加充电功率和类型字段...")

        try:
            # 连接数据库
            conn = sqlite3.connect('sys.db')
            cursor = conn.cursor()

            # 检查充电记录表字段
            cursor.execute("PRAGMA table_info(charging_records)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'power' not in columns:
                print("添加充电记录表的power字段...")
                cursor.execute("ALTER TABLE charging_records ADD COLUMN power FLOAT")

            if 'charging_type' not in columns:
                print("添加充电记录表的charging_type字段...")
                cursor.execute("ALTER TABLE charging_records ADD COLUMN charging_type INTEGER")

            # 检查停车位表字段
            cursor.execute("PRAGMA table_info(parking_spaces)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'power' not in columns:
                print("添加停车位表的power字段...")
                cursor.execute("ALTER TABLE parking_spaces ADD COLUMN power FLOAT")

            if 'charging_type' not in columns:
                print("添加停车位表的charging_type字段...")
                cursor.execute("ALTER TABLE parking_spaces ADD COLUMN charging_type INTEGER")

            if 'remarks' not in columns:
                print("添加停车位表的remarks字段...")
                cursor.execute("ALTER TABLE parking_spaces ADD COLUMN remarks TEXT")

            # 提交更改
            conn.commit()
            print("字段添加完成")
        except Exception as e:
            print(f"添加字段失败: {str(e)}")
        finally:
            conn.close()

        # 更新充电车位的充电功率和类型
        try:
            print("更新充电车位的充电功率和类型...")

            # 获取所有充电车位
            charging_spaces = ParkingSpace.query.filter_by(type=3).all()
            print(f"找到 {len(charging_spaces)} 个充电车位")

            # 充电类型和功率选项
            charging_options = [
                {'type': 1, 'power': 2.2},  # 慢充
                {'type': 2, 'power': 3.3},  # 标准充
                {'type': 3, 'power': 7.0}   # 快充
            ]

            # 更新每个充电车位
            for space in charging_spaces:
                # 随机选择充电类型和功率
                option = random.choice(charging_options)
                space.charging_type = option['type']
                space.power = option['power']

                # 更新备注
                type_text = {1: '慢充', 2: '标准充', 3: '快充'}.get(option['type'], '未知')
                if not space.remarks:
                    space.remarks = f"{type_text}充电桩 - {space.power}kW"

            # 提交更改
            db.session.commit()
            print("充电车位更新完成")
        except Exception as e:
            db.session.rollback()
            print(f"更新充电车位失败: {str(e)}")

        # 更新充电记录的充电功率和类型
        try:
            print("更新充电记录的充电功率和类型...")

            # 获取所有充电记录
            charging_records = ChargingRecord.query.all()
            print(f"找到 {len(charging_records)} 条充电记录")

            # 更新每条充电记录
            for record in charging_records:
                # 获取对应的充电车位
                space = ParkingSpace.query.get(record.parking_space_id)
                if space and space.type == 2:
                    # 使用车位的充电类型和功率
                    record.charging_type = space.charging_type
                    record.power = space.power
                else:
                    # 随机分配充电类型和功率
                    option = random.choice(charging_options)
                    record.charging_type = option['type']
                    record.power = option['power']

            # 提交更改
            db.session.commit()
            print("充电记录更新完成")
        except Exception as e:
            db.session.rollback()
            print(f"更新充电记录失败: {str(e)}")

        print("数据库升级完成")

def downgrade():
    """降级数据库，删除充电功率和类型字段"""
    with app.app_context():
        print("开始删除充电功率和类型字段...")

        # 使用原生SQL删除字段（SQLite不支持直接删除列，需要创建新表并复制数据）
        # 这里我们只是将字段值设为NULL
        try:
            # 更新充电车位
            charging_spaces = ParkingSpace.query.filter_by(type=3).all()
            for space in charging_spaces:
                space.power = None
                space.charging_type = None

            # 更新充电记录
            charging_records = ChargingRecord.query.all()
            for record in charging_records:
                record.power = None
                record.charging_type = None

            # 提交更改
            db.session.commit()
            print("字段值清空完成")
        except Exception as e:
            db.session.rollback()
            print(f"清空字段值失败: {str(e)}")

        print("数据库降级完成")

if __name__ == '__main__':
    # 根据命令行参数执行升级或降级
    if len(sys.argv) > 1 and sys.argv[1] == 'downgrade':
        downgrade()
    else:
        upgrade()
