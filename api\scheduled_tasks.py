"""
定期任务脚本
包含数据一致性检查和其他定期维护任务
可以通过cron或Windows计划任务定期执行
"""

import os
import sys
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduled_tasks.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('scheduled_tasks')

def check_parking_data_consistency():
    """检查停车场数据一致性"""
    logger.info("开始检查停车场数据一致性...")
    
    try:
        from app import create_app, db
        from app.parkinglots.models import ParkingLot, ParkingSpace
        
        # 创建应用上下文
        app = create_app()
        
        # 在应用上下文中执行检查
        with app.app_context():
            # 获取所有停车场
            parking_lots = ParkingLot.query.all()
            logger.info(f"找到 {len(parking_lots)} 个停车场")
            
            inconsistent_count = 0
            
            for lot in parking_lots:
                logger.info(f"检查停车场: {lot.name} (ID: {lot.id})")
                
                # 查询实际占用车位数
                occupied_count = ParkingSpace.query.filter_by(
                    parking_lot_id=lot.id,
                    status=1  # 已占用状态
                ).count()
                
                # 查询总车位数
                total_spaces = ParkingSpace.query.filter_by(
                    parking_lot_id=lot.id
                ).count()
                
                # 检查是否一致
                if lot.total_spaces != total_spaces or lot.occupied_spaces != occupied_count:
                    inconsistent_count += 1
                    logger.warning(f"数据不一致: {lot.name} - 记录的总车位数: {lot.total_spaces}, 实际总车位数: {total_spaces}, 记录的占用数: {lot.occupied_spaces}, 实际占用数: {occupied_count}")
                    
                    # 自动修复
                    old_total = lot.total_spaces
                    old_occupied = lot.occupied_spaces
                    
                    lot.total_spaces = total_spaces
                    lot.occupied_spaces = occupied_count
                    db.session.commit()
                    
                    logger.info(f"自动修复完成: 总车位数 {old_total} -> {lot.total_spaces}, 已占用车位数 {old_occupied} -> {lot.occupied_spaces}")
                else:
                    logger.info(f"数据一致: {lot.name} - 总车位数: {lot.total_spaces}, 占用数: {lot.occupied_spaces}")
            
            if inconsistent_count > 0:
                logger.warning(f"发现 {inconsistent_count} 个停车场数据不一致，已自动修复")
            else:
                logger.info("所有停车场数据一致")
                
            return inconsistent_count
            
    except Exception as e:
        logger.error(f"检查停车场数据一致性时出错: {str(e)}")
        return -1

def check_orphaned_parking_records():
    """检查孤立的停车记录（状态为进行中但对应车位不是占用状态）"""
    logger.info("开始检查孤立的停车记录...")
    
    try:
        from app import create_app, db
        from app.parking_records.models import ParkingRecord
        from app.parkinglots.models import ParkingSpace
        
        # 创建应用上下文
        app = create_app()
        
        # 在应用上下文中执行检查
        with app.app_context():
            # 获取所有进行中的停车记录
            active_records = ParkingRecord.query.filter_by(status=0).all()
            logger.info(f"找到 {len(active_records)} 个进行中的停车记录")
            
            orphaned_count = 0
            
            for record in active_records:
                # 获取对应的车位
                space = ParkingSpace.query.get(record.parking_space_id)
                
                if not space:
                    logger.warning(f"停车记录 ID: {record.id} 引用了不存在的车位 ID: {record.parking_space_id}")
                    continue
                
                # 检查车位状态是否与记录一致
                if space.status != 1 or space.current_vehicle_id != record.vehicle_id:
                    orphaned_count += 1
                    logger.warning(f"孤立的停车记录 ID: {record.id}, 车位 {space.space_number} 状态: {space.status}, 当前车辆: {space.current_vehicle_id}, 记录车辆: {record.vehicle_id}")
                    
                    # 自动修复 - 将车位状态设为已占用
                    space.status = 1
                    space.current_vehicle_id = record.vehicle_id
                    db.session.commit()
                    
                    logger.info(f"自动修复完成: 车位 {space.space_number} 状态已更新为已占用，当前车辆已设置为 {record.vehicle_id}")
            
            if orphaned_count > 0:
                logger.warning(f"发现 {orphaned_count} 个孤立的停车记录，已自动修复")
            else:
                logger.info("没有发现孤立的停车记录")
                
            return orphaned_count
            
    except Exception as e:
        logger.error(f"检查孤立的停车记录时出错: {str(e)}")
        return -1

def run_all_checks():
    """运行所有检查"""
    logger.info("=" * 50)
    logger.info(f"开始定期维护任务 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 50)
    
    # 检查停车场数据一致性
    inconsistent_count = check_parking_data_consistency()
    
    # 检查孤立的停车记录
    orphaned_count = check_orphaned_parking_records()
    
    logger.info("=" * 50)
    logger.info(f"定期维护任务完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    if inconsistent_count > 0 or orphaned_count > 0:
        logger.info(f"修复了 {inconsistent_count} 个停车场数据不一致问题和 {orphaned_count} 个孤立的停车记录")
    else:
        logger.info("所有检查通过，数据一致")
    logger.info("=" * 50)

if __name__ == "__main__":
    run_all_checks()
