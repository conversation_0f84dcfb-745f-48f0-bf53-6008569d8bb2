2025/04/04 19:16:32 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/04 19:16:32 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/04 19:16:32 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/04 19:16:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:16:41] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/04 19:16:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:16:41] "POST /api/login HTTP/1.1" 200 -
2025/04/04 19:16:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:16:46] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/04 19:16:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:16:46] "GET /api/bikes HTTP/1.1" 200 -
2025/04/04 19:18:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:18:32] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 19:18:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:18:35] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 19:18:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:18:35] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 19:18:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:18:35] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/04 19:18:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:18:39] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/04 19:18:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:18:39] "POST /api/login HTTP/1.1" 200 -
2025/04/04 19:22:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:22:50] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/04 19:22:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:22:50] "[31m[1mGET /api/users/me HTTP/1.1[0m" 422 -
2025/04/04 19:22:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:22:50] "OPTIONS /api/my-bikes HTTP/1.1" 200 -
2025/04/04 19:22:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:22:50] "[31m[1mGET /api/my-bikes HTTP/1.1[0m" 422 -
2025/04/04 19:23:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:23:10] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/04 19:23:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:23:10] "GET /api/bikes HTTP/1.1" 200 -
2025/04/04 19:23:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:23:20] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/04 19:23:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:23:20] "POST /api/login HTTP/1.1" 200 -
2025/04/04 19:23:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:23:20] "OPTIONS /api/bikes?user_id=3 HTTP/1.1" 200 -
2025/04/04 19:23:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:23:20] "GET /api/bikes?user_id=3 HTTP/1.1" 200 -
2025/04/04 19:28:57 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/04 19:29:03 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/04 19:29:03 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/04 19:29:03 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/04 19:29:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:29:07] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/04 19:29:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:29:07] "[31m[1mGET /api/users/me HTTP/1.1[0m" 422 -
2025/04/04 19:29:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:29:07] "OPTIONS /api/my-bikes HTTP/1.1" 200 -
2025/04/04 19:29:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:29:07] "[31m[1mGET /api/my-bikes HTTP/1.1[0m" 422 -
2025/04/04 19:29:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:29:08] "[31m[1mGET /api/users/me HTTP/1.1[0m" 422 -
2025/04/04 19:29:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:29:08] "[31m[1mGET /api/my-bikes HTTP/1.1[0m" 422 -
2025/04/04 19:30:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:30:02] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/04 19:30:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:30:02] "[31m[1mGET /api/users/me HTTP/1.1[0m" 422 -
2025/04/04 19:30:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:30:03] "OPTIONS /api/my-bikes HTTP/1.1" 200 -
2025/04/04 19:30:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:30:03] "[31m[1mGET /api/my-bikes HTTP/1.1[0m" 422 -
2025/04/04 19:32:19 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/04 19:32:19 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/04 19:32:19 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/04 19:32:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:32:25] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/04 19:32:25 flask_api __init__.py[140] not_found() ERROR: 路由未找到: /users/me, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0Mzc2NTgwMCwianRpIjoiNjAwMmI0YmEtNGQ4MC00OWE2LWFlYTktMDg3MjJhNmY5OWUzIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6MywibmJmIjoxNzQzNzY1ODAwLCJjc3JmIjoiYzMyMjlkYzktYTJiNi00NjRkLThmZDktMmFlYWE0YTcyN2Q3IiwiZXhwIjoxNzQzODUyMjAwfQ.NdB4j1sFqen0rBVLlncnN8f5dv0ZBL_QlSggCxyDSQY

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/04 19:32:25 root __init__.py[141] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/04 19:32:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:32:25] "[33mGET /users/me HTTP/1.1[0m" 404 -
2025/04/04 19:32:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:32:25] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/04 19:32:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:32:25] "GET /api/bikes HTTP/1.1" 200 -
2025/04/04 19:32:26 flask_api __init__.py[140] not_found() ERROR: 路由未找到: /users/me, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0Mzc2NTgwMCwianRpIjoiNjAwMmI0YmEtNGQ4MC00OWE2LWFlYTktMDg3MjJhNmY5OWUzIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6MywibmJmIjoxNzQzNzY1ODAwLCJjc3JmIjoiYzMyMjlkYzktYTJiNi00NjRkLThmZDktMmFlYWE0YTcyN2Q3IiwiZXhwIjoxNzQzODUyMjAwfQ.NdB4j1sFqen0rBVLlncnN8f5dv0ZBL_QlSggCxyDSQY

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/04 19:32:26 root __init__.py[141] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/04 19:32:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:32:26] "[33mGET /users/me HTTP/1.1[0m" 404 -
2025/04/04 19:32:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:32:26] "GET /api/bikes HTTP/1.1" 200 -
2025/04/04 19:33:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:16] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/04 19:33:16 flask_api __init__.py[140] not_found() ERROR: 路由未找到: /users/me, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0Mzc2NTgwMCwianRpIjoiNjAwMmI0YmEtNGQ4MC00OWE2LWFlYTktMDg3MjJhNmY5OWUzIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6MywibmJmIjoxNzQzNzY1ODAwLCJjc3JmIjoiYzMyMjlkYzktYTJiNi00NjRkLThmZDktMmFlYWE0YTcyN2Q3IiwiZXhwIjoxNzQzODUyMjAwfQ.NdB4j1sFqen0rBVLlncnN8f5dv0ZBL_QlSggCxyDSQY

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/04 19:33:16 root __init__.py[141] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/04 19:33:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:16] "[33mGET /users/me HTTP/1.1[0m" 404 -
2025/04/04 19:33:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:16] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/04 19:33:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:16] "GET /api/bikes HTTP/1.1" 200 -
2025/04/04 19:33:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:23] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/04 19:33:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:24] "POST /api/login HTTP/1.1" 200 -
2025/04/04 19:33:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:27] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/04 19:33:27 flask_api __init__.py[140] not_found() ERROR: 路由未找到: /users/me, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0Mzc2NjQwNCwianRpIjoiZWJkNDc2ODUtMWMzNy00NGQ4LTkyMzEtNWFlMWJhYjJkZDA4IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6MywibmJmIjoxNzQzNzY2NDA0LCJjc3JmIjoiOWYxYmY2MWUtZmY0Yi00ZTQ4LThlMmMtMDY2YTZkNmU3MjJiIiwiZXhwIjoxNzQzODUyODA0fQ.8t2NsYxhflbgEC7Lnm7a_VcL9FsOA-Pqv0HyQjjHn3k

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/04 19:33:27 root __init__.py[141] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/04 19:33:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:27] "[33mGET /users/me HTTP/1.1[0m" 404 -
2025/04/04 19:33:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:27] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/04 19:33:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:27] "GET /api/bikes HTTP/1.1" 200 -
2025/04/04 19:33:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:38] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/04 19:33:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:38] "GET /api/bikes HTTP/1.1" 200 -
2025/04/04 19:33:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:48] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/04 19:33:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:49] "POST /api/login HTTP/1.1" 200 -
2025/04/04 19:33:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:51] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/04 19:33:51 flask_api __init__.py[140] not_found() ERROR: 路由未找到: /users/me, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0Mzc2NjQyOSwianRpIjoiMDZkYTY1Y2YtNmU2Ni00MTA1LWI2MGQtZmU4NDhkODAzOTAxIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6MiwibmJmIjoxNzQzNzY2NDI5LCJjc3JmIjoiODY4ODE0YzktMGI5Yi00MmZmLWFlODQtMjI5MTVlNWI0ZWM3IiwiZXhwIjoxNzQzODUyODI5fQ.V7fLs73-82VYi2L5kuc2sFV63A9z_FmqbiktIoXzEic

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/04 19:33:51 root __init__.py[141] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/04 19:33:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:51] "[33mGET /users/me HTTP/1.1[0m" 404 -
2025/04/04 19:33:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:51] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/04 19:33:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:33:51] "GET /api/bikes HTTP/1.1" 200 -
2025/04/04 19:35:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:35:41] "GET / HTTP/1.1" 200 -
2025/04/04 19:37:56 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/04 19:37:56 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/04 19:37:56 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/04 19:38:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:38:00] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/04 19:38:00 flask_api __init__.py[140] not_found() ERROR: 路由未找到: /users/me, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0Mzc2NjQyOSwianRpIjoiMDZkYTY1Y2YtNmU2Ni00MTA1LWI2MGQtZmU4NDhkODAzOTAxIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6MiwibmJmIjoxNzQzNzY2NDI5LCJjc3JmIjoiODY4ODE0YzktMGI5Yi00MmZmLWFlODQtMjI5MTVlNWI0ZWM3IiwiZXhwIjoxNzQzODUyODI5fQ.V7fLs73-82VYi2L5kuc2sFV63A9z_FmqbiktIoXzEic

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/04 19:38:00 root __init__.py[141] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/04 19:38:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:38:00] "[33mGET /users/me HTTP/1.1[0m" 404 -
2025/04/04 19:38:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:38:00] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/04 19:38:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:38:00] "GET /api/bikes HTTP/1.1" 200 -
2025/04/04 19:38:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:38:08] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/04 19:38:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:38:08] "POST /api/login HTTP/1.1" 200 -
2025/04/04 19:38:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:38:16] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/04 19:38:16 flask_api __init__.py[140] not_found() ERROR: 路由未找到: /users/me, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0Mzc2NjY4OCwianRpIjoiMmRlYzM3YTQtNDM0OC00YjM2LTlmOGEtZWQ1Mjk5NWYzZDA3IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6MiwibmJmIjoxNzQzNzY2Njg4LCJjc3JmIjoiNzQ3MmVmZjYtN2MzYS00NjVhLTk1YjctMGY1NjllYjE4YmYxIiwiZXhwIjoxNzQzODUzMDg4fQ.fTaP4XmGRD4JKU2svpiU3RlJQKRiSqU8QG1nve89Jsk

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/04 19:38:16 root __init__.py[141] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/04 19:38:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:38:16] "[33mGET /users/me HTTP/1.1[0m" 404 -
2025/04/04 19:38:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:38:16] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/04 19:38:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:38:16] "GET /api/bikes HTTP/1.1" 200 -
2025/04/04 19:40:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:40:04] "OPTIONS /users/me HTTP/1.1" 200 -
2025/04/04 19:40:04 flask_api __init__.py[140] not_found() ERROR: 路由未找到: /users/me, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0Mzc2NjY4OCwianRpIjoiMmRlYzM3YTQtNDM0OC00YjM2LTlmOGEtZWQ1Mjk5NWYzZDA3IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6MiwibmJmIjoxNzQzNzY2Njg4LCJjc3JmIjoiNzQ3MmVmZjYtN2MzYS00NjVhLTk1YjctMGY1NjllYjE4YmYxIiwiZXhwIjoxNzQzODUzMDg4fQ.fTaP4XmGRD4JKU2svpiU3RlJQKRiSqU8QG1nve89Jsk

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/04 19:40:04 root __init__.py[141] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/04 19:40:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:40:04] "[33mGET /users/me HTTP/1.1[0m" 404 -
2025/04/04 19:40:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:40:04] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/04 19:40:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:40:04] "GET /api/bikes HTTP/1.1" 200 -
2025/04/04 19:42:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:42:56] "OPTIONS /api/users/2 HTTP/1.1" 200 -
2025/04/04 19:42:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:42:56] "[31m[1mGET /api/users/2 HTTP/1.1[0m" 422 -
2025/04/04 19:43:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:43:51] "OPTIONS /api/users/2 HTTP/1.1" 200 -
2025/04/04 19:43:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:43:51] "[31m[1mGET /api/users/2 HTTP/1.1[0m" 422 -
2025/04/04 19:48:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:48:05] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/04 19:48:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 19:48:05] "POST /api/login HTTP/1.1" 200 -
2025/04/04 20:05:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:05:02] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/04 20:05:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:05:02] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/04/04 20:09:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:09:35] "OPTIONS /api/users/2 HTTP/1.1" 200 -
2025/04/04 20:09:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:09:35] "[31m[1mPUT /api/users/2 HTTP/1.1[0m" 422 -
2025/04/04 20:09:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:09:48] "OPTIONS /api/users/2 HTTP/1.1" 200 -
2025/04/04 20:09:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:09:48] "[31m[1mPUT /api/users/2 HTTP/1.1[0m" 422 -
2025/04/04 20:43:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:43:44] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:43:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:43:47] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:43:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:43:48] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 20:43:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:43:48] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/04 20:44:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:44:01] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:44:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:44:19] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:44:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:44:33] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:44:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:44:38] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:44:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:44:44] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:44:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:44:44] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 20:44:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:44:44] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/04 20:49:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:49:54] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:49:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:49:56] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:50:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:50:00] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:50:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:50:00] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 20:50:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:50:00] "[31m[1mPOST /api/register HTTP/1.1[0m" 409 -
2025/04/04 20:50:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:50:08] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:50:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:50:08] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:50:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:50:08] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 20:50:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:50:08] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/04 20:51:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:51:35] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:51:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:51:37] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:51:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:51:41] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:51:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:51:41] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 20:51:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:51:41] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/04 20:55:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:55:53] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:55:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:55:58] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:55:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:55:58] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 20:55:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:55:58] "[31m[1mPOST /api/register HTTP/1.1[0m" 409 -
2025/04/04 20:56:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:56:03] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:56:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:56:03] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:56:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:56:03] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 20:56:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:56:03] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/04 20:56:23 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/04 20:56:23 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/04 20:56:23 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/04 20:57:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:57:38] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:57:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:57:40] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:57:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:57:43] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:57:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:57:43] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 20:57:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:57:43] "[35m[1mPOST /api/register HTTP/1.1[0m" 500 -
2025/04/04 20:57:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:57:47] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 20:57:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 20:57:47] "[35m[1mPOST /api/register HTTP/1.1[0m" 500 -
2025/04/04 21:03:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:03:00] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 21:03:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:03:03] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 21:03:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:03:03] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 21:03:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:03:03] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/04 21:07:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:07:26] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 21:07:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:07:37] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 21:07:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:07:37] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 21:07:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:07:37] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/04 21:14:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:14:14] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 21:14:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:14:44] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 21:14:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:14:45] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 21:14:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:14:45] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 21:14:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:14:45] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/04 21:20:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:20:13] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 21:20:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:20:19] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 21:20:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:20:19] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 21:20:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:20:19] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/04 21:25:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:25:02] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 21:25:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:25:05] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 21:25:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:25:09] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/04 21:25:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:25:09] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/04 21:25:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [04/Apr/2025 21:25:09] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
