/**
 * 通用数据处理工具
 * 用于统一处理API响应数据格式，减少重复代码
 */

/**
 * 从API响应中提取数组数据
 * @param {Object|Array} response - API响应数据
 * @param {Object} options - 配置选项
 * @param {string} options.dataKey - 数据字段名，默认为 'items'
 * @param {boolean} options.allowDirectArray - 是否允许直接返回数组，默认为 true
 * @returns {Object} { data: Array, total: number, success: boolean, error?: string }
 */
export function extractArrayData(response, options = {}) {
  const {
    dataKey = 'items',
    allowDirectArray = true
  } = options

  const result = {
    data: [],
    total: 0,
    success: false,
    error: null
  }

  try {
    if (!response) {
      result.error = '响应数据为空'
      return result
    }

    // 按优先级尝试不同的数据结构
    const extractionPaths = [
      // 标准格式: { items: [...], total: number }
      () => {
        if (response[dataKey] && Array.isArray(response[dataKey])) {
          return {
            data: response[dataKey],
            total: response.total || response.pagination?.total || response[dataKey].length
          }
        }
        return null
      },

      // 嵌套格式: { data: { items: [...], total: number } }
      () => {
        if (response.data && response.data[dataKey] && Array.isArray(response.data[dataKey])) {
          return {
            data: response.data[dataKey],
            total: response.data.total || response.data.pagination?.total || response.data[dataKey].length
          }
        }
        return null
      },

      // 直接数组格式: [...]
      () => {
        if (allowDirectArray && Array.isArray(response)) {
          return {
            data: response,
            total: response.length
          }
        }
        return null
      },

      // 嵌套数组格式: { data: [...] }
      () => {
        if (allowDirectArray && response.data && Array.isArray(response.data)) {
          return {
            data: response.data,
            total: response.total || response.data.length
          }
        }
        return null
      }
    ]

    // 尝试各种提取方式
    for (const extractor of extractionPaths) {
      const extracted = extractor()
      if (extracted) {
        result.data = extracted.data
        result.total = extracted.total
        result.success = true
        return result
      }
    }

    // 如果所有方式都失败，返回错误
    result.error = '无法识别的数据格式'
    console.warn('数据提取失败，响应结构:', response)
    
  } catch (error) {
    result.error = `数据提取异常: ${error.message}`
    console.error('数据提取异常:', error)
  }

  return result
}

/**
 * 通用的数据加载处理器
 * @param {Object} options - 配置选项
 * @param {Function} options.apiCall - API调用函数
 * @param {Function} options.onSuccess - 成功回调
 * @param {Function} options.onError - 错误回调
 * @param {Object} options.messageConfig - 消息配置
 * @param {boolean} options.messageConfig.showSuccess - 是否显示成功消息
 * @param {boolean} options.messageConfig.showError - 是否显示错误消息
 * @param {string} options.messageConfig.successTemplate - 成功消息模板
 * @param {string} options.messageConfig.errorTemplate - 错误消息模板
 * @param {Object} options.extractConfig - 数据提取配置
 */
export async function handleDataLoading(options) {
  const {
    apiCall,
    onSuccess,
    onError,
    messageConfig = {},
    extractConfig = {}
  } = options

  const {
    showSuccess = true,
    showError = true,
    successTemplate = '成功加载 {count} 条数据',
    errorTemplate = '数据加载失败: {error}'
  } = messageConfig

  try {
    const response = await apiCall()
    const extracted = extractArrayData(response, extractConfig)

    if (extracted.success) {
      // 调用成功回调
      if (onSuccess) {
        onSuccess(extracted.data, extracted.total)
      }

      // 显示成功消息
      if (showSuccess && extracted.data.length > 0) {
        const message = successTemplate.replace('{count}', extracted.data.length)
        // 这里需要在调用处传入Vue实例的$message方法
        return { success: true, message, data: extracted.data, total: extracted.total }
      }

      return { success: true, data: extracted.data, total: extracted.total }
    } else {
      throw new Error(extracted.error)
    }
  } catch (error) {
    console.error('数据加载失败:', error)
    
    // 调用错误回调
    if (onError) {
      onError(error)
    }

    // 返回错误信息
    const message = errorTemplate.replace('{error}', error.message || '未知错误')
    return { success: false, message, error }
  }
}

/**
 * 创建防重复提示的消息显示器
 * @param {Object} vueInstance - Vue实例
 * @param {number} debounceTime - 防抖时间（毫秒）
 */
export function createMessageDebouncer(vueInstance, debounceTime = 1000) {
  const messageTimers = new Map()

  return {
    success(message, key = message) {
      this.showMessage('success', message, key)
    },

    error(message, key = message) {
      this.showMessage('error', message, key)
    },

    info(message, key = message) {
      this.showMessage('info', message, key)
    },

    warning(message, key = message) {
      this.showMessage('warning', message, key)
    },

    showMessage(type, message, key) {
      // 清除之前的定时器
      if (messageTimers.has(key)) {
        clearTimeout(messageTimers.get(key))
      }

      // 设置新的定时器
      const timer = setTimeout(() => {
        vueInstance.$message[type](message)
        messageTimers.delete(key)
      }, debounceTime)

      messageTimers.set(key, timer)
    },

    // 立即显示消息（不防抖）
    immediate: {
      success: (message) => vueInstance.$message.success(message),
      error: (message) => vueInstance.$message.error(message),
      info: (message) => vueInstance.$message.info(message),
      warning: (message) => vueInstance.$message.warning(message)
    }
  }
}

/**
 * 通用的加载状态管理器
 */
export class LoadingManager {
  constructor() {
    this.loadingStates = new Map()
    this.loadingMessages = new Map()
  }

  start(key, vueInstance, message = '加载中...', silent = false) {
    // 如果已经在加载中，直接返回
    if (this.loadingStates.get(key)) {
      return false
    }

    this.loadingStates.set(key, true)

    if (!silent && vueInstance) {
      const loadingMessage = vueInstance.$message({
        message,
        type: 'info',
        duration: 0
      })
      this.loadingMessages.set(key, loadingMessage)
    }

    return true
  }

  finish(key) {
    this.loadingStates.set(key, false)
    
    const loadingMessage = this.loadingMessages.get(key)
    if (loadingMessage) {
      loadingMessage.close()
      this.loadingMessages.delete(key)
    }
  }

  isLoading(key) {
    return this.loadingStates.get(key) || false
  }

  clear() {
    // 关闭所有加载消息
    this.loadingMessages.forEach(message => message.close())
    this.loadingMessages.clear()
    this.loadingStates.clear()
  }
}

// 导出单例实例
export const globalLoadingManager = new LoadingManager()
