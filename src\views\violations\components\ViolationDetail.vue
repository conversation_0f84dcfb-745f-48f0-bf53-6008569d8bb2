<template>
  <div class="violation-detail">
    <el-card class="box-card detail-card">
      <div slot="header" class="clearfix">
        <div class="card-header">
          <div class="header-left">
            <span class="card-title">违规记录详情</span>
            <div class="status-tag" :class="'status-' + violationDetail.status">
              <i :class="getStatusIcon(violationDetail.status)"></i>
              <span>{{ violationDetail.status_text }}</span>
            </div>
          </div>
          <div class="header-right">
            <el-button v-if="canEdit" type="primary" size="small" icon="el-icon-edit" @click="handleEdit">编辑</el-button>
          </div>
        </div>
      </div>

      <div class="detail-content">
        <!-- 基本信息区域 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-info"></i>
            <span>基本信息</span>
          </div>

          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">车牌号</div>
              <div class="info-value highlight">{{ violationDetail.bike_number || '未知' }}</div>
            </div>

            <div class="info-item">
              <div class="info-label">车主</div>
              <div class="info-value">
                <template v-if="violationDetail.user_name">
                  {{ violationDetail.user_name }}
                </template>
                <template v-else-if="violationDetail.user_id">
                  用户ID: {{ violationDetail.user_id }}
                </template>
                <template v-else>
                  未知
                </template>
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">违规类型</div>
              <div class="info-value">
                <el-tag
                  :type="getViolationTypeTag(violationDetail.violation_type || '其他违规')"
                  effect="plain"
                  size="medium"
                >
                  {{ violationDetail.violation_type || '其他违规' }}
                </el-tag>
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">违规时间</div>
              <div class="info-value">{{ formatDateTime(violationDetail.violation_time) || '未知时间' }}</div>
            </div>

            <div class="info-item">
              <div class="info-label">违规地点</div>
              <div class="info-value">{{ violationDetail.location || '未知地点' }}</div>
            </div>

            <div class="info-item">
              <div class="info-label">记录人</div>
              <div class="info-value">
                <template v-if="violationDetail.recorder_name">
                  {{ violationDetail.recorder_name }}
                </template>
                <template v-else-if="violationDetail.recorder_id">
                  用户ID: {{ violationDetail.recorder_id }}
                </template>
                <template v-else>
                  未知
                </template>
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">处理人</div>
              <div class="info-value">{{ violationDetail.handler_name || '-' }}</div>
            </div>
          </div>
        </div>

        <!-- 详细信息区域 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-document"></i>
            <span>详细信息</span>
          </div>

          <div class="description-box">
            <div class="description-item">
              <div class="description-label">违规描述</div>
              <div class="description-content">{{ violationDetail.description || '无' }}</div>
            </div>

            <div class="description-item">
              <div class="description-label">处理结果</div>
              <div class="description-content">{{ violationDetail.result || '暂无处理结果' }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 违规证据 -->
    <el-card class="box-card evidence-card">
      <div slot="header" class="clearfix">
        <div class="card-header">
          <div class="header-left">
            <span class="card-title">
              <i class="el-icon-picture-outline"></i> 违规证据
              <el-badge v-if="violationEvidences.length > 0" :value="violationEvidences.length" class="evidence-badge" type="primary" />
            </span>
          </div>
          <div class="header-right">
            <el-button
              v-if="canUpload"
              type="success"
              size="small"
              icon="el-icon-upload2"
              @click="handleUploadEvidence('violation')"
            >
              上传证据
            </el-button>
          </div>
        </div>
      </div>

      <div class="detail-content">
        <div v-if="violationEvidences.length === 0" class="empty-evidence">
          <el-empty description="暂无证据" :image-size="120">
            <el-button v-if="canUpload" type="primary" size="small" @click="handleUploadEvidence('violation')">上传证据</el-button>
          </el-empty>
        </div>

        <div v-else class="evidence-gallery">
          <el-row :gutter="16">
            <el-col
              v-for="(evidence, index) in violationEvidences"
              :key="index"
              :xs="24"
              :sm="12"
              :md="8"
              :lg="6"
              class="evidence-col"
            >
              <div class="evidence-card">
                <div class="evidence-type-tag" :class="evidence.evidence_type">
                  <i :class="evidence.evidence_type === 'image' ? 'el-icon-picture-outline' : 'el-icon-video-camera'"></i>
                  <span>{{ evidence.evidence_type === 'image' ? '图片' : '视频' }}</span>
                </div>

                <div v-if="evidence.evidence_type === 'image'" class="evidence-image">
                  <el-image
                    :src="evidence.file_path"
                    :preview-src-list="[evidence.file_path]"
                    fit="cover"
                  >
                    <div slot="error" class="image-error">
                      <i class="el-icon-picture-outline"></i>
                      <span>图片加载失败</span>
                    </div>
                  </el-image>
                </div>
                <div v-else-if="evidence.evidence_type === 'video'" class="evidence-video">
                  <video :src="evidence.file_path" controls width="100%" />
                </div>

                <div class="evidence-info">
                  <div class="evidence-meta">
                    <div class="evidence-uploader">
                      <i class="el-icon-user"></i>
                      <span>{{ evidence.uploader_name || '未知用户' }}</span>
                    </div>
                    <div class="evidence-time">
                      <i class="el-icon-time"></i>
                      <span>{{ formatDateTime(evidence.created_at) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>

    <!-- 申诉记录部分已移除 -->

    <!-- 上传证据对话框 -->
    <el-dialog
      :title="uploadDialogTitle"
      :visible.sync="uploadDialogVisible"
      width="550px"
      :close-on-click-modal="false"
      center
      custom-class="custom-dialog"
    >
      <div class="dialog-content">
        <el-form :model="uploadForm" label-width="80px">
          <el-form-item label="证据类型">
            <el-radio-group v-model="uploadForm.evidence_type">
              <el-radio label="image">
                <div class="evidence-type-option">
                  <i class="el-icon-picture-outline"></i>
                  <span>图片证据</span>
                </div>
              </el-radio>
              <el-radio label="video">
                <div class="evidence-type-option">
                  <i class="el-icon-video-camera"></i>
                  <span>视频证据</span>
                </div>
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="上传文件">
            <el-upload
              :action="uploadUrl"
              :headers="uploadHeaders"
              :data="uploadData"
              :before-upload="beforeUpload"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :file-list="fileList"
              :limit="1"
              class="upload-area"
              drag
            >
              <div class="upload-content">
                <i class="el-icon-upload"></i>
                <div class="upload-text">将文件拖到此处，或<em>点击上传</em></div>
              </div>
              <div slot="tip" class="upload-tip">
                <i class="el-icon-info"></i>
                <span>{{ uploadForm.evidence_type === 'image' ? '只能上传jpg/png文件，且不超过10MB' : '只能上传mp4/avi文件，且不超过50MB' }}</span>
              </div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false" icon="el-icon-close">取消</el-button>
        <el-button
          type="primary"
          @click="submitUpload"
          icon="el-icon-check"
          :disabled="fileList.length === 0"
        >
          确定上传
        </el-button>
      </span>
    </el-dialog>

    <!-- 处理违规对话框 -->
    <el-dialog
      title="处理违规记录"
      :visible.sync="processDialogVisible"
      width="550px"
      :close-on-click-modal="false"
      center
      custom-class="custom-dialog"
    >
      <div class="dialog-content">
        <div class="process-card">
          <div class="process-header">
            <i class="el-icon-info"></i>
            <span>违规信息</span>
          </div>

          <div class="process-summary">
            <div class="summary-grid">
              <div class="summary-item">
                <div class="summary-label">车牌号</div>
                <div class="summary-value highlight">{{ violationDetail.bike_number }}</div>
              </div>

              <div class="summary-item">
                <div class="summary-label">违规类型</div>
                <div class="summary-value">
                  <el-tag :type="getViolationTypeTag(violationDetail.violation_type)" size="small">
                    {{ violationDetail.violation_type }}
                  </el-tag>
                </div>
              </div>

              <div class="summary-item">
                <div class="summary-label">违规时间</div>
                <div class="summary-value">{{ formatDateTime(violationDetail.violation_time) }}</div>
              </div>

              <div class="summary-item">
                <div class="summary-label">违规地点</div>
                <div class="summary-value">{{ violationDetail.location }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="process-card">
          <div class="process-header">
            <i class="el-icon-edit"></i>
            <span>处理信息</span>
          </div>

          <el-form :model="processForm" label-width="100px">
            <el-form-item label="处理状态" required>
              <el-select v-model="processForm.status" placeholder="请选择处理状态" style="width: 100%">
                <el-option :label="'已处理'" :value="1">
                  <div class="status-option">
                    <span class="status-text">已处理</span>
                    <el-tag type="success" size="small" effect="plain">保留记录</el-tag>
                  </div>
                </el-option>
                <el-option :label="'已撤销'" :value="3">
                  <div class="status-option">
                    <span class="status-text">已撤销</span>
                    <el-tag type="danger" size="small" effect="plain">撤销记录</el-tag>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="处理结果" required>
              <el-input
                v-model="processForm.result"
                type="textarea"
                :rows="3"
                placeholder="请输入处理结果"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="processDialogVisible = false" icon="el-icon-close">取消</el-button>
        <el-button
          type="primary"
          @click="submitProcess"
          icon="el-icon-check"
          :disabled="!processForm.result"
        >
          提交处理
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
import { getToken } from '@/utils/auth'

export default {
  name: 'ViolationDetail',
  props: {
    // 违规记录详情
    violationDetail: {
      type: Object,
      required: true
    },
    // 申诉记录列表
    appeals: {
      type: Array,
      default: () => []
    },
    // 证据列表
    evidences: {
      type: Array,
      default: () => []
    },
    // 是否可以编辑
    canEdit: {
      type: Boolean,
      default: false
    },
    // 是否可以上传证据
    canUpload: {
      type: Boolean,
      default: false
    },
    // 是否可以申诉
    canAppeal: {
      type: Boolean,
      default: false
    },
    // 是否可以处理申诉
    canHandleAppeal: {
      type: Boolean,
      default: false
    },
    // 处理违规记录的方法
    processViolationMethod: {
      type: Function,
      default: null
    },
    // 处理申诉的方法
    processAppealMethod: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      // 上传证据对话框
      uploadDialogVisible: false,
      uploadDialogTitle: '上传违规证据',
      uploadForm: {
        evidence_type: 'image',
        related_type: 'violation',
        related_id: null
      },
      fileList: [],

      // 处理违规对话框
      processDialogVisible: false,
      processForm: {
        status: 1,
        result: ''
      },

      // 处理申诉表单
      appealForm: {
        status: 1,
        comment: ''
      }
    }
  },
  computed: {
    // 违规证据
    violationEvidences() {
      return this.evidences.filter(item => item.related_type === 'violation')
    },
    // 上传URL
    uploadUrl() {
      return process.env.VUE_APP_BASE_API + '/violations/evidences'
    },
    // 上传请求头
    uploadHeaders() {
      return {
        Authorization: 'Bearer ' + getToken()
      }
    },
    // 上传数据
    uploadData() {
      return {
        related_type: this.uploadForm.related_type,
        related_id: this.uploadForm.related_id
      }
    }
  },
  methods: {
    formatDateTime(time) {
      if (!time) return ''
      return parseTime(new Date(time), '{y}-{m}-{d} {h}:{i}')
    },
    getStatusType(status) {
      const statusMap = {
        0: 'info',    // 待审核
        1: 'success', // 已处理
        2: 'warning', // 申诉中
        3: 'danger'   // 已撤销
      }
      return statusMap[status] || 'info'
    },
    getStatusIcon(status) {
      const iconMap = {
        0: 'el-icon-time',         // 待审核
        1: 'el-icon-check',        // 已处理
        2: 'el-icon-warning',      // 申诉中
        3: 'el-icon-circle-close'  // 已撤销
      }
      return iconMap[status] || 'el-icon-info'
    },
    getViolationTypeTag(type) {
      const typeMap = {
        '违规停车': 'danger',
        '占用消防通道': 'danger',
        '占用无障碍通道': 'warning',
        '超时停车': 'warning',
        '车辆损坏公物': 'danger',
        '无证驾驶': 'danger',
        '其他违规': 'info'
      }
      return typeMap[type] || 'info'
    },
    getAppealStatusType(status) {
      const statusMap = {
        0: 'warning', // 待审核
        1: 'success', // 已通过
        2: 'danger'   // 未通过
      }
      return statusMap[status] || 'info'
    },
    getAppealStatusIcon(status) {
      const iconMap = {
        0: 'el-icon-time',        // 待审核
        1: 'el-icon-circle-check', // 已通过
        2: 'el-icon-circle-close'  // 未通过
      }
      return iconMap[status] || 'el-icon-time'
    },
    getAppealStatusColor(status) {
      const colorMap = {
        0: '#E6A23C', // 待审核 - 黄色
        1: '#67C23A', // 已通过 - 绿色
        2: '#F56C6C'  // 未通过 - 红色
      }
      return colorMap[status] || '#909399'
    },
    getAppealStatusText(status) {
      const textMap = {
        0: '待审核',
        1: '已通过',
        2: '未通过'
      }
      return textMap[status] || '未知状态'
    },
    getAppealEvidences(appealId) {
      return this.evidences.filter(item => item.related_type === 'appeal' && item.related_id === appealId)
    },
    canUploadAppealEvidence(appeal) {
      // 只有申诉创建者和管理员可以上传申诉证据，且只能在申诉待审核状态下上传
      return this.canUpload && appeal.status === 0
    },
    handleEdit() {
      this.$emit('edit', this.violationDetail)
    },
    handleAppeal() {
      this.$emit('appeal', this.violationDetail)
    },
    handleUploadEvidence(type, id = null) {
      this.uploadForm.related_type = type
      this.uploadForm.related_id = id || this.violationDetail.id
      this.uploadDialogTitle = type === 'violation' ? '上传违规证据' : '上传申诉证据'
      this.uploadDialogVisible = true
      this.fileList = []
    },
    beforeUpload(file) {
      const isImage = this.uploadForm.evidence_type === 'image'
      const isVideo = this.uploadForm.evidence_type === 'video'

      if (isImage) {
        const isJPG = file.type === 'image/jpeg'
        const isPNG = file.type === 'image/png'
        const isLt10M = file.size / 1024 / 1024 < 10

        if (!isJPG && !isPNG) {
          this.$message.error('上传图片只能是 JPG/PNG 格式!')
          return false
        }
        if (!isLt10M) {
          this.$message.error('上传图片大小不能超过 10MB!')
          return false
        }
      }

      if (isVideo) {
        const isMP4 = file.type === 'video/mp4'
        const isAVI = file.type === 'video/avi'
        const isLt50M = file.size / 1024 / 1024 < 50

        if (!isMP4 && !isAVI) {
          this.$message.error('上传视频只能是 MP4/AVI 格式!')
          return false
        }
        if (!isLt50M) {
          this.$message.error('上传视频大小不能超过 50MB!')
          return false
        }
      }

      return true
    },
    handleUploadSuccess(response, file, fileList) {
      if (response.status === 'success') {
        this.$message.success('上传成功')
        this.$emit('refresh')
      } else {
        this.$message.error(response.message || '上传失败')
      }
    },
    handleUploadError(err) {
      console.error(err)
      this.$message.error('上传失败')
    },
    submitUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning('请先上传文件')
        return
      }
      this.uploadDialogVisible = false
    },
    // 处理违规记录
    handleProcess() {
      this.processForm = {
        status: 1,
        result: this.violationDetail.result || ''
      }
      this.processDialogVisible = true
    },
    submitProcess() {
      if (!this.processForm.result) {
        this.$message.warning('请输入处理结果')
        return
      }

      if (this.processViolationMethod) {
        this.processViolationMethod(this.violationDetail.id, this.processForm)
          .then(response => {
            this.$message.success('处理成功')
            this.processDialogVisible = false
            this.$emit('refresh')
          })
          .catch(error => {
            console.error(error)
            this.$message.error('处理失败')
          })
      }
    },
    // 处理申诉
    handleAppealProcess(appealId) {
      if (!this.appealForm.comment) {
        this.$message.warning('请输入处理意见')
        return
      }

      if (this.processAppealMethod) {
        this.processAppealMethod(appealId, this.appealForm)
          .then(response => {
            this.$message.success('处理成功')
            this.$emit('refresh')
          })
          .catch(error => {
            console.error(error)
            this.$message.error('处理失败')
          })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.violation-detail {
  padding: 20px;

  // 通用卡片样式
  .box-card {
    margin-bottom: 24px;
    transition: all 0.3s;
    border-radius: 8px;
    overflow: hidden;

    &:hover {
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
  }

  // 卡片头部样式
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .header-left {
      display: flex;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        margin-right: 12px;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
        }
      }

      .status-tag {
        display: inline-flex;
        align-items: center;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 13px;
        font-weight: bold;

        i {
          margin-right: 6px;
        }

        &.status-0 {
          background-color: #f4f4f5;
          color: #909399;
        }

        &.status-1 {
          background-color: #f0f9eb;
          color: #67C23A;
        }

        &.status-2 {
          background-color: #fdf6ec;
          color: #E6A23C;
        }

        &.status-3 {
          background-color: #fef0f0;
          color: #F56C6C;
        }
      }

      .evidence-badge, .appeal-badge {
        margin-left: 8px;
      }
    }
  }

  // 详情内容区域
  .detail-content {
    padding: 16px;
  }

  // 详情部分样式
  .detail-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #EBEEF5;

      i {
        font-size: 18px;
        color: #409EFF;
        margin-right: 8px;
      }

      span {
        font-size: 15px;
        font-weight: bold;
        color: #303133;
      }
    }
  }

  // 基本信息网格布局
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;

    .info-item {
      .info-label {
        font-size: 13px;
        color: #909399;
        margin-bottom: 6px;
      }

      .info-value {
        font-size: 14px;
        color: #303133;

        &.highlight {
          font-weight: bold;
          color: #409EFF;
        }
      }
    }
  }

  // 详细描述样式
  .description-box {
    .description-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .description-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .description-content {
        padding: 12px;
        background-color: #f8f8f8;
        border-radius: 6px;
        line-height: 1.6;
        white-space: pre-line;
        color: #303133;
      }
    }
  }

  // 证据展示区域
  .evidence-gallery {
    .evidence-col {
      margin-bottom: 20px;
    }

    .evidence-card {
      position: relative;
      border: 1px solid #EBEEF5;
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.3s;
      background-color: #fff;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
      }

      .evidence-type-tag {
        position: absolute;
        top: 8px;
        right: 8px;
        display: flex;
        align-items: center;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        z-index: 1;

        i {
          margin-right: 4px;
        }

        &.image {
          background-color: rgba(64, 158, 255, 0.8);
          color: #fff;
        }

        &.video {
          background-color: rgba(103, 194, 58, 0.8);
          color: #fff;
        }
      }

      .evidence-image, .evidence-video {
        width: 100%;
        height: 160px;
        overflow: hidden;

        .el-image {
          width: 100%;
          height: 100%;
        }

        .image-error {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          background-color: #f5f7fa;
          color: #909399;

          i {
            font-size: 32px;
            margin-bottom: 8px;
          }
        }
      }

      .evidence-info {
        padding: 12px;
        background-color: #f5f7fa;

        .evidence-meta {
          .evidence-uploader, .evidence-time {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #606266;
            margin-bottom: 4px;

            i {
              margin-right: 6px;
              color: #909399;
            }
          }
        }
      }
    }
  }

  // 申诉时间线样式
  .appeal-timeline {
    padding: 8px 0;

    .appeal-item-card {
      border-radius: 8px;
      overflow: hidden;

      .appeal-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #EBEEF5;

        .appeal-status-badge {
          display: inline-flex;
          align-items: center;
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 13px;
          font-weight: bold;

          i {
            margin-right: 6px;
          }

          &.status-0 {
            background-color: #fdf6ec;
            color: #E6A23C;
          }

          &.status-1 {
            background-color: #f0f9eb;
            color: #67C23A;
          }

          &.status-2 {
            background-color: #fef0f0;
            color: #F56C6C;
          }
        }

        .appeal-user-info {
          display: flex;
          align-items: center;
          font-size: 13px;
          color: #606266;

          i {
            margin-right: 6px;
          }
        }
      }

      .appeal-card-body {
        padding: 16px;

        .appeal-section {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            i {
              font-size: 16px;
              color: #409EFF;
              margin-right: 8px;
            }

            span {
              font-size: 14px;
              font-weight: bold;
              color: #303133;
            }

            .upload-evidence-btn {
              margin-left: auto;
            }
          }

          .appeal-reason {
            padding: 12px;
            background-color: #f8f8f8;
            border-radius: 6px;
            line-height: 1.6;
            white-space: pre-line;
            color: #303133;
          }

          .appeal-result {
            .result-grid {
              display: grid;
              grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
              gap: 12px;

              .result-item {
                &.full-width {
                  grid-column: 1 / -1;
                }

                .result-label {
                  font-size: 13px;
                  color: #909399;
                  margin-bottom: 6px;
                }

                .result-value {
                  font-size: 14px;
                  color: #303133;

                  &.comment {
                    padding: 10px;
                    background-color: #f8f8f8;
                    border-radius: 4px;
                    line-height: 1.6;
                    white-space: pre-line;
                  }
                }
              }
            }
          }

          .appeal-process-form {
            padding: 16px;
            background-color: #f8f8f8;
            border-radius: 6px;
          }
        }
      }
    }
  }

  // 空状态样式
  .empty-evidence, .empty-appeal {
    color: #909399;
    text-align: center;
    padding: 24px 0;
  }

  // 对话框样式
  .custom-dialog {
    .dialog-content {
      padding: 0;

      .process-card {
        margin-bottom: 20px;
        border: 1px solid #EBEEF5;
        border-radius: 8px;
        overflow: hidden;

        &:last-child {
          margin-bottom: 0;
        }

        .process-header {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          background-color: #f5f7fa;
          border-bottom: 1px solid #EBEEF5;

          i {
            font-size: 16px;
            color: #409EFF;
            margin-right: 8px;
          }

          span {
            font-size: 14px;
            font-weight: bold;
            color: #303133;
          }
        }

        .process-summary {
          padding: 16px;

          .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 12px;

            .summary-item {
              .summary-label {
                font-size: 13px;
                color: #909399;
                margin-bottom: 6px;
              }

              .summary-value {
                font-size: 14px;
                color: #303133;

                &.highlight {
                  font-weight: bold;
                  color: #409EFF;
                }
              }
            }
          }
        }
      }

      .evidence-type-option {
        display: flex;
        align-items: center;

        i {
          margin-right: 6px;
          font-size: 16px;
        }
      }

      .upload-area {
        width: 100%;

        .upload-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 20px 0;

          i {
            font-size: 28px;
            color: #C0C4CC;
            margin-bottom: 8px;
          }

          .upload-text {
            color: #606266;

            em {
              color: #409EFF;
              font-style: normal;
            }
          }
        }

        .upload-tip {
          display: flex;
          align-items: center;
          color: #909399;
          font-size: 12px;
          margin-top: 8px;

          i {
            margin-right: 6px;
            color: #E6A23C;
          }
        }
      }

      .status-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .status-text {
          font-weight: 500;
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .violation-detail {
    padding: 12px;

    .info-grid {
      grid-template-columns: 1fr;
    }

    .result-grid {
      grid-template-columns: 1fr !important;
    }

    .appeal-card-header {
      flex-direction: column;
      align-items: flex-start !important;

      .appeal-user-info {
        margin-top: 8px;
      }
    }
  }
}
</style>
