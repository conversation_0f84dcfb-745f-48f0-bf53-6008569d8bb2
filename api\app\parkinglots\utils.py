#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停车场工具函数
"""

from .models import ParkingSpace

# 车位类型映射
TYPE_PREFIX_MAP = {
    1: 'N',  # 普通车位
    2: 'D',  # 残疾人车位
    3: 'C'   # 充电车位
}

def generate_space_number(parking_lot_id, space_type):
    """
    生成车位号
    
    Args:
        parking_lot_id: 停车场ID
        space_type: 车位类型（1=普通车位，2=残疾人车位，3=充电车位）
        
    Returns:
        str: 生成的车位号
    """
    # 获取类型前缀
    prefix = TYPE_PREFIX_MAP.get(space_type, 'N')
    
    # 查找同一停车场同类型车位的数量，用于生成序号
    existing_spaces = ParkingSpace.query.filter_by(
        parking_lot_id=parking_lot_id,
        type=space_type
    ).count()
    
    # 计算新序号
    next_number = existing_spaces + 1
    
    # 生成新车位号
    return f"{prefix}-{parking_lot_id}-{next_number}"
