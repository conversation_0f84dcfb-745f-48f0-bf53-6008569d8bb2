import request from '@/utils/request'
import store from '@/store'

// 获取有充电车位的停车场列表
export function getChargingParkingLots() {
  return request({
    url: '/api/charging/parking-lots',
    method: 'get'
  })
}

// 获取进行中的充电记录
export function getActiveChargingRecords() {
  return request({
    url: '/api/charging/records/active',
    method: 'get'
  })
}

// 获取历史充电记录
export function getHistoryChargingRecords(query) {
  console.log('历史充电记录查询参数:', JSON.stringify(query))
  return request({
    url: '/api/charging/records/history',
    method: 'get',
    params: query
  })
}

// 开始充电
export function startCharging(data) {
  console.log('发送开始充电请求，数据:', data)

  // 确保数据格式正确 - 不强制转换为数字，保持原始格式
  const requestData = {
    vehicle_id: data.vehicle_id,
    parking_lot_id: data.parking_lot_id,
    parking_space_id: data.parking_space_id,
    estimated_duration: data.estimated_duration,
    remarks: data.remarks || ''
  }

  console.log('格式化后的请求数据:', requestData)

  // 使用统一的API端点
  return request({
    url: '/api/charging/start',
    method: 'post',
    data: requestData
  }).catch(error => {
    console.error('开始充电请求失败:', error.response?.data || error)
    if (error.response && error.response.data) {
      console.error('服务器返回的错误信息:', error.response.data.message || error.response.data)
    }
    throw error
  })
}

// 结束充电
export function endCharging(id) {
  return request({
    url: `/api/charging/end/${id}`,
    method: 'post'
  })
}

// 获取充电记录列表
export function getChargingRecords(params) {
  console.log('获取充电记录列表，参数:', params);

  // 确保分页参数合理
  const queryParams = { ...params };
  if (!queryParams.per_page || queryParams.per_page < 1) {
    queryParams.per_page = 10; // 默认每页10条
  }

  if (!queryParams.page || queryParams.page < 1) {
    queryParams.page = 1; // 默认第1页
  }

  // 使用正确的API路径
  return request({
    url: '/api/charging-records',
    method: 'get',
    params: queryParams,
    timeout: 10000 // 10秒超时
  });
}

// 获取单个充电记录
export function getChargingRecord(id) {
  console.log(`获取充电记录详情，ID: ${id}`);

  // 使用正确的API路径
  return request({
    url: `/api/charging-records/${id}`,
    method: 'get',
    timeout: 10000 // 10秒超时
  });
}

// 创建充电记录
export function createChargingRecord(data) {
  console.log('创建充电记录，数据:', data);

  // 使用正确的API路径
  return request({
    url: '/api/charging-records',
    method: 'post',
    data,
    timeout: 10000 // 10秒超时
  });
}

// 结束充电记录
export function endChargingRecord(id, data) {
  console.log(`结束充电记录，ID: ${id}，数据:`, data);

  // 使用正确的API路径
  return request({
    url: `/api/charging-records/${id}/end`,
    method: 'put',
    data,
    timeout: 10000 // 10秒超时
  });
}

// 获取用户的充电记录
export function getUserChargingRecords(userId, params) {
  console.log(`获取用户充电记录，用户ID: ${userId}，参数:`, params);

  // 使用正确的API路径
  return request({
    url: `/api/users/${userId}/charging-records`,
    method: 'get',
    params,
    timeout: 10000 // 10秒超时
  });
}

// 获取车辆的充电记录
export function getVehicleChargingRecords(vehicleId, params) {
  console.log(`获取车辆充电记录，车辆ID: ${vehicleId}，参数:`, params);

  // 使用正确的API路径
  return request({
    url: `/api/vehicles/${vehicleId}/charging-records`,
    method: 'get',
    params,
    timeout: 10000 // 10秒超时
  });
}

// 编辑充电记录
export function updateChargingRecord(id, data) {
  console.log(`更新充电记录，ID: ${id}，数据:`, data);

  // 使用正确的API路径
  return request({
    url: `/api/charging-records/${id}`,
    method: 'put',
    data,
    timeout: 10000 // 10秒超时
  });
}

// 删除充电记录
export function deleteChargingRecord(id) {
  console.log(`删除充电记录，ID: ${id}`);

  // 使用正确的API路径
  return request({
    url: `/api/charging-records/${id}`,
    method: 'delete',
    timeout: 10000 // 10秒超时
  });
}


// 获取充电统计数据
export function getChargingStats(params) {
  return request({
    url: '/api/charging-stats',
    method: 'get',
    params
  })
}

// 获取每日充电统计数据
export function getChargingDailyStats(params) {
  return request({
    url: '/api/charging-daily-stats',
    method: 'get',
    params
  })
}

// 获取充电时长分布数据
export function getChargingDurationDistribution(params) {
  return request({
    url: '/api/charging-duration-distribution',
    method: 'get',
    params
  })
}

// 获取停车场充电统计数据
export function getParkingLotChargingStats(params) {
  return request({
    url: '/api/parking-lot-charging-stats',
    method: 'get',
    params
  })
}

// 获取充电预约列表
export function getChargingReservations(params) {
  return request({
    url: '/api/charging-reservations',
    method: 'get',
    params
  })
}

// 获取单个充电预约
export function getChargingReservation(id) {
  return request({
    url: `/api/charging-reservations/${id}`,
    method: 'get'
  })
}

// 创建充电预约
export function createChargingReservation(data) {
  return request({
    url: '/api/charging-reservations',
    method: 'post',
    data
  })
}

// 取消充电预约
export function cancelChargingReservation(id) {
  return request({
    url: `/api/charging-reservations/${id}/cancel`,
    method: 'post'
  })
}

// 标记充电预约为已使用
export function useChargingReservation(id) {
  return request({
    url: `/api/charging-reservations/${id}/use`,
    method: 'put'
  })
}

// 获取用户的充电预约
export function getUserChargingReservations(userId, params) {
  return request({
    url: `/api/users/${userId}/charging-reservations`,
    method: 'get',
    params
  })
}

// 获取充电车位列表
export function getChargingSpaces(params) {
  // 添加日志，便于调试
  console.log('获取充电车位列表，参数:', params);

  // 确保参数中包含type=3（充电车位）
  const queryParams = { ...params };
  if (queryParams.type === undefined) {
    queryParams.type = 3; // 默认只获取充电车位
  }

  // 确保分页参数合理
  if (!queryParams.per_page || queryParams.per_page < 1) {
    queryParams.per_page = 20; // 默认每页20条
  }

  if (!queryParams.page || queryParams.page < 1) {
    queryParams.page = 1; // 默认第1页
  }

  console.log('处理后的参数:', queryParams);

  // 使用正确的API路径
  return request({
    url: '/api/charging/spaces',
    method: 'get',
    params: queryParams,
    timeout: 10000 // 10秒超时
  });
}

// 创建充电车位
export function createChargingSpace(data) {
  return request({
    url: '/api/charging-spaces',
    method: 'post',
    data
  })
}

// 更新充电车位
export function updateChargingSpace(id, data) {
  console.log(`发送更新充电车位请求，ID: ${id}，数据:`, data);

  // 使用正确的API路径
  return request({
    url: `/api/charging-spaces/${id}`,
    method: 'put',
    data,
    timeout: 10000 // 10秒超时
  }).catch(error => {
    console.error('更新充电车位失败:', error);

    // 提取错误信息
    let errorMessage = '更新充电车位失败';
    if (error.response && error.response.data) {
      errorMessage = error.response.data.message || errorMessage;
    }

    // 创建一个包含详细错误信息的新错误对象
    const enhancedError = new Error(errorMessage);
    enhancedError.originalError = error;
    enhancedError.response = error.response;

    throw enhancedError;
  });
}

// 删除充电车位
export function deleteChargingSpace(id) {
  return request({
    url: `/api/charging-spaces/${id}`,
    method: 'delete'
  })
}

// 维护充电车位
export function maintainChargingSpace(id, data) {
  return request({
    url: `/api/charging-spaces/${id}/maintain`,
    method: 'put',
    data
  })
}

// 获取充电故障列表
export function getChargingFaults(params) {
  return request({
    url: '/api/charging-faults',
    method: 'get',
    params
  })
}

// 获取充电故障详情
export function getChargingFaultDetail(id) {
  return request({
    url: `/api/charging-faults/${id}`,
    method: 'get'
  })
}

// 创建充电故障
export function createChargingFault(data) {
  console.log('发送创建充电故障请求，数据:', data);
  return request({
    url: '/api/charging-faults',
    method: 'post',
    data,
    timeout: 10000 // 10秒超时
  }).catch(error => {
    console.error('创建充电故障失败:', error);

    // 提取错误信息
    let errorMessage = '创建充电故障失败';
    if (error.response && error.response.data) {
      errorMessage = error.response.data.message || errorMessage;
      console.error('服务器返回的错误信息:', error.response.data);
    }

    // 创建一个包含详细错误信息的新错误对象
    const enhancedError = new Error(errorMessage);
    enhancedError.originalError = error;
    enhancedError.response = error.response;

    throw enhancedError;
  });
}

// 处理充电故障
export function processChargingFault(id, data) {
  return request({
    url: `/api/charging-faults/${id}/process`,
    method: 'put',
    data
  })
}

// 关闭充电故障
export function closeChargingFault(id) {
  return request({
    url: `/api/charging-faults/${id}/close`,
    method: 'put'
  })
}

// 删除充电故障
export function deleteChargingFault(id) {
  console.log(`删除充电故障，ID: ${id}`);
  return request({
    url: `/api/charging-faults/${id}`,
    method: 'delete',
    timeout: 10000 // 10秒超时
  });
}

// 获取充电异常列表
export function getChargingExceptions(params) {
  return request({
    url: '/api/charging-exceptions',
    method: 'get',
    params
  })
}

// 获取充电异常统计
export function getChargingExceptionStats(params) {
  return request({
    url: '/api/charging-exception-stats',
    method: 'get',
    params
  })
}

// 处理充电异常
export function processChargingException(id, data) {
  return request({
    url: `/api/charging-exceptions/${id}/process`,
    method: 'put',
    data
  })
}

// 删除充电异常
export function deleteChargingException(id) {
  return request({
    url: `/api/charging-exceptions/${id}`,
    method: 'delete',
    timeout: 10000 // 10秒超时
  })
}

// 获取充电记录详情
export function getChargingRecordDetail(id) {
  console.log(`获取充电记录详情，ID: ${id}`);

  // 使用正确的API路径
  return request({
    url: `/api/charging-records/${id}/detail`,
    method: 'get',
    timeout: 10000 // 10秒超时
  });
}

// 标记充电异常
export function markChargingException(data) {
  console.log(`标记充电异常，记录ID: ${data.record_id}，数据:`, data);

  // 使用正确的API路径
  return request({
    url: `/api/charging-records/${data.record_id}/mark-exception`,
    method: 'put',
    data,
    timeout: 10000 // 10秒超时
  });
}

// 导出充电记录
export function exportChargingRecords(params) {
  console.log('导出充电记录，参数:', params);

  // 使用正确的API路径
  return request({
    url: '/api/charging-records/export',
    method: 'get',
    params,
    responseType: 'blob',
    timeout: 30000 // 30秒超时，导出可能需要更长时间
  });
}


