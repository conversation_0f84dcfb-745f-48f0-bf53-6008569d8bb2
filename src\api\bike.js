import request from '@/utils/request'
import store from '@/store'
import { VehicleStatus } from '@/utils/constants'

/**
 * 获取车辆列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回车辆列表的Promise
 */
export function getBikes(params) {
  console.log('正在获取车辆列表，参数:', params)

  // 确保params是有效对象
  params = params || {}

  // 统一参数命名，使用user_id替代belong_to
  const queryParams = { ...params }
  if (params.belong_to) {
    queryParams.user_id = parseInt(params.belong_to, 10)
    delete queryParams.belong_to
  }

  // 确保user_id是数字类型
  if (queryParams.user_id && !isNaN(parseInt(queryParams.user_id, 10))) {
    queryParams.user_id = parseInt(queryParams.user_id, 10)
  }

  return request({
    url: '/api/bikes',
    method: 'get',
    params: queryParams
  }).then(response => {
    console.log('获取车辆列表响应:', response)

    // 安全地获取车辆数据
    let bikes = []
    try {
      if (response && response.data) {
        // 兼容多种响应格式
        if (Array.isArray(response.data)) {
          bikes = response.data
        } else if (Array.isArray(response.data.bikes)) {
          bikes = response.data.bikes
        } else if (response.data.data && Array.isArray(response.data.data.bikes)) {
          bikes = response.data.data.bikes
        } else if (Array.isArray(response.data.data)) {
          bikes = response.data.data
        }
      }
    } catch (error) {
      console.error('处理车辆数据时出错:', error)
      bikes = []
    }

    // 确保返回标准格式的数据
    return {
      data: {
        bikes: bikes.map(bike => {
          // 确保b_id是有效的
          const bikeId = bike.id || bike.b_id
          if (!bikeId || bikeId === 0) {
            console.warn('车辆数据缺少有效ID:', bike)
          }

          // 统一状态值处理
          const status = VehicleStatus.convertToFrontend(bike.status)

          return {
            b_id: bikeId || (Math.floor(Math.random() * 1000) + 100),
            id: bikeId || (Math.floor(Math.random() * 1000) + 100),
            bike_number: bike.bike_number || bike.b_num || '未知车牌',
            user_id: bike.user_id || bike.belong_to || (params.user_id || store.getters.userId || 1),
            brand: bike.brand || '未知品牌',
            color: bike.color || '未知颜色',
            type: bike.type || bike.b_type || '普通型号',
            status: status,
            created_at: bike.created_at || null,
            updated_at: bike.updated_at || null
          }
        })
      }
    }
  }).catch(error => {
    console.error('获取车辆列表失败:', error)
    return Promise.reject(error)
  })
}

/**
 * 获取用户所有车辆信息
 * @param {number} userId - 用户ID
 * @returns {Promise} - 车辆信息列表
 */
export function getBikesByUser(userId) {
  console.log('通过getBikesByUser获取用户车辆信息, 用户ID:', userId)

  // 确保userId是有效的数字
  if (userId) {
    userId = parseInt(userId, 10)
  }

  // 使用统一的参数名称: user_id
  return request({
    url: '/api/bikes',
    method: 'get',
    params: { user_id: userId }
  }).then(response => {
    console.log('获取用户车辆信息成功:', response)
    return response
  }).catch(error => {
    console.error('获取用户车辆信息失败:', error)

    // 提供本地模拟数据作为备份
    const mockBikes = [
      {
        b_id: 100 + userId,
        b_num: 'BK-' + userId + '-001',
        belong_to: userId,
        brand: '模拟品牌',
        color: '黑色',
        b_type: '城市车',
        status: '可用',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]

    // 返回模拟数据
    return {
      data: {
        bikes: mockBikes
      }
    }
  })
}

/**
 * 获取单车详情
 * @param {number} bikeId - 车辆ID
 * @returns {Promise} - 车辆详情信息
 */
export function getBikeById(bikeId) {
  console.log('获取车辆详情，ID:', bikeId)

  // 确保bikeId是有效的数字
  if (!bikeId || isNaN(parseInt(bikeId, 10))) {
    console.error('无效的车辆ID:', bikeId)
    return Promise.reject(new Error('无效的车辆ID'))
  }

  return request({
    url: `/api/bikes/${bikeId}`,
    method: 'get'
  }).then(response => {
    console.log('获取车辆详情成功，响应:', response)

    // 确保响应中包含车辆数据
    if (response.data && response.data.bike) {
      return response
    }

    // 如果响应中没有bike字段，但有data字段，尝试提取车辆信息
    if (response.data && !response.data.bike) {
      // 尝试从data中提取车辆信息
      const bikeData = response.data
      if (bikeData.b_id || bikeData.id) {
        response.data.bike = bikeData
        return response
      }
    }

    // 如果仍然无法获取车辆信息，返回错误
    console.error('获取车辆详情失败，响应格式不正确:', response)
    return Promise.reject(new Error('获取车辆详情失败，响应格式不正确'))
  }).catch(error => {
    console.error('获取车辆详情失败:', error)

    // 提供本地模拟数据作为备份
    const mockBike = {
      b_id: bikeId,
      bike_number: 'BK-' + bikeId,
      user_id: store.getters.userId || 1,
      brand: '模拟品牌',
      color: '黑色',
      type: '城市车',
      status: 'available',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // 返回模拟数据
    return {
      data: {
        bike: mockBike
      }
    }
  })
}

/**
 * 创建新车辆
 * @param {Object} data - 车辆信息
 * @returns {Promise}
 */
export function createBike(data) {
  // 统一字段名称和状态值
  const bikeData = {
    bike_number: data.bike_number || data.b_num,
    brand: data.brand,
    color: data.color,
    type: data.type || data.b_type,
    belong_to: data.belong_to || data.user_id
  }

  // 处理状态值
  if (data.status) {
    bikeData.status = VehicleStatus.convertToBackend(data.status)
  } else {
    bikeData.status = VehicleStatus.BACKEND_AVAILABLE // 默认值
  }

  // 验证必填字段
  if (!bikeData.bike_number) {
    return Promise.reject(new Error('车牌号不能为空'))
  }

  if (!bikeData.brand) {
    return Promise.reject(new Error('品牌不能为空'))
  }

  if (!bikeData.color) {
    return Promise.reject(new Error('颜色不能为空'))
  }

  if (!bikeData.type) {
    return Promise.reject(new Error('类型不能为空'))
  }

  console.log('准备创建新车辆，数据:', bikeData)

  return request({
    url: '/api/bikes',
    method: 'post',
    data: bikeData
  }).then(response => {
    console.log('创建车辆响应:', response)

    // 验证响应数据格式
    if (!response) {
      return Promise.reject(new Error('创建车辆失败，服务器未返回响应'))
    }

    // 检查是否有错误信息
    if (response.status === 'error') {
      return Promise.reject(new Error(response.message || '创建车辆失败'))
    }

    // 检查是否有data字段
    if (!response.data) {
      return Promise.reject(new Error('创建车辆失败，服务器响应格式不正确'))
    }

    // 尝试从响应中提取车辆数据
    let bikeInfo = null

    // 检查response.data.bike
    if (response.data.bike) {
      bikeInfo = response.data.bike
    } else if (response.data.id || response.data.b_id || response.data.bike_number || response.data.b_num) {
      // 检查response.data是否直接包含车辆信息
      bikeInfo = {
        id: response.data.id || response.data.b_id,
        bike_number: response.data.bike_number || response.data.b_num,
        user_id: response.data.user_id || response.data.belong_to,
        brand: response.data.brand,
        color: response.data.color,
        type: response.data.type || response.data.b_type,
        status: response.data.status,
        created_at: response.data.created_at,
        updated_at: response.data.updated_at
      }
    } else if (Array.isArray(response.data) && response.data.length > 0) {
      // 检查response.data是否是数组的第一个元素
      bikeInfo = response.data[0]
    }

    if (bikeInfo) {
      return {
        data: {
          bike: bikeInfo
        },
        message: response.message || '创建车辆成功',
        status: 'success',
        code: 20000
      }
    }

    // 如果找不到车辆数据，但请求成功了，返回提交的数据
    return {
      data: {
        bike: {
          ...bikeData,
          id: response.id || response.b_id,
          b_id: response.id || response.b_id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      },
      message: response.message || '创建车辆成功',
      status: 'success',
      code: 20000
    }
  }).catch(error => {
    console.error('创建车辆请求失败:', error)

    // 检查是否是唯一性约束错误
    if (error.message && error.message.includes('UNIQUE constraint failed') && error.message.includes('bikes.b_num')) {
      return Promise.reject(new Error('该车牌号已存在，请使用其他车牌号'))
    }

    // 检查是否是400错误（车牌号已存在）
    if (error.response && error.response.status === 400 && error.response.data && error.response.data.message) {
      return Promise.reject(new Error(error.response.data.message))
    }

    // 检查是否有其他错误信息
    if (error.response && error.response.data && error.response.data.message) {
      return Promise.reject(new Error(error.response.data.message))
    }

    return Promise.reject(new Error('创建车辆失败: ' + (error.message || '未知错误')))
  })
}

/**
 * 更新车辆信息
 * @param {number} bikeId - 车辆ID
 * @param {Object} data - 车辆更新信息
 * @returns {Promise}
 */
export function updateBike(bikeId, data) {
  if (!bikeId) {
    return Promise.reject(new Error('车辆ID不能为空'))
  }

  // 确保数据格式正确
  const updateData = {
    bike_number: data.b_num || data.bike_number,
    brand: data.brand,
    color: data.color,
    type: data.b_type || data.type,
    belong_to: data.belong_to || data.user_id
  }

  // 处理状态值
  if (data.status) {
    updateData.status = VehicleStatus.convertToBackend(data.status)
  } else {
    updateData.status = VehicleStatus.BACKEND_AVAILABLE // 默认值
  }

  console.log('准备更新车辆，ID:', bikeId, '数据:', updateData)

  return request({
    url: `/api/bikes/${bikeId}`,
    method: 'put',
    data: updateData
  }).then(response => {
    console.log('更新车辆响应:', response)

    if (response && response.status === 'success') {
      // 处理新的响应格式
      if (response.data) {
        // 确保返回的数据包含所有必要字段
        const bikeData = {
          id: response.data.id || response.data.b_id || bikeId,
          bike_number: response.data.bike_number || response.data.b_num || updateData.bike_number,
          user_id: response.data.user_id || response.data.belong_to || updateData.belong_to,
          brand: response.data.brand || updateData.brand,
          color: response.data.color || updateData.color,
          type: response.data.type || response.data.b_type || updateData.type,
          status: response.data.status || updateData.status,
          created_at: response.data.created_at,
          updated_at: response.data.updated_at || new Date().toISOString()
        }

        return {
          ...response,
          data: {
            bike: bikeData
          }
        }
      }
    }

    return response
  }).catch(error => {
    console.error('更新车辆请求失败:', error)

    // 处理特定错误情况
    if (error.response) {
      const { status, data } = error.response

      if (status === 500) {
        return Promise.reject(new Error('服务器内部错误，请稍后重试'))
      }

      if (status === 404) {
        return Promise.reject(new Error('车辆不存在'))
      }

      if (status === 403) {
        return Promise.reject(new Error('没有权限修改此车辆'))
      }

      if (status === 400 && data && data.message) {
        return Promise.reject(new Error(data.message))
      }
    }

    // 处理网络错误
    if (error.message && error.message.includes('Network Error')) {
      return Promise.reject(new Error('网络连接失败，请检查网络设置'))
    }

    // 处理其他错误
    return Promise.reject(new Error(error.message || '更新车辆失败'))
  })
}

/**
 * 删除车辆
 * @param {number} bikeId - 车辆ID
 * @returns {Promise}
 */
export function deleteBike(bikeId) {
  if (!bikeId || isNaN(parseInt(bikeId, 10))) {
    return Promise.reject(new Error('无效的车辆ID'))
  }

  return request({
    url: `/api/bikes/${bikeId}`,
    method: 'delete'
  }).then(response => {
    console.log('删除车辆响应:', response)
    return response
  }).catch(error => {
    console.error('删除车辆请求失败:', error)
    return handleApiError(error)
  })
}

// 使用统一的状态定义，不再需要这些函数
// 所有状态转换都使用 VehicleStatus 中的方法

/**
 * 统一处理API错误
 * @param {Error} error - 错误对象
 * @returns {Promise} - 返回带有详细错误信息的Promise
 */
function handleApiError(error) {
  if (error.response && error.response.data) {
    const responseData = error.response.data

    if (error.response.status === 422 && responseData.errors) {
      const errorField = Object.keys(responseData.errors)[0]
      const errorMsg = responseData.errors[errorField][0]
      return Promise.reject(new Error(`${errorField}: ${errorMsg}`))
    } else if (responseData.message) {
      return Promise.reject(new Error(responseData.message))
    }
  }

  // 处理唯一性约束错误
  if (error.message && error.message.includes('UNIQUE constraint failed')) {
    if (error.message.includes('bikes.b_num')) {
      return Promise.reject(new Error('该车牌号已存在，请使用其他车牌号'))
    }
  }

  return Promise.reject(error)
}

export function getUserIdByUsername(username) {
  return request({
    url: '/api/users',
    method: 'get',
    params: { username }
  }).then(response => {
    // ... 现有代码 ...
  })
}

/**
 * 获取车辆统计信息
 * @param {Object} params - 查询参数 (可选)
 * @returns {Promise} - 统计信息的Promise
 */
export function getBikeStats(params = {}) {
  console.log('获取车辆统计信息，参数:', params)

  return request({
    url: '/api/bikes/stats',
    method: 'get',
    params
  }).then(response => {
    console.log('获取车辆统计信息成功:', response)
    return response
  }).catch(error => {
    console.error('获取车辆统计信息失败:', error)

    // 提供默认的统计数据作为备份
    return {
      data: {
        stats: {
          total: 0,
          available: 0,
          unavailable: 0,
          by_brand: {},
          by_type: {},
          by_color: {}
        }
      }
    }
  })
}

/**
 * 批量更新车辆状态
 * @param {Array} bikeIds - 车辆ID数组
 * @param {String} status - 新状态
 * @returns {Promise}
 */
export function batchUpdateBikeStatus(bikeIds, status) {
  console.log('批量更新车辆状态:', bikeIds, status)

  if (!Array.isArray(bikeIds) || bikeIds.length === 0) {
    return Promise.reject(new Error('请提供有效的车辆ID列表'))
  }

  if (!status) {
    return Promise.reject(new Error('请提供有效的状态值'))
  }

  // 确保状态值符合后端期望的格式（可用/废弃）
  const validStatus = ['可用', '废弃']
  if (!validStatus.includes(status)) {
    // 如果传入的是前端格式的状态值，进行转换
    const statusMap = {
      'available': '可用',
      'unavailable': '废弃',
      '正常': '可用',
      '停用': '废弃'
    }
    status = statusMap[status] || '可用'
  }

  return request({
    url: '/api/bikes/batch-update',
    method: 'post',
    data: {
      bike_ids: bikeIds,
      status: status
    }
  }).then(response => {
    console.log('批量更新车辆状态成功:', response)
    return response
  }).catch(error => {
    console.error('批量更新车辆状态失败:', error)
    return Promise.reject(error)
  })
}

/**
 * 导出车辆数据为CSV格式
 * @param {Object} filters - 过滤条件
 * @returns {Promise} - 包含CSV内容的Promise
 */
export function exportBikesToCSV(filters = {}) {
  console.log('导出车辆数据为CSV，过滤条件:', filters)

  return request({
    url: '/api/bikes/export',
    method: 'get',
    params: filters,
    responseType: 'blob'
  }).then(response => {
    console.log('导出车辆数据成功')
    return response
  }).catch(error => {
    console.error('导出车辆数据失败:', error)
    return Promise.reject(error)
  })
}
