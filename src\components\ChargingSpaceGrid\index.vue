<template>
  <div class="charging-space-grid">
    <div class="grid-header">
      <div class="title">充电车位</div>
      <div class="legend">
        <div class="legend-item">
          <div class="legend-color available"></div>
          <div class="legend-text">空闲</div>
        </div>
        <div class="legend-item">
          <div class="legend-color occupied"></div>
          <div class="legend-text">已占用</div>
        </div>
        <div class="legend-item">
          <div class="legend-color maintenance"></div>
          <div class="legend-text">维护中</div>
        </div>
        <div class="legend-item">
          <div class="legend-color selected"></div>
          <div class="legend-text">已选择</div>
        </div>
      </div>
    </div>

    <div class="grid-container">
      <div
        v-for="space in spaces"
        :key="space.id"
        class="space-item"
        :class="{
          'status-available': space.status === 0,
          'status-occupied': space.status === 1,
          'status-maintenance': space.status === 2,
          'selected': space.id === selectedSpaceId
        }"
        @click="handleSpaceClick(space)"
      >
        <div class="space-content">
          <div class="space-number">{{ space.space_number }}</div>
          <div class="space-icon">
            <i class="el-icon-s-opportunity"></i>
          </div>
          <div class="space-power" v-if="space.power">{{ space.power }}kW</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChargingSpaceGrid',
  props: {
    spaces: {
      type: Array,
      required: true
    },
    selectedSpaceId: {
      type: Number,
      default: null
    }
  },
  methods: {
    handleSpaceClick(space) {
      this.$emit('space-click', space)
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-space-grid {
  .grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }

    .legend {
      display: flex;

      .legend-item {
        display: flex;
        align-items: center;
        margin-left: 15px;

        .legend-color {
          width: 16px;
          height: 16px;
          border-radius: 3px;
          margin-right: 5px;

          &.available {
            background-color: #67c23a;
          }

          &.occupied {
            background-color: #f56c6c;
          }

          &.maintenance {
            background-color: #e6a23c;
          }

          &.selected {
            background-color: #409EFF;
          }
        }

        .legend-text {
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }

  .grid-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;

    .space-item {
      width: calc(20% - 12px);
      aspect-ratio: 1;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
      position: relative;

      &.status-available {
        background-color: rgba(103, 194, 58, 0.1);
        border: 2px solid #67c23a;

        &:hover {
          background-color: rgba(103, 194, 58, 0.2);
          transform: translateY(-2px);
        }
      }

      &.status-occupied {
        background-color: rgba(245, 108, 108, 0.1);
        border: 2px solid #f56c6c;
        cursor: not-allowed;
      }

      &.status-maintenance {
        background-color: rgba(230, 162, 60, 0.1);
        border: 2px solid #e6a23c;
        cursor: not-allowed;
      }

      &.selected {
        background-color: rgba(64, 158, 255, 0.1);
        border: 2px solid #409EFF;

        &::after {
          content: '✓';
          position: absolute;
          top: -10px;
          right: -10px;
          width: 20px;
          height: 20px;
          background-color: #409EFF;
          color: white;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
        }
      }

      .space-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        padding: 10px;

        .space-number {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }

        .space-icon {
          font-size: 24px;
          color: #606266;
          margin-bottom: 5px;
        }

        .space-power {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .charging-space-grid {
    .grid-container {
      .space-item {
        width: calc(25% - 12px);
      }
    }
  }
}

@media (max-width: 992px) {
  .charging-space-grid {
    .grid-container {
      .space-item {
        width: calc(33.33% - 10px);
      }
    }
  }
}

@media (max-width: 768px) {
  .charging-space-grid {
    .grid-container {
      .space-item {
        width: calc(50% - 8px);
      }
    }
  }
}
</style>
