from app import db
from datetime import datetime
from sqlalchemy.sql import func
from sqlalchemy.exc import SQLAlchemyError
import logging
from app.utils.response import api_response
from app.utils.models import BaseModel

# 停车场信息模型
class ParkingLot(db.Model):
    __tablename__ = 'parking_lots'
    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 停车场名称
    name = db.Column(db.String(100), nullable=False, unique=True)
    # 地址
    address = db.Column(db.String(255), nullable=False)
    # 总车位数
    total_spaces = db.Column(db.Integer, nullable=False, default=0)
    # 已占用车位数
    occupied_spaces = db.Column(db.Integer, nullable=False, default=0)
    # 经度
    longitude = db.Column(db.Float, nullable=True)
    # 纬度
    latitude = db.Column(db.Float, nullable=True)
    # 开放时间
    opening_hours = db.Column(db.String(100), default="24小时")
    # 状态，1表示正常运营，0表示暂停使用
    status = db.Column(db.Integer, nullable=False, default=1)
    # 创建时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    # 描述信息
    description = db.Column(db.Text, nullable=True)
    # 校区
    campus = db.Column(db.String(50), nullable=True)
    # 区域
    area = db.Column(db.String(50), nullable=True)
    # 管理员
    manager = db.Column(db.String(50), nullable=True)
    # 联系电话
    contact = db.Column(db.String(20), nullable=True)

    # 关联车位
    parking_spaces = db.relationship('ParkingSpace', backref='lot', lazy=True, cascade="all, delete-orphan")
    # 关联停车记录
    parking_records = db.relationship('ParkingRecord', backref='parking_lot', lazy=True)

    def __init__(self, name, address, total_spaces, longitude=None, latitude=None, opening_hours="24小时", description=None, status=1, campus=None, area=None, manager=None, contact=None):
        self.name = name
        self.address = address
        self.total_spaces = total_spaces
        self.occupied_spaces = 0  # 初始没有车辆
        self.longitude = longitude
        self.latitude = latitude
        self.opening_hours = opening_hours
        self.status = status  # 默认状态为正常运营
        self.description = description
        self.campus = campus
        self.area = area
        self.manager = manager
        self.contact = contact
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

    def __repr__(self):
        return f'<ParkingLot {self.name} ({self.id})>'

    # 获取停车场详情，包括车位使用情况
    def get_details(self, include_spaces=False):
        """获取停车场详情"""
        # 获取充电车位统计
        charging_stats = self.get_charging_spaces_stats()

        data = {
            'id': self.id,
            'name': self.name,
            'address': self.address,
            'total_spaces': self.total_spaces,
            'available_spaces': self.total_spaces - self.occupied_spaces,
            'occupied_spaces': self.occupied_spaces,
            'utilization_rate': round(self.occupied_spaces / self.total_spaces * 100, 2) if self.total_spaces > 0 else 0,
            'longitude': self.longitude,
            'latitude': self.latitude,
            'opening_hours': self.opening_hours,
            'status': self.status,
            'status_text': '正常运营' if self.status == 1 else '暂停使用',
            'description': self.description,
            'campus': self.campus,
            'area': self.area,
            'manager': self.manager,
            'contact': self.contact,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            # 充电车位统计
            'charging_spaces_total': charging_stats['total'],
            'charging_spaces_available': charging_stats['available'],
            'charging_spaces_occupied': charging_stats['occupied'],
            'charging_utilization_rate': charging_stats['utilization_rate']
        }

        if include_spaces:
            data['spaces'] = [space.get_details() for space in self.parking_spaces]

        return data

    # 获取充电车位统计
    def get_charging_spaces_stats(self):
        """获取充电车位统计"""
        try:
            # 避免循环导入
            from app.parkinglots.models import ParkingSpace

            # 使用try-except块处理可能的异常
            try:
                # 获取所有充电车位
                charging_spaces = ParkingSpace.query.filter_by(
                    parking_lot_id=self.id,
                    type=3  # 充电车位
                ).all()

                # 统计数据
                total = len(charging_spaces)
                occupied = sum(1 for space in charging_spaces if space.status == 1)
                available = sum(1 for space in charging_spaces if space.status == 0)
                utilization_rate = round(occupied / total * 100, 2) if total > 0 else 0

                return {
                    'total': total,
                    'occupied': occupied,
                    'available': available,
                    'utilization_rate': utilization_rate
                }
            except Exception as e:
                # 记录错误但返回默认值
                import logging
                logging.error(f"获取充电车位统计失败: {str(e)}")

                # 返回默认值
                return {
                    'total': 0,
                    'occupied': 0,
                    'available': 0,
                    'utilization_rate': 0
                }
        except ImportError as e:
            # 处理导入错误
            import logging
            logging.error(f"导入ParkingSpace模型失败: {str(e)}")

            # 返回默认值
            return {
                'total': 0,
                'occupied': 0,
                'available': 0,
                'utilization_rate': 0
            }

    # 创建停车场
    def create(self):
        db.session.add(self)
        db.session.commit()
        return self

    # 保存停车场 (与BaseModel兼容)
    def save(self):
        db.session.add(self)
        db.session.commit()
        return self

    # 删除停车场 (与BaseModel兼容)
    def delete(self):
        db.session.delete(self)
        db.session.commit()
        return True

    # 更新停车场信息
    def update(self, **kwargs):
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.now()
        db.session.commit()
        return self

    # 更新已占用车位数
    def update_occupied_spaces(self):
        """更新已占用车位数 - 使用事务确保数据一致性"""
        try:
            # 使用事务处理
            from app.parkinglots.models import ParkingSpace

            # 查询已占用车位数
            occupied = ParkingSpace.query.filter_by(parking_lot_id=self.id, status=1).count()

            # 查询维护中车位数
            maintenance = ParkingSpace.query.filter_by(parking_lot_id=self.id, status=2).count()

            # 查询总车位数
            total_spaces = ParkingSpace.query.filter_by(parking_lot_id=self.id).count()

            # 更新停车场信息
            self.occupied_spaces = occupied
            self.total_spaces = total_spaces

            # 记录日志
            logging.info(f"停车场 {self.name} (ID: {self.id}) 占用率: {occupied}/{total_spaces}, 维护中: {maintenance}")

            # 提交事务
            db.session.commit()
            return self

        except SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"更新停车场占用数失败: {str(e)}")
            raise ValueError(f"更新停车场占用数失败: {str(e)}")

# 停车位模型
class ParkingSpace(db.Model):
    __tablename__ = 'parking_spaces'
    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 所属停车场ID
    parking_lot_id = db.Column(db.Integer, db.ForeignKey('parking_lots.id'), nullable=False)
    # 车位编号
    space_number = db.Column(db.String(20), nullable=False)
    # 车位类型：1普通车位，2残疾人车位，3充电车位
    type = db.Column(db.Integer, nullable=False, default=1)
    # 车位状态：0空闲，1已占用，2故障，3维修中，4禁用
    status = db.Column(db.Integer, nullable=False, default=0)
    # 当前车辆ID（如果有）
    current_vehicle_id = db.Column(db.Integer, db.ForeignKey('bikes.b_id', ondelete='SET NULL'), nullable=True)
    # 充电功率（kW）- 仅充电车位
    power = db.Column(db.Float, nullable=True)
    # 备注
    remarks = db.Column(db.Text, nullable=True)
    # 最近维护时间
    last_maintenance_time = db.Column(db.DateTime, nullable=True)
    # 创建时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联停车记录
    parking_records = db.relationship('ParkingRecord', backref='parking_space', lazy=True)

    def __init__(self, parking_lot_id, space_number, type=1, status=0, power=None):
        self.parking_lot_id = parking_lot_id
        self.space_number = space_number
        self.type = type
        self.status = status  # 初始状态为空闲 (0)
        self.current_vehicle_id = None
        self.power = power  # 充电功率
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

    def __repr__(self):
        return f'<ParkingSpace {self.space_number} ({self.id})>'

    # 获取车位详情
    def get_details(self):
        """获取车位详情"""
        try:
            type_map = {1: '普通车位', 2: '残疾人车位', 3: '充电车位'}
            status_map = {0: '空闲', 1: '已占用', 2: '故障', 3: '维修中', 4: '禁用'}

            # 获取车辆信息
            vehicle_info = None
            if self.status == 1 and self.current_vehicle_id:
                try:
                    # 使用ORM关系获取车辆信息
                    from app.bikes.models import Bikes
                    vehicle = Bikes.query.get(self.current_vehicle_id)

                    if vehicle:
                        # 使用车辆的属性构建信息
                        b_num = vehicle.b_num
                        brand = vehicle.brand
                        color = vehicle.color
                        vehicle_info = f"{b_num} ({brand} {color})"
                    else:
                        # 如果没有找到车辆信息，尝试从活动的停车记录中获取
                        from app.parking_records.models import ParkingRecord
                        active_record = ParkingRecord.query.filter_by(
                            parking_space_id=self.id,
                            status=0  # 进行中
                        ).order_by(ParkingRecord.entry_time.desc()).first()

                        if active_record:
                            # 从停车记录中获取车辆ID
                            vehicle_id = active_record.vehicle_id
                            # 再次尝试获取车辆信息
                            vehicle = Bikes.query.get(vehicle_id)
                            if vehicle:
                                # 更新当前车辆ID以保持一致性
                                self.current_vehicle_id = vehicle_id
                                b_num = vehicle.b_num
                                brand = vehicle.brand
                                color = vehicle.color
                                vehicle_info = f"{b_num} ({brand} {color})"
                            else:
                                vehicle_info = f"车辆ID: {self.current_vehicle_id}"
                        else:
                            vehicle_info = f"车辆ID: {self.current_vehicle_id}"
                except Exception as e:
                    import logging
                    logging.error(f"获取车辆信息失败: {str(e)}")
                    vehicle_info = f"车辆ID: {self.current_vehicle_id}"

            # 安全地获取日期时间字符串
            def safe_isoformat(dt):
                try:
                    return dt.isoformat() if dt else None
                except Exception:
                    return None

            return {
                'id': self.id,
                'parking_lot_id': self.parking_lot_id,
                'space_number': self.space_number,
                'type': self.type,
                'type_text': type_map.get(self.type, '未知类型'),
                'status': self.status,
                'status_text': status_map.get(self.status, '未知状态'),
                'current_vehicle_id': self.current_vehicle_id,
                'vehicle_info': vehicle_info,
                'power': self.power,  # 充电功率
                'last_maintenance_time': safe_isoformat(self.last_maintenance_time),
                'created_at': safe_isoformat(self.created_at),
                'updated_at': safe_isoformat(self.updated_at)
            }
        except Exception as e:
            # 记录错误并返回简化版信息
            import logging
            logging.error(f"获取车位详情失败: {str(e)}")

            # 返回最基本的信息
            return {
                'id': self.id,
                'parking_lot_id': self.parking_lot_id,
                'space_number': self.space_number,
                'type': self.type,
                'status': self.status,
                'error': '获取详情失败'
            }

    # 创建车位
    def create(self):
        db.session.add(self)
        db.session.commit()
        return self

    # 保存车位 (与BaseModel兼容)
    def save(self):
        db.session.add(self)
        db.session.commit()
        return self

    # 删除车位 (与BaseModel兼容)
    def delete(self):
        db.session.delete(self)
        db.session.commit()
        return True

    # 更新车位信息
    def update(self, **kwargs):
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.now()
        db.session.commit()

        # 如果状态发生变化，更新停车场的已占用车位数
        if 'status' in kwargs:
            parking_lot = ParkingLot.query.get(self.parking_lot_id)
            if parking_lot:
                parking_lot.update_occupied_spaces()

        return self