"""empty message

Revision ID: 5d3db3023c74
Revises: 26f22139688a
Create Date: 2025-05-17 10:09:08.100436

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5d3db3023c74'
down_revision = '26f22139688a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('appeals', schema=None) as batch_op:
        batch_op.drop_index('uq_appeal_violation_id')
        batch_op.create_unique_constraint('uq_appeal_violation_id', ['violation_id'])

    with op.batch_alter_table('status_change_logs', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.alter_column('change_time',
               existing_type=sa.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_nullable=False)
        batch_op.drop_index('idx_status_change_logs_entity')
        batch_op.drop_index('idx_status_change_logs_operator')

    with op.batch_alter_table('vehicle_disable_records', schema=None) as batch_op:
        batch_op.alter_column('violation_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.create_foreign_key('fk_vehicle_disable_enable_operator', 'users', ['enable_operator_id'], ['u_id'])
        batch_op.create_foreign_key('fk_vehicle_disable_operator', 'users', ['operator_id'], ['u_id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('vehicle_disable_records', schema=None) as batch_op:
        batch_op.drop_constraint('fk_vehicle_disable_enable_operator', type_='foreignkey')
        batch_op.drop_constraint('fk_vehicle_disable_operator', type_='foreignkey')
        batch_op.alter_column('violation_id',
               existing_type=sa.INTEGER(),
               nullable=False)

    with op.batch_alter_table('status_change_logs', schema=None) as batch_op:
        batch_op.create_index('idx_status_change_logs_operator', ['operator_id'], unique=False)
        batch_op.create_index('idx_status_change_logs_entity', ['entity_type', 'entity_id'], unique=False)
        batch_op.alter_column('change_time',
               existing_type=sa.DateTime(),
               type_=sa.TIMESTAMP(),
               existing_nullable=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('appeals', schema=None) as batch_op:
        batch_op.drop_constraint('uq_appeal_violation_id', type_='unique')
        batch_op.create_index('uq_appeal_violation_id', ['violation_id'], unique=1)

    # ### end Alembic commands ###
