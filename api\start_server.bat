@echo off
echo ======================================
echo 校园电动车管理系统 - 服务器启动脚本
echo ======================================
echo.

REM 设置环境变量
set FLASK_APP=wsgi.py
set FLASK_ENV=development
set FLASK_DEBUG=1

echo 环境变量设置完成:
echo FLASK_APP=%FLASK_APP%
echo FLASK_ENV=%FLASK_ENV%
echo FLASK_DEBUG=%FLASK_DEBUG%
echo.

REM 检查参数
if "%1"=="help" goto :help
if "%1"=="prod" goto :prod
if "%1"=="debug" goto :debug

REM 默认启动开发服务器
:default
echo 正在启动开发服务器...
python wsgi.py
goto :end

REM 生产模式
:prod
echo 正在启动生产模式服务器...
set FLASK_ENV=production
set FLASK_DEBUG=0
python wsgi.py
goto :end

REM 调试模式
:debug
echo 正在启动调试模式服务器...
python -m flask run --host=0.0.0.0 --port=5000 --debugger --reload
goto :end

REM 帮助信息
:help
echo 使用方法:
echo   start_server.bat         - 启动开发服务器
echo   start_server.bat prod    - 启动生产模式服务器
echo   start_server.bat debug   - 启动调试模式服务器
echo   start_server.bat help    - 显示此帮助信息
echo.

:end
echo.
echo 服务器已关闭。
