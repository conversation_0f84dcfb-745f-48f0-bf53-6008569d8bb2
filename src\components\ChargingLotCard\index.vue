<template>
  <el-card class="charging-lot-card" :body-style="{ padding: '0px' }">
    <div class="card-header">
      <div class="lot-name">{{ lot.name }}</div>
      <div class="lot-status" :class="{ 'status-active': lot.status === 1, 'status-inactive': lot.status !== 1 }">
        {{ lot.status === 1 ? '正常运营' : '暂停使用' }}
      </div>
    </div>
    
    <div class="card-content">
      <div class="lot-tags">
        <el-tag v-if="lot.campus" size="small" type="primary">{{ lot.campus }}</el-tag>
        <el-tag v-if="lot.area" size="small" type="success">{{ lot.area }}</el-tag>
      </div>
      
      <div class="lot-address">
        <i class="el-icon-location"></i>
        <span>{{ lot.address }}</span>
      </div>
      
      <div class="charging-stats">
        <div class="utilization-rate">
          <div class="rate-label">充电车位利用率</div>
          <div class="rate-value">{{ chargingStats.utilization_rate }}%</div>
          <el-progress 
            :percentage="chargingStats.utilization_rate" 
            :color="getProgressColor(chargingStats.utilization_rate)"
          ></el-progress>
        </div>
        
        <div class="spaces-count">
          <div class="count-item">
            <div class="count-label">已用</div>
            <div class="count-value">{{ chargingStats.occupied }}</div>
          </div>
          <div class="count-item">
            <div class="count-label">总数</div>
            <div class="count-value">{{ chargingStats.total }}</div>
          </div>
          <div class="count-item">
            <div class="count-label">空闲</div>
            <div class="count-value">{{ chargingStats.available }}</div>
          </div>
        </div>
      </div>
      
      <div class="lot-info">
        <div class="info-item">
          <i class="el-icon-time"></i>
          <span>开放时间: {{ lot.opening_hours || '24小时' }}</span>
        </div>
        <div class="info-item">
          <i class="el-icon-user"></i>
          <span>管理员: {{ lot.manager || '未设置' }}</span>
        </div>
        <div class="info-item">
          <i class="el-icon-phone"></i>
          <span>联系电话: {{ lot.contact || '未设置' }}</span>
        </div>
      </div>
    </div>
    
    <div class="card-footer">
      <el-button 
        type="primary" 
        size="small" 
        @click="$emit('view-details', lot)"
      >
        查看详情
      </el-button>
      <el-button 
        type="success" 
        size="small" 
        :disabled="chargingStats.available === 0 || lot.status !== 1"
        @click="$emit('start-charging', lot)"
      >
        我要充电
      </el-button>
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'ChargingLotCard',
  props: {
    lot: {
      type: Object,
      required: true
    }
  },
  computed: {
    chargingStats() {
      // 如果停车场对象中有充电车位统计信息，则直接使用
      if (this.lot.charging_spaces_stats) {
        return this.lot.charging_spaces_stats;
      }
      
      // 否则使用默认值
      return {
        total: this.lot.charging_spaces_total || 0,
        occupied: this.lot.charging_spaces_occupied || 0,
        available: this.lot.charging_spaces_available || 0,
        utilization_rate: this.lot.charging_utilization_rate || 0
      };
    }
  },
  methods: {
    getProgressColor(rate) {
      if (rate < 60) return '#67c23a';  // 绿色
      if (rate < 80) return '#e6a23c';  // 黄色
      return '#f56c6c';  // 红色
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-lot-card {
  margin-bottom: 20px;
  transition: all 0.3s;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  .card-header {
    padding: 15px;
    background-color: #f5f7fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
    
    .lot-name {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }
    
    .lot-status {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 10px;
      
      &.status-active {
        background-color: #f0f9eb;
        color: #67c23a;
      }
      
      &.status-inactive {
        background-color: #fef0f0;
        color: #f56c6c;
      }
    }
  }
  
  .card-content {
    padding: 15px;
    
    .lot-tags {
      margin-bottom: 10px;
      
      .el-tag {
        margin-right: 5px;
      }
    }
    
    .lot-address {
      display: flex;
      align-items: flex-start;
      margin-bottom: 15px;
      color: #606266;
      
      i {
        margin-right: 5px;
        margin-top: 3px;
      }
    }
    
    .charging-stats {
      background-color: #f5f7fa;
      border-radius: 4px;
      padding: 10px;
      margin-bottom: 15px;
      
      .utilization-rate {
        margin-bottom: 10px;
        
        .rate-label {
          font-size: 14px;
          color: #606266;
          margin-bottom: 5px;
        }
        
        .rate-value {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }
      }
      
      .spaces-count {
        display: flex;
        justify-content: space-between;
        
        .count-item {
          text-align: center;
          flex: 1;
          
          .count-label {
            font-size: 12px;
            color: #909399;
          }
          
          .count-value {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
          }
        }
      }
    }
    
    .lot-info {
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        color: #606266;
        
        i {
          margin-right: 5px;
          width: 16px;
          text-align: center;
        }
      }
    }
  }
  
  .card-footer {
    padding: 10px 15px;
    border-top: 1px solid #ebeef5;
    display: flex;
    justify-content: flex-end;
    
    .el-button {
      margin-left: 10px;
    }
  }
}
</style>
