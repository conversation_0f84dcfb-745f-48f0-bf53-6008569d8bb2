<template>
  <div class="app-container">
    <el-card class="appeal-card">
      <div slot="header" class="appeal-card-header">
        <div class="header-left">
          <el-button icon="el-icon-back" size="small" type="text" @click="goBack">返回</el-button>
          <h2>提交申诉</h2>
        </div>
        <div class="header-right">
          <el-tag type="warning">{{ violationDetail.status === 1 ? '已处理' : '' }}</el-tag>
        </div>
      </div>

      <div v-loading="loading" class="appeal-content">
        <div v-if="loading" class="loading-placeholder">
          <i class="el-icon-loading"></i>
          <p>正在加载违规记录信息...</p>
        </div>

        <appeal-form
          v-if="!loading && violationDetail.id"
          :violation-detail="violationDetail"
          :submit-method="submitAppeal"
          @success="handleSuccess"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { getViolationDetail, createAppeal } from '@/api/violations'
import AppealForm from '../components/AppealForm'

export default {
  name: 'UserViolationAppeal',
  components: {
    AppealForm
  },
  data() {
    return {
      loading: true,
      violationDetail: {}
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      const id = this.$route.params.id

      console.log('申诉页面 - 获取违规ID:', id)

      getViolationDetail(id)
        .then(response => {
          console.log('申诉页面 - 获取违规详情响应:', response)

          if (!response.data || !response.data.violation) {
            console.error('申诉页面 - 响应中缺少违规记录数据')
            this.$message.error('获取违规详情失败：数据格式错误')
            this.loading = false
            this.goBack()
            return
          }

          this.violationDetail = response.data.violation
          console.log('申诉页面 - 设置违规详情:', this.violationDetail)

          // 检查违规记录ID是否存在
          if (!this.violationDetail.id) {
            console.error('申诉页面 - 违规记录缺少ID')
            this.$message.error('违规记录数据不完整，无法提交申诉')
            this.loading = false
            this.goBack()
            return
          }

          // 检查是否可以申诉
          if (this.violationDetail.status === 0) {
            this.$message.warning('该违规记录正在审核中，暂不能申诉')
            this.goBack()
            return
          }

          if (this.violationDetail.status === 2) {
            this.$message.warning('该违规记录已经在申诉中')
            this.goBack()
            return
          }

          if (this.violationDetail.status === 3) {
            this.$message.warning('该违规记录已经被撤销，无需申诉')
            this.goBack()
            return
          }

          // 只允许已处理(状态为1)的违规记录进行申诉
          if (this.violationDetail.status !== 1) {
            console.warn('申诉页面 - 违规记录状态不是"已处理":', this.violationDetail.status)
            this.$message.warning('只有已处理的违规记录才能申诉')
            this.goBack()
            return
          }

          this.loading = false
        })
        .catch(error => {
          console.error('申诉页面 - 获取违规详情失败:', error)
          this.$message.error('获取违规详情失败')
          this.loading = false
          this.goBack()
        })
    },
    submitAppeal(data) {
      console.log('申诉页面 - 提交申诉数据:', data)

      // 确保数据中包含违规ID
      if (!data.violation_id) {
        console.error('申诉页面 - 提交数据缺少违规ID')
        return Promise.reject(new Error('申诉数据不完整：缺少违规记录ID'))
      }

      return createAppeal(data)
        .then(response => {
          console.log('申诉页面 - 提交申诉响应:', response)
          return response
        })
        .catch(error => {
          console.error('申诉页面 - 提交申诉失败:', error)
          throw error
        })
    },
    handleSuccess(data) {
      this.$message.success('申诉提交成功')
      this.$router.push({ name: 'MyViolationDetail', params: { id: this.violationDetail.id }})
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .appeal-card {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;

    .appeal-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        display: flex;
        align-items: center;

        h2 {
          margin: 0 0 0 10px;
          font-size: 18px;
          color: #303133;
        }
      }
    }

    .appeal-content {
      min-height: 300px;

      .loading-placeholder {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 300px;

        i {
          font-size: 32px;
          color: #409EFF;
          margin-bottom: 15px;
        }

        p {
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
