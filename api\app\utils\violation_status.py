"""
违规记录和申诉状态管理工具

提供统一的状态定义、状态转换和状态同步功能，确保违规记录、申诉和车辆禁用状态的一致性。
"""

from app import db
from app.utils.constants import VehicleStatus
from app.tasks.check_vehicle_disable import enable_vehicle, disable_vehicle
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# 违规记录状态定义
class ViolationStatus:
    # 状态值
    PENDING = 0      # 待审核
    PROCESSED = 1    # 已处理
    APPEALING = 2    # 申诉中
    REVOKED = 3      # 已撤销

    # 状态文本
    TEXT_PENDING = "待审核"
    TEXT_PROCESSED = "已处理"
    TEXT_APPEALING = "申诉中"
    TEXT_REVOKED = "已撤销"

    # 状态映射
    STATUS_TEXT = {
        PENDING: TEXT_PENDING,
        PROCESSED: TEXT_PROCESSED,
        APPEALING: TEXT_APPEALING,
        REVOKED: TEXT_REVOKED
    }

    @classmethod
    def get_text(cls, status):
        """获取状态文本"""
        return cls.STATUS_TEXT.get(status, "未知状态")

# 申诉状态定义
class AppealStatus:
    # 状态值
    PENDING = 0      # 待审核
    APPROVED = 1     # 已通过
    REJECTED = 2     # 未通过

    # 状态文本
    TEXT_PENDING = "待审核"
    TEXT_APPROVED = "已通过"
    TEXT_REJECTED = "未通过"

    # 状态映射
    STATUS_TEXT = {
        PENDING: TEXT_PENDING,
        APPROVED: TEXT_APPROVED,
        REJECTED: TEXT_REJECTED
    }

    @classmethod
    def get_text(cls, status):
        """获取状态文本"""
        return cls.STATUS_TEXT.get(status, "未知状态")

def create_appeal(violation_id, user_id, reason, **kwargs):
    """
    创建申诉记录并更新违规记录状态

    Args:
        violation_id: 违规记录ID
        user_id: 用户ID
        reason: 申诉理由
        **kwargs: 其他参数

    Returns:
        tuple: (是否成功, 消息, 申诉记录)
    """
    from app.violations.models import ViolationRecord, Appeal

    try:
        # 开始事务
        db.session.begin_nested()

        # 查找违规记录
        violation = ViolationRecord.query.get(violation_id)
        if not violation:
            return False, "违规记录不存在", None

        # 验证是否是违规记录的所有者
        if user_id != violation.user_id:
            return False, "只有违规记录的所有者才能提交申诉", None

        # 验证违规记录状态是否允许申诉
        if violation.status == ViolationStatus.PENDING:
            return False, "该违规记录正在审核中，暂不能申诉", None
        if violation.status == ViolationStatus.APPEALING:
            return False, "该违规记录已经在申诉中", None
        if violation.status == ViolationStatus.REVOKED:
            return False, "该违规记录已经被撤销，无需申诉", None
        # 只有状态为PROCESSED（已处理）的记录允许申诉

        # 检查是否已存在申诉记录
        existing_appeal = Appeal.query.filter_by(violation_id=violation_id).first()
        if existing_appeal:
            return False, "该违规记录已有申诉，不能重复提交", None

        # 创建申诉记录
        appeal = Appeal(
            violation_id=violation_id,
            user_id=user_id,
            reason=reason,
            status=AppealStatus.PENDING  # 默认为待审核状态
        )

        # 更新违规记录状态为"申诉中"
        violation.status = ViolationStatus.APPEALING

        # 保存到数据库
        db.session.add(appeal)
        db.session.commit()

        return True, "申诉提交成功", appeal

    except Exception as e:
        db.session.rollback()
        logger.error(f"创建申诉记录失败: {str(e)}")
        return False, f"创建申诉记录失败: {str(e)}", None

def handle_appeal(appeal_id, status, comment, handler_id, **kwargs):
    """
    处理申诉记录并同步更新违规记录和车辆状态

    Args:
        appeal_id: 申诉记录ID
        status: 申诉状态（1-已通过，2-未通过）
        comment: 处理意见
        handler_id: 处理人ID
        **kwargs: 其他参数

    Returns:
        tuple: (是否成功, 消息, 申诉记录)
    """
    from app.violations.models import Appeal, ViolationRecord

    try:
        # 开始事务
        db.session.begin_nested()

        # 查找申诉记录
        appeal = Appeal.query.get(appeal_id)
        if not appeal:
            return False, "申诉记录不存在", None

        # 获取关联的违规记录
        violation = ViolationRecord.query.get(appeal.violation_id)
        if not violation:
            return False, "关联的违规记录不存在", None

        # 记录原始状态，用于后续判断状态变化
        original_appeal_status = appeal.status

        # 更新申诉记录
        appeal.status = status
        appeal.comment = comment
        appeal.handler_id = handler_id
        appeal.updated_at = datetime.now()

        # 根据申诉处理结果更新违规记录状态和车辆状态
        if status == AppealStatus.APPROVED:  # 申诉通过
            # 更新违规记录状态为"已撤销"
            violation.status = ViolationStatus.REVOKED
            violation.result = f"申诉已通过: {comment}"
            violation.handler_id = handler_id
            violation.updated_at = datetime.now()

            # 如果违规记录关联了车辆，则解除禁用
            if violation.bike_id:
                try:
                    # 解除车辆禁用
                    success, message, record_ids = enable_vehicle(
                        bike_id=violation.bike_id,
                        operator_id=handler_id,
                        reason=f"申诉已通过: {comment}"
                    )

                    if not success:
                        logger.warning(f"解除车辆禁用失败: {message}")
                        # 这里我们不回滚事务，但记录警告日志
                except Exception as e:
                    logger.error(f"解除车辆禁用时发生异常: {str(e)}")
                    # 这里我们不回滚事务，但记录错误日志

        elif status == AppealStatus.REJECTED:  # 申诉未通过
            # 更新违规记录状态为"已处理"
            violation.status = ViolationStatus.PROCESSED
            violation.result = f"申诉未通过: {comment}"
            violation.handler_id = handler_id
            violation.updated_at = datetime.now()

            # 如果违规记录关联了车辆，确保车辆处于禁用状态
            if violation.bike_id:
                try:
                    # 检查车辆是否已被禁用
                    from app.utils.vehicle_status import is_vehicle_disabled
                    is_disabled, reason, _ = is_vehicle_disabled(violation.bike_id)

                    if not is_disabled:
                        # 禁用车辆
                        success, message, record_id = disable_vehicle(
                            bike_id=violation.bike_id,
                            violation_id=violation.id,
                            reason=f"申诉未通过: {comment}",
                            operator_id=handler_id
                        )

                        if not success:
                            logger.warning(f"禁用车辆失败: {message}")
                            # 这里我们不回滚事务，但记录警告日志
                except Exception as e:
                    logger.error(f"禁用车辆时发生异常: {str(e)}")
                    # 这里我们不回滚事务，但记录错误日志

        # 保存到数据库
        db.session.commit()

        # 记录状态变更日志
        log_status_change(
            entity_type="appeal",
            entity_id=appeal_id,
            old_status=original_appeal_status,
            new_status=status,
            operator_id=handler_id,
            related_entities=[
                {"type": "violation", "id": violation.id, "status": violation.status}
            ]
        )

        return True, "申诉处理成功", appeal

    except Exception as e:
        db.session.rollback()
        logger.error(f"处理申诉记录失败: {str(e)}")
        return False, f"处理申诉记录失败: {str(e)}", None

def log_status_change(entity_type, entity_id, old_status, new_status, operator_id, related_entities=None):
    """
    记录状态变更日志

    Args:
        entity_type: 实体类型（violation, appeal）
        entity_id: 实体ID
        old_status: 旧状态
        new_status: 新状态
        operator_id: 操作人ID
        related_entities: 相关实体列表
    """
    try:
        from app.violations.models import StatusChangeLog

        # 创建状态变更日志
        log = StatusChangeLog(
            entity_type=entity_type,
            entity_id=entity_id,
            old_status=old_status,
            new_status=new_status,
            operator_id=operator_id,
            change_time=datetime.now(),
            related_entities=str(related_entities) if related_entities else None
        )

        # 保存到数据库
        db.session.add(log)
        db.session.commit()

        logger.info(f"状态变更日志已记录: {entity_type} {entity_id} 从 {old_status} 变更为 {new_status}")

    except Exception as e:
        logger.error(f"记录状态变更日志失败: {str(e)}")
        # 日志记录失败不影响主流程，所以不回滚事务
