import request from '@/utils/request'

/**
 * 获取当前用户的车辆列表
 * @param {object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getMyVehicles(params = {}) {
  return request({
    url: `/api/my-bikes`,
    method: 'get',
    params
  })
}

/**
 * 获取用户车辆列表
 * @param {number} userId 用户ID
 * @param {object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getUserVehicles(userId, params = {}) {
  return request({
    url: `/api/my-bikes`,
    method: 'get',
    params
  })
}

/**
 * 获取所有车辆列表
 * @param {object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getAllVehicles(params) {
  return request({
    url: '/api/bikes',
    method: 'get',
    params
  })
}

/**
 * 获取车辆详情
 * @param {number} vehicleId 车辆ID
 * @returns {Promise} 请求Promise
 */
export function getVehicleDetail(vehicleId) {
  return request({
    url: `/api/bikes/${vehicleId}`,
    method: 'get'
  })
}

/**
 * 创建车辆
 * @param {object} data 车辆数据
 * @returns {Promise} 请求Promise
 */
export function createVehicle(data) {
  return request({
    url: '/api/bikes',
    method: 'post',
    data
  })
}

/**
 * 更新车辆
 * @param {number} vehicleId 车辆ID
 * @param {object} data 车辆数据
 * @returns {Promise} 请求Promise
 */
export function updateVehicle(vehicleId, data) {
  return request({
    url: `/api/bikes/${vehicleId}`,
    method: 'put',
    data
  })
}

/**
 * 删除车辆
 * @param {number} vehicleId 车辆ID
 * @returns {Promise} 请求Promise
 */
export function deleteVehicle(vehicleId) {
  return request({
    url: `/api/bikes/${vehicleId}`,
    method: 'delete'
  })
}

/**
 * 获取车辆统计数据
 * @returns {Promise} 请求Promise
 */
export function getVehicleStats() {
  return request({
    url: '/api/bikes/stats',
    method: 'get'
  })
}

/**
 * 批量更新车辆状态
 * @param {object} data 批量更新数据
 * @returns {Promise} 请求Promise
 */
export function batchUpdateVehicleStatus(data) {
  return request({
    url: '/api/bikes/batch-update',
    method: 'post',
    data
  })
}

/**
 * 导出车辆数据为CSV
 * @returns {Promise} 请求Promise
 */
export function exportVehiclesCSV() {
  return request({
    url: '/api/bikes/export',
    method: 'get',
    responseType: 'blob'
  })
}
