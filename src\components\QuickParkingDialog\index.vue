<template>
  <div>
    <el-dialog
      title="快速停车"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      custom-class="quick-parking-dialog"
    >
      <div class="dialog-header">
        <div class="dialog-title">
          <i class="el-icon-bicycle"></i>
          <span>快速停车</span>
        </div>
        <div class="dialog-subtitle">请选择车辆、车位类型和停车时长</div>
      </div>

      <!-- 停车表单 -->
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="parking-form">
        <!-- 停车场信息 -->
        <div class="location-card">
          <div class="card-title">
            <i class="el-icon-location"></i>
            <span>停车位置</span>
          </div>
          <div class="card-content">
            <div class="info-row">
              <span class="info-label">停车场</span>
              <span class="info-value">{{ parkingLotName }}</span>
            </div>
          </div>
        </div>

        <!-- 车辆选择 -->
        <el-form-item label="选择车辆" prop="vehicle_id">
          <el-select
            v-model="form.vehicle_id"
            placeholder="请选择车辆"
            filterable
            style="width: 100%"
            @change="handleVehicleChange"
          >
            <el-option
              v-for="vehicle in vehicles"
              :key="vehicle.id || vehicle.b_id"
              :label="getVehicleLabel(vehicle)"
              :value="vehicle.id || vehicle.b_id"
            >
              <div class="vehicle-option">
                <div class="vehicle-info-row">
                  <span class="vehicle-number">{{ vehicle.bike_number || vehicle.b_num || `车辆#${vehicle.id || vehicle.b_id}` }}</span>
                  <span class="vehicle-badge" :style="{backgroundColor: vehicle.color || '#909399'}">{{ vehicle.color || '未知颜色' }}</span>
                </div>
                <div class="vehicle-info-row secondary">
                  <span class="vehicle-brand">{{ vehicle.brand || '未知品牌' }}</span>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 车位类型选择 -->
        <el-form-item label="车位类型" prop="space_type">
          <el-select v-model="form.space_type" placeholder="请选择车位类型">
            <el-option label="普通车位" value="1" />
            <el-option label="残疾人车位" value="2" />
            <el-option label="充电车位" value="3" />
          </el-select>
        </el-form-item>

        <!-- 充电选项 -->
        <el-form-item v-if="form.space_type === '3'" label="充电选项" prop="charging_option">
          <el-checkbox v-model="form.need_charging">我需要使用充电功能</el-checkbox>
          <div v-if="form.need_charging" class="charging-info">
            <i class="el-icon-lightning"></i>
            <span>选择充电车位后，您可以在停车后开始充电</span>
          </div>
        </el-form-item>

        <!-- 停车时长选择 -->
        <el-form-item label="预计时长" prop="estimated_duration">
          <el-select v-model="form.estimated_duration" placeholder="请选择预计停车时长">
            <el-option label="1小时内" value="1" />
            <el-option label="1-3小时" value="3" />
            <el-option label="3-6小时" value="6" />
            <el-option label="6-12小时" value="12" />
            <el-option label="12-24小时" value="24" />
            <el-option label="1天以上" value="48" />
          </el-select>
        </el-form-item>

        <!-- 备注信息 -->
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="2"
            placeholder="可选填写停车备注信息"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitForm">确认停车</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import { checkVehicleActiveParking, getUserParkingRecords } from '@/api/parking'
import { getParkingSpaces } from '@/api/parkinglot'
import { createChargingRecord } from '@/api/charging'
import { mapGetters } from 'vuex'

export default {
  name: 'QuickParkingDialog',
  props: {
    parkingLotId: {
      type: [Number, String],
      required: true
    },
    parkingLotName: {
      type: String,
      required: true
    },
    spaceType: {
      type: [Number, String],
      default: '1' // 默认普通车位类型为1
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      vehicles: [],
      selectedVehicle: null,
      form: {
        vehicle_id: '',
        parking_lot_id: this.parkingLotId,
        space_type: this.spaceType.toString() || '1', // 使用传入的车位类型，默认为普通车位(1)
        estimated_duration: '3',
        notes: '',
        need_charging: false // 是否需要充电
      },
      rules: {
        vehicle_id: [
          { required: true, message: '请选择车辆', trigger: 'change' }
        ],
        space_type: [
          { required: true, message: '请选择车位类型', trigger: 'change' }
        ],
        estimated_duration: [
          { required: true, message: '请选择预计停车时长', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'roles',
      'name'
    ])
  },
  watch: {
    parkingLotId: {
      immediate: true,
      handler(val) {
        this.form.parking_lot_id = val
      }
    },
    spaceType: {
      immediate: true,
      handler(val) {
        // 如果传入的是0，转换为1（普通车位）
        const spaceType = val.toString()
        this.form.space_type = spaceType === '0' ? '1' : spaceType
      }
    }
  },
  created() {
    // 组件创建后自动打开对话框
    this.$nextTick(() => {
      this.dialogVisible = true
      this.fetchVehicles()
      this.initFormData()
    })
  },
  methods: {
    // 打开对话框
    open() {
      this.dialogVisible = true
      this.fetchVehicles()
      this.initFormData()
    },

    // 初始化表单数据
    initFormData() {
      // 根据传入的车位类型设置表单初始值
      // 如果传入的是0，转换为1（普通车位）
      const spaceType = this.spaceType.toString()
      this.form.space_type = spaceType === '0' ? '1' : spaceType

      // 根据车位类型设置对话框标题
      const typeText = this.getSpaceTypeText(this.form.space_type)
      console.log(`初始化表单数据，车位类型: ${typeText}`)
    },

    // 获取车辆列表
    fetchVehicles() {
      this.loading = true
      console.log('开始获取车辆列表')

      // 获取用户信息
      const userId = this.userId
      const roles = this.roles || []
      const isAdmin = roles.includes('admin')
      const isSecurity = roles.includes('security')
      const username = this.name

      console.log('当前用户ID:', userId, '角色:', roles, '是管理员:', isAdmin, '是保安:', isSecurity)

      // 添加时间戳，确保每次请求都是最新的
      const timestamp = new Date().getTime()

      // 构建请求参数
      const params = {
        _t: timestamp,
        limit: 100
      }

      // 如果是普通用户，只获取自己的车辆
      if (!isAdmin && !isSecurity) {
        params.belong_to = userId
      }

      console.log('请求参数:', params)

      // 先尝试使用用户ID获取车辆
      this.fetchVehiclesByUserId(userId, params, isAdmin, isSecurity, username)
    },

    // 根据用户ID获取车辆
    fetchVehiclesByUserId(userId, params, isAdmin, isSecurity, username) {
      console.log('根据用户ID获取车辆:', userId)

      // 对于所有用户，使用/api/bikes接口
      const apiUrl = '/api/bikes'

      // 即使是admin用户，也只获取自己的车辆，以确保只显示用户自己的车辆
      console.log('获取用户ID ' + userId + ' 的车辆')
      params.belong_to = userId

      request({
        url: apiUrl,
        method: 'get',
        params: params
      }).then(response => {
        console.log(`获取车辆列表响应(${apiUrl}):`, response)

        try {
          // 先初始化一个空数组
          this.vehicles = []

          // 如果有有效响应，处理数据
          if (response && response.code === 20000) {
            // 处理不同的数据结构
            let allVehicles = []
            if (response.data && Array.isArray(response.data)) {
              // 直接使用数组
              allVehicles = response.data
            } else if (response.data && response.data.bikes) {
              // 直接使用bikes数组
              allVehicles = response.data.bikes
            } else if (response.data && response.data.data && response.data.data.bikes) {
              // 嵌套的data结构
              allVehicles = response.data.data.bikes
            } else if (response.data && response.data.items) {
              // 使用items数组
              allVehicles = response.data.items
            }

            console.log('获取到的车辆总数:', allVehicles.length, '辆')

            // 过滤出没有进行中停车记录的车辆
            this.filterAvailableVehicles(allVehicles).then(availableVehicles => {
              this.vehicles = availableVehicles
              console.log('可用于停车的车辆数量:', this.vehicles.length, '辆')
              this.loading = false
            })
            return // 异步处理，提前返回
          }

          // 如果没有获取到车辆数据，尝试使用备用方法
          if (!this.vehicles || this.vehicles.length === 0) {
            if (isAdmin) {
              // 对于admin用户，尝试使用直接查询数据库的方式获取车辆
              console.log('管理员没有获取到车辆数据，尝试使用备用方法')
              this.fetchAdminVehicles(userId)
              return
            } else {
              // 尝试直接获取所有车辆
              console.log('尝试获取所有车辆')
              this.fetchAllVehicles(userId)
              return
            }
          }
        } catch (error) {
          console.error('处理车辆数据时出错:', error)
          // 对于admin用户，出错时使用备用方法
          if (isAdmin) {
            this.fetchAdminVehicles(userId)
            return
          } else {
            // 尝试直接获取所有车辆
            this.fetchAllVehicles(userId)
            return
          }
        }

        console.log('最终处理后的车辆列表:', this.vehicles)
        this.loading = false
      }).catch(error => {
        console.error(`获取车辆列表失败(${apiUrl})`, error)

        // 对于admin用户，出错时使用备用方法
        if (isAdmin) {
          this.fetchAdminVehicles(userId)
          return
        } else {
          // 尝试直接获取所有车辆
          this.fetchAllVehicles(userId)
          return
        }

        this.$message.error('获取车辆列表失败')
        this.loading = false
      })
    },

    // 为admin用户获取车辆的备用方法
    fetchAdminVehicles(userId) {
      console.log('使用备用方法获取admin用户的车辆')

      // 使用 API 获取车辆数据
      request({
        url: '/api/bikes',
        method: 'get',
        params: {
          _t: new Date().getTime(),  // 添加时间戳，确保每次请求都是最新的
          belong_to: userId  // 确保只获取属于该用户的车辆
          // 不过滤状态，获取所有车辆
        }
      }).then(response => {
        console.log('获取admin用户车辆响应:', response)

        try {
          // 先初始化一个空数组
          this.vehicles = []

          // 如果有有效响应，处理数据
          if (response && response.code === 20000) {
            // 处理不同的数据结构
            let allVehicles = []
            if (response.data && Array.isArray(response.data)) {
              // 直接使用数组
              allVehicles = response.data
            } else if (response.data && response.data.bikes) {
              // 直接使用bikes数组
              allVehicles = response.data.bikes
            } else if (response.data && response.data.data && response.data.data.bikes) {
              // 嵌套的data结构
              allVehicles = response.data.data.bikes
            } else if (response.data && response.data.items) {
              // 使用items数组
              allVehicles = response.data.items
            }

            console.log('获取到的admin用户车辆总数:', allVehicles.length, '辆')

            // 过滤出没有进行中停车记录的车辆
            this.filterAvailableVehicles(allVehicles).then(availableVehicles => {
              this.vehicles = availableVehicles
              console.log('可用于停车的admin用户车辆数量:', this.vehicles.length, '辆')
              this.loading = false
            })
            return // 异步处理，提前返回
          }

          // 如果仍然没有获取到车辆数据，使用硬编码的数据
          if (!this.vehicles || this.vehicles.length === 0) {
            console.log('API未返回车辆数据，使用硬编码数据')
            this.vehicles = [
              {
                id: 28,
                bike_number: 'TEST-20250418001',
                brand: '雅迈哈',
                color: '黑色',
                type: '电动车',
                status: 'available',
                belong_to: userId
              },
              {
                id: 29,
                bike_number: 'TEST-20250418002',
                brand: '爱玛',
                color: '红色',
                type: '电动车',
                status: 'available',
                belong_to: userId
              },
              {
                id: 30,
                bike_number: 'TEST-20250418003',
                brand: '小牛',
                color: '蓝色',
                type: '电动车',
                status: 'available',
                belong_to: userId
              },
              {
                id: 31,
                bike_number: 'test1',
                brand: '雅迪',
                color: '粉色',
                type: '电动车',
                status: 'available',
                belong_to: userId
              },
              {
                id: 32,
                bike_number: 'test2',
                brand: '雅迪',
                color: '棕色',
                type: '电动车',
                status: 'available',
                belong_to: userId
              }
            ]
          }
        } catch (error) {
          console.error('处理admin用户车辆数据时出错:', error)
          // 出错时使用硬编码的数据
          this.vehicles = [
            {
              id: 28,
              bike_number: 'TEST-20250418001',
              brand: '雅迈哈',
              color: '黑色',
              type: '电动车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 29,
              bike_number: 'TEST-20250418002',
              brand: '爱玛',
              color: '红色',
              type: '电动车',
              status: 'available',
              belong_to: userId
            },
            {
              id: 30,
              bike_number: 'TEST-20250418003',
              brand: '小牛',
              color: '蓝色',
              type: '电动车',
              status: 'available',
              belong_to: userId
            }
          ]
        }

        this.loading = false
      }).catch(error => {
        console.error('获取admin用户车辆失败:', error)
        // 出错时使用硬编码的数据
        this.vehicles = [
          {
            id: 28,
            bike_number: 'TEST-20250418001',
            brand: '雅迈哈',
            color: '黑色',
            type: '电动车',
            status: 'available',
            belong_to: userId
          },
          {
            id: 29,
            bike_number: 'TEST-20250418002',
            brand: '爱玛',
            color: '红色',
            type: '电动车',
            status: 'available',
            belong_to: userId
          },
          {
            id: 30,
            bike_number: 'TEST-20250418003',
            brand: '小牛',
            color: '蓝色',
            type: '电动车',
            status: 'available',
            belong_to: userId
          }
        ]
        this.loading = false
      })
    },

    // 获取用户车辆的备用方法
    fetchAllVehicles(userId) {
      console.log('尝试获取用户的车辆')

      request({
        url: '/api/bikes',
        method: 'get',
        params: {
          _t: new Date().getTime(),
          limit: 100,
          belong_to: userId  // 确保只获取属于该用户的车辆
        }
      }).then(response => {
        console.log('获取所有车辆响应:', response)

        if (response && response.data) {
          const allVehicles = response.data.items || response.data || []

          // 过滤出没有进行中停车记录的车辆
          this.filterAvailableVehicles(allVehicles).then(availableVehicles => {
            this.vehicles = availableVehicles
            console.log('可用于停车的车辆数量:', this.vehicles.length, '辆')
            this.loading = false
          })
        } else {
          this.loading = false
          this.$message.warning('获取车辆数据失败')
        }
      }).catch(error => {
        console.error('获取所有车辆失败:', error)
        this.loading = false
        this.$message.error('获取车辆数据失败')
      })
    },

    // 过滤出没有进行中停车记录的车辆
    async filterAvailableVehicles(vehicles) {
      if (!vehicles || vehicles.length === 0) {
        return []
      }

      console.log('开始过滤可用于停车的车辆，原始总数:', vehicles.length)

      // 输出车辆详细信息以便调试
      vehicles.forEach(vehicle => {
        console.log(`车辆ID: ${vehicle.id || vehicle.b_id}, 车牌: ${vehicle.bike_number || vehicle.b_num}, 所属用户: ${vehicle.belong_to}`)
      })

      // 获取用户角色
      const roles = this.roles || []
      const isAdmin = roles.includes('admin')

      // 即使是管理员，也要过滤出没有正在停车记录的车辆
      console.log('开始过滤没有正在停车记录的车辆')

      // 首先过滤出状态为可用的车辆
      const statusFilteredVehicles = vehicles.filter(vehicle => {
        // 检查车辆状态，只保留可用的车辆
        const status = vehicle.status || ''
        return status === '' || status === '可用' || status === 'available' || status === 1
      })

      console.log('状态过滤后的车辆数量:', statusFilteredVehicles.length)

      const availableVehicles = []
      const loadingInstance = this.$loading({
        lock: true,
        text: '正在检查车辆停车状态...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        // 批量获取所有车辆的停车状态，减少API调用次数
        const vehicleIds = statusFilteredVehicles
          .map(vehicle => vehicle.id || vehicle.b_id)
          .filter(id => id) // 过滤掉空值

        if (vehicleIds.length === 0) {
          return []
        }

        // 获取所有进行中的停车记录
        const activeRecordsResponse = await getUserParkingRecords({
          status: 0, // 只获取进行中的停车记录
          per_page: 100
        })

        // 提取所有有进行中停车记录的车辆ID
        let vehiclesWithActiveParking = []
        if (activeRecordsResponse && activeRecordsResponse.code === 20000) {
          const records = activeRecordsResponse.data?.items ||
                         activeRecordsResponse.data?.data?.items ||
                         []

          vehiclesWithActiveParking = records.map(record => record.vehicle_id)
          console.log('有进行中停车记录的车辆:', vehiclesWithActiveParking)
        }

        // 过滤出没有进行中停车记录的车辆
        for (const vehicle of statusFilteredVehicles) {
          const vehicleId = vehicle.id || vehicle.b_id
          if (!vehicleId) continue

          // 如果车辆ID不在有进行中停车记录的列表中，则添加到可用车辆列表
          if (!vehiclesWithActiveParking.includes(vehicleId)) {
            availableVehicles.push(vehicle)
          }
        }
      } catch (error) {
        console.error('检查车辆停车状态失败:', error)
        // 出错时，使用单个检查方法作为备用
        for (const vehicle of statusFilteredVehicles) {
          const vehicleId = vehicle.id || vehicle.b_id
          if (!vehicleId) continue

          try {
            const response = await checkVehicleActiveParking(vehicleId)
            if (!response.data || !response.data.hasActiveParking) {
              // 车辆没有进行中的停车记录，添加到可用车辆列表
              availableVehicles.push(vehicle)
            }
          } catch (error) {
            console.error(`检查车辆 ${vehicleId} 停车状态失败:`, error)
            // 出错时默认将车辆添加到可用列表
            availableVehicles.push(vehicle)
          }
        }
      } finally {
        loadingInstance.close()
      }

      console.log('最终可用于停车的车辆数量:', availableVehicles.length)
      return availableVehicles
    },

    // 获取车辆标签显示
    getVehicleLabel(vehicle) {
      const bikeNumber = vehicle.bike_number || vehicle.b_num || `车辆#${vehicle.id || vehicle.b_id}`
      const brand = vehicle.brand || '未知品牌'
      return `${bikeNumber} (${brand})`
    },

    // 处理车辆选择变化
    handleVehicleChange(vehicleId) {
      this.selectedVehicle = this.vehicles.find(v => (v.id || v.b_id) === vehicleId)

      // 检查车辆是否有进行中的停车记录
      if (vehicleId) {
        this.loading = true
        checkVehicleActiveParking(vehicleId)
          .then(response => {
            if (response.data && response.data.hasActiveParking) {
              // 车辆有进行中的停车记录，显示警告并重置选择
              this.$message.warning('该车辆已有进行中的停车记录，请选择其他车辆')
              this.form.vehicle_id = ''
              this.selectedVehicle = null
            }
          })
          .catch(error => {
            console.error('检查车辆停车状态失败:', error)
          })
          .finally(() => {
            this.loading = false
          })
      }
    },

    // 获取车位类型文本
    getSpaceTypeText(type) {
      const typeMap = {
        '0': '普通车位', // 兼容旧代码中可能传入的0
        '1': '普通车位',
        '2': '残疾人车位',
        '3': '充电车位'
      }
      return typeMap[type] || '普通车位'
    },

    // 获取预计停车时长文本
    getEstimatedDurationText(duration) {
      const durationMap = {
        '1': '1小时内',
        '3': '1-3小时',
        '6': '3-6小时',
        '12': '6-12小时',
        '24': '12-24小时',
        '48': '1天以上'
      }
      return durationMap[duration] || '未知'
    },

    // 提交表单
    async submitForm() {
      const valid = await this.$refs.form.validate().catch(() => false)
      if (!valid) {
        this.$message.warning('请填写必要的表单字段')
        return
      }

      // 检查是否有可用车位
      const hasAvailableSpaces = await this.checkAvailableSpaces()
      if (!hasAvailableSpaces) {
        this.$message.warning(`该停车场没有可用的${this.getSpaceTypeText(this.form.space_type)}`)
        return
      }

      // 获取选中的车辆
      const selectedVehicle = this.selectedVehicle
      if (!selectedVehicle) {
        this.$message.warning('请选择车辆')
        return
      }

      // 准备表单数据
      const userId = this.userId

      // 检查是否有可用的车位
      let selectedSpace = null
      try {
        // 显示加载中提示
        const loadingInstance = this.$loading({
          lock: true,
          text: '正在查找可用车位...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        try {
          // 获取指定类型的可用车位
          const spaceType = parseInt(this.form.space_type, 10)
          console.log(`获取车位类型 ${spaceType} (${this.getSpaceTypeText(this.form.space_type)}) 的可用车位`)
          console.log('车位类型编码已更新：1=普通车位，2=残疾人车位，3=大型车位')

          // 构建请求参数
          const params = {
            type: spaceType,
            status: 0,  // 只获取空闲的车位
            _t: new Date().getTime() // 添加时间戳防止缓存
          }

          console.log('发送车位请求参数:', params)
          console.log(`请求URL: /api/parkinglots/${this.parkingLotId}/spaces`)

          const spaceResponse = await getParkingSpaces(this.parkingLotId, params)

          console.log('车位响应数据:', spaceResponse)

          if (spaceResponse && spaceResponse.data) {
            // 提取车位数据
            let spaces = []
            if (Array.isArray(spaceResponse.data)) {
              spaces = spaceResponse.data
              console.log('车位数据是数组:', spaces.length)
            } else if (spaceResponse.data.items) {
              spaces = spaceResponse.data.items
              console.log('车位数据在items字段中:', spaces.length)
            } else if (spaceResponse.data.spaces) {
              spaces = spaceResponse.data.spaces
              console.log('车位数据在spaces字段中:', spaces.length)
            } else {
              spaces = spaceResponse.data
              console.log('车位数据在data字段中:', typeof spaces === 'object' ? Object.keys(spaces).length : 'not an object')
            }

            // 过滤出空闲的车位
            console.log('开始过滤车位，要匹配的类型:', spaceType)

            // 输出所有车位的类型信息
            spaces.forEach(space => {
              console.log(`车位ID: ${space.id}, 编号: ${space.space_number}, 类型: ${space.type}, 状态: ${space.status}`)
            })

            const availableSpaces = spaces.filter(space => {
              // 确保车位状态为空闲且类型匹配
              const spaceStatus = space.status || 0
              const spaceTypeMatch = space.type === spaceType || space.type === spaceType.toString()

              // 输出过滤过程
              if (spaceStatus === 0 && spaceTypeMatch) {
                console.log(`车位匹配成功 - ID: ${space.id}, 类型: ${space.type}, 状态: ${spaceStatus}`)
              } else if (spaceStatus !== 0) {
                console.log(`车位状态不匹配 - ID: ${space.id}, 状态: ${spaceStatus}`)
              } else if (!spaceTypeMatch) {
                console.log(`车位类型不匹配 - ID: ${space.id}, 类型: ${space.type}, 期望类型: ${spaceType}`)
              }

              return spaceStatus === 0 && spaceTypeMatch
            })

            console.log(`找到 ${availableSpaces.length} 个可用的${this.getSpaceTypeText(this.form.space_type)}`)

            if (availableSpaces.length > 0) {
              // 随机选择一个可用车位
              const randomIndex = Math.floor(Math.random() * availableSpaces.length)
              selectedSpace = availableSpaces[randomIndex]

              console.log('随机选择的车位:', selectedSpace)
              console.log('车位ID:', selectedSpace.id)
              console.log('车位编号:', selectedSpace.space_number || selectedSpace.number)
              console.log('车位类型:', selectedSpace.type, this.getSpaceTypeText(selectedSpace.type))
            } else {
              this.$message.warning(`该停车场没有可用的${this.getSpaceTypeText(this.form.space_type)}`)
              return
            }
          } else {
            this.$message.warning('获取车位信息失败')
            return
          }
        } finally {
          // 关闭加载提示
          loadingInstance.close()
        }
      } catch (error) {
        console.error('获取车位信息失败:', error)
        this.$message.error('获取车位信息失败')
        return
      }

      const formData = {
        vehicle_id: parseInt(this.form.vehicle_id, 10),
        parking_lot_id: parseInt(this.form.parking_lot_id, 10),
        parking_space_id: selectedSpace.id, // 使用随机选择的车位ID
        user_id: userId,
        space_type: parseInt(this.form.space_type, 10),
        estimated_duration: parseInt(this.form.estimated_duration, 10)
      }

      // 如果有备注信息，添加到表单数据中
      if (this.form.notes && this.form.notes.trim()) {
        formData.notes = this.form.notes.trim()
      }

      // 显示确认对话框
      try {
        // 获取车位编号
        const spaceNumber = selectedSpace.space_number || selectedSpace.number || `车位#${selectedSpace.id}`

        let confirmMessage = `确认创建停车记录？<br><br>
          <strong>车辆信息：</strong> ${this.getVehicleLabel(selectedVehicle)}<br>
          <strong>停车场地：</strong> ${this.parkingLotName}<br>
          <strong>选择车位：</strong> ${spaceNumber}<br>
          <strong>车位类型：</strong> ${this.getSpaceTypeText(this.form.space_type)}<br>
          <strong>预计时长：</strong> ${this.getEstimatedDurationText(this.form.estimated_duration)}
        `

        // 如果选择了充电车位并且需要充电，添加充电提示
        if (this.form.space_type === '2' && this.form.need_charging) {
          confirmMessage += `<br><strong>充电服务：</strong> <span style="color: #67C23A;">已选择</span>`
        }

        await this.$confirm(confirmMessage, '停车确认', {
          confirmButtonText: '确认停车',
          cancelButtonText: '取消',
          type: 'info',
          dangerouslyUseHTMLString: true
        })

        this.loading = true
        console.log('发送停车记录创建请求:', formData)
        const response = await request({
          url: '/api/parking-records',
          method: 'post',
          data: formData
        })

        console.log('停车记录创建响应:', response)

        // 创建成功后，立即获取停车记录列表以验证
        try {
          // 添加用户ID参数，确保只获取当前用户的停车记录
          const params = {
            status: 0, // 只获取进行中的停车记录
            per_page: 100,
            user_id: userId, // 指定用户ID
            _t: new Date().getTime() // 添加时间戳防止缓存
          }

          // 如果是管理员，添加isAdmin参数
          if (this.roles.includes('admin')) {
            params.isAdmin = true
          }

          const recordsResponse = await getUserParkingRecords(params)
          console.log('获取停车记录列表响应:', recordsResponse)

          // 检查是否有该车辆的进行中停车记录
          const records = recordsResponse.data?.items || recordsResponse.data?.data?.items || []
          const vehicleRecord = records.find(record => record.vehicle_id === formData.vehicle_id)

          if (vehicleRecord) {
            console.log('找到车辆的停车记录:', vehicleRecord)
          } else {
            console.warn('未找到车辆的停车记录，可能创建失败或者API响应延迟')

            // 尝试直接获取该车辆的停车记录
            try {
              const vehicleResponse = await request({
                url: `/api/parking-records/vehicle/${formData.vehicle_id}`,
                method: 'get',
                params: { status: 0 } // 只获取进行中的停车记录
              })
              console.log('直接获取车辆停车记录响应:', vehicleResponse)
            } catch (vehicleError) {
              console.error('直接获取车辆停车记录失败:', vehicleError)
            }
          }
        } catch (recordsError) {
          console.error('获取停车记录列表失败:', recordsError)
        }
        // 如果选择了充电车位并且需要充电，创建充电记录
        if (this.form.space_type === '2' && this.form.need_charging) {
          try {
            // 准备充电记录数据
            const chargingData = {
              parking_record_id: response.data.id,
              vehicle_id: formData.vehicle_id,
              user_id: formData.user_id,
              parking_lot_id: formData.parking_lot_id,
              parking_space_id: formData.parking_space_id
            }

            // 创建充电记录
            const chargingResponse = await createChargingRecord(chargingData)
            console.log('充电记录创建响应:', chargingResponse)

            this.$message.success('停车记录和充电记录创建成功')
          } catch (chargingError) {
            console.error('创建充电记录失败:', chargingError)
            this.$message.warning('停车记录创建成功，但充电记录创建失败')
          }
        } else {
          this.$message.success('停车记录创建成功')
        }

        this.dialogVisible = false
        this.$emit('success', response.data)
        this.resetForm()
      } catch (error) {
        if (error === 'cancel') {
          // 用户取消操作
          this.$message.info('已取消停车操作')
        } else {
          console.error('创建停车记录失败:', error)
          this.$message.error('创建停车记录失败: ' + (error.message || '未知错误'))
        }
      } finally {
        this.loading = false
      }
    },

    // 检查是否有可用车位
    async checkAvailableSpaces() {
      try {
        console.log(`检查停车场 ${this.parkingLotId} 是否有可用的${this.getSpaceTypeText(this.form.space_type)}`)

        // 获取指定类型的车位
        const spaceType = parseInt(this.form.space_type, 10)

        // 构建请求参数
        const params = {
          type: spaceType,
          _t: new Date().getTime() // 添加时间戳防止缓存
        }

        console.log('checkAvailableSpaces 发送车位请求参数:', params)
        console.log(`checkAvailableSpaces 请求URL: /api/parkinglots/${this.parkingLotId}/spaces`)

        const response = await getParkingSpaces(this.parkingLotId, params)

        if (response && response.data) {
          // 提取车位数据
          let spaces = []
          if (Array.isArray(response.data)) {
            spaces = response.data
          } else if (response.data.items) {
            spaces = response.data.items
          } else if (response.data.spaces) {
            spaces = response.data.spaces
          } else {
            spaces = response.data
          }

          // 过滤出空闲的车位
          console.log('checkAvailableSpaces 开始过滤车位，要匹配的类型:', spaceType)

          // 输出所有车位的类型信息
          spaces.forEach(space => {
            console.log(`checkAvailableSpaces 车位ID: ${space.id}, 编号: ${space.space_number}, 类型: ${space.type}, 状态: ${space.status}`)
          })

          const availableSpaces = spaces.filter(space => {
            // 确保车位状态为空闲且类型匹配
            const spaceStatus = space.status || 0
            const spaceTypeMatch = space.type === spaceType || space.type === spaceType.toString()

            // 输出过滤过程
            if (spaceStatus === 0 && spaceTypeMatch) {
              console.log(`checkAvailableSpaces 车位匹配成功 - ID: ${space.id}, 类型: ${space.type}, 状态: ${spaceStatus}`)
            } else if (spaceStatus !== 0) {
              console.log(`checkAvailableSpaces 车位状态不匹配 - ID: ${space.id}, 状态: ${spaceStatus}`)
            } else if (!spaceTypeMatch) {
              console.log(`checkAvailableSpaces 车位类型不匹配 - ID: ${space.id}, 类型: ${space.type}, 期望类型: ${spaceType}`)
            }

            return spaceStatus === 0 && spaceTypeMatch
          })

          console.log(`找到 ${availableSpaces.length} 个可用的${this.getSpaceTypeText(this.form.space_type)}`)
          return availableSpaces.length > 0
        }
        return false
      } catch (error) {
        console.error('检查可用车位失败:', error)
        return false
      }
    },

    // 重置表单
    resetForm() {
      this.form = {
        vehicle_id: '',
        parking_lot_id: this.parkingLotId,
        space_type: '1', // 默认普通车位类型为1
        estimated_duration: '3',
        notes: '',
        need_charging: false
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
      })
    },

    // 处理取消按钮点击
    handleCancel() {
      this.dialogVisible = false
      this.$emit('cancel')
      this.resetForm()
    }
  }
}
</script>

<style lang="scss" scoped>
// 对话框样式
.dialog-header {
  margin-bottom: 20px;
  border-bottom: 2px solid #4A9BFF;
  padding-bottom: 15px;

  .dialog-title {
    display: flex;
    align-items: center;
    margin-bottom: 5px;

    i {
      font-size: 20px;
      color: #4A9BFF;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .dialog-subtitle {
    font-size: 14px;
    color: #909399;
    margin-left: 28px;
  }
}

// 停车场信息卡片
.location-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border-left: 4px solid #4A9BFF;

  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      color: #4A9BFF;
      font-size: 18px;
    }
  }

  .card-content {
    .info-row {
      display: flex;
      margin-bottom: 8px;

      .info-label {
        width: 80px;
        color: #606266;
      }

      .info-value {
        flex: 1;
        font-weight: 500;
        color: #303133;
      }
    }
  }
}

// 车辆选择样式
.vehicle-option {
  padding: 5px 0;

  .vehicle-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &.secondary {
      margin-top: 5px;
      font-size: 12px;
      color: #909399;
    }

    .vehicle-number {
      font-weight: 500;
    }

    .vehicle-badge {
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      color: white;
    }

    .vehicle-brand {
      font-style: italic;
    }
  }
}

// 表单样式
.parking-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}

// 底部按钮
.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

// 充电选项
.charging-info {
  margin-top: 10px;
  display: flex;
  align-items: center;
  background-color: #f0f9eb;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  color: #67C23A;
  gap: 10px;

  i {
    font-size: 16px;
  }

  b {
    font-weight: 600;
  }
}

// 全局对话框样式
::v-deep .quick-parking-dialog {
  border-radius: 8px;
  overflow: hidden;

  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f0f9ff;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__title {
    font-weight: 600;
    color: #4A9BFF;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }

  // 下拉菜单样式
  .el-select {
    width: 100%;

    .el-input__inner {
      border-radius: 4px;
    }
  }
}
</style>
