from app import db
from datetime import datetime
from sqlalchemy.ext.declarative import declared_attr

class BaseModel:
    """基础模型类，提供通用的方法和属性"""
    
    @declared_attr
    def created_at(cls):
        return db.Column(db.DateTime, default=datetime.now)
    
    @declared_attr
    def updated_at(cls):
        return db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    def save(self):
        """保存模型到数据库"""
        db.session.add(self)
        db.session.commit()
        return self
    
    def delete(self):
        """从数据库删除模型"""
        db.session.delete(self)
        db.session.commit()
        return True
    
    @classmethod
    def get_by_id(cls, id):
        """根据ID获取模型实例"""
        return cls.query.get(id)
    
    @classmethod
    def get_all(cls):
        """获取所有模型实例"""
        return cls.query.all()
    
    def to_dict(self):
        """将模型转换为字典（基本实现）"""
        result = {}
        for key in self.__mapper__.c.keys():
            value = getattr(self, key)
            # 处理日期时间对象的序列化
            if isinstance(value, datetime):
                result[key] = value.strftime('%Y-%m-%d %H:%M:%S')
            else:
                result[key] = value
        return result
    
    def from_dict(self, data):
        """从字典更新模型属性"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
        return self
    
    def update(self, **kwargs):
        """更新模型属性并保存"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        db.session.commit()
        return self 