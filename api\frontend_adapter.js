/**
 * 前端适配器 - 用于处理API响应格式
 * 
 * 该适配器用于标准化后端API响应，使其符合前端期望的格式
 * 后端API可能会返回不同格式的响应，此适配器确保统一处理
 */

/**
 * 标准化API响应
 * @param {Object} response - 后端返回的原始响应
 * @return {Object} 标准化后的响应对象
 */
export function normalizeResponse(response) {
  // 已经是标准格式的情况
  if (response.data && response.data.code === 20000) {
    return response.data;
  }
  
  // 登录API的特殊处理
  if (response.data && response.data.access_token) {
    return {
      code: 20000,
      data: {
        token: response.data.access_token,
        user: response.data.user
      }
    };
  }
  
  // 错误响应处理
  if (response.data && response.data.message) {
    let code = 50000; // 默认服务器错误码
    
    // 根据HTTP状态码或响应中的状态标记确定错误码
    if (response.status === 401 || (response.data.status && response.data.status === 'error')) {
      code = 40100; // 未授权
    } else if (response.status === 403) {
      code = 40300; // 禁止访问
    } else if (response.status === 404) {
      code = 40400; // 未找到
    }
    
    return {
      code: code,
      data: {
        message: response.data.message,
        status: 'error'
      }
    };
  }
  
  // 标准格式包装
  return {
    code: 20000,
    data: response.data
  };
}

/**
 * 处理API错误
 * @param {Error} error - 捕获的错误对象
 * @return {Object} 标准化的错误响应
 */
export function handleApiError(error) {
  // 网络错误
  if (!error.response) {
    return {
      code: 50000,
      data: {
        message: '网络错误，请检查您的网络连接',
        status: 'error'
      }
    };
  }
  
  // 服务器返回的错误
  const status = error.response.status;
  let message = '未知错误';
  let code = 50000;
  
  switch (status) {
    case 400:
      code = 40000;
      message = error.response.data?.message || '请求参数错误';
      break;
    case 401:
      code = 40100;
      message = error.response.data?.message || '未授权，请重新登录';
      break;
    case 403:
      code = 40300;
      message = error.response.data?.message || '您没有权限执行此操作';
      break;
    case 404:
      code = 40400;
      message = error.response.data?.message || '请求的资源不存在';
      break;
    case 500:
      code = 50000;
      message = error.response.data?.message || '服务器错误';
      break;
    default:
      message = error.response.data?.message || `请求失败 (${status})`;
  }
  
  return {
    code: code,
    data: {
      message: message,
      status: 'error'
    }
  };
}

/**
 * 适配登录流程的代码示例 (store/modules/user.js)
 * 
 * import { normalizeResponse } from '@/utils/request'
 * 
 * // 登录动作
 * login({ commit }, userInfo) {
 *   return new Promise((resolve, reject) => {
 *     login(userInfo)
 *       .then(response => {
 *         // 标准化响应格式
 *         const normalizedResponse = normalizeResponse(response);
 *         const { data } = normalizedResponse.data;
 *         
 *         // 提取token并存储
 *         commit('SET_TOKEN', data.token);
 *         setToken(data.token);
 *         resolve(normalizedResponse);
 *       })
 *       .catch(error => {
 *         reject(error);
 *       });
 *   });
 * }
 */ 