"""
仪表盘相关路由
提供系统概览数据和统计信息
"""

from flask import Blueprint, request, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import func, and_, or_
from datetime import datetime, timedelta
from app.utils.response import api_response
from app.utils.auth import is_admin, admin_or_security_required
from app.users.models import Users
from app.bikes.models import Bikes
from app.parking_records.models import ParkingRecord
from app.parkinglots.models import ParkingLot, ParkingSpace
from app.violations.models import ViolationRecord
from app.charging.models import ChargingRecord
from app import db

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/dashboard/overview', methods=['GET'])
@jwt_required()
def get_dashboard_overview():
    """获取仪表盘概览数据"""
    try:
        user_id = get_jwt_identity()
        is_admin_user = is_admin()
        
        # 获取用户信息
        user = Users.query.get(user_id)
        if not user:
            return api_response(message="用户不存在", status="error", code=404)
        
        # 基础统计数据
        overview_data = {}
        
        if is_admin_user:
            # 管理员看到全局数据
            overview_data = {
                'total_users': Users.query.count(),
                'total_vehicles': Bikes.query.count(),
                'total_parking_lots': ParkingLot.query.count(),
                'total_parking_spaces': ParkingSpace.query.count(),
                'active_parking_records': ParkingRecord.query.filter_by(status=0).count(),
                'total_violations': ViolationRecord.query.count(),
                'total_charging_spaces': ParkingSpace.query.filter_by(space_type='charging').count(),
                'active_charging_records': ChargingRecord.query.filter_by(status=0).count()
            }
        else:
            # 普通用户看到个人数据
            overview_data = {
                'my_vehicles': Bikes.query.filter_by(belong_to=user_id).count(),
                'my_active_parking': ParkingRecord.query.filter_by(user_id=user_id, status=0).count(),
                'my_violations': ViolationRecord.query.filter_by(user_id=user_id).count(),
                'my_charging_records': ChargingRecord.query.filter_by(user_id=user_id).count()
            }
        
        # 今日统计
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start + timedelta(days=1)
        
        if is_admin_user:
            today_parking = ParkingRecord.query.filter(
                ParkingRecord.entry_time >= today_start,
                ParkingRecord.entry_time < today_end
            ).count()
            
            today_violations = ViolationRecord.query.filter(
                ViolationRecord.created_at >= today_start,
                ViolationRecord.created_at < today_end
            ).count()
            
            today_charging = ChargingRecord.query.filter(
                ChargingRecord.start_time >= today_start,
                ChargingRecord.start_time < today_end
            ).count()
        else:
            today_parking = ParkingRecord.query.filter(
                ParkingRecord.user_id == user_id,
                ParkingRecord.entry_time >= today_start,
                ParkingRecord.entry_time < today_end
            ).count()
            
            today_violations = ViolationRecord.query.filter(
                ViolationRecord.user_id == user_id,
                ViolationRecord.created_at >= today_start,
                ViolationRecord.created_at < today_end
            ).count()
            
            today_charging = ChargingRecord.query.filter(
                ChargingRecord.user_id == user_id,
                ChargingRecord.start_time >= today_start,
                ChargingRecord.start_time < today_end
            ).count()
        
        overview_data.update({
            'today_parking_count': today_parking,
            'today_violations': today_violations,
            'today_charging_count': today_charging
        })
        
        # 最近7天趋势数据
        trend_data = []
        for i in range(7):
            date = datetime.now() - timedelta(days=i)
            date_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
            date_end = date_start + timedelta(days=1)
            
            if is_admin_user:
                daily_parking = ParkingRecord.query.filter(
                    ParkingRecord.entry_time >= date_start,
                    ParkingRecord.entry_time < date_end
                ).count()
            else:
                daily_parking = ParkingRecord.query.filter(
                    ParkingRecord.user_id == user_id,
                    ParkingRecord.entry_time >= date_start,
                    ParkingRecord.entry_time < date_end
                ).count()
            
            trend_data.append({
                'date': date.strftime('%Y-%m-%d'),
                'parking_count': daily_parking
            })
        
        # 反转数据，使其按时间正序排列
        trend_data.reverse()
        
        # 停车场使用率（仅管理员）
        parking_lot_stats = []
        if is_admin_user:
            lots = ParkingLot.query.all()
            for lot in lots:
                total_spaces = ParkingSpace.query.filter_by(parking_lot_id=lot.id).count()
                occupied_spaces = ParkingRecord.query.filter_by(
                    parking_lot_id=lot.id, 
                    status=0
                ).count()
                
                utilization_rate = (occupied_spaces / total_spaces * 100) if total_spaces > 0 else 0
                
                parking_lot_stats.append({
                    'lot_id': lot.id,
                    'lot_name': lot.name,
                    'total_spaces': total_spaces,
                    'occupied_spaces': occupied_spaces,
                    'utilization_rate': round(utilization_rate, 2)
                })
        
        result = {
            'overview': overview_data,
            'trend_data': trend_data,
            'parking_lot_stats': parking_lot_stats,
            'user_role': user.role,
            'is_admin': is_admin_user
        }
        
        return api_response(
            data=result,
            message="获取仪表盘概览数据成功",
            status="success"
        )
        
    except Exception as e:
        current_app.logger.error(f"获取仪表盘概览数据失败: {str(e)}")
        return api_response(
            message=f"获取仪表盘概览数据失败: {str(e)}", 
            status="error", 
            code=500
        )

@dashboard_bp.route('/dashboard/quick-stats', methods=['GET'])
@jwt_required()
def get_quick_stats():
    """获取快速统计数据"""
    try:
        user_id = get_jwt_identity()
        is_admin_user = is_admin()
        
        # 车辆状态统计
        if is_admin_user:
            vehicle_stats = db.session.query(
                Bikes.status,
                func.count(Bikes.b_id).label('count')
            ).group_by(Bikes.status).all()
        else:
            vehicle_stats = db.session.query(
                Bikes.status,
                func.count(Bikes.b_id).label('count')
            ).filter_by(belong_to=user_id).group_by(Bikes.status).all()
        
        vehicle_status_data = {status: count for status, count in vehicle_stats}
        
        # 违规状态统计（管理员或保安）
        violation_status_data = {}
        if is_admin_user or (hasattr(Users.query.get(user_id), 'role') and Users.query.get(user_id).role == 'security'):
            violation_stats = db.session.query(
                ViolationRecord.status,
                func.count(ViolationRecord.id).label('count')
            ).group_by(ViolationRecord.status).all()
            
            violation_status_data = {status: count for status, count in violation_stats}
        
        result = {
            'vehicle_status': vehicle_status_data,
            'violation_status': violation_status_data
        }
        
        return api_response(
            data=result,
            message="获取快速统计数据成功",
            status="success"
        )
        
    except Exception as e:
        current_app.logger.error(f"获取快速统计数据失败: {str(e)}")
        return api_response(
            message=f"获取快速统计数据失败: {str(e)}", 
            status="error", 
            code=500
        )
