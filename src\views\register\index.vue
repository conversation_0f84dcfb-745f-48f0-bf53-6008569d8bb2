<script>
import { register } from '@/api/user'

export default {
  name: 'Register',
  data() {
    // 重复密码验证
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.registerForm.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    return {
      registerForm: {
        username: '',
        password: '',
        confirmPassword: '',
        belong: '',
        phone: ''
      },
      registerRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在3到20个字符之间', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在6到20个字符之间', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      loading: false,
      errorMessage: ''
    }
  },
  methods: {
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.errorMessage = ''

          // 修正表单字段名，确保与API期望的字段名匹配
          const registerData = {
            username: this.registerForm.username,
            password: this.registerForm.password,
            confirm_password: this.registerForm.confirmPassword, // 字段名修正为API期望的confirm_password
            belong: this.registerForm.belong,
            phone: this.registerForm.phone
          }

          // 调用注册API，使用修正后的数据
          register(registerData)
            .then(response => {
              console.log('注册响应:', response)

              this.loading = false
              if (response.data && response.data.user) {
                // 注册成功
                this.$notify({
                  title: '注册成功',
                  message: '已成功注册账号，现在可以登录了',
                  type: 'success',
                  duration: 3000
                })

                // 延迟跳转到登录页
                setTimeout(() => {
                  this.$router.push('/login?username=' + this.registerForm.username)
                }, 1500)
              } else {
                // 处理其他类型的成功响应
                this.$notify({
                  title: '注册成功',
                  message: '已成功注册账号，现在可以登录了',
                  type: 'success',
                  duration: 3000
                })

                // 延迟跳转到登录页
                setTimeout(() => {
                  this.$router.push('/login?username=' + this.registerForm.username)
                }, 1500)
              }
            })
            .catch(error => {
              this.loading = false
              console.error('注册错误:', error)

              // 错误处理
              if (error.response && error.response.data) {
                const errorData = error.response.data
                console.log('注册错误响应数据:', errorData)

                // 检查不同类型的错误消息格式
                if (errorData.code === 'UNIQUE_CONSTRAINT_ERROR' ||
                    (errorData.original_error && errorData.original_error.includes('UNIQUE constraint failed'))) {
                  // 数据库唯一性约束错误处理
                  this.errorMessage = '该用户名已在系统中存在，请选择其他用户名'
                  this.$notify({
                    title: '注册失败',
                    message: '该用户名已存在，请使用其他用户名注册',
                    type: 'error',
                    duration: 5000
                  })
                } else if (errorData.errors && errorData.errors.username) {
                  this.errorMessage = errorData.errors.username[0]
                } else if (errorData.message) {
                  this.errorMessage = errorData.message
                } else {
                  this.errorMessage = '注册失败，请稍后重试'
                }
              } else if (error.message && error.message.includes('UNIQUE constraint failed')) {
                // 直接处理原始SQLite错误
                this.errorMessage = '该用户名已在系统中存在，请选择其他用户名'
                this.$notify({
                  title: '注册失败',
                  message: '该用户名已存在，请使用其他用户名注册',
                  type: 'error',
                  duration: 5000
                })
              } else if (error.message) {
                this.errorMessage = error.message
              } else {
                this.errorMessage = '注册失败，请稍后重试'
              }

              // 如果是用户名相关错误，聚焦到用户名输入框
              if (this.errorMessage.includes('用户名已存在') ||
                  this.errorMessage.includes('用户名已在系统中存在')) {
                this.$nextTick(() => {
                  // 清空用户名并聚焦
                  this.registerForm.username = ''
                  this.$refs.username && this.$refs.username.focus()
                })
              }
            })
        }
      })
    }
  }
}
</script>
