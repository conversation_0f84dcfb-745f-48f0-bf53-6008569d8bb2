#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据迁移脚本：将充电异常记录中的旧类型名称更新为新的类型名称
"""

import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 简单的打印函数，替代logger
def log_info(message):
    print(f"[INFO] {message}")

def log_error(message):
    print(f"[ERROR] {message}")

# 导入应用和数据库模型
from app import create_app
from app import db
from app.charging.models import ChargingException

# 创建应用实例
app = create_app()

# 定义类型映射关系
TYPE_MAPPING = {
    'charging_gun': 'connector',    # 充电枪故障 -> 充电接口故障
    'power': 'startup',             # 电力故障 -> 设备无法启动
    'communication': 'interruption', # 通信故障 -> 充电中断
    'software': 'other',            # 软件故障 -> 其他故障
    'user_operation': 'other'       # 用户操作异常 -> 其他故障
}

def migrate_charging_exception_types():
    """
    迁移充电异常记录中的类型名称
    """
    with app.app_context():
        try:
            # 查询所有充电异常记录
            exceptions = ChargingException.query.all()
            log_info(f"找到 {len(exceptions)} 条充电异常记录")

            # 统计需要更新的记录数量
            update_count = 0

            # 遍历所有记录
            for exception in exceptions:
                old_type = exception.exception_type

                # 检查是否需要更新
                if old_type in TYPE_MAPPING:
                    new_type = TYPE_MAPPING[old_type]

                    # 更新类型名称
                    exception.exception_type = new_type
                    update_count += 1

                    log_info(f"更新记录 ID: {exception.id}, 旧类型: {old_type}, 新类型: {new_type}")

            # 提交更改
            if update_count > 0:
                db.session.commit()
                log_info(f"成功更新 {update_count} 条记录")
            else:
                log_info("没有需要更新的记录")

            return update_count

        except Exception as e:
            # 回滚事务
            db.session.rollback()
            log_error(f"迁移失败: {str(e)}")
            raise

if __name__ == '__main__':
    log_info("开始迁移充电异常记录类型名称")

    try:
        updated_count = migrate_charging_exception_types()
        log_info(f"迁移完成，共更新 {updated_count} 条记录")
    except Exception as e:
        log_error(f"迁移过程中发生错误: {str(e)}")
        sys.exit(1)

    log_info("迁移脚本执行完毕")
    sys.exit(0)
