# API 接口标准文档

## 响应格式标准

为了确保前后端数据交互的一致性，所有API响应应当遵循以下标准格式：

```json
{
  "code": 20000,  // 状态码，20000表示成功，其他值表示错误
  "data": {       // 数据载荷，具体结构取决于API
    // 具体内容
  }
}
```

### 成功响应示例

```json
{
  "code": 20000,
  "data": {
    "items": [...],
    "total": 100
  }
}
```

### 错误响应示例

```json
{
  "code": 40100,  // 错误码
  "data": {
    "message": "未授权，请重新登录",
    "status": "error"
  }
}
```

## 常用状态码

| 状态码 | 描述 |
|--------|------|
| 20000  | 成功 |
| 40000  | 请求参数错误 |
| 40100  | 未授权或认证失败 |
| 40300  | 禁止访问（权限不足） |
| 40400  | 资源不存在 |
| 50000  | 服务器错误 |

## 特定API标准格式

### 登录API

**请求**：
```json
{
  "username": "user",
  "password": "password"
}
```

**响应**：
```json
{
  "code": 20000,
  "data": {
    "token": "eyJhbGciOiJIUzI1...",
    "user": {
      "id": 1,
      "username": "user",
      "role": "admin",
      "belong": null
    }
  }
}
```

### 注册API

**请求**：
```json
{
  "username": "newuser",
  "password": "password",
  "role": "user"  // 可选
}
```

**响应**：
```json
{
  "code": 20000,
  "data": {
    "message": "注册成功",
    "user_id": 123
  }
}
```

### 注销API

**响应**：
```json
{
  "code": 20000,
  "message": "登出成功"
}
```

### 用户名检查API

**请求**：
```json
{
  "username": "testuser"
}
```

**响应**：
```json
{
  "code": 20000,
  "data": {
    "exists": true,
    "message": "用户名已存在"
  }
}
```

## 前端适配器

由于后端API可能存在不符合上述标准的格式，我们提供了前端适配器来统一处理这些差异。适配器位于 `frontend_adapter.js` 文件中，主要包含以下功能：

1. `normalizeResponse(response)` - 将后端API响应标准化为前端期望的格式
2. `handleApiError(error)` - 统一处理API错误并返回标准格式

### 在Vue项目中使用适配器

1. 在请求拦截器中应用：

```javascript
// 在 src/utils/request.js 中
import { normalizeResponse, handleApiError } from '@/utils/frontend_adapter'

// 响应拦截器
service.interceptors.response.use(
  response => {
    return normalizeResponse(response)
  },
  error => {
    return Promise.reject(handleApiError(error))
  }
)
```

2. 在API调用处理中应用：

```javascript
import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/api/login',
    method: 'post',
    data
  }).then(response => {
    // 这里的response已经是标准化后的格式
    return response
  }).catch(error => {
    // 这里的error也是标准化后的格式
    return Promise.reject(error)
  })
}
```

## 接口迁移计划

为确保系统平稳过渡到标准化API格式，我们将采取以下迁移策略：

1. 在前端实现适配器，以兼容当前后端API格式
2. 逐步更新后端API以符合标准格式
3. 对于新开发的API，直接采用标准格式

我们将优先更新以下核心API：
- 登录/注册/注销
- 用户信息获取
- 数据列表获取

## 附录：当前实际后端响应格式示例

### 登录API实际响应

```json
{
  "code": 20000,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "player": {
      "id": 6,
      "username": "newuser123"
    },
    "user": {
      "u_belong": null,
      "u_id": 6,
      "u_name": "newuser123",
      "u_phone": null,
      "u_role": "user"
    }
  }
}
```

### 使用适配器转换后的格式

```json
{
  "code": 20000,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "u_belong": null,
      "u_id": 6,
      "u_name": "newuser123",
      "u_phone": null,
      "u_role": "user"
    }
  }
}
``` 