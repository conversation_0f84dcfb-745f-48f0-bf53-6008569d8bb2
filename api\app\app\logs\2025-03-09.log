2025/03/09 10:42:33 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/09 10:42:33 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/09 10:42:33 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/09 10:43:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:43:12] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/09 10:43:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:43:12] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/09 10:43:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:43:31] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/09 10:43:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:43:31] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/09 10:43:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:43:39] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/09 10:43:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:43:39] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/09 10:43:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:43:52] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/09 10:43:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:43:52] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/09 10:44:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:44:02] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/09 10:44:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:44:02] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/09 10:44:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:44:10] "OPTIONS /users/ HTTP/1.1" 200 -
2025/03/09 10:44:10 flask_api __init__.py[127] not_found() ERROR: 路由未找到: /users/, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0MTQ4ODI0MiwianRpIjoiM2U3MjEyZDktMzEzYy00ZjY2LTk4NzMtNDcyNWI4ODc4NDRiIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6InRlc3R1c2VyIiwibmJmIjoxNzQxNDg4MjQyLCJjc3JmIjoiZWYwM2ZlZDQtM2EzNi00NmM3LWFiNjQtNzMzMWMwYjA2ZmVlIiwiZXhwIjoxNzQxNDg5MTQyfQ.e42M9ZvuHlMI4q1udVbCgK0We8eEKDIkdD9buoQVI3w

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/03/09 10:44:10 root __init__.py[128] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/09 10:44:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:44:10] "[33mGET /users/ HTTP/1.1[0m" 404 -
2025/03/09 10:44:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:44:17] "OPTIONS /users/ HTTP/1.1" 200 -
2025/03/09 10:44:17 flask_api __init__.py[127] not_found() ERROR: 路由未找到: /users/, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0MTQ4ODI0MiwianRpIjoiM2U3MjEyZDktMzEzYy00ZjY2LTk4NzMtNDcyNWI4ODc4NDRiIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6InRlc3R1c2VyIiwibmJmIjoxNzQxNDg4MjQyLCJjc3JmIjoiZWYwM2ZlZDQtM2EzNi00NmM3LWFiNjQtNzMzMWMwYjA2ZmVlIiwiZXhwIjoxNzQxNDg5MTQyfQ.e42M9ZvuHlMI4q1udVbCgK0We8eEKDIkdD9buoQVI3w

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/03/09 10:44:17 root __init__.py[128] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/09 10:44:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:44:17] "[33mGET /users/ HTTP/1.1[0m" 404 -
2025/03/09 10:44:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:44:35] "OPTIONS /users/ HTTP/1.1" 200 -
2025/03/09 10:44:35 flask_api __init__.py[127] not_found() ERROR: 路由未找到: /users/, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0MTQ4ODI0MiwianRpIjoiM2U3MjEyZDktMzEzYy00ZjY2LTk4NzMtNDcyNWI4ODc4NDRiIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6InRlc3R1c2VyIiwibmJmIjoxNzQxNDg4MjQyLCJjc3JmIjoiZWYwM2ZlZDQtM2EzNi00NmM3LWFiNjQtNzMzMWMwYjA2ZmVlIiwiZXhwIjoxNzQxNDg5MTQyfQ.e42M9ZvuHlMI4q1udVbCgK0We8eEKDIkdD9buoQVI3w

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/03/09 10:44:35 root __init__.py[128] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/09 10:44:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 10:44:35] "[33mGET /users/ HTTP/1.1[0m" 404 -
2025/03/09 11:32:26 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/09 11:32:26 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/09 11:32:26 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/09 11:32:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:32:33] "OPTIONS /users HTTP/1.1" 200 -
2025/03/09 11:32:33 flask_api __init__.py[127] not_found() ERROR: 路由未找到: /users, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0MTQ4ODI0MiwianRpIjoiM2U3MjEyZDktMzEzYy00ZjY2LTk4NzMtNDcyNWI4ODc4NDRiIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6InRlc3R1c2VyIiwibmJmIjoxNzQxNDg4MjQyLCJjc3JmIjoiZWYwM2ZlZDQtM2EzNi00NmM3LWFiNjQtNzMzMWMwYjA2ZmVlIiwiZXhwIjoxNzQxNDg5MTQyfQ.e42M9ZvuHlMI4q1udVbCgK0We8eEKDIkdD9buoQVI3w

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/03/09 11:32:33 root __init__.py[128] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/09 11:32:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:32:33] "[33mGET /users HTTP/1.1[0m" 404 -
2025/03/09 11:32:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:32:55] "OPTIONS /users HTTP/1.1" 200 -
2025/03/09 11:32:55 flask_api __init__.py[146] handle_exception() ERROR: 未处理的异常: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025/03/09 11:32:55 flask_api __init__.py[148] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.

2025/03/09 11:32:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:32:55] "[35m[1mPOST /users HTTP/1.1[0m" 500 -
2025/03/09 11:34:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:34:21] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:34:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:34:21] "GET /api/users HTTP/1.1" 200 -
2025/03/09 11:34:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:34:46] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:34:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:34:46] "GET /api/users HTTP/1.1" 200 -
2025/03/09 11:35:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:35:21] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:35:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:35:21] "GET /api/users HTTP/1.1" 200 -
2025/03/09 11:36:46 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/09 11:36:46 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/09 11:36:46 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/09 11:37:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:37:44] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:37:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:37:44] "GET /api/users HTTP/1.1" 200 -
2025/03/09 11:38:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:38:05] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:38:05 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_id': None, 'u_name': 'jyj', 'u_role': 'admin', 'u_belong': 'cs', 'u_phone': '13276595156'}
2025/03/09 11:38:05 flask_api routes.py[56] create_user() ERROR: Missing required field: u_pwd
2025/03/09 11:38:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:38:05] "[31m[1mPOST /api/users HTTP/1.1[0m" 422 -
2025/03/09 11:39:02 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/09 11:39:02 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/09 11:39:02 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/09 11:39:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:39:07] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:39:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:39:07] "GET /api/users HTTP/1.1" 200 -
2025/03/09 11:39:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:39:13] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:39:13 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_id': None, 'u_name': 'jyj', 'u_role': 'admin', 'u_belong': 'cs', 'u_phone': '13276595156'}
2025/03/09 11:39:13 flask_api routes.py[56] create_user() ERROR: Missing required field: u_pwd
2025/03/09 11:39:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:39:13] "[31m[1mPOST /api/users HTTP/1.1[0m" 422 -
2025/03/09 11:40:46 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/09 11:40:46 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/09 11:40:46 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/09 11:40:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:40:48] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:40:48 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_id': None, 'u_name': 'jyj', 'u_role': 'admin', 'u_belong': 'cs', 'u_phone': '13276595156'}
2025/03/09 11:40:48 flask_api routes.py[86] create_user() ERROR: Error creating user: {'u_id': ['Unknown field.']}
2025/03/09 11:40:48 flask_api routes.py[88] create_user() ERROR: Traceback (most recent call last):
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 68, in create_user
    new_user = user_schema.load(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_id': ['Unknown field.']}

2025/03/09 11:40:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:40:48] "[35m[1mPOST /api/users HTTP/1.1[0m" 500 -
2025/03/09 11:42:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:42:57] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:42:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:42:57] "GET /api/users HTTP/1.1" 200 -
2025/03/09 11:43:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:43:28] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:43:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:43:28] "GET /api/users HTTP/1.1" 200 -
2025/03/09 11:43:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:43:49] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:43:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:43:49] "GET /api/users HTTP/1.1" 200 -
2025/03/09 11:44:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:44:20] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:44:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:44:20] "GET /api/users HTTP/1.1" 200 -
2025/03/09 11:44:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:44:25] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:44:25 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_name': 'jyj', 'u_role': 'admin', 'u_pwd': '123456', 'u_belong': 'cs', 'u_phone': 13276595156}
2025/03/09 11:44:25 flask_api routes.py[69] create_user() INFO: Loaded user data: {'u_name': 'jyj', 'u_pwd': '123456', 'u_role': 'admin', 'u_belong': 'cs', 'u_phone': 13276595156}
2025/03/09 11:44:25 flask_api routes.py[86] create_user() ERROR: Error creating user: %d format: a real number is required, not NoneType
2025/03/09 11:44:25 flask_api routes.py[88] create_user() ERROR: Traceback (most recent call last):
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 73, in create_user
    app.logger.info(f"Created user instance: {user_instance}")
                                             ^^^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\models.py", line 27, in __repr__
    return '<User %d>' % self.u_id
           ~~~~~~~~~~~~^~~~~~~~~~~
TypeError: %d format: a real number is required, not NoneType

2025/03/09 11:44:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:44:25] "[35m[1mPOST /api/users HTTP/1.1[0m" 500 -
2025/03/09 11:48:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:48:50] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:48:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:48:50] "GET /api/users HTTP/1.1" 200 -
2025/03/09 11:49:01 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/09 11:49:02 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/09 11:49:02 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/09 11:49:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:49:15] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:49:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:49:15] "GET /api/users HTTP/1.1" 200 -
2025/03/09 11:49:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:49:22] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:49:22 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_name': 'jyj', 'u_role': 'admin', 'u_pwd': '123456', 'u_belong': 'cs', 'u_phone': 13276595156}
2025/03/09 11:49:22 flask_api routes.py[90] create_user() INFO: User created successfully: {'u_id': 1, 'u_name': 'jyj', 'u_role': 'admin', 'u_belong': 'cs', 'u_phone': 13276595156, 'bikes': [{'b_id': 1, 'belong_to': 1, 'brand': 'yadi', 'color': None, 'b_type': None, 'status': None}, {'b_id': 2, 'belong_to': 1, 'brand': 'Giant', 'color': 'Red', 'b_type': 'Mountain', 'status': 'Available'}, {'b_id': 3, 'belong_to': 1, 'brand': '未知品牌', 'color': '未知颜色', 'b_type': '普通型号', 'status': '可用'}]}
2025/03/09 11:49:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:49:22] "[35m[1mPOST /api/users HTTP/1.1[0m" 201 -
2025/03/09 11:49:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:49:47] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 11:49:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:49:47] "GET /api/users HTTP/1.1" 200 -
2025/03/09 11:49:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:49:57] "OPTIONS /vue-admin-template/table/list HTTP/1.1" 200 -
2025/03/09 11:49:57 flask_api __init__.py[127] not_found() ERROR: 路由未找到: /vue-admin-template/table/list, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0MTQ4ODI0MiwianRpIjoiM2U3MjEyZDktMzEzYy00ZjY2LTk4NzMtNDcyNWI4ODc4NDRiIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6InRlc3R1c2VyIiwibmJmIjoxNzQxNDg4MjQyLCJjc3JmIjoiZWYwM2ZlZDQtM2EzNi00NmM3LWFiNjQtNzMzMWMwYjA2ZmVlIiwiZXhwIjoxNzQxNDg5MTQyfQ.e42M9ZvuHlMI4q1udVbCgK0We8eEKDIkdD9buoQVI3w

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/03/09 11:49:57 root __init__.py[128] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/09 11:49:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 11:49:57] "[33mGET /vue-admin-template/table/list HTTP/1.1[0m" 404 -
2025/03/09 14:23:45 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/09 14:23:45 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/09 14:23:45 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/09 14:23:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:23:49] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/09 14:23:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:23:49] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/09 14:23:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:23:59] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:23:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:23:59] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:24:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:24:06] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:24:06 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_name': 'jyj', 'u_role': 'admin', 'u_pwd': '123456', 'u_belong': 'cs', 'u_phone': 13276595156}
2025/03/09 14:24:06 flask_api routes.py[70] create_user() ERROR: User with name jyj already exists
2025/03/09 14:24:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:24:06] "[31m[1mPOST /api/users HTTP/1.1[0m" 422 -
2025/03/09 14:25:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:25:51] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:25:51 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_name': 'wc', 'u_role': 'admin', 'u_pwd': '123456', 'u_belong': 'cs', 'u_phone': 18852182417}
2025/03/09 14:25:51 flask_api routes.py[90] create_user() INFO: User created successfully: {'u_id': 3, 'u_name': 'wc', 'u_role': 'admin', 'u_belong': 'cs', 'u_phone': 18852182417, 'bikes': []}
2025/03/09 14:25:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:25:51] "[35m[1mPOST /api/users HTTP/1.1[0m" 201 -
2025/03/09 14:28:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:28:38] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:28:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:28:38] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:29:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:29:12] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:29:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:29:12] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:30:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:30:39] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:30:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:30:39] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:30:45 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/09 14:30:45 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/09 14:30:45 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/09 14:30:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:30:50] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:30:50 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_name': 'wc', 'u_role': 'admin', 'u_pwd': '123456', 'u_belong': 'cs', 'u_phone': 18852182417}
2025/03/09 14:30:50 flask_api routes.py[70] create_user() ERROR: User with name wc already exists
2025/03/09 14:30:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:30:50] "[31m[1mPOST /api/users HTTP/1.1[0m" 422 -
2025/03/09 14:30:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:30:58] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:30:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:30:58] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:31:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:31:15] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:31:15 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_name': 'a', 'u_role': 'a', 'u_pwd': 'a', 'u_belong': 'a', 'u_phone': 13276595156}
2025/03/09 14:31:15 flask_api routes.py[90] create_user() INFO: User created successfully: {'u_id': 4, 'u_name': 'a', 'u_role': 'a', 'u_belong': 'a', 'u_phone': 13276595156, 'bikes': []}
2025/03/09 14:31:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:31:15] "[35m[1mPOST /api/users HTTP/1.1[0m" 201 -
2025/03/09 14:31:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:31:29] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:31:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:31:29] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:31:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:31:35] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:31:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:31:35] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:31:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:31:43] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:31:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:31:43] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:32:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:32:04] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:32:04 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_name': 'b', 'u_role': 'b', 'u_pwd': '123456', 'u_belong': 'b', 'u_phone': 13276595156}
2025/03/09 14:32:04 flask_api routes.py[90] create_user() INFO: User created successfully: {'u_id': 5, 'u_name': 'b', 'u_role': 'b', 'u_belong': 'b', 'u_phone': 13276595156, 'bikes': []}
2025/03/09 14:32:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:32:04] "[35m[1mPOST /api/users HTTP/1.1[0m" 201 -
2025/03/09 14:33:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:33:49] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:33:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:33:49] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:34:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:34:02] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:34:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:34:02] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:34:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:34:26] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:34:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:34:26] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:34:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:34:35] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:34:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:34:35] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:36:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:36:07] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/03/09 14:36:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:36:07] "GET /api/users/1 HTTP/1.1" 200 -
2025/03/09 14:36:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:36:12] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/03/09 14:36:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:36:12] "GET /api/users/1 HTTP/1.1" 200 -
2025/03/09 14:36:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:36:14] "OPTIONS /api/users/3 HTTP/1.1" 200 -
2025/03/09 14:36:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:36:14] "GET /api/users/3 HTTP/1.1" 200 -
2025/03/09 14:36:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:36:18] "OPTIONS /api/users/3 HTTP/1.1" 200 -
2025/03/09 14:36:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:36:18] "PUT /api/users/3 HTTP/1.1" 200 -
2025/03/09 14:37:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:37:04] "OPTIONS /api/users/4 HTTP/1.1" 200 -
2025/03/09 14:37:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:37:04] "GET /api/users/4 HTTP/1.1" 200 -
2025/03/09 14:37:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:37:08] "OPTIONS /api/users/4 HTTP/1.1" 200 -
2025/03/09 14:37:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:37:08] "PUT /api/users/4 HTTP/1.1" 200 -
2025/03/09 14:37:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:37:11] "OPTIONS /api/users/5 HTTP/1.1" 200 -
2025/03/09 14:37:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:37:11] "DELETE /api/users/5 HTTP/1.1" 200 -
2025/03/09 14:37:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:37:15] "OPTIONS /api/users/4 HTTP/1.1" 200 -
2025/03/09 14:37:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:37:15] "DELETE /api/users/4 HTTP/1.1" 200 -
2025/03/09 14:37:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:37:45] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:37:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:37:45] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:38:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:38:11] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/09 14:38:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:38:11] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/03/09 14:38:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:38:18] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/09 14:38:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:38:18] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/09 14:38:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:38:22] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:38:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:38:22] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:39:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:39:21] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:39:21 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_name': '1212jyj', 'u_role': '1', 'u_pwd': '123456', 'u_belong': '1', 'u_phone': 13276595156}
2025/03/09 14:39:21 flask_api routes.py[90] create_user() INFO: User created successfully: {'u_id': 4, 'u_name': '1212jyj', 'u_role': '1', 'u_belong': '1', 'u_phone': 13276595156, 'bikes': []}
2025/03/09 14:39:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:39:21] "[35m[1mPOST /api/users HTTP/1.1[0m" 201 -
2025/03/09 14:39:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:39:27] "OPTIONS /api/users/4 HTTP/1.1" 200 -
2025/03/09 14:39:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:39:27] "GET /api/users/4 HTTP/1.1" 200 -
2025/03/09 14:39:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:39:30] "DELETE /api/users/4 HTTP/1.1" 200 -
2025/03/09 14:41:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:41:15] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:41:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:41:15] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:46:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:46:39] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:46:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:46:39] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:46:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:46:49] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:46:49 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_name': '蒋月涓', 'u_role': 'a', 'u_pwd': '123456', 'u_belong': 'a', 'u_phone': 13276595156}
2025/03/09 14:46:49 flask_api routes.py[90] create_user() INFO: User created successfully: {'u_id': 4, 'u_name': '蒋月涓', 'u_role': 'a', 'u_belong': 'a', 'u_phone': 13276595156, 'bikes': []}
2025/03/09 14:46:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:46:49] "[35m[1mPOST /api/users HTTP/1.1[0m" 201 -
2025/03/09 14:46:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:46:56] "OPTIONS /api/users/4 HTTP/1.1" 200 -
2025/03/09 14:46:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:46:56] "GET /api/users/4 HTTP/1.1" 200 -
2025/03/09 14:47:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:47:13] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/03/09 14:47:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:47:13] "GET /api/users/1 HTTP/1.1" 200 -
2025/03/09 14:47:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:47:58] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:47:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:47:58] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:48:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:48:51] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/09 14:48:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:48:51] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/03/09 14:49:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:49:08] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/09 14:49:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:49:08] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/09 14:49:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:49:28] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:49:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:49:28] "GET /api/users HTTP/1.1" 200 -
2025/03/09 14:50:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:50:09] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/09 14:50:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:50:09] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/09 14:50:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:50:09] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/09 14:50:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [09/Mar/2025 14:50:09] "GET /api/users HTTP/1.1" 200 -
