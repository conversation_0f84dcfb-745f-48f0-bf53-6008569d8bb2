// 更新停车场统计信息的脚本
const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./sys.db');

// 计算每个停车场的车位统计信息
db.serialize(() => {
  // 获取所有停车场
  db.all(`SELECT id, name FROM parking_lots`, [], (err, parkingLots) => {
    if (err) {
      console.error('获取停车场失败:', err);
      return;
    }

    console.log(`找到 ${parkingLots.length} 个停车场`);

    // 为每个停车场计算车位统计信息
    parkingLots.forEach(lot => {
      // 获取该停车场的车位统计
      db.all(`
        SELECT 
          type, 
          COUNT(*) as total, 
          SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as available 
        FROM parking_spaces 
        WHERE parking_lot_id = ? 
        GROUP BY type
      `, [lot.id], (err, stats) => {
        if (err) {
          console.error(`获取停车场 ${lot.id} 的车位统计失败:`, err);
          return;
        }

        // 更新停车场的总车位数和已占用车位数
        let totalSpaces = 0;
        let occupiedSpaces = 0;

        stats.forEach(stat => {
          totalSpaces += stat.total;
          occupiedSpaces += (stat.total - stat.available);
        });

        // 更新停车场表
        db.run(`
          UPDATE parking_lots 
          SET total_spaces = ?, occupied_spaces = ? 
          WHERE id = ?
        `, [totalSpaces, occupiedSpaces, lot.id], function(err) {
          if (err) {
            console.error(`更新停车场 ${lot.id} 失败:`, err);
          } else {
            console.log(`更新停车场 ${lot.id} (${lot.name}) 成功: 总车位 ${totalSpaces}, 已占用 ${occupiedSpaces}`);
          }
        });
      });
    });
  });
});

// 关闭数据库连接
setTimeout(() => {
  db.close();
  console.log('数据库连接已关闭');
}, 2000);
