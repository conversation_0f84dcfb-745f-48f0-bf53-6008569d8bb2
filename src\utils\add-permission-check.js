/**
 * 批量添加权限检查脚本
 * 
 * 此脚本用于批量为指定页面添加权限检查组件
 * 使用方法：
 * 1. 在浏览器控制台中运行此脚本
 * 2. 脚本会自动为指定页面添加权限检查组件
 */

// 需要添加权限检查的页面配置
const pagesToModify = [
  {
    path: '/parking/stats',
    component: 'src/views/parking/stats.vue',
    allowedRoles: ['admin'],
    message: '您没有权限访问停车统计数据，请联系管理员获取权限。'
  },
  {
    path: '/charging-admin/spaces',
    component: 'src/views/charging/admin/spaces.vue',
    allowedRoles: ['admin'],
    message: '您没有权限访问充电车位管理数据，请联系管理员获取权限。'
  },
  {
    path: '/charging-admin/records',
    component: 'src/views/charging/admin/records.vue',
    allowedRoles: ['admin'],
    message: '您没有权限访问充电记录管理数据，请联系管理员获取权限。'
  },
  {
    path: '/charging-admin/dashboard',
    component: 'src/views/charging/admin/dashboard.vue',
    allowedRoles: ['admin'],
    message: '您没有权限访问充电统计数据，请联系管理员获取权限。'
  },
  {
    path: '/charging-admin/faults',
    component: 'src/views/charging/admin/faults.vue',
    allowedRoles: ['admin', 'security'],
    message: '您没有权限访问充电故障管理数据，请联系管理员获取权限。'
  },
  {
    path: '/charging-admin/exceptions',
    component: 'src/views/charging/admin/exceptions.vue',
    allowedRoles: ['admin'],
    message: '您没有权限访问充电异常管理数据，请联系管理员获取权限。'
  },
  {
    path: '/violation/security/create',
    component: 'src/views/violations/security/create.vue',
    allowedRoles: ['admin', 'security'],
    message: '您没有权限创建违规记录，请联系管理员获取权限。'
  },
  {
    path: '/violation/admin/dashboard',
    component: 'src/views/violations/admin/dashboard.vue',
    allowedRoles: ['admin'],
    message: '您没有权限访问违规统计数据，请联系管理员获取权限。'
  },
  {
    path: '/violation/admin/management',
    component: 'src/views/violations/admin/management.vue',
    allowedRoles: ['admin'],
    message: '您没有权限访问违规管理数据，请联系管理员获取权限。'
  }
];

// 修改页面的函数
function modifyPage(page) {
  const fs = require('fs');
  const path = require('path');
  
  // 读取文件内容
  const filePath = path.resolve(page.component);
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 添加权限检查组件
  // 1. 修改模板开始部分
  content = content.replace(
    /<template>\s*<div class="[^"]*">/,
    `<template>\n  <div class="${page.component.includes('app-container') ? 'app-container' : 'page-container'}">\n    <permission-wrapper :allowed-roles="${JSON.stringify(page.allowedRoles)}" message="${page.message}">`
  );
  
  // 2. 修改模板结束部分
  content = content.replace(
    /<\/div>\s*<\/template>/,
    `    </permission-wrapper>\n  </div>\n</template>`
  );
  
  // 3. 添加导入语句
  if (!content.includes("import PermissionWrapper from '@/components/PermissionWrapper'")) {
    const scriptStart = content.indexOf('<script>');
    const importEnd = content.indexOf('export default', scriptStart);
    
    if (scriptStart !== -1 && importEnd !== -1) {
      const imports = content.substring(scriptStart + 8, importEnd).trim();
      const newImports = imports + "\nimport PermissionWrapper from '@/components/PermissionWrapper'";
      content = content.replace(imports, newImports);
    }
  }
  
  // 4. 添加组件注册
  if (!content.includes('components: {')) {
    // 如果没有components属性，添加一个
    content = content.replace(
      /export default\s*{/,
      `export default {\n  components: { PermissionWrapper },`
    );
  } else if (!content.includes('PermissionWrapper')) {
    // 如果有components属性但没有注册PermissionWrapper，添加注册
    content = content.replace(
      /components:\s*{([^}]*)}/,
      (match, p1) => {
        const components = p1.trim();
        return `components: { ${components}${components ? ', ' : ''}PermissionWrapper }`;
      }
    );
  }
  
  // 写入修改后的内容
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`已为页面 ${page.path} 添加权限检查组件`);
}

// 批量修改页面
function batchModifyPages() {
  pagesToModify.forEach(page => {
    try {
      modifyPage(page);
    } catch (error) {
      console.error(`修改页面 ${page.path} 失败:`, error);
    }
  });
  
  console.log('批量添加权限检查完成');
}

// 导出函数供外部调用
module.exports = {
  modifyPage,
  batchModifyPages,
  pagesToModify
};
