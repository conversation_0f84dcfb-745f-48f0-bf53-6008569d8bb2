<template>
  <div class="app-container charging-faults-container">
    <permission-wrapper :allowed-roles="['admin', 'security']" message="您没有权限访问充电故障管理数据，此功能仅对管理员和保安开放">
      <div class="filter-container">
        <h2>充电故障录入</h2>
      </div>

      <!-- 故障录入列表 -->
      <el-card class="filter-container">
          <div class="filter-item">
            <el-form :inline="true" :model="listQuery" class="demo-form-inline">
              <el-form-item label="停车场">
                <el-select v-model="listQuery.parking_lot_id" placeholder="选择停车场" clearable @change="handleFilter">
                  <el-option
                    v-for="item in parkingLots"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="listQuery.status" placeholder="选择状态" clearable @change="handleFilter">
                  <el-option label="待处理" :value="0" />
                  <el-option label="处理中" :value="1" />
                  <el-option label="已完成" :value="2" />
                  <el-option label="已关闭" :value="3" />
                </el-select>
              </el-form-item>
              <el-form-item label="报修时间排序">
                <el-select v-model="listQuery.report_time_order" placeholder="选择排序方式" clearable @change="handleFilter">
                  <el-option label="时间升序" value="asc" />
                  <el-option label="时间降序" value="desc" />
                </el-select>
              </el-form-item>
              <el-form-item label="故障类型">
                <el-select v-model="listQuery.fault_type" placeholder="选择故障类型" clearable @change="handleFilter">
                  <el-option label="充电接口故障" value="connector" />
                  <el-option label="设备无法启动" value="startup" />
                  <el-option label="充电中断" value="interruption" />
                  <el-option label="其他故障" value="other" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleFilter">查询</el-button>
                <el-button type="default" @click="resetFilter">重置</el-button>
                <el-button type="success" @click="handleAddFault">新增故障报修</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>

      <el-card class="list-container">
        <div class="list-header">
          <div class="header-title">
            <i class="el-icon-warning"></i>
            <span>故障报修列表</span>
          </div>
          <div class="header-actions">
            <el-button type="text" @click="fetchData">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
        </div>

        <el-table
          v-loading="listLoading"
          :data="list"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          :header-cell-style="{background:'#f5f7fa'}"
          @sort-change="handleSortChange"
        >
          <el-table-column
            align="center"
            label="ID"
            width="80"
            prop="id"
            sortable="custom"
          >
            <template slot-scope="scope">
              {{ scope.row.id }}
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            label="故障车位"
            width="120"
          >
            <template slot-scope="scope">
              {{ getSpaceInfo(scope.row.space_id) }}
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            label="所属停车场"
          >
            <template slot-scope="scope">
              {{ getParkingLotName(scope.row.parking_lot_id) }}
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            label="故障类型"
            width="120"
          >
            <template slot-scope="scope">
              {{ getFaultTypeName(scope.row.fault_type) }}
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            label="报修人"
            width="120"
          >
            <template slot-scope="scope">
              {{ scope.row.reporter_name }}
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            label="报修时间"
            width="180"
            prop="report_time"
            sortable="custom"
          >
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.report_time) }}
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            label="状态"
            width="100"
          >
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            label="操作"
            width="120"
          >
            <template slot-scope="scope">
              <el-button
                v-if="hasPermission('admin')"
                size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="listQuery.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="listQuery.per_page"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </el-card>

    <!-- 新增故障报修对话框 -->
    <el-dialog title="新增故障报修" :visible.sync="dialogFormVisible" width="500px">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="110px">
        <el-form-item label="停车场" prop="parking_lot_id">
          <el-select v-model="temp.parking_lot_id" class="filter-item" placeholder="请选择停车场" @change="handleParkingLotChange">
            <el-option
              v-for="item in parkingLots"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="充电车位" prop="space_id">
          <el-select v-model="temp.space_id" class="filter-item" placeholder="请选择充电车位">
            <el-option
              v-for="item in filteredSpaces"
              :key="item.id"
              :label="item.space_number"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="故障类型" prop="fault_type">
          <el-select v-model="temp.fault_type" class="filter-item" placeholder="请选择故障类型">
            <el-option label="充电接口故障" value="connector" />
            <el-option label="设备无法启动" value="startup" />
            <el-option label="充电中断" value="interruption" />
            <el-option label="其他故障" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="故障等级" prop="severity">
          <el-select v-model="temp.severity" class="filter-item" placeholder="请选择故障等级">
            <el-option label="轻微" value="low" />
            <el-option label="一般" value="medium" />
            <el-option label="严重" value="high" />
            <el-option label="紧急" value="critical" />
          </el-select>
        </el-form-item>
        <el-form-item label="报修人" prop="reporter_name">
          <el-input v-model="temp.reporter_name" placeholder="请输入报修人姓名" />
        </el-form-item>
        <el-form-item label="联系电话" prop="reporter_phone">
          <el-input v-model="temp.reporter_phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="故障描述" prop="fault_description">
          <el-input
            v-model="temp.fault_description"
            type="textarea"
            :rows="3"
            placeholder="请输入故障描述"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="createData">确认</el-button>
      </div>
    </el-dialog>




    </permission-wrapper>
  </div>
</template>

<script>
import { getChargingFaults, createChargingFault, deleteChargingFault, getChargingSpaces } from '@/api/charging'
import { getParkingLots } from '@/api/parkinglot'
import PermissionWrapper from '@/components/PermissionWrapper'

export default {
  name: 'ChargingFaults',
  components: {
    PermissionWrapper
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        per_page: 10,
        parking_lot_id: undefined,
        status: undefined,
        fault_type: undefined,
        sort_field: 'id',
        sort_order: 'asc',
        report_time_order: undefined
      },
      parkingLots: [],
      spaces: [],
      filteredSpaces: [],
      dialogFormVisible: false,
      temp: {
        parking_lot_id: '',
        space_id: '',
        fault_type: '',
        severity: 'medium',
        reporter_name: '',
        reporter_phone: '',
        fault_description: ''
      },
      rules: {
        parking_lot_id: [{ required: true, message: '请选择停车场', trigger: 'change' }],
        space_id: [{ required: true, message: '请选择充电车位', trigger: 'change' }],
        fault_type: [{ required: true, message: '请选择故障类型', trigger: 'change' }],
        severity: [{ required: true, message: '请选择故障等级', trigger: 'change' }],
        reporter_name: [{ required: true, message: '请输入报修人姓名', trigger: 'blur' }],
        reporter_phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        fault_description: [{ required: true, message: '请输入故障描述', trigger: 'blur' }]
      },

      userRole: 'admin' // 默认设置为管理员角色
    }
  },
  created() {
    // this.fetchUserRole() // 暂时注释掉获取用户角色的方法
    this.fetchParkingLots()
    this.fetchSpaces()
    this.fetchData()
  },
  methods: {

    // 获取用户角色
    async fetchUserRole() {
      try {
        // 这里使用模拟数据，避免API调用错误
        // const response = await getUserInfo()
        // this.userRole = response.data.role
        this.userRole = 'admin' // 直接设置为管理员角色
      } catch (error) {
        console.error('获取用户角色失败:', error)
        this.$message.error('获取用户角色失败')
      }
    },
    // 检查权限
    hasPermission(role) {
      return this.userRole === role
    },
    // 获取停车场列表
    async fetchParkingLots() {
      try {
        const response = await getParkingLots()
        this.parkingLots = response.data.items || []
      } catch (error) {
        console.error('获取停车场列表失败:', error)
        this.$message.error('获取停车场列表失败')
      }
    },
    // 获取充电车位列表
    async fetchSpaces() {
      try {
        const params = { per_page: 100, type: 3 } // 只获取充电车位
        const response = await getChargingSpaces(params)
        this.spaces = response.data.items || []
      } catch (error) {
        console.error('获取充电车位列表失败:', error)
        this.$message.error('获取充电车位列表失败')
      }
    },
    // 获取故障报修列表
    async fetchData() {
      this.listLoading = true
      try {
        // 创建一个新的参数对象，避免修改原始对象
        const params = { ...this.listQuery }

        // 处理报修时间排序
        if (params.report_time_order) {
          params.sort_field = 'report_time'
          params.sort_order = params.report_time_order
          // 删除临时参数，避免发送到后端
          delete params.report_time_order
        }

        console.log('获取故障报修列表，参数:', params)
        const response = await getChargingFaults(params)
        this.list = response.data.items || []
        this.total = response.data.total || 0
        console.log('获取故障报修列表成功，总数:', this.total, '当前页数据:', this.list.length)
      } catch (error) {
        console.error('获取故障报修列表失败:', error)
        this.$message.error('获取故障报修列表失败')
      } finally {
        this.listLoading = false
      }
    },
    // 获取停车场名称
    getParkingLotName(id) {
      const lot = this.parkingLots.find(item => item.id === id)
      return lot ? lot.name : '-'
    },
    // 获取车位信息
    getSpaceInfo(id) {
      const space = this.spaces.find(item => item.id === id)
      return space ? space.space_number : `ID: ${id}`
    },
    // 获取故障类型名称
    getFaultTypeName(type) {
      const typeMap = {
        'connector': '充电接口故障',
        'startup': '设备无法启动',
        'interruption': '充电中断',
        'other': '其他故障'
      }
      return typeMap[type] || type
    },
    // 获取故障等级名称
    getSeverityName(severity) {
      const severityMap = {
        'low': '轻微',
        'medium': '一般',
        'high': '严重',
        'critical': '紧急'
      }
      return severityMap[severity] || severity
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'warning',  // 待处理
        1: 'primary',  // 处理中
        2: 'success',  // 已完成
        3: 'info'      // 已关闭
      }
      return statusMap[status] || 'info'
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待处理',
        1: '处理中',
        2: '已完成',
        3: '已关闭'
      }
      return statusMap[status] || '未知'
    },
    // 格式化日期时间
    formatDateTime(dateStr) {
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    // 处理筛选
    handleFilter() {
      this.listQuery.page = 1
      console.log('应用筛选条件:', this.listQuery)
      this.fetchData()
    },
    // 重置筛选条件
    resetFilter() {
      this.listQuery = {
        page: 1,
        per_page: 10,
        parking_lot_id: undefined,
        status: undefined,
        fault_type: undefined,
        sort_field: 'id',
        sort_order: 'asc',
        report_time_order: undefined
      }
      console.log('重置筛选条件')
      this.fetchData()
    },
    // 处理页面大小变化
    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.listQuery.page = 1 // 切换每页显示数量时重置为第一页
      console.log('页面大小变化:', val)
      this.fetchData()
    },
    // 处理页码变化
    handleCurrentChange(val) {
      this.listQuery.page = val
      console.log('页码变化:', val)
      this.fetchData()
    },
    // 处理表格排序变化
    handleSortChange({ column, prop, order }) {
      console.log('表格排序变化:', prop, order)

      if (!prop || !order) {
        return
      }

      // 映射排序字段
      const fieldMap = {
        'id': 'id',
        'report_time': 'report_time'
      }

      // 映射排序顺序
      const orderMap = {
        'ascending': 'asc',
        'descending': 'desc'
      }

      // 设置排序参数
      this.listQuery.sort_field = fieldMap[prop] || 'id'
      this.listQuery.sort_order = orderMap[order] || 'asc'

      // 如果是按报修时间排序，同步更新报修时间排序下拉框
      if (prop === 'report_time') {
        this.listQuery.report_time_order = orderMap[order]
      } else {
        // 如果不是按报修时间排序，清除报修时间排序下拉框的选择
        this.listQuery.report_time_order = undefined
      }

      // 重置页码并重新获取数据
      this.listQuery.page = 1
      this.fetchData()
    },
    // 处理停车场变更
    handleParkingLotChange() {
      this.temp.space_id = ''
      this.filteredSpaces = this.spaces.filter(item => item.parking_lot_id === this.temp.parking_lot_id)
    },
    // 重置表单
    resetTemp() {
      this.temp = {
        parking_lot_id: '',
        space_id: '',
        fault_type: '',
        severity: 'medium',
        reporter_name: '',
        reporter_phone: '',
        fault_description: ''
      }
      this.filteredSpaces = []
    },
    // 处理添加故障报修
    handleAddFault() {
      this.resetTemp()
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },


    // 处理删除故障
    handleDelete(row) {
      if (!this.hasPermission('admin')) {
        this.$message.error('您没有权限删除故障')
        return
      }
      this.$confirm('确认删除该故障报修?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          console.log('开始删除故障报修，ID:', row.id)
          await deleteChargingFault(row.id)
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          // 重新获取当前页数据
          console.log('删除成功，重新获取数据')
          this.fetchData()
        } catch (error) {
          console.error('删除故障报修失败:', error)
          this.$message.error('删除故障报修失败')
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 创建故障报修
    createData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          try {
            console.log('开始创建故障报修，原始数据:', JSON.stringify(this.temp))

            // 深度复制对象，避免直接修改原始对象
            const formData = JSON.parse(JSON.stringify(this.temp))

            // 确保数据类型正确
            if (formData.parking_lot_id) {
              formData.parking_lot_id = Number(formData.parking_lot_id)
            }

            if (formData.space_id) {
              formData.space_id = Number(formData.space_id)
            }

            console.log('处理后的数据:', JSON.stringify(formData))
            console.log('数据类型检查:')
            console.log('- parking_lot_id:', formData.parking_lot_id, typeof formData.parking_lot_id)
            console.log('- space_id:', formData.space_id, typeof formData.space_id)

            // 添加延迟，确保日志完整显示
            setTimeout(async () => {
              try {
                const response = await createChargingFault(formData)
                console.log('创建故障报修成功，响应:', response)

                this.dialogFormVisible = false
                this.$message({
                  type: 'success',
                  message: '创建成功!'
                })
                this.fetchData()
              } catch (submitError) {
                console.error('提交故障报修失败:', submitError)

                // 提取详细错误信息
                let errorMessage = '创建故障报修失败'
                if (submitError.response && submitError.response.data) {
                  errorMessage = submitError.response.data.message || errorMessage
                  console.error('服务器返回的错误信息:', submitError.response.data)
                }

                this.$message.error(errorMessage)
              }
            }, 100)
          } catch (error) {
            console.error('创建故障报修数据处理失败:', error)
            this.$message.error('创建故障报修失败: ' + error.message)
          }
        }
      })
    },
    // 获取故障等级对应的类型
    getSeverityType(severity) {
      const severityMap = {
        'low': 'info',
        'medium': 'warning',
        'high': 'danger',
        'critical': 'danger'
      }
      return severityMap[severity] || 'info'
    },

  }
}
</script>

<style lang="scss" scoped>
.charging-faults-container {
  .filter-container {
    margin-bottom: 20px;
  }

  .list-container {
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .header-title {
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;

        i {
          margin-right: 10px;
          color: #E6A23C;
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }


}
</style>
