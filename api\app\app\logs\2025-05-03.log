2025/05/03 12:38:51 flask_api __init__.py[81] create_app() INFO: Flask Rest Api startup
2025/05/03 12:38:51 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/05/03 12:38:51 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/05/03 12:38:52 engineio.server socket.py[78] send() INFO: vlvSwIrhfJgsrTKpAAAA: Sending packet OPEN data {'sid': 'vlvSwIrhfJgsrTKpAAAA', 'upgrades': [], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025/05/03 12:38:52 engineio.server socket.py[90] handle_get_request() INFO: vlvSwIrhfJgsrTKpAAAA: Received request to upgrade to websocket
2025/05/03 12:38:52 engineio.server socket.py[219] _websocket_handler() INFO: vlvSwIrhfJgsrTKpAAAA: Upgrade to websocket successful
2025/05/03 12:38:52 engineio.server socket.py[39] receive() INFO: vlvSwIrhfJgsrTKpAAAA: Received packet MESSAGE data 0
2025/05/03 12:38:52 flask_api routes.py[48] handle_connect() ERROR: Token验证失败: Signature has expired
2025/05/03 12:38:52 socketio.server server.py[164] emit() INFO: emitting event "connection_response" to _PtE9toqk3kmDLf8AAAB [/]
2025/05/03 12:38:52 engineio.server socket.py[78] send() INFO: vlvSwIrhfJgsrTKpAAAA: Sending packet MESSAGE data 2["connection_response",{"status":"connected","authenticated":false,"error":"Invalid token"}]
2025/05/03 12:38:52 engineio.server socket.py[78] send() INFO: vlvSwIrhfJgsrTKpAAAA: Sending packet MESSAGE data 0{"sid":"_PtE9toqk3kmDLf8AAAB"}
2025/05/03 12:38:52 engineio.server socket.py[39] receive() INFO: vlvSwIrhfJgsrTKpAAAA: Received packet MESSAGE data 2["join",{"room":"user_1"}]
2025/05/03 12:38:52 socketio.server server.py[576] _handle_event() INFO: received event "join" from _PtE9toqk3kmDLf8AAAB [/]
2025/05/03 12:38:52 flask_api routes.py[24] wrapped() ERROR: Token验证失败: Signature has expired
2025/05/03 12:38:52 socketio.server server.py[402] disconnect() INFO: Disconnecting _PtE9toqk3kmDLf8AAAB [/]
2025/05/03 12:38:52 engineio.server socket.py[78] send() INFO: vlvSwIrhfJgsrTKpAAAA: Sending packet MESSAGE data 1
2025/05/03 12:38:52 flask_api routes.py[60] handle_disconnect() INFO: Client disconnected
2025/05/03 12:38:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [03/May/2025 12:38:52] "[35m[1mGET /socket.io/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NjEwMjI4MywianRpIjoiMGRjZTA3NWQtNjU2NC00M2FhLWFkMzktYWIyZmQwOWYyNzc3IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5IjoxLCJuYmYiOjE3NDYxMDIyODMsImNzcmYiOiI3MzdjN2I3YS1jZGE5LTQxYWQtYWQyNC04YTkyZTYxNjNhYWIiLCJleHAiOjE3NDYxODg2ODMsInJvbGUiOiJhZG1pbiIsInN1YiI6IjEifQ.CrbvKl0EjwLGJa0SnctGWIFE0XaxkExAbWiAxajvWnw&EIO=4&transport=websocket HTTP/1.1[0m" 500 -
2025/05/03 12:38:52 werkzeug _internal.py[97] _log() ERROR: Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
    ~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 336, in execute
    write(b"")
    ~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\serving.py", line 261, in write
    assert status_set is not None, "write() before start_response"
           ^^^^^^^^^^^^^^^^^^^^^^
AssertionError: write() before start_response
2025/05/03 12:41:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [03/May/2025 12:41:07] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/03 12:41:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [03/May/2025 12:41:07] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 401 -
2025/05/03 13:00:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [03/May/2025 13:00:51] "OPTIONS /api/login HTTP/1.1" 200 -
2025/05/03 13:00:51 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/03 13:00:51 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?
2025/05/03 13:00:51 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00237s] ('admin', 1, 0)
2025/05/03 13:00:51 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_name = ?
 LIMIT ? OFFSET ?
2025/05/03 13:00:51 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00161s] ('admin', 1, 0)
2025/05/03 13:00:51 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/03 13:00:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [03/May/2025 13:00:51] "POST /api/login HTTP/1.1" 200 -
2025/05/03 13:00:51 engineio.server socket.py[78] send() INFO: wXM1Sf4g7cUuVGtKAAAC: Sending packet OPEN data {'sid': 'wXM1Sf4g7cUuVGtKAAAC', 'upgrades': [], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025/05/03 13:00:51 engineio.server socket.py[90] handle_get_request() INFO: wXM1Sf4g7cUuVGtKAAAC: Received request to upgrade to websocket
2025/05/03 13:00:51 engineio.server socket.py[219] _websocket_handler() INFO: wXM1Sf4g7cUuVGtKAAAC: Upgrade to websocket successful
2025/05/03 13:00:51 engineio.server socket.py[78] send() INFO: HoJEceMgYKrS-bUAAAAD: Sending packet OPEN data {'sid': 'HoJEceMgYKrS-bUAAAAD', 'upgrades': [], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025/05/03 13:00:51 engineio.server socket.py[90] handle_get_request() INFO: HoJEceMgYKrS-bUAAAAD: Received request to upgrade to websocket
2025/05/03 13:00:51 engineio.server socket.py[219] _websocket_handler() INFO: HoJEceMgYKrS-bUAAAAD: Upgrade to websocket successful
2025/05/03 13:00:51 engineio.server socket.py[39] receive() INFO: wXM1Sf4g7cUuVGtKAAAC: Received packet MESSAGE data 0
2025/05/03 13:00:51 engineio.server socket.py[39] receive() INFO: HoJEceMgYKrS-bUAAAAD: Received packet MESSAGE data 0
2025/05/03 13:00:51 flask_api routes.py[45] handle_connect() INFO: Client connected with user_id: 1
2025/05/03 13:00:51 flask_api routes.py[45] handle_connect() INFO: Client connected with user_id: 1
2025/05/03 13:00:51 socketio.server server.py[164] emit() INFO: emitting event "connection_response" to SrzWyoWda1Z-VOIVAAAE [/]
2025/05/03 13:00:51 socketio.server server.py[164] emit() INFO: emitting event "connection_response" to mwuVHIUpHXTD6AtVAAAF [/]
2025/05/03 13:00:51 engineio.server socket.py[78] send() INFO: wXM1Sf4g7cUuVGtKAAAC: Sending packet MESSAGE data 2["connection_response",{"status":"connected","authenticated":true}]
2025/05/03 13:00:51 engineio.server socket.py[78] send() INFO: HoJEceMgYKrS-bUAAAAD: Sending packet MESSAGE data 2["connection_response",{"status":"connected","authenticated":true}]
2025/05/03 13:00:51 engineio.server socket.py[78] send() INFO: wXM1Sf4g7cUuVGtKAAAC: Sending packet MESSAGE data 0{"sid":"SrzWyoWda1Z-VOIVAAAE"}
2025/05/03 13:00:51 engineio.server socket.py[78] send() INFO: HoJEceMgYKrS-bUAAAAD: Sending packet MESSAGE data 0{"sid":"mwuVHIUpHXTD6AtVAAAF"}
2025/05/03 13:00:51 engineio.server socket.py[39] receive() INFO: HoJEceMgYKrS-bUAAAAD: Received packet MESSAGE data 2["join",{"room":"user_1"}]
2025/05/03 13:00:51 socketio.server server.py[576] _handle_event() INFO: received event "join" from mwuVHIUpHXTD6AtVAAAF [/]
2025/05/03 13:00:51 socketio.server server.py[284] enter_room() INFO: mwuVHIUpHXTD6AtVAAAF is entering room user_1 [/]
2025/05/03 13:00:51 engineio.server socket.py[39] receive() INFO: HoJEceMgYKrS-bUAAAAD: Received packet MESSAGE data 2["join",{"room":"user_1"}]
2025/05/03 13:00:51 flask_api routes.py[83] on_join() INFO: 用户 1 加入房间: user_1
2025/05/03 13:00:51 socketio.server server.py[576] _handle_event() INFO: received event "join" from mwuVHIUpHXTD6AtVAAAF [/]
2025/05/03 13:00:51 socketio.server server.py[284] enter_room() INFO: mwuVHIUpHXTD6AtVAAAF is entering room admin [/]
2025/05/03 13:00:51 socketio.server server.py[284] enter_room() INFO: mwuVHIUpHXTD6AtVAAAF is entering room user_1 [/]
2025/05/03 13:00:51 flask_api routes.py[91] on_join() INFO: 管理员 1 加入管理员房间
2025/05/03 13:00:51 flask_api routes.py[83] on_join() INFO: 用户 1 加入房间: user_1
2025/05/03 13:00:51 socketio.server server.py[284] enter_room() INFO: mwuVHIUpHXTD6AtVAAAF is entering room user_1 [/]
2025/05/03 13:00:51 socketio.server server.py[284] enter_room() INFO: mwuVHIUpHXTD6AtVAAAF is entering room admin [/]
2025/05/03 13:00:51 flask_api routes.py[95] on_join() INFO: 用户 1 加入房间: user_1
2025/05/03 13:00:51 flask_api routes.py[91] on_join() INFO: 管理员 1 加入管理员房间
2025/05/03 13:00:51 socketio.server server.py[164] emit() INFO: emitting event "join_response" to mwuVHIUpHXTD6AtVAAAF [/]
2025/05/03 13:00:51 socketio.server server.py[284] enter_room() INFO: mwuVHIUpHXTD6AtVAAAF is entering room user_1 [/]
2025/05/03 13:00:51 engineio.server socket.py[78] send() INFO: HoJEceMgYKrS-bUAAAAD: Sending packet MESSAGE data 2["join_response",{"status":"success","room":"user_1"}]
2025/05/03 13:00:51 flask_api routes.py[95] on_join() INFO: 用户 1 加入房间: user_1
2025/05/03 13:00:51 socketio.server server.py[164] emit() INFO: emitting event "join_response" to mwuVHIUpHXTD6AtVAAAF [/]
2025/05/03 13:00:51 engineio.server socket.py[78] send() INFO: HoJEceMgYKrS-bUAAAAD: Sending packet MESSAGE data 2["join_response",{"status":"success","room":"user_1"}]
2025/05/03 13:00:51 flask_api routes.py[60] handle_disconnect() INFO: Client disconnected
2025/05/03 13:00:51 flask_api routes.py[60] handle_disconnect() INFO: Client disconnected
2025/05/03 13:00:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [03/May/2025 13:00:52] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/05/03 13:00:52 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/05/03 13:00:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/03 13:00:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00150s] (1,)
2025/05/03 13:00:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/05/03 13:00:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.007205s ago] (1,)
2025/05/03 13:00:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/05/03 13:00:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00125s] (1,)
2025/05/03 13:00:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/05/03 13:00:52 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00114s] (1,)
2025/05/03 13:00:52 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/05/03 13:00:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [03/May/2025 13:00:52] "GET /api/users/1 HTTP/1.1" 200 -
2025/05/03 13:00:52 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet OPEN data {'sid': 'XA9R0dSYjkyjIUozAAAG', 'upgrades': [], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025/05/03 13:00:52 engineio.server socket.py[90] handle_get_request() INFO: XA9R0dSYjkyjIUozAAAG: Received request to upgrade to websocket
2025/05/03 13:00:52 engineio.server socket.py[219] _websocket_handler() INFO: XA9R0dSYjkyjIUozAAAG: Upgrade to websocket successful
2025/05/03 13:00:52 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet MESSAGE data 0
2025/05/03 13:00:52 flask_api routes.py[45] handle_connect() INFO: Client connected with user_id: 1
2025/05/03 13:00:52 socketio.server server.py[164] emit() INFO: emitting event "connection_response" to WeWcciKAyeDHU7RsAAAH [/]
2025/05/03 13:00:52 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet MESSAGE data 2["connection_response",{"status":"connected","authenticated":true}]
2025/05/03 13:00:52 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet MESSAGE data 0{"sid":"WeWcciKAyeDHU7RsAAAH"}
2025/05/03 13:00:52 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet MESSAGE data 2["join",{"room":"user_1"}]
2025/05/03 13:00:52 socketio.server server.py[576] _handle_event() INFO: received event "join" from WeWcciKAyeDHU7RsAAAH [/]
2025/05/03 13:00:52 socketio.server server.py[284] enter_room() INFO: WeWcciKAyeDHU7RsAAAH is entering room user_1 [/]
2025/05/03 13:00:52 flask_api routes.py[83] on_join() INFO: 用户 1 加入房间: user_1
2025/05/03 13:00:52 socketio.server server.py[284] enter_room() INFO: WeWcciKAyeDHU7RsAAAH is entering room admin [/]
2025/05/03 13:00:52 flask_api routes.py[91] on_join() INFO: 管理员 1 加入管理员房间
2025/05/03 13:00:52 socketio.server server.py[284] enter_room() INFO: WeWcciKAyeDHU7RsAAAH is entering room user_1 [/]
2025/05/03 13:00:52 flask_api routes.py[95] on_join() INFO: 用户 1 加入房间: user_1
2025/05/03 13:00:52 socketio.server server.py[164] emit() INFO: emitting event "join_response" to WeWcciKAyeDHU7RsAAAH [/]
2025/05/03 13:00:52 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet MESSAGE data 2["join_response",{"status":"success","room":"user_1"}]
2025/05/03 13:01:17 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:01:17 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:01:42 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:01:42 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:02:07 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:02:07 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:02:32 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:02:32 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:02:57 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:02:57 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:03:22 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:03:22 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:03:47 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:03:47 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:04:12 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:04:12 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:04:37 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:04:37 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:05:02 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:05:02 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:05:27 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:05:27 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:05:52 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:05:52 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:06:17 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:06:17 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:06:42 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:06:42 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
2025/05/03 13:07:07 engineio.server socket.py[78] send() INFO: XA9R0dSYjkyjIUozAAAG: Sending packet PING data None
2025/05/03 13:07:07 engineio.server socket.py[39] receive() INFO: XA9R0dSYjkyjIUozAAAG: Received packet PONG data 
