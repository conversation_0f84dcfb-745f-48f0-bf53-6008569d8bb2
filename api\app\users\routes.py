# api/app/users/routes.py

from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.users import users, users_bp
from app.users.models import Users
from app.users.schema import UserSchema, UserUpdateSchema
from app.utils.response import api_response
from app.utils.auth_helpers import get_current_user_id, is_admin
from app.utils.password_utils import verify_password, hash_password, get_salt_if_none
from marshmallow import ValidationError
from datetime import datetime
import re
import os
from werkzeug.utils import secure_filename

# 公共API - 获取部分用户信息（不需要JWT认证）
@users_bp.route('/public', methods=['GET'])
def get_public_users():
    """获取用户公开信息，用于前端不需要认证的场景"""
    # 获取查询参数
    username = request.args.get('username')
    limit = request.args.get('limit', 20, type=int)

    # 构建查询
    query = Users.query

    # 应用过滤条件
    if username:
        query = query.filter(Users.u_name.like(f'%{username}%'))

    # 限制结果数量，避免返回太多数据
    users_list = query.limit(limit).all()

    # 使用统一的字段名称
    user_data = [
        {
            'id': user.u_id,
            'username': user.u_name,
            'role': user.u_role,
            'department': user.u_belong
        }
        for user in users_list
    ]

    return api_response(
        data={'users': user_data},
        message="获取用户列表成功"
    )

# 获取当前用户的个人信息
@users.route('/me', methods=['GET'])
@jwt_required()
def get_current_user_info():
    try:
        current_user_id = get_jwt_identity()
        user = Users.query.get(current_user_id)

        if not user:
            return api_response(message="用户不存在", status="error", code=404)

        schema = UserSchema()
        result = schema.dump(user)

        return api_response(
            data={'user': result},
            message="获取用户信息成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取当前用户信息时出错: {str(e)}")
        return api_response(message=f"获取用户信息失败: {str(e)}", status="error", code=500)

# 获取当前用户的车辆列表
@users.route('/me/bikes', methods=['GET'])
@jwt_required()
def get_my_bikes():
    """获取当前用户的所有车辆"""
    user_id = get_current_user_id()

    if not user_id:
        return api_response(message="无法获取用户信息，请重新登录", status="error", code=401)

    # 获取用户详情（包含车辆）
    user = Users.query.get(user_id)
    if not user:
        return api_response(message="用户不存在", status="error", code=404)

    user_with_bikes = user.get_details(include_bikes=True)

    return api_response(data=user_with_bikes, message="获取用户车辆信息成功")

# 更新当前用户信息
@users.route('/me', methods=['PUT'])
@jwt_required()
def update_me():
    current_user_id = get_jwt_identity()
    user = Users.query.get_or_404(current_user_id)

    if not user:
        return api_response(message="无法获取用户信息，请重新登录", status="error", code=401)

    data = request.get_json()
    print("接收到的更新数据:", data)

    # 转换为后端字段名
    backend_data = {}
    if 'u_name' in data:
        backend_data['u_name'] = data['u_name']
    if 'u_belong' in data:
        backend_data['u_belong'] = data['u_belong']
    if 'u_phone' in data:
        backend_data['u_phone'] = data['u_phone']
    if 'u_email' in data:
        backend_data['u_email'] = data['u_email']
    if 'avatar' in data:
        backend_data['avatar'] = data['avatar']

    print("转换后的后端数据:", backend_data)

    # 使用schema进行验证
    try:
        user_schema = UserUpdateSchema()
        validated_data = user_schema.load(backend_data)
        print("验证后的数据:", validated_data)

        # 更新用户信息
        for key, value in validated_data.items():
            setattr(user, key, value)

        user.updated_at = datetime.now()
        db.session.commit()

        # 返回更新后的用户信息
        result = user_schema.dump(user)

        return api_response(
            data={"user": result},
            message="个人信息更新成功"
        )
    except ValidationError as err:
        return api_response(
            message=f"数据验证失败: {err.messages}",
            errors=err.messages,
            status="error",
            code=422
        )
    except Exception as e:
        db.session.rollback()
        return api_response(message=f"更新失败: {str(e)}", status="error", code=500)

# 管理员获取所有用户列表
@users.route('', methods=['GET'])
@jwt_required()
def get_users():
    """获取所有用户（仅管理员可用）"""
    # 检查权限
    if not is_admin():
        return api_response(message="需要管理员权限", status="error", code=403)

    # 分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)

    # 搜索参数
    name = request.args.get('name')
    role = request.args.get('role')
    department = request.args.get('department')
    phone = request.args.get('phone')

    # 排序参数
    sort_by = request.args.get('sort_by', 'u_id')
    sort_desc = request.args.get('sort_desc', 'false').lower() == 'true'

    # 构建查询
    query = Users.query

    # 应用搜索过滤条件
    if name:
        query = query.filter(Users.u_name.like(f'%{name}%'))
    if role:
        query = query.filter(Users.u_role == role)
    if department:
        query = query.filter(Users.u_belong.like(f'%{department}%'))
    if phone:
        query = query.filter(Users.u_phone.like(f'%{phone}%'))

    # 应用排序
    if sort_by in ['u_id', 'u_name', 'u_role', 'u_belong', 'created_at']:
        order_column = getattr(Users, sort_by)
        if sort_desc:
            query = query.order_by(order_column.desc())
        else:
            query = query.order_by(order_column.asc())
    else:
        # 默认按ID排序
        query = query.order_by(Users.u_id.asc())

    # 分页
    users_page = query.paginate(page=page, per_page=per_page)

    # 序列化用户数据
    schema = UserSchema(many=True)
    users_data_raw = schema.dump(users_page.items)

    # 转换为统一字段名 - 使前端更容易处理
    users_data = []
    for user in users_data_raw:
        users_data.append({
            'id': user.get('u_id'),
            'username': user.get('u_name'),
            'role': user.get('u_role'),
            'department': user.get('u_belong'),
            'phone': user.get('u_phone'),
            'email': user.get('u_email'),
            'status': 1,  # 默认状态为激活
            'created_at': user.get('created_at'),
            'updated_at': user.get('updated_at')
        })

    return api_response(
        data={
            'users': users_data,
            'pagination': {
                'total': users_page.total,
                'pages': users_page.pages,
                'current_page': page,
                'per_page': per_page
            }
        },
        message="获取用户列表成功"
    )

# 管理员获取特定用户信息
@users.route('/<int:user_id>', methods=['GET'])
@jwt_required()
def get_user(user_id):
    """获取特定用户信息（仅管理员或自己）"""
    try:
        # 获取当前用户ID
        current_user_id = get_jwt_identity()
        if not current_user_id:
            return api_response(message="未登录", status="error", code=401)

        # 获取当前用户
        current_user = Users.query.get(current_user_id)
        if not current_user:
            return api_response(message="当前用户不存在", status="error", code=401)

        # 权限检查：自己或管理员
        if current_user.u_id != user_id and not is_admin():
            return api_response(message="没有权限查看此用户", status="error", code=403)

        # 查找目标用户
        user = Users.query.get(user_id)
        if not user:
            return api_response(message="用户不存在", status="error", code=404)

        # 序列化并转换为统一字段名
        schema = UserSchema()
        user_data = schema.dump(user)

        formatted_data = {
            'id': user_data.get('u_id'),
            'username': user_data.get('u_name'),
            'role': user_data.get('u_role'),
            'department': user_data.get('u_belong'),
            'phone': user_data.get('u_phone'),
            'created_at': user_data.get('created_at'),
            'updated_at': user_data.get('updated_at')
        }

        return api_response(data={'user': formatted_data}, message="获取用户信息成功")
    except Exception as e:
        current_app.logger.error(f"获取用户信息时出错: {str(e)}")
        return api_response(message=f"获取用户信息失败: {str(e)}", status="error", code=500)

# 管理员更新用户信息
@users.route('/<int:user_id>', methods=['PUT'])
@jwt_required()
def update_user(user_id):
    """更新特定用户信息（仅管理员）"""
    # 权限检查
    if not is_admin():
        return api_response(message="需要管理员权限", status="error", code=403)

    # 查找用户
    user = Users.query.get(user_id)

    if not user:
        return api_response(message="用户不存在", status="error", code=404)

    data = request.get_json()

    try:
        # 统一字段名称映射
        field_mapping = {
            'username': 'u_name',
            'role': 'u_role',
            'department': 'u_belong',
            'phone': 'u_phone'
        }

        # 将前端字段名转换为后端字段名
        backend_data = {}
        for frontend_field, backend_field in field_mapping.items():
            if frontend_field in data:
                backend_data[backend_field] = data[frontend_field]

        # 验证数据
        schema = UserSchema(only=list(backend_data.keys()))
        validated_data = schema.load(backend_data, partial=True)

        # 更新字段
        for field, value in validated_data.items():
            setattr(user, field, value)

        user.updated_at = datetime.now()
        db.session.commit()

        # 返回更新后的数据，使用统一字段名
        user_data = UserSchema().dump(user)
        formatted_data = {
            'id': user_data.get('u_id'),
            'username': user_data.get('u_name'),
            'role': user_data.get('u_role'),
            'department': user_data.get('u_belong'),
            'phone': user_data.get('u_phone'),
            'created_at': user_data.get('created_at'),
            'updated_at': user_data.get('updated_at')
        }

        return api_response(
            data={'user': formatted_data},
            message="用户信息更新成功"
        )
    except ValidationError as e:
        return api_response(message="数据验证失败", errors=e.messages, status="error", code=400)
    except Exception as e:
        db.session.rollback()
        return api_response(message=f"更新失败: {str(e)}", status="error", code=500)

# 修改密码
@users.route('/me/password', methods=['PUT'])
@jwt_required()
def change_password():
    """修改当前用户的密码"""
    user_id = get_jwt_identity()
    user = Users.query.get(user_id)

    if not user:
        return api_response(message="无法获取用户信息，请重新登录", status="error", code=401)

    data = request.get_json()
    old_password = data.get('old_password')
    new_password = data.get('new_password')

    if not old_password or not new_password:
        return api_response(message="请提供旧密码和新密码", status="error", code=400)

    # 验证旧密码
    if not verify_password(old_password, user.u_pwd, user.salt):
        return api_response(message="旧密码错误", status="error", code=400)

    # 验证新密码强度
    if len(new_password) < 8:
        return api_response(message="新密码长度不能少于8位", status="error", code=400)

    if not re.search(r'[A-Za-z]', new_password) or not re.search(r'\d', new_password):
        return api_response(message="新密码必须包含字母和数字", status="error", code=400)

    try:
        # 生成新的盐值
        new_salt = get_salt_if_none()
        # 使用新盐值哈希新密码
        hashed_password = hash_password(new_password, new_salt)

        # 更新密码和盐值
        user.u_pwd = hashed_password
        user.salt = new_salt
        user.updated_at = datetime.now()

        db.session.commit()

        return api_response(message="密码修改成功")
    except Exception as e:
        db.session.rollback()
        return api_response(message=f"密码修改失败: {str(e)}", status="error", code=500)

# 上传头像
@users.route('/me/avatar', methods=['POST'])
@jwt_required()
def upload_avatar():
    """上传用户头像"""
    user_id = get_jwt_identity()
    user = Users.query.get(user_id)

    if not user:
        return api_response(message="无法获取用户信息，请重新登录", status="error", code=401)

    if 'file' not in request.files:
        return api_response(message="请选择要上传的头像文件", status="error", code=400)

    file = request.files['file']

    if file.filename == '':
        return api_response(message="未选择文件", status="error", code=400)

    # 检查文件类型
    if not allowed_file(file.filename, {'png', 'jpg', 'jpeg'}):
        return api_response(message="不支持的文件类型，请上传PNG或JPG格式的图片", status="error", code=400)

    try:
        # 生成安全的文件名
        filename = secure_filename(file.filename)
        # 添加用户ID前缀，避免文件名冲突
        filename = f"avatar_{user.u_id}_{int(datetime.now().timestamp())}_{filename}"

        # 确保上传目录存在
        upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'avatars')
        os.makedirs(upload_folder, exist_ok=True)

        # 保存文件
        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)

        # 更新用户头像URL
        avatar_url = f"/uploads/avatars/{filename}"
        user.avatar = avatar_url
        user.updated_at = datetime.now()

        db.session.commit()

        return api_response(
            data={'url': avatar_url, 'avatar': avatar_url},
            message="头像上传成功"
        )
    except Exception as e:
        db.session.rollback()
        return api_response(message=f"头像上传失败: {str(e)}", status="error", code=500)

def allowed_file(filename, allowed_extensions):
    """检查文件类型是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

# 删除用户
@users.route('/<int:user_id>', methods=['DELETE'])
@jwt_required()
def delete_user(user_id):
    """删除指定用户（仅管理员）"""
    # 权限检查
    if not is_admin():
        return api_response(message="需要管理员权限", status="error", code=403)

    # 防止删除自己
    current_user_id = get_jwt_identity()
    if int(current_user_id) == user_id:
        return api_response(message="不能删除当前登录的账号", status="error", code=400)

    # 查找用户
    user = Users.query.get(user_id)
    if not user:
        return api_response(message="用户不存在", status="error", code=404)

    try:
        # 查找并删除关联的Players记录
        from app.players.models import Players
        player = Players.query.filter_by(user_id=user_id).first()
        if player:
            db.session.delete(player)
            current_app.logger.info(f"删除了用户ID={user_id}关联的Players记录")

        # 查找并删除关联的Bikes记录
        from app.bikes.models import Bikes
        bikes = Bikes.query.filter_by(belong_to=user_id).all()
        bikes_count = len(bikes)
        for bike in bikes:
            db.session.delete(bike)
        if bikes_count > 0:
            current_app.logger.info(f"删除了用户ID={user_id}关联的{bikes_count}条Bikes记录")

        # 删除用户（级联删除关联数据）
        db.session.delete(user)
        db.session.commit()

        return api_response(
            message=f"用户删除成功，同时删除了关联的{bikes_count}辆车辆信息和{'1条玩家记录' if player else '0条玩家记录'}",
            data={"deleted_user_id": user_id, "deleted_bikes_count": bikes_count, "deleted_player": player is not None}
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除用户时出错: {str(e)}")
        return api_response(message=f"删除失败: {str(e)}", status="error", code=500)

# 管理员创建新用户
@users.route('', methods=['POST'])
@jwt_required()
def create_user():
    """创建新用户（仅管理员）"""
    # 权限检查
    if not is_admin():
        return api_response(message="需要管理员权限", status="error", code=403)

    data = request.get_json()

    # 必需字段验证
    if not data or not data.get('username'):
        return api_response(message="用户名是必填项", status="error", code=400)

    # 检查用户名是否已存在
    existing_user = Users.query.filter_by(u_name=data.get('username')).first()
    if existing_user:
        return api_response(message="用户名已存在", status="error", code=409)

    try:
        # 准备用户数据
        username = data.get('username')
        password = data.get('password', '123456')  # 默认密码
        role = data.get('role', 'user')  # 默认角色

        # 验证角色权限 - 非管理员不能创建管理员账号
        current_user_id = get_jwt_identity()
        current_user = Users.query.get(current_user_id)
        if role == 'admin' and (not current_user or current_user.u_role != 'admin'):
            return api_response(message="无权创建管理员账号", status="error", code=403)

        # 创建盐值并哈希密码
        salt = get_salt_if_none()
        hashed_password = hash_password(password, salt)

        # 创建用户
        new_user = Users(
            u_name=username,
            u_pwd=hashed_password,
            u_role=role,
            u_belong=data.get('department', ''),
            u_phone=data.get('phone', ''),
            u_email=data.get('email', '')
        )
        new_user.salt = salt

        db.session.add(new_user)
        db.session.commit()

        # 返回新用户数据
        user_data = UserSchema().dump(new_user)
        formatted_data = {
            'id': user_data.get('u_id'),
            'username': user_data.get('u_name'),
            'role': user_data.get('u_role'),
            'department': user_data.get('u_belong'),
            'phone': user_data.get('u_phone'),
            'email': user_data.get('u_email'),
            'status': 1
        }

        return api_response(
            data={'user': formatted_data},
            message="用户创建成功",
            code=201
        )
    except Exception as e:
        db.session.rollback()
        return api_response(message=f"创建用户失败: {str(e)}", status="error", code=500)

# 在users_bp蓝图上也注册删除用户的路由，确保路径能被正确匹配
@users_bp.route('/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
def delete_user_bp(user_id):
    """删除指定用户（仅管理员）- 从users_bp蓝图路由"""
    return delete_user(user_id)

# 在users_bp蓝图上也注册获取用户的路由
@users_bp.route('/users/<int:user_id>', methods=['GET'])
@jwt_required()
def get_user_bp(user_id):
    """获取特定用户信息 - 从users_bp蓝图路由"""
    return get_user(user_id)

# 在users_bp蓝图上也注册更新用户的路由
@users_bp.route('/users/<int:user_id>', methods=['PUT'])
@jwt_required()
def update_user_bp(user_id):
    """更新特定用户信息 - 从users_bp蓝图路由"""
    return update_user(user_id)

# 更新用户角色
@users.route('/<int:user_id>/role', methods=['PUT'])
@jwt_required()
def update_user_role(user_id):
    """更新用户角色（仅管理员）"""
    # 权限检查
    if not is_admin():
        return api_response(message="需要管理员权限", status="error", code=403)

    # 查找用户
    user = Users.query.get(user_id)
    if not user:
        return api_response(message="用户不存在", status="error", code=404)

    data = request.get_json()
    role = data.get('role')

    # 验证角色值
    if not role or role not in ['admin', 'user', 'security']:
        return api_response(message="无效的角色值", status="error", code=400)

    try:
        # 更新角色
        user.u_role = role
        user.updated_at = datetime.now()
        db.session.commit()

        return api_response(
            data={
                'user_id': user.u_id,
                'role': user.u_role
            },
            message="用户角色更新成功"
        )
    except Exception as e:
        db.session.rollback()
        return api_response(message=f"更新角色失败: {str(e)}", status="error", code=500)

# 在users_bp蓝图上也注册更新用户角色的路由
@users_bp.route('/users/<int:user_id>/role', methods=['PUT'])
@jwt_required()
def update_user_role_bp(user_id):
    """更新用户角色 - 从users_bp蓝图路由"""
    return update_user_role(user_id)
