"""
车辆禁用相关路由

提供车辆禁用状态查询、禁用车辆、解除禁用等功能。
"""

from flask import request, current_app
from app import db
from app.bikes import bikes_bp
from app.bikes.models import Bikes
from app.violations.models import VehicleDisableRecord, ViolationRecord
from app.utils.response import api_response
from app.utils.auth import jwt_required, admin_required
from app.utils.auth_helpers import get_current_user_id, is_admin
from app.utils.vehicle_status import is_vehicle_disabled, get_vehicle_status_info
from app.tasks.check_vehicle_disable import disable_vehicle, enable_vehicle, get_vehicle_disable_records
from datetime import datetime
import logging

# 获取车辆禁用状态
@bikes_bp.route('/bikes/<int:bike_id>/disable-status', methods=['GET'])
@jwt_required()
def get_bike_disable_status(bike_id):
    """获取车辆禁用状态"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 查找车辆
        bike = Bikes.query.get(bike_id)
        if not bike:
            return api_response(message="车辆不存在", status="error", code=404)

        # 检查权限：只有车辆的所有者或管理员可以查询
        if bike.belong_to != user_id and not is_admin():
            return api_response(message="没有权限查询此车辆", status="error", code=403)

        # 获取车辆状态信息
        status_info = get_vehicle_status_info(bike_id)

        # 如果车辆被禁用，获取禁用记录详情
        if status_info['is_disabled'] and status_info['disable_info']:
            disable_info = status_info['disable_info']
            
            # 获取禁用记录
            disable_record = VehicleDisableRecord.query.get(disable_info['disable_id']) if disable_info['disable_id'] else None
            
            if disable_record:
                # 转换为字典
                disable_record_dict = disable_record.to_dict()
                status_info['disable_record'] = disable_record_dict

        return api_response(
            data=status_info,
            message="获取车辆禁用状态成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取车辆禁用状态失败: {str(e)}")
        return api_response(message=f"获取车辆禁用状态失败: {str(e)}", status="error", code=500)

# 禁用车辆
@bikes_bp.route('/bikes/<int:bike_id>/disable', methods=['POST'])
@jwt_required()
@admin_required
def disable_bike(bike_id):
    """禁用车辆（仅管理员）"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取请求数据
        data = request.get_json() or {}
        
        # 获取禁用原因和关联的违规记录ID
        reason = data.get('reason')
        violation_id = data.get('violation_id')
        
        # 调用禁用函数
        success, message, record_id = disable_vehicle(bike_id, violation_id, reason, user_id)
        
        if not success:
            return api_response(message=message, status="error", code=400)
            
        # 获取禁用记录详情
        disable_record = VehicleDisableRecord.query.get(record_id) if record_id else None
        
        return api_response(
            data={
                'record_id': record_id,
                'record': disable_record.to_dict() if disable_record else None
            },
            message=message,
            status="success"
        )
    except Exception as e:
        current_app.logger.error(f"禁用车辆失败: {str(e)}")
        return api_response(message=f"禁用车辆失败: {str(e)}", status="error", code=500)

# 解除车辆禁用
@bikes_bp.route('/bikes/<int:bike_id>/enable', methods=['POST'])
@jwt_required()
@admin_required
def enable_bike(bike_id):
    """解除车辆禁用（仅管理员）"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取请求数据
        data = request.get_json() or {}
        
        # 获取解除禁用原因
        reason = data.get('reason')
        
        # 调用解除禁用函数
        success, message, record_ids = enable_vehicle(bike_id, user_id, reason)
        
        if not success:
            return api_response(message=message, status="error", code=400)
            
        return api_response(
            data={
                'record_ids': record_ids
            },
            message=message,
            status="success"
        )
    except Exception as e:
        current_app.logger.error(f"解除车辆禁用失败: {str(e)}")
        return api_response(message=f"解除车辆禁用失败: {str(e)}", status="error", code=500)

# 获取车辆禁用记录
@bikes_bp.route('/bikes/<int:bike_id>/disable-records', methods=['GET'])
@jwt_required()
def get_bike_disable_records(bike_id):
    """获取车辆禁用记录"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 查找车辆
        bike = Bikes.query.get(bike_id)
        if not bike:
            return api_response(message="车辆不存在", status="error", code=404)

        # 检查权限：只有车辆的所有者或管理员可以查询
        if bike.belong_to != user_id and not is_admin():
            return api_response(message="没有权限查询此车辆", status="error", code=403)

        # 获取查询参数
        active_only = request.args.get('active_only', 'false').lower() == 'true'
        
        # 获取禁用记录
        records = get_vehicle_disable_records(bike_id, active_only)
        
        # 转换为字典列表
        records_dict = [record.to_dict() for record in records]
        
        return api_response(
            data=records_dict,
            message="获取车辆禁用记录成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取车辆禁用记录失败: {str(e)}")
        return api_response(message=f"获取车辆禁用记录失败: {str(e)}", status="error", code=500)

# 获取所有禁用记录
@bikes_bp.route('/bikes/disable-records', methods=['GET'])
@jwt_required()
@admin_required
def get_all_disable_records():
    """获取所有禁用记录（仅管理员）"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        active_only = request.args.get('active_only', 'false').lower() == 'true'
        
        # 获取禁用记录
        records = get_vehicle_disable_records(None, active_only)
        
        # 手动分页
        total = len(records)
        start = (page - 1) * per_page
        end = start + per_page
        paginated_records = records[start:end]
        
        # 转换为字典列表
        records_dict = [record.to_dict() for record in paginated_records]
        
        return api_response(
            data={
                'items': records_dict,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page
            },
            message="获取所有禁用记录成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取所有禁用记录失败: {str(e)}")
        return api_response(message=f"获取所有禁用记录失败: {str(e)}", status="error", code=500)
