2025/04/14 00:06:17 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 00:06:17 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 00:06:17 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 00:06:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:06:23] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/14 00:06:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:06:23] "POST /api/login HTTP/1.1" 200 -
2025/04/14 00:06:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:06:24] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 00:06:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:06:24] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 00:06:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:06:26] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:06:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:06:26] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:06:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:06:31] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/04/14 00:06:31 flask_api routes.py[120] create_parkinglot() ERROR: 创建停车场出错: __init__() got an unexpected keyword argument 'occupied_spaces'
2025/04/14 00:06:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:06:31] "[35m[1mPOST /api/parkinglots HTTP/1.1[0m" 500 -
2025/04/14 00:14:08 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 00:14:08 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 00:14:08 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 00:14:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:14:18] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 00:14:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:14:18] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 00:14:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:14:18] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:14:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:14:18] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:14:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:14:22] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/04/14 00:14:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:14:22] "[35m[1mPOST /api/parkinglots HTTP/1.1[0m" 201 -
2025/04/14 00:14:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:14:22] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:14:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:14:26] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:14:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:14:26] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:14:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:14:30] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 00:14:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:14:30] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 00:14:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:14:30] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:28:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:23] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:28:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:23] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:28:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:26] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 00:28:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:26] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 00:28:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:27] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:28:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:35] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/04/14 00:28:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:35] "[31m[1mPOST /api/parkinglots HTTP/1.1[0m" 400 -
2025/04/14 00:28:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:40] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:28:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:40] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:28:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:48] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/04/14 00:28:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:49] "[35m[1mPOST /api/parkinglots HTTP/1.1[0m" 201 -
2025/04/14 00:28:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:49] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:28:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:49] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:28:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:56] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 00:28:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:56] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 00:28:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:56] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:28:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:56] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:28:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:59] "OPTIONS /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/14 00:28:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:28:59] "GET /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/14 00:29:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:29:02] "OPTIONS /api/parking-records/stats HTTP/1.1" 200 -
2025/04/14 00:29:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:29:02] "GET /api/parking-records/stats HTTP/1.1" 200 -
2025/04/14 00:29:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:29:04] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:29:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:29:04] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:47:56 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 00:47:56 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 00:47:56 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 00:48:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:03] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:48:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:03] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:48:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:06] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 00:48:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:06] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 00:48:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:06] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:48:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:11] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/04/14 00:48:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:11] "[35m[1mPOST /api/parkinglots HTTP/1.1[0m" 201 -
2025/04/14 00:48:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:16] "OPTIONS /api/parkinglots/3 HTTP/1.1" 200 -
2025/04/14 00:48:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:16] "GET /api/parkinglots/3 HTTP/1.1" 200 -
2025/04/14 00:48:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:16] "OPTIONS /api/parkinglots/3/spaces?page=1&limit=50 HTTP/1.1" 200 -
2025/04/14 00:48:16 flask_api routes.py[286] get_parking_spaces() ERROR: 获取停车位列表出错: Object of type InstanceState is not JSON serializable
2025/04/14 00:48:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:16] "[35m[1mGET /api/parkinglots/3/spaces?page=1&limit=50 HTTP/1.1[0m" 500 -
2025/04/14 00:48:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:29] "OPTIONS /api/parking-records/stats HTTP/1.1" 200 -
2025/04/14 00:48:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:29] "GET /api/parking-records/stats HTTP/1.1" 200 -
2025/04/14 00:48:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:31] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:48:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:31] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:48:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:48:33] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:49:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:49:14] "OPTIONS /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/14 00:49:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:49:14] "GET /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/14 00:49:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:49:18] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:49:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:49:18] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:49:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:49:22] "OPTIONS /api/parking-records/stats HTTP/1.1" 200 -
2025/04/14 00:49:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:49:22] "GET /api/parking-records/stats HTTP/1.1" 200 -
2025/04/14 00:49:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:49:23] "OPTIONS /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/14 00:49:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:49:23] "GET /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/14 00:49:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:49:26] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:49:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:49:26] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:56:06 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 00:56:06 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 00:56:06 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 00:56:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:11] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 00:56:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:11] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 00:56:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:11] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:56:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:11] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:56:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:13] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:56:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:16] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/04/14 00:56:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:16] "[31m[1mPOST /api/parkinglots HTTP/1.1[0m" 400 -
2025/04/14 00:56:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:22] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/04/14 00:56:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:22] "[35m[1mPOST /api/parkinglots HTTP/1.1[0m" 201 -
2025/04/14 00:56:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:30] "OPTIONS /api/parkinglots/4 HTTP/1.1" 200 -
2025/04/14 00:56:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:30] "PUT /api/parkinglots/4 HTTP/1.1" 200 -
2025/04/14 00:56:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:39] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/04/14 00:56:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:39] "[35m[1mPOST /api/parkinglots HTTP/1.1[0m" 201 -
2025/04/14 00:56:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:42] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 00:56:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 00:56:42] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:14:17 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 12:14:17 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 12:14:17 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 12:14:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:14:54] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/14 12:14:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:14:54] "POST /api/login HTTP/1.1" 200 -
2025/04/14 12:14:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:14:55] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 12:14:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:14:55] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 12:14:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:14:58] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:14:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:14:58] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:18:34 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 12:18:34 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 12:18:34 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 12:18:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:18:43] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 12:18:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:18:43] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 12:18:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:18:43] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:18:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:18:43] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:18:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:18:46] "OPTIONS /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/14 12:18:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:18:46] "GET /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/14 12:18:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:18:48] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:18:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:18:48] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:18:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:18:56] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:18:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:18:56] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:20:38 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 12:21:34 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 12:22:09 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 12:23:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:23:28] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:23:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:23:28] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:23:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:23:30] "OPTIONS /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/14 12:23:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:23:30] "GET /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/14 12:23:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:23:32] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:28:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:28:39] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:28:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:28:39] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:28:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:28:43] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 12:28:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:28:43] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 12:28:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:28:43] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:29:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:29:32] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/04/14 12:29:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:29:32] "[35m[1mPOST /api/parkinglots HTTP/1.1[0m" 201 -
2025/04/14 12:29:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:29:37] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 12:29:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:29:37] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 12:29:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:29:38] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:29:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 12:29:38] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 12:31:10 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 13:44:55 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 13:44:55 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 13:44:55 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 13:47:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 13:47:18] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 13:47:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 13:47:18] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 13:47:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 13:47:18] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 13:47:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 13:47:18] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 13:49:44 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 13:53:09 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 13:53:09 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 13:53:09 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 13:53:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 13:53:14] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 13:53:14 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 13:53:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/04/14 13:53:14 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00148s] (10,)
2025/04/14 13:53:14 flask_api __init__.py[253] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such table: users
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?]
[parameters: (10,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/14 13:53:15 flask_api __init__.py[255] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: users

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_jwt_extended\view_decorators.py", line 170, in decorator
    return current_app.ensure_sync(fn)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 57, in get_current_user
    user = Users.query.get_or_404(current_user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_sqlalchemy\query.py", line 30, in get_or_404
    rv = self.get(ident)
  File "<string>", line 2, in get
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1121, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 1130, in _get_impl
    return self.session._get_impl(
           ~~~~~~~~~~~~~~~~~~~~~~^
        mapper,
        ^^^^^^^
    ...<6 lines>...
        execution_options=self._execution_options,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 3874, in _get_impl
    return db_load_fn(
        self,
    ...<5 lines>...
        bind_arguments=bind_arguments,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
    ~~~~~~~~~~~~~~~^
        q,
        ^^
    ...<2 lines>...
        bind_arguments=bind_arguments,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: users
[SQL: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?]
[parameters: (10,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/04/14 13:53:15 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 13:53:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 13:53:15] "[35m[1mGET /api/users/me HTTP/1.1[0m" 500 -
2025/04/14 13:53:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 13:53:22] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/14 13:53:22 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 13:53:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?
2025/04/14 13:53:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00130s] ('admin', 1, 0)
2025/04/14 13:53:22 flask_api __init__.py[253] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such table: players
[SQL: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('admin', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/14 13:53:22 flask_api __init__.py[255] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: players

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 101, in login
    player = Players.query.filter_by(username=data['username']).first()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 2754, in first
    return self.limit(1)._iter().first()  # type: ignore
           ~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 2853, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
        params,
        ^^^^^^^
        execution_options={"_sa_orm_load_options": self.load_options},
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: players
[SQL: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('admin', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/04/14 13:53:22 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 13:53:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 13:53:22] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/04/14 15:43:00 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 15:43:00 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 15:43:00 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 15:44:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 15:44:08] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/14 15:44:08 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 15:44:08 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?
2025/04/14 15:44:08 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00137s] ('admin', 1, 0)
2025/04/14 15:44:08 flask_api __init__.py[253] handle_exception() ERROR: 未处理的异常: (sqlite3.OperationalError) no such table: players
[SQL: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('admin', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/14 15:44:09 flask_api __init__.py[255] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: players

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\players\routes.py", line 101, in login
    player = Players.query.filter_by(username=data['username']).first()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 2754, in first
    return self.limit(1)._iter().first()  # type: ignore
           ~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\query.py", line 2853, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
        params,
        ^^^^^^^
        execution_options={"_sa_orm_load_options": self.load_options},
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: players
[SQL: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?]
[parameters: ('admin', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025/04/14 15:44:09 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 15:44:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 15:44:09] "[35m[1mPOST /api/login HTTP/1.1[0m" 500 -
2025/04/14 21:18:08 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:18:08 root parking_tasks.py[16] check_parking_lot_consistency() INFO: 开始执行停车场数据一致性检查...
2025/04/14 21:18:08 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:18:08 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots
2025/04/14 21:18:08 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00148s] ()
2025/04/14 21:18:08 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:18:08 root parking_tasks.py[63] check_parking_lot_consistency() ERROR: 停车场数据一致性检查失败: (sqlite3.OperationalError) no such table: parking_lots
[SQL: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/14 21:18:08 root parking_tasks.py[72] check_orphaned_parking_records() INFO: 开始检查异常停车记录...
2025/04/14 21:18:08 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:18:08 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_records.id AS parking_records_id, parking_records.vehicle_id AS parking_records_vehicle_id, parking_records.user_id AS parking_records_user_id, parking_records.parking_lot_id AS parking_records_parking_lot_id, parking_records.parking_space_id AS parking_records_parking_space_id, parking_records.entry_time AS parking_records_entry_time, parking_records.exit_time AS parking_records_exit_time, parking_records.status AS parking_records_status, parking_records.created_at AS parking_records_created_at, parking_records.updated_at AS parking_records_updated_at, parking_records.remarks AS parking_records_remarks 
FROM parking_records 
WHERE parking_records.status = ? AND parking_records.entry_time < ?
2025/04/14 21:18:08 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00206s] (0, '2025-04-13 21:18:08.840030')
2025/04/14 21:18:08 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:18:08 root parking_tasks.py[134] check_orphaned_parking_records() ERROR: 检查异常停车记录失败: (sqlite3.OperationalError) no such table: parking_records
[SQL: SELECT parking_records.id AS parking_records_id, parking_records.vehicle_id AS parking_records_vehicle_id, parking_records.user_id AS parking_records_user_id, parking_records.parking_lot_id AS parking_records_parking_lot_id, parking_records.parking_space_id AS parking_records_parking_space_id, parking_records.entry_time AS parking_records_entry_time, parking_records.exit_time AS parking_records_exit_time, parking_records.status AS parking_records_status, parking_records.created_at AS parking_records_created_at, parking_records.updated_at AS parking_records_updated_at, parking_records.remarks AS parking_records_remarks 
FROM parking_records 
WHERE parking_records.status = ? AND parking_records.entry_time < ?]
[parameters: (0, '2025-04-13 21:18:08.840030')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025/04/14 21:19:02 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:19:21 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: DELETE FROM parking_spaces
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00063s] ()
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: DELETE FROM parking_lots
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00084s] ()
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[2705] _connection_commit_impl() INFO: COMMIT
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: INSERT INTO parking_lots (name, address, total_spaces, occupied_spaces, longitude, latitude, opening_hours, status, created_at, updated_at, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00141s] ('北门停车场', '校园北门附近', 20, 0, None, None, '24小时', 1, '2025-04-14 21:19:21.628963', '2025-04-14 21:19:21.628974', '位于学校北门，靠近图书馆')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: INSERT INTO parking_lots (name, address, total_spaces, occupied_spaces, longitude, latitude, opening_hours, status, created_at, updated_at, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.009053s ago] ('东区停车场', '东区教学楼旁', 30, 0, None, None, '24小时', 1, '2025-04-14 21:19:21.640611', '2025-04-14 21:19:21.640613', '东区主停车场，靠近食堂')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [generated in 0.00062s (insertmanyvalues) 1/20 (ordered; batch not supported)] (1, '1-001', 1, 0, None, '2025-04-14 21:19:21.639405', '2025-04-14 21:19:21.639412')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 2/20 (ordered; batch not supported)] (1, '1-002', 1, 0, None, '2025-04-14 21:19:21.639509', '2025-04-14 21:19:21.639512')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 3/20 (ordered; batch not supported)] (1, '1-003', 1, 0, None, '2025-04-14 21:19:21.639554', '2025-04-14 21:19:21.639556')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 4/20 (ordered; batch not supported)] (1, '1-004', 1, 0, None, '2025-04-14 21:19:21.639589', '2025-04-14 21:19:21.639591')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 5/20 (ordered; batch not supported)] (1, '1-005', 1, 0, None, '2025-04-14 21:19:21.639623', '2025-04-14 21:19:21.639635')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 6/20 (ordered; batch not supported)] (1, '1-006', 1, 0, None, '2025-04-14 21:19:21.639667', '2025-04-14 21:19:21.639669')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 7/20 (ordered; batch not supported)] (1, '1-007', 1, 0, None, '2025-04-14 21:19:21.639698', '2025-04-14 21:19:21.639700')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 8/20 (ordered; batch not supported)] (1, '1-008', 1, 0, None, '2025-04-14 21:19:21.639727', '2025-04-14 21:19:21.639729')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 9/20 (ordered; batch not supported)] (1, '1-009', 1, 0, None, '2025-04-14 21:19:21.639756', '2025-04-14 21:19:21.639758')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 10/20 (ordered; batch not supported)] (1, '1-010', 1, 0, None, '2025-04-14 21:19:21.639787', '2025-04-14 21:19:21.639789')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 11/20 (ordered; batch not supported)] (1, '1-011', 1, 0, None, '2025-04-14 21:19:21.639817', '2025-04-14 21:19:21.639819')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 12/20 (ordered; batch not supported)] (1, '1-012', 1, 0, None, '2025-04-14 21:19:21.639850', '2025-04-14 21:19:21.639852')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 13/20 (ordered; batch not supported)] (1, '1-013', 1, 0, None, '2025-04-14 21:19:21.639884', '2025-04-14 21:19:21.639886')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 14/20 (ordered; batch not supported)] (1, '1-014', 1, 0, None, '2025-04-14 21:19:21.639914', '2025-04-14 21:19:21.639916')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 15/20 (ordered; batch not supported)] (1, '1-015', 1, 0, None, '2025-04-14 21:19:21.639943', '2025-04-14 21:19:21.639945')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 16/20 (ordered; batch not supported)] (1, '1-016', 1, 0, None, '2025-04-14 21:19:21.639971', '2025-04-14 21:19:21.639973')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 17/20 (ordered; batch not supported)] (1, '1-017', 1, 0, None, '2025-04-14 21:19:21.640470', '2025-04-14 21:19:21.640473')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 18/20 (ordered; batch not supported)] (1, '1-018', 1, 0, None, '2025-04-14 21:19:21.640506', '2025-04-14 21:19:21.640508')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 19/20 (ordered; batch not supported)] (1, '1-019', 1, 0, None, '2025-04-14 21:19:21.640537', '2025-04-14 21:19:21.640538')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 20/20 (ordered; batch not supported)] (1, '1-020', 1, 0, None, '2025-04-14 21:19:21.640566', '2025-04-14 21:19:21.640568')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: INSERT INTO parking_lots (name, address, total_spaces, occupied_spaces, longitude, latitude, opening_hours, status, created_at, updated_at, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.04744s ago] ('科研楼停车场', '科研楼南侧', 15, 0, None, None, '24小时', 1, '2025-04-14 21:19:21.677144', '2025-04-14 21:19:21.677151', '仅供科研人员使用')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [cached since 0.03675s ago (insertmanyvalues) 1/30 (ordered; batch not supported)] (2, '2-001', 1, 0, None, '2025-04-14 21:19:21.675614', '2025-04-14 21:19:21.675621')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 2/30 (ordered; batch not supported)] (2, '2-002', 1, 0, None, '2025-04-14 21:19:21.675747', '2025-04-14 21:19:21.675750')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 3/30 (ordered; batch not supported)] (2, '2-003', 1, 0, None, '2025-04-14 21:19:21.675789', '2025-04-14 21:19:21.675791')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 4/30 (ordered; batch not supported)] (2, '2-004', 1, 0, None, '2025-04-14 21:19:21.675823', '2025-04-14 21:19:21.675826')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 5/30 (ordered; batch not supported)] (2, '2-005', 1, 0, None, '2025-04-14 21:19:21.675897', '2025-04-14 21:19:21.675899')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 6/30 (ordered; batch not supported)] (2, '2-006', 1, 0, None, '2025-04-14 21:19:21.675946', '2025-04-14 21:19:21.675948')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 7/30 (ordered; batch not supported)] (2, '2-007', 1, 0, None, '2025-04-14 21:19:21.676004', '2025-04-14 21:19:21.676007')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 8/30 (ordered; batch not supported)] (2, '2-008', 1, 0, None, '2025-04-14 21:19:21.676040', '2025-04-14 21:19:21.676042')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 9/30 (ordered; batch not supported)] (2, '2-009', 1, 0, None, '2025-04-14 21:19:21.676073', '2025-04-14 21:19:21.676075')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 10/30 (ordered; batch not supported)] (2, '2-010', 1, 0, None, '2025-04-14 21:19:21.676120', '2025-04-14 21:19:21.676123')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 11/30 (ordered; batch not supported)] (2, '2-011', 1, 0, None, '2025-04-14 21:19:21.676248', '2025-04-14 21:19:21.676253')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 12/30 (ordered; batch not supported)] (2, '2-012', 1, 0, None, '2025-04-14 21:19:21.676334', '2025-04-14 21:19:21.676338')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 13/30 (ordered; batch not supported)] (2, '2-013', 1, 0, None, '2025-04-14 21:19:21.676406', '2025-04-14 21:19:21.676409')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 14/30 (ordered; batch not supported)] (2, '2-014', 1, 0, None, '2025-04-14 21:19:21.676452', '2025-04-14 21:19:21.676454')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 15/30 (ordered; batch not supported)] (2, '2-015', 1, 0, None, '2025-04-14 21:19:21.676485', '2025-04-14 21:19:21.676487')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 16/30 (ordered; batch not supported)] (2, '2-016', 1, 0, None, '2025-04-14 21:19:21.676515', '2025-04-14 21:19:21.676517')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 17/30 (ordered; batch not supported)] (2, '2-017', 1, 0, None, '2025-04-14 21:19:21.676545', '2025-04-14 21:19:21.676547')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 18/30 (ordered; batch not supported)] (2, '2-018', 1, 0, None, '2025-04-14 21:19:21.676577', '2025-04-14 21:19:21.676579')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 19/30 (ordered; batch not supported)] (2, '2-019', 1, 0, None, '2025-04-14 21:19:21.676606', '2025-04-14 21:19:21.676608')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 20/30 (ordered; batch not supported)] (2, '2-020', 1, 0, None, '2025-04-14 21:19:21.676634', '2025-04-14 21:19:21.676636')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 21/30 (ordered; batch not supported)] (2, '2-021', 1, 0, None, '2025-04-14 21:19:21.676665', '2025-04-14 21:19:21.676667')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 22/30 (ordered; batch not supported)] (2, '2-022', 1, 0, None, '2025-04-14 21:19:21.676696', '2025-04-14 21:19:21.676698')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 23/30 (ordered; batch not supported)] (2, '2-023', 1, 0, None, '2025-04-14 21:19:21.676731', '2025-04-14 21:19:21.676733')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 24/30 (ordered; batch not supported)] (2, '2-024', 1, 0, None, '2025-04-14 21:19:21.676761', '2025-04-14 21:19:21.676763')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 25/30 (ordered; batch not supported)] (2, '2-025', 1, 0, None, '2025-04-14 21:19:21.676789', '2025-04-14 21:19:21.676791')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 26/30 (ordered; batch not supported)] (2, '2-026', 1, 0, None, '2025-04-14 21:19:21.676817', '2025-04-14 21:19:21.676818')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 27/30 (ordered; batch not supported)] (2, '2-027', 1, 0, None, '2025-04-14 21:19:21.676852', '2025-04-14 21:19:21.676854')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 28/30 (ordered; batch not supported)] (2, '2-028', 1, 0, None, '2025-04-14 21:19:21.676882', '2025-04-14 21:19:21.676884')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 29/30 (ordered; batch not supported)] (2, '2-029', 1, 0, None, '2025-04-14 21:19:21.676935', '2025-04-14 21:19:21.676939')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 30/30 (ordered; batch not supported)] (2, '2-030', 1, 0, None, '2025-04-14 21:19:21.676993', '2025-04-14 21:19:21.676996')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [cached since 0.08605s ago (insertmanyvalues) 1/15 (ordered; batch not supported)] (3, '3-001', 1, 0, None, '2025-04-14 21:19:21.731212', '2025-04-14 21:19:21.731218')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 2/15 (ordered; batch not supported)] (3, '3-002', 1, 0, None, '2025-04-14 21:19:21.731352', '2025-04-14 21:19:21.731356')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 3/15 (ordered; batch not supported)] (3, '3-003', 1, 0, None, '2025-04-14 21:19:21.731415', '2025-04-14 21:19:21.731417')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 4/15 (ordered; batch not supported)] (3, '3-004', 1, 0, None, '2025-04-14 21:19:21.731461', '2025-04-14 21:19:21.731463')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 5/15 (ordered; batch not supported)] (3, '3-005', 1, 0, None, '2025-04-14 21:19:21.731504', '2025-04-14 21:19:21.731506')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 6/15 (ordered; batch not supported)] (3, '3-006', 1, 0, None, '2025-04-14 21:19:21.731561', '2025-04-14 21:19:21.731563')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 7/15 (ordered; batch not supported)] (3, '3-007', 1, 0, None, '2025-04-14 21:19:21.731600', '2025-04-14 21:19:21.731602')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 8/15 (ordered; batch not supported)] (3, '3-008', 1, 0, None, '2025-04-14 21:19:21.731630', '2025-04-14 21:19:21.731633')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 9/15 (ordered; batch not supported)] (3, '3-009', 1, 0, None, '2025-04-14 21:19:21.731665', '2025-04-14 21:19:21.731666')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 10/15 (ordered; batch not supported)] (3, '3-010', 1, 0, None, '2025-04-14 21:19:21.731701', '2025-04-14 21:19:21.731705')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 11/15 (ordered; batch not supported)] (3, '3-011', 1, 0, None, '2025-04-14 21:19:21.731733', '2025-04-14 21:19:21.731735')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 12/15 (ordered; batch not supported)] (3, '3-012', 1, 0, None, '2025-04-14 21:19:21.731768', '2025-04-14 21:19:21.731769')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 13/15 (ordered; batch not supported)] (3, '3-013', 1, 0, None, '2025-04-14 21:19:21.731802', '2025-04-14 21:19:21.731804')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 14/15 (ordered; batch not supported)] (3, '3-014', 1, 0, None, '2025-04-14 21:19:21.731830', '2025-04-14 21:19:21.731832')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 15/15 (ordered; batch not supported)] (3, '3-015', 1, 0, None, '2025-04-14 21:19:21.731870', '2025-04-14 21:19:21.731873')
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[2705] _connection_commit_impl() INFO: COMMIT
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots) AS anon_1
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00174s] ()
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00094s] ()
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00114s] (1,)
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.004721s ago] (2,)
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.007244s ago] (3,)
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots) AS anon_1
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.025s ago] ()
2025/04/14 21:19:21 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:19:21 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 21:19:21 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 21:19:21 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/14 21:19:23 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: DELETE FROM parking_spaces
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00094s] ()
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: DELETE FROM parking_lots
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00089s] ()
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[2705] _connection_commit_impl() INFO: COMMIT
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: INSERT INTO parking_lots (name, address, total_spaces, occupied_spaces, longitude, latitude, opening_hours, status, created_at, updated_at, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00099s] ('北门停车场', '校园北门附近', 20, 0, None, None, '24小时', 1, '2025-04-14 21:19:24.079006', '2025-04-14 21:19:24.079015', '位于学校北门，靠近图书馆')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: INSERT INTO parking_lots (name, address, total_spaces, occupied_spaces, longitude, latitude, opening_hours, status, created_at, updated_at, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.007639s ago] ('东区停车场', '东区教学楼旁', 30, 0, None, None, '24小时', 1, '2025-04-14 21:19:24.087913', '2025-04-14 21:19:24.087915', '东区主停车场，靠近食堂')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [generated in 0.00078s (insertmanyvalues) 1/20 (ordered; batch not supported)] (1, '1-001', 1, 0, None, '2025-04-14 21:19:24.086690', '2025-04-14 21:19:24.086696')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 2/20 (ordered; batch not supported)] (1, '1-002', 1, 0, None, '2025-04-14 21:19:24.086792', '2025-04-14 21:19:24.086794')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 3/20 (ordered; batch not supported)] (1, '1-003', 1, 0, None, '2025-04-14 21:19:24.086837', '2025-04-14 21:19:24.086839')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 4/20 (ordered; batch not supported)] (1, '1-004', 1, 0, None, '2025-04-14 21:19:24.086874', '2025-04-14 21:19:24.086876')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 5/20 (ordered; batch not supported)] (1, '1-005', 1, 0, None, '2025-04-14 21:19:24.086908', '2025-04-14 21:19:24.086922')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 6/20 (ordered; batch not supported)] (1, '1-006', 1, 0, None, '2025-04-14 21:19:24.086955', '2025-04-14 21:19:24.086957')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 7/20 (ordered; batch not supported)] (1, '1-007', 1, 0, None, '2025-04-14 21:19:24.086986', '2025-04-14 21:19:24.086988')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 8/20 (ordered; batch not supported)] (1, '1-008', 1, 0, None, '2025-04-14 21:19:24.087016', '2025-04-14 21:19:24.087018')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 9/20 (ordered; batch not supported)] (1, '1-009', 1, 0, None, '2025-04-14 21:19:24.087045', '2025-04-14 21:19:24.087047')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 10/20 (ordered; batch not supported)] (1, '1-010', 1, 0, None, '2025-04-14 21:19:24.087073', '2025-04-14 21:19:24.087075')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 11/20 (ordered; batch not supported)] (1, '1-011', 1, 0, None, '2025-04-14 21:19:24.087103', '2025-04-14 21:19:24.087105')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 12/20 (ordered; batch not supported)] (1, '1-012', 1, 0, None, '2025-04-14 21:19:24.087136', '2025-04-14 21:19:24.087138')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 13/20 (ordered; batch not supported)] (1, '1-013', 1, 0, None, '2025-04-14 21:19:24.087170', '2025-04-14 21:19:24.087172')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 14/20 (ordered; batch not supported)] (1, '1-014', 1, 0, None, '2025-04-14 21:19:24.087202', '2025-04-14 21:19:24.087204')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 15/20 (ordered; batch not supported)] (1, '1-015', 1, 0, None, '2025-04-14 21:19:24.087236', '2025-04-14 21:19:24.087238')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 16/20 (ordered; batch not supported)] (1, '1-016', 1, 0, None, '2025-04-14 21:19:24.087265', '2025-04-14 21:19:24.087267')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 17/20 (ordered; batch not supported)] (1, '1-017', 1, 0, None, '2025-04-14 21:19:24.087777', '2025-04-14 21:19:24.087779')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 18/20 (ordered; batch not supported)] (1, '1-018', 1, 0, None, '2025-04-14 21:19:24.087812', '2025-04-14 21:19:24.087814')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 19/20 (ordered; batch not supported)] (1, '1-019', 1, 0, None, '2025-04-14 21:19:24.087842', '2025-04-14 21:19:24.087844')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 20/20 (ordered; batch not supported)] (1, '1-020', 1, 0, None, '2025-04-14 21:19:24.087871', '2025-04-14 21:19:24.087872')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: INSERT INTO parking_lots (name, address, total_spaces, occupied_spaces, longitude, latitude, opening_hours, status, created_at, updated_at, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.04296s ago] ('科研楼停车场', '科研楼南侧', 15, 0, None, None, '24小时', 1, '2025-04-14 21:19:24.123812', '2025-04-14 21:19:24.123813', '仅供科研人员使用')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [cached since 0.03398s ago (insertmanyvalues) 1/30 (ordered; batch not supported)] (2, '2-001', 1, 0, None, '2025-04-14 21:19:24.122838', '2025-04-14 21:19:24.122842')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 2/30 (ordered; batch not supported)] (2, '2-002', 1, 0, None, '2025-04-14 21:19:24.122920', '2025-04-14 21:19:24.122922')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 3/30 (ordered; batch not supported)] (2, '2-003', 1, 0, None, '2025-04-14 21:19:24.122957', '2025-04-14 21:19:24.122958')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 4/30 (ordered; batch not supported)] (2, '2-004', 1, 0, None, '2025-04-14 21:19:24.122988', '2025-04-14 21:19:24.122990')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 5/30 (ordered; batch not supported)] (2, '2-005', 1, 0, None, '2025-04-14 21:19:24.123020', '2025-04-14 21:19:24.123022')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 6/30 (ordered; batch not supported)] (2, '2-006', 1, 0, None, '2025-04-14 21:19:24.123051', '2025-04-14 21:19:24.123053')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 7/30 (ordered; batch not supported)] (2, '2-007', 1, 0, None, '2025-04-14 21:19:24.123083', '2025-04-14 21:19:24.123085')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 8/30 (ordered; batch not supported)] (2, '2-008', 1, 0, None, '2025-04-14 21:19:24.123114', '2025-04-14 21:19:24.123115')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 9/30 (ordered; batch not supported)] (2, '2-009', 1, 0, None, '2025-04-14 21:19:24.123143', '2025-04-14 21:19:24.123145')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 10/30 (ordered; batch not supported)] (2, '2-010', 1, 0, None, '2025-04-14 21:19:24.123171', '2025-04-14 21:19:24.123173')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 11/30 (ordered; batch not supported)] (2, '2-011', 1, 0, None, '2025-04-14 21:19:24.123200', '2025-04-14 21:19:24.123202')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 12/30 (ordered; batch not supported)] (2, '2-012', 1, 0, None, '2025-04-14 21:19:24.123233', '2025-04-14 21:19:24.123235')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 13/30 (ordered; batch not supported)] (2, '2-013', 1, 0, None, '2025-04-14 21:19:24.123263', '2025-04-14 21:19:24.123265')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 14/30 (ordered; batch not supported)] (2, '2-014', 1, 0, None, '2025-04-14 21:19:24.123291', '2025-04-14 21:19:24.123293')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 15/30 (ordered; batch not supported)] (2, '2-015', 1, 0, None, '2025-04-14 21:19:24.123319', '2025-04-14 21:19:24.123321')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 16/30 (ordered; batch not supported)] (2, '2-016', 1, 0, None, '2025-04-14 21:19:24.123347', '2025-04-14 21:19:24.123349')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 17/30 (ordered; batch not supported)] (2, '2-017', 1, 0, None, '2025-04-14 21:19:24.123374', '2025-04-14 21:19:24.123376')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 18/30 (ordered; batch not supported)] (2, '2-018', 1, 0, None, '2025-04-14 21:19:24.123401', '2025-04-14 21:19:24.123402')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 19/30 (ordered; batch not supported)] (2, '2-019', 1, 0, None, '2025-04-14 21:19:24.123445', '2025-04-14 21:19:24.123447')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 20/30 (ordered; batch not supported)] (2, '2-020', 1, 0, None, '2025-04-14 21:19:24.123478', '2025-04-14 21:19:24.123479')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 21/30 (ordered; batch not supported)] (2, '2-021', 1, 0, None, '2025-04-14 21:19:24.123507', '2025-04-14 21:19:24.123508')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 22/30 (ordered; batch not supported)] (2, '2-022', 1, 0, None, '2025-04-14 21:19:24.123535', '2025-04-14 21:19:24.123536')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 23/30 (ordered; batch not supported)] (2, '2-023', 1, 0, None, '2025-04-14 21:19:24.123563', '2025-04-14 21:19:24.123565')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 24/30 (ordered; batch not supported)] (2, '2-024', 1, 0, None, '2025-04-14 21:19:24.123591', '2025-04-14 21:19:24.123593')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 25/30 (ordered; batch not supported)] (2, '2-025', 1, 0, None, '2025-04-14 21:19:24.123620', '2025-04-14 21:19:24.123622')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 26/30 (ordered; batch not supported)] (2, '2-026', 1, 0, None, '2025-04-14 21:19:24.123647', '2025-04-14 21:19:24.123648')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 27/30 (ordered; batch not supported)] (2, '2-027', 1, 0, None, '2025-04-14 21:19:24.123680', '2025-04-14 21:19:24.123682')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 28/30 (ordered; batch not supported)] (2, '2-028', 1, 0, None, '2025-04-14 21:19:24.123710', '2025-04-14 21:19:24.123712')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 29/30 (ordered; batch not supported)] (2, '2-029', 1, 0, None, '2025-04-14 21:19:24.123743', '2025-04-14 21:19:24.123745')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 30/30 (ordered; batch not supported)] (2, '2-030', 1, 0, None, '2025-04-14 21:19:24.123770', '2025-04-14 21:19:24.123772')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [cached since 0.08094s ago (insertmanyvalues) 1/15 (ordered; batch not supported)] (3, '3-001', 1, 0, None, '2025-04-14 21:19:24.173264', '2025-04-14 21:19:24.173269')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 2/15 (ordered; batch not supported)] (3, '3-002', 1, 0, None, '2025-04-14 21:19:24.173380', '2025-04-14 21:19:24.173382')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 3/15 (ordered; batch not supported)] (3, '3-003', 1, 0, None, '2025-04-14 21:19:24.173424', '2025-04-14 21:19:24.173426')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 4/15 (ordered; batch not supported)] (3, '3-004', 1, 0, None, '2025-04-14 21:19:24.173457', '2025-04-14 21:19:24.173459')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 5/15 (ordered; batch not supported)] (3, '3-005', 1, 0, None, '2025-04-14 21:19:24.173493', '2025-04-14 21:19:24.173495')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 6/15 (ordered; batch not supported)] (3, '3-006', 1, 0, None, '2025-04-14 21:19:24.173528', '2025-04-14 21:19:24.173530')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 7/15 (ordered; batch not supported)] (3, '3-007', 1, 0, None, '2025-04-14 21:19:24.173560', '2025-04-14 21:19:24.173561')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 8/15 (ordered; batch not supported)] (3, '3-008', 1, 0, None, '2025-04-14 21:19:24.173591', '2025-04-14 21:19:24.173592')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 9/15 (ordered; batch not supported)] (3, '3-009', 1, 0, None, '2025-04-14 21:19:24.173621', '2025-04-14 21:19:24.173623')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 10/15 (ordered; batch not supported)] (3, '3-010', 1, 0, None, '2025-04-14 21:19:24.173668', '2025-04-14 21:19:24.173671')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 11/15 (ordered; batch not supported)] (3, '3-011', 1, 0, None, '2025-04-14 21:19:24.173725', '2025-04-14 21:19:24.173729')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 12/15 (ordered; batch not supported)] (3, '3-012', 1, 0, None, '2025-04-14 21:19:24.173772', '2025-04-14 21:19:24.173774')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 13/15 (ordered; batch not supported)] (3, '3-013', 1, 0, None, '2025-04-14 21:19:24.173803', '2025-04-14 21:19:24.173805')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 14/15 (ordered; batch not supported)] (3, '3-014', 1, 0, None, '2025-04-14 21:19:24.173857', '2025-04-14 21:19:24.173859')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: INSERT INTO parking_spaces (parking_lot_id, space_number, type, status, current_vehicle_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1841] _execute_context() INFO: [insertmanyvalues 15/15 (ordered; batch not supported)] (3, '3-015', 1, 0, None, '2025-04-14 21:19:24.173887', '2025-04-14 21:19:24.173889')
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[2705] _connection_commit_impl() INFO: COMMIT
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots) AS anon_1
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00116s] ()
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00101s] ()
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00120s] (1,)
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.004159s ago] (2,)
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.006638s ago] (3,)
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots) AS anon_1
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.02398s ago] ()
2025/04/14 21:19:24 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:19:24 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/14 21:19:24 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/14 21:21:50 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:21:50 root parking_tasks.py[16] check_parking_lot_consistency() INFO: 开始执行停车场数据一致性检查...
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00231s] ()
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ?) AS anon_1
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00139s] (1, 1)
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00149s] (1,)
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ?) AS anon_1
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.01081s ago] (2, 1)
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.008521s ago] (2,)
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ? AND parking_spaces.status = ?) AS anon_1
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.0166s ago] (3, 1)
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_spaces.id AS parking_spaces_id, parking_spaces.parking_lot_id AS parking_spaces_parking_lot_id, parking_spaces.space_number AS parking_spaces_space_number, parking_spaces.type AS parking_spaces_type, parking_spaces.status AS parking_spaces_status, parking_spaces.current_vehicle_id AS parking_spaces_current_vehicle_id, parking_spaces.created_at AS parking_spaces_created_at, parking_spaces.updated_at AS parking_spaces_updated_at 
FROM parking_spaces 
WHERE parking_spaces.parking_lot_id = ?) AS anon_1
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.01378s ago] (3,)
2025/04/14 21:21:50 root parking_tasks.py[57] check_parking_lot_consistency() INFO: 所有停车场数据一致性检查通过，无需修复
2025/04/14 21:21:50 root parking_tasks.py[72] check_orphaned_parking_records() INFO: 开始检查异常停车记录...
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_records.id AS parking_records_id, parking_records.vehicle_id AS parking_records_vehicle_id, parking_records.user_id AS parking_records_user_id, parking_records.parking_lot_id AS parking_records_parking_lot_id, parking_records.parking_space_id AS parking_records_parking_space_id, parking_records.entry_time AS parking_records_entry_time, parking_records.exit_time AS parking_records_exit_time, parking_records.status AS parking_records_status, parking_records.created_at AS parking_records_created_at, parking_records.updated_at AS parking_records_updated_at, parking_records.remarks AS parking_records_remarks 
FROM parking_records 
WHERE parking_records.status = ? AND parking_records.entry_time < ?
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00109s] (0, '2025-04-13 21:21:50.435621')
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_records.id AS parking_records_id, parking_records.vehicle_id AS parking_records_vehicle_id, parking_records.user_id AS parking_records_user_id, parking_records.parking_lot_id AS parking_records_parking_lot_id, parking_records.parking_space_id AS parking_records_parking_space_id, parking_records.entry_time AS parking_records_entry_time, parking_records.exit_time AS parking_records_exit_time, parking_records.status AS parking_records_status, parking_records.created_at AS parking_records_created_at, parking_records.updated_at AS parking_records_updated_at, parking_records.remarks AS parking_records_remarks 
FROM parking_records 
WHERE parking_records.status = ?
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00128s] (0,)
2025/04/14 21:21:50 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:27:41 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:27:41 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 21:27:41 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 21:31:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:31:01] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/14 21:31:01 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:31:01 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?
2025/04/14 21:31:01 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00101s] ('admin', 1, 0)
2025/04/14 21:31:01 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:31:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:31:01] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/14 21:36:22 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:36:22 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:36:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_name = ?
 LIMIT ? OFFSET ?
2025/04/14 21:36:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00146s] ('admin', 1, 0)
2025/04/14 21:36:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: INSERT INTO users (u_name, u_pwd, salt, u_role, u_belong, u_phone, u_email, avatar, created_at, updated_at, version) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025/04/14 21:36:22 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00136s] ('admin', 'acc0ab36a6dacefe84e86d7b82894d532801ea8c2b288e2ad4d7799a4cbe7dbf', 'admin-salt-123456', 'admin', 'System', '13800138000', None, None, '2025-04-14 21:36:22.988433', '2025-04-14 21:36:22.988438', 1)
2025/04/14 21:36:22 sqlalchemy.engine.Engine base.py[2705] _connection_commit_impl() INFO: COMMIT
2025/04/14 21:36:23 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:36:23 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/04/14 21:36:23 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00107s] (1,)
2025/04/14 21:36:23 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:37:22 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:37:22 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 21:37:22 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 21:37:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:37:24] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/14 21:37:24 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:37:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?
2025/04/14 21:37:24 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00077s] ('admin', 1, 0)
2025/04/14 21:37:24 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:37:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:37:24] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/14 21:37:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:37:30] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/14 21:37:30 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:37:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?
2025/04/14 21:37:30 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 5.958s ago] ('admin', 1, 0)
2025/04/14 21:37:30 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:37:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:37:30] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/14 21:38:14 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:38:23 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:38:29 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:38:37 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:38:37 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 21:38:37 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 21:38:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:38:43] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/14 21:38:43 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:38:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?
2025/04/14 21:38:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00112s] ('admin', 1, 0)
2025/04/14 21:38:43 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:38:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:38:43] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/14 21:41:29 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/14 21:41:29 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/14 21:41:29 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/14 21:41:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:33] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/14 21:41:33 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:41:33 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE players.username = ?
 LIMIT ? OFFSET ?
2025/04/14 21:41:33 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00116s] ('admin', 1, 0)
2025/04/14 21:41:33 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_name = ?
 LIMIT ? OFFSET ?
2025/04/14 21:41:33 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00142s] ('admin', 1, 0)
2025/04/14 21:41:33 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:41:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:33] "POST /api/login HTTP/1.1" 200 -
2025/04/14 21:41:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 21:41:34 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:41:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/04/14 21:41:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00105s] (1,)
2025/04/14 21:41:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/04/14 21:41:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00103s] (1,)
2025/04/14 21:41:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/04/14 21:41:34 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00087s] (1,)
2025/04/14 21:41:34 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:41:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 3.107s ago] (1,)
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 3.102s ago] (1,)
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 3.1s ago] (1,)
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:41:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:37] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 3.124s ago] (1,)
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 3.12s ago] (1,)
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 3.121s ago] (1,)
2025/04/14 21:41:37 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:41:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:37] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 21:41:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:39] "OPTIONS /api/bikes?page=1&limit=10&user_id=1 HTTP/1.1" 200 -
2025/04/14 21:41:39 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:41:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.belong_to = ?
2025/04/14 21:41:39 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00098s] (1,)
2025/04/14 21:41:39 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:41:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:39] "GET /api/bikes?page=1&limit=10&user_id=1 HTTP/1.1" 200 -
2025/04/14 21:41:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:42] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_name = ?
 LIMIT ? OFFSET ?
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00109s] (1, 1, 0)
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 7.711s ago] (1,)
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users ORDER BY users.u_id ASC
 LIMIT ? OFFSET ?
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00179s] (10, 0)
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users) AS anon_1
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00135s] ()
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 7.729s ago] (1,)
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 7.728s ago] (1,)
2025/04/14 21:41:42 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:41:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:42] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/14 21:41:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:45] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/14 21:41:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:45] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00300s] ()
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_name = ?
 LIMIT ? OFFSET ?
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:41:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:46] "GET /api/bikes HTTP/1.1" 200 -
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 3.894s ago] (1, 1, 0)
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 11.61s ago] (1,)
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes) AS anon_1
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00184s] ()
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.status = ?) AS anon_1
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00146s] ('可用',)
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.status = ?) AS anon_1
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.005558s ago] ('废弃',)
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.brand AS bikes_brand, count(bikes.b_id) AS count_1 
FROM bikes GROUP BY bikes.brand
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00098s] ()
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_type AS bikes_b_type, count(bikes.b_id) AS count_1 
FROM bikes GROUP BY bikes.b_type
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00092s] ()
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.color AS bikes_color, count(bikes.b_id) AS count_1 
FROM bikes GROUP BY bikes.color
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00084s] ()
2025/04/14 21:41:46 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:41:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:46] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/14 21:41:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:48] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_name = ?
 LIMIT ? OFFSET ?
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 5.98s ago] (1, 1, 0)
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 13.69s ago] (1,)
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users ORDER BY users.u_id ASC
 LIMIT ? OFFSET ?
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 5.984s ago] (10, 0)
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users) AS anon_1
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 5.976s ago] ()
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 13.7s ago] (1,)
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 13.7s ago] (1,)
2025/04/14 21:41:48 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:41:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:48] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/14 21:41:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:53] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 21:41:53 flask_api routes.py[16] get_all_parkinglots() INFO: 开始处理获取停车场列表请求
2025/04/14 21:41:53 flask_api routes.py[26] get_all_parkinglots() INFO: 请求参数: page=1, limit=10, search='', status=
2025/04/14 21:41:53 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:41:53 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots) AS anon_1
2025/04/14 21:41:53 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00127s] ()
2025/04/14 21:41:53 flask_api routes.py[30] get_all_parkinglots() INFO: 数据库中停车场总数: 3
2025/04/14 21:41:53 flask_api routes.py[103] get_all_parkinglots() WARNING: 无效的状态值: 
2025/04/14 21:41:53 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots ORDER BY parking_lots.id ASC) AS anon_1
2025/04/14 21:41:53 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00120s] ()
2025/04/14 21:41:54 flask_api routes.py[116] get_all_parkinglots() INFO: 过滤后但分页前的记录数: 3
2025/04/14 21:41:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots ORDER BY parking_lots.id ASC
 LIMIT ? OFFSET ?
2025/04/14 21:41:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [generated in 0.00165s] (10, 0)
2025/04/14 21:41:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT parking_lots.id AS parking_lots_id, parking_lots.name AS parking_lots_name, parking_lots.address AS parking_lots_address, parking_lots.total_spaces AS parking_lots_total_spaces, parking_lots.occupied_spaces AS parking_lots_occupied_spaces, parking_lots.longitude AS parking_lots_longitude, parking_lots.latitude AS parking_lots_latitude, parking_lots.opening_hours AS parking_lots_opening_hours, parking_lots.status AS parking_lots_status, parking_lots.created_at AS parking_lots_created_at, parking_lots.updated_at AS parking_lots_updated_at, parking_lots.description AS parking_lots_description 
FROM parking_lots) AS anon_1
2025/04/14 21:41:54 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 0.01872s ago] ()
2025/04/14 21:41:54 flask_api routes.py[123] get_all_parkinglots() INFO: 获取到 3 条停车场数据
2025/04/14 21:41:54 flask_api routes.py[137] get_all_parkinglots() INFO: 返回停车场数据: 3 条记录
2025/04/14 21:41:54 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:41:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:41:54] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/14 21:43:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:43:09] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/14 21:43:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:43:09] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_name = ?
 LIMIT ? OFFSET ?
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 84.02s ago] ()
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 87.9s ago] (1, 1, 0)
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:43:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:43:10] "GET /api/bikes HTTP/1.1" 200 -
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 95.62s ago] (1,)
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes) AS anon_1
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 84.01s ago] ()
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.status = ?) AS anon_1
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 84.01s ago] ('可用',)
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.status = ?) AS anon_1
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 84.01s ago] ('废弃',)
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.brand AS bikes_brand, count(bikes.b_id) AS count_1 
FROM bikes GROUP BY bikes.brand
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 84s ago] ()
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_type AS bikes_b_type, count(bikes.b_id) AS count_1 
FROM bikes GROUP BY bikes.b_type
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 84s ago] ()
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.color AS bikes_color, count(bikes.b_id) AS count_1 
FROM bikes GROUP BY bikes.color
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 84s ago] ()
2025/04/14 21:43:10 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:43:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:43:10] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/14 21:47:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:47:43] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/14 21:47:43 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:47:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/04/14 21:47:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 369s ago] (1,)
2025/04/14 21:47:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/04/14 21:47:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 369s ago] (1,)
2025/04/14 21:47:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/04/14 21:47:43 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 369s ago] (1,)
2025/04/14 21:47:43 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:47:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:47:43] "GET /api/users/me HTTP/1.1" 200 -
2025/04/14 21:48:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:48:15] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/14 21:48:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:48:15] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 389.4s ago] ()
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_name = ?
 LIMIT ? OFFSET ?
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 393.3s ago] (1, 1, 0)
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:48:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:48:15] "GET /api/bikes HTTP/1.1" 200 -
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 401s ago] (1,)
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes) AS anon_1
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 389.4s ago] ()
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.status = ?) AS anon_1
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 389.4s ago] ('可用',)
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE bikes.status = ?) AS anon_1
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 389.4s ago] ('废弃',)
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.brand AS bikes_brand, count(bikes.b_id) AS count_1 
FROM bikes GROUP BY bikes.brand
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 389.4s ago] ()
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_type AS bikes_b_type, count(bikes.b_id) AS count_1 
FROM bikes GROUP BY bikes.b_type
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 389.4s ago] ()
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.color AS bikes_color, count(bikes.b_id) AS count_1 
FROM bikes GROUP BY bikes.color
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 389.4s ago] ()
2025/04/14 21:48:15 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:48:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:48:15] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/14 21:48:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:48:17] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[2699] _connection_begin_impl() INFO: BEGIN (implicit)
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_name = ?
 LIMIT ? OFFSET ?
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 395.2s ago] (1, 1, 0)
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users 
WHERE users.u_id = ?
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 402.9s ago] (1,)
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users ORDER BY users.u_id ASC
 LIMIT ? OFFSET ?
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 395.1s ago] (10, 0)
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT count(*) AS count_1 
FROM (SELECT users.u_id AS users_u_id, users.u_name AS users_u_name, users.u_pwd AS users_u_pwd, users.salt AS users_salt, users.u_role AS users_u_role, users.u_belong AS users_u_belong, users.u_phone AS users_u_phone, users.u_email AS users_u_email, users.avatar AS users_avatar, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.version AS users_version 
FROM users) AS anon_1
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 395.1s ago] ()
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT bikes.b_id AS bikes_b_id, bikes.belong_to AS bikes_belong_to, bikes.b_num AS bikes_b_num, bikes.brand AS bikes_brand, bikes.color AS bikes_color, bikes.b_type AS bikes_b_type, bikes.status AS bikes_status, bikes.created_at AS bikes_created_at, bikes.updated_at AS bikes_updated_at 
FROM bikes 
WHERE ? = bikes.belong_to
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 402.9s ago] (1,)
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: SELECT players.id AS players_id, players.username AS players_username, players.password AS players_password, players.user_id AS players_user_id 
FROM players 
WHERE ? = players.user_id
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[1843] _execute_context() INFO: [cached since 402.9s ago] (1,)
2025/04/14 21:48:17 sqlalchemy.engine.Engine base.py[2702] _connection_rollback_impl() INFO: ROLLBACK
2025/04/14 21:48:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Apr/2025 21:48:17] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
