<template>
  <div class="permission-denied-container">
    <div class="permission-denied-content">
      <div class="icon-container">
        <i class="el-icon-warning-outline"></i>
      </div>
      <h2 class="title">{{ title }}</h2>
      <p class="message">{{ message }}</p>
      <div class="actions">
        <el-button type="primary" @click="goBack">返回上一页</el-button>
        <el-button @click="goHome">返回首页</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PermissionDenied',
  props: {
    title: {
      type: String,
      default: '权限不足'
    },
    message: {
      type: String,
      default: '您没有权限访问此页面的数据'
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goHome() {
      this.$router.push('/')
    }
  }
}
</script>

<style scoped>
.permission-denied-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
  padding: 20px;
}

.permission-denied-content {
  text-align: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 40px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.icon-container {
  margin-bottom: 20px;
}

.icon-container i {
  font-size: 64px;
  color: #E6A23C;
}

.title {
  font-size: 24px;
  color: #303133;
  margin-bottom: 16px;
}

.message {
  font-size: 16px;
  color: #606266;
  margin-bottom: 30px;
  line-height: 1.6;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style>
