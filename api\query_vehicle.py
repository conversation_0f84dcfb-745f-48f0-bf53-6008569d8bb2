import sqlite3

def query_vehicle(bike_number):
    conn = sqlite3.connect('sys.db')
    cursor = conn.cursor()
    
    # 查询车辆信息
    cursor.execute('SELECT * FROM bikes WHERE bike_number = ?', (bike_number,))
    vehicle_info = cursor.fetchall()
    print('车辆信息:', vehicle_info)
    
    if vehicle_info:
        vehicle_id = vehicle_info[0][0]  # 假设第一列是ID
        
        # 查询进行中的停车记录
        cursor.execute('SELECT * FROM parking_records WHERE vehicle_id = ? AND status = 0', (vehicle_id,))
        parking_records = cursor.fetchall()
        print('进行中的停车记录:', parking_records)
        
        # 查询进行中的充电记录
        cursor.execute('SELECT * FROM charging_records WHERE vehicle_id = ? AND status = 0', (vehicle_id,))
        charging_records = cursor.fetchall()
        print('进行中的充电记录:', charging_records)
        
        # 查询违规记录
        cursor.execute('SELECT * FROM violation_records WHERE vehicle_id = ?', (vehicle_id,))
        violation_records = cursor.fetchall()
        print('违规记录:', violation_records)
    
    conn.close()

if __name__ == '__main__':
    query_vehicle('京A12345')
