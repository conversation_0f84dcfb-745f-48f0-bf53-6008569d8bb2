/* 停车中心全局样式 */
@import './variables.scss';

// 使用全局变量
$primary-color: $primaryColor;
$success-color: $successColor;
$warning-color: $warningColor;
$danger-color: $dangerColor;
$info-color: $infoColor;

$light-bg: $pageBgColor;
$card-shadow: $boxShadow;
$hover-shadow: $hoverShadow;

// 卡片样式
.parking-card {
  border-radius: 8px;
  box-shadow: $card-shadow;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    box-shadow: $hover-shadow;
    transform: translateY(-5px);
  }

  .card-header {
    padding: 16px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }

    .actions {
      display: flex;
      gap: 8px;
    }
  }

  .card-body {
    padding: 20px;
  }

  .card-footer {
    padding: 12px 16px;
    border-top: 1px solid #ebeef5;
    background-color: #fafafa;
  }
}

// 停车场状态标签
.status-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.operating {
    background-color: rgba($success-color, 0.1);
    color: $success-color;
    border: 1px solid rgba($success-color, 0.2);
  }

  &.paused {
    background-color: rgba($info-color, 0.1);
    color: $info-color;
    border: 1px solid rgba($info-color, 0.2);
  }

  &.full {
    background-color: rgba($danger-color, 0.1);
    color: $danger-color;
    border: 1px solid rgba($danger-color, 0.2);
  }

  &.available {
    background-color: rgba($success-color, 0.1);
    color: $success-color;
    border: 1px solid rgba($success-color, 0.2);
  }

  &.occupied {
    background-color: rgba($warning-color, 0.1);
    color: $warning-color;
    border: 1px solid rgba($warning-color, 0.2);
  }

  &.maintenance {
    background-color: rgba($info-color, 0.1);
    color: $info-color;
    border: 1px solid rgba($info-color, 0.2);
  }

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;

    &.operating { background-color: $success-color; }
    &.paused { background-color: $info-color; }
    &.full { background-color: $danger-color; }
    &.available { background-color: $success-color; }
    &.occupied { background-color: $warning-color; }
    &.maintenance { background-color: $info-color; }
  }
}

// 数据卡片
.data-card {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: $card-shadow;

  .data-title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
  }

  .data-value {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }

  .data-change {
    font-size: 12px;
    margin-top: 4px;

    &.positive {
      color: $success-color;
    }

    &.negative {
      color: $danger-color;
    }
  }

  .data-icon {
    float: right;
    font-size: 36px;
    color: rgba($primary-color, 0.2);
  }
}

// 停车场布局
.parking-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 20px;

  .parking-space {
    position: relative;
    width: 100px;
    height: 150px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .space-number {
      font-weight: 600;
      font-size: 16px;
      text-align: center;
    }

    .space-type {
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 16px;
    }

    .space-status {
      font-size: 12px;
      text-align: center;
      padding: 4px;
      border-radius: 4px;
      margin-top: 8px;
    }

    .vehicle-info {
      background-color: rgba(0, 0, 0, 0.1);
      padding: 6px;
      border-radius: 4px;
      font-size: 12px;
      text-align: center;
      word-break: break-all;
    }

    // 状态样式
    &.available {
      background-color: #f0f9eb;
      border: 2px solid $success-color;
      color: #333;

      .space-status {
        background-color: rgba($success-color, 0.1);
        color: $success-color;
      }
    }

    &.occupied {
      background-color: #fdf6ec;
      border: 2px solid $warning-color;
      color: #333;

      .space-status {
        background-color: rgba($warning-color, 0.1);
        color: $warning-color;
      }
    }

    &.maintenance {
      background-color: #f4f4f5;
      border: 2px solid $info-color;
      color: #333;

      .space-status {
        background-color: rgba($info-color, 0.1);
        color: $info-color;
      }

      &::after {
        content: '维护中';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-30deg);
        font-size: 18px;
        font-weight: bold;
        color: rgba($info-color, 0.5);
        white-space: nowrap;
      }
    }

    // 类型样式
    &.type-1 {
      // 普通车位
    }

    &.type-2::before {
      content: "⚡";
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 14px;
      color: $primary-color;
    }

    &.type-3::before {
      content: "♿";
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 14px;
      color: $primary-color;
    }

    &.type-4::before {
      content: "VIP";
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 10px;
      font-weight: bold;
      color: $primary-color;
    }
  }
}

// 动画效果
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active, .slide-up-leave-active {
  transition: all 0.3s ease;
}
.slide-up-enter, .slide-up-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

// 响应式调整
@media (max-width: 768px) {
  .parking-layout .parking-space {
    width: 80px;
    height: 120px;
    padding: 8px;

    .space-number {
      font-size: 14px;
    }

    .vehicle-info {
      font-size: 10px;
    }
  }

  .data-card .data-value {
    font-size: 20px;
  }
}
