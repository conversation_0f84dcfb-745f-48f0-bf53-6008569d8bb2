2025/03/08 14:41:40 flask_api __init__.py[47] create_app() INFO: Flask Rest Api startup
2025/03/08 14:41:48 flask_api __init__.py[47] create_app() INFO: Flask Rest Api startup
2025/03/08 14:41:54 flask_api __init__.py[47] create_app() INFO: Flask Rest Api startup
2025/03/08 14:43:49 flask_api __init__.py[47] create_app() INFO: Flask Rest Api startup
2025/03/08 15:01:02 flask_api __init__.py[47] create_app() INFO: Flask Rest Api startup
2025/03/08 15:01:11 flask_api __init__.py[47] create_app() INFO: Flask Rest Api startup
2025/03/08 15:06:02 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:06:07 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:06:07 flask_migrate __init__.py[113] wrapped() ERROR: Error: Directory migrations already exists and is not empty
2025/03/08 15:06:15 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:09:15 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:11:00 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:13:46 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:14:08 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:14:53 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:15:00 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:15:10 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:16:56 flask_api __init__.py[46] create_app() INFO: Flask Rest Api startup
2025/03/08 15:17:01 flask_api __init__.py[46] create_app() INFO: Flask Rest Api startup
2025/03/08 15:17:09 flask_api __init__.py[46] create_app() INFO: Flask Rest Api startup
2025/03/08 15:18:23 flask_api __init__.py[46] create_app() INFO: Flask Rest Api startup
2025/03/08 15:19:09 flask_api __init__.py[46] create_app() INFO: Flask Rest Api startup
2025/03/08 15:19:15 flask_api __init__.py[46] create_app() INFO: Flask Rest Api startup
2025/03/08 15:19:20 flask_api __init__.py[46] create_app() INFO: Flask Rest Api startup
2025/03/08 15:19:24 flask_api __init__.py[46] create_app() INFO: Flask Rest Api startup
2025/03/08 15:19:29 flask_api __init__.py[46] create_app() INFO: Flask Rest Api startup
2025/03/08 15:28:08 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:28:13 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:28:20 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:28:24 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:29:41 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:29:41 flask_migrate __init__.py[113] wrapped() ERROR: Error: Directory migrations already exists and is not empty
2025/03/08 15:29:54 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:30:10 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:30:30 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:30:51 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:35:18 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:35:25 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:35:39 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:35:56 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:36:23 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:36:58 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:37:04 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:37:18 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:37:27 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:37:33 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:37:39 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:41:01 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 15:41:01 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 15:41:01 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 15:41:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 15:41:04] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 15:41:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 15:41:04] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 15:41:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 15:41:24] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 15:41:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 15:41:24] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 15:41:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 15:41:50] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 15:41:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 15:41:50] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 15:42:01 root __init__.py[124] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/08 15:42:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 15:42:01] "[33mGET /api/login HTTP/1.1[0m" 404 -
2025/03/08 16:39:28 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 16:39:28 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 16:39:28 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 16:39:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:39:37] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 16:39:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:39:37] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 16:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:39:44] "[36mGET / HTTP/1.1[0m" 304 -
2025/03/08 16:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:39:44] "[36mGET /static/css/app.949a0224.css HTTP/1.1[0m" 304 -
2025/03/08 16:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:39:44] "[36mGET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1[0m" 304 -
2025/03/08 16:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:39:44] "[36mGET /static/css/chunk-libs.3dfb7769.css HTTP/1.1[0m" 304 -
2025/03/08 16:39:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:39:45] "[36mGET /static/js/app.a7289db6.js HTTP/1.1[0m" 304 -
2025/03/08 16:39:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:39:45] "[36mGET /static/js/chunk-libs.4a5831c0.js HTTP/1.1[0m" 304 -
2025/03/08 16:39:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:39:45] "[36mGET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1[0m" 304 -
2025/03/08 16:39:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:39:45] "GET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1" 200 -
2025/03/08 16:39:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:39:45] "GET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1" 200 -
2025/03/08 16:39:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:39:45] "[36mGET /static/fonts/element-icons.535877f5.woff HTTP/1.1[0m" 304 -
2025/03/08 16:40:15 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 16:40:15 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 16:40:15 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 16:41:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:41:06] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 16:41:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:41:07] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 16:41:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:41:28] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 16:41:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:41:28] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 16:41:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:41:29] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 16:41:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:41:29] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 16:41:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:41:34] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 16:41:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:41:34] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 16:43:49 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 16:43:49 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 16:43:49 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 16:43:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:43:51] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 16:43:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:43:51] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 16:43:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:43:54] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 16:45:55 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 16:45:55 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 16:45:55 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 16:45:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:45:58] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 16:45:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:45:59] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 16:46:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:46:00] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 16:46:38 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 16:46:38 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 16:46:38 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 16:46:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:46:42] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 16:46:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 16:46:42] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 18:51:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:51:23] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 18:51:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:51:23] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 18:51:52 flask_api __init__.py[50] create_app() INFO: Flask Rest Api startup
2025/03/08 18:51:52 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 18:51:52 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 18:52:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:14] "[31m[1mPUT /api/user HTTP/1.1[0m" 405 -
2025/03/08 18:52:24 flask_api __init__.py[124] not_found() ERROR: Route not found: /api/user
2025/03/08 18:52:24 root __init__.py[125] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/08 18:52:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:24] "[33mGET /api/user HTTP/1.1[0m" 404 -
2025/03/08 18:52:30 flask_api __init__.py[124] not_found() ERROR: Route not found: /api/user
2025/03/08 18:52:30 root __init__.py[125] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/08 18:52:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:30] "[33mGET /api/user HTTP/1.1[0m" 404 -
2025/03/08 18:52:37 flask_api __init__.py[124] not_found() ERROR: Route not found: /user
2025/03/08 18:52:37 root __init__.py[125] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/08 18:52:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:37] "[33mGET /user HTTP/1.1[0m" 404 -
2025/03/08 18:52:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:42] "[36mGET / HTTP/1.1[0m" 304 -
2025/03/08 18:52:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:42] "[36mGET /static/css/app.949a0224.css HTTP/1.1[0m" 304 -
2025/03/08 18:52:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:42] "[36mGET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1[0m" 304 -
2025/03/08 18:52:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:42] "[36mGET /static/css/chunk-libs.3dfb7769.css HTTP/1.1[0m" 304 -
2025/03/08 18:52:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:42] "[36mGET /static/js/app.a7289db6.js HTTP/1.1[0m" 304 -
2025/03/08 18:52:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:42] "[36mGET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1[0m" 304 -
2025/03/08 18:52:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:42] "[36mGET /static/js/chunk-libs.4a5831c0.js HTTP/1.1[0m" 304 -
2025/03/08 18:52:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:42] "[36mGET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1[0m" 304 -
2025/03/08 18:52:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:42] "[36mGET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1[0m" 304 -
2025/03/08 18:52:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:42] "[36mGET /static/fonts/element-icons.535877f5.woff HTTP/1.1[0m" 304 -
2025/03/08 18:52:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:44] "[36mGET /?imageView2/1/w/80/h/80 HTTP/1.1[0m" 304 -
2025/03/08 18:52:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:44] "GET /static/css/chunk-a8539586.94702ff7.css HTTP/1.1" 200 -
2025/03/08 18:52:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:44] "GET /static/js/chunk-a8539586.06bed4b6.js HTTP/1.1" 200 -
2025/03/08 18:52:57 flask_api __init__.py[124] not_found() ERROR: Route not found: /api/user
2025/03/08 18:52:57 root __init__.py[125] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/08 18:52:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:52:57] "[33mGET /api/user HTTP/1.1[0m" 404 -
2025/03/08 18:53:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:53:17] "GET /api/users HTTP/1.1" 200 -
2025/03/08 18:56:56 flask_api app.py[875] log_exception() ERROR: Exception on /api/users [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 46, in create_user
    new_user = user_schema.load(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_pwd': ['Not a valid string.']}
2025/03/08 18:56:56 root __init__.py[130] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/08 18:56:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:56:56] "[35m[1mPOST /api/users HTTP/1.1[0m" 500 -
2025/03/08 18:57:27 flask_api app.py[875] log_exception() ERROR: Exception on /api/users [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 46, in create_user
    new_user = user_schema.load(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_pwd': ['Not a valid string.']}
2025/03/08 18:57:27 root __init__.py[130] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/08 18:57:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:57:27] "[35m[1mPOST /api/users HTTP/1.1[0m" 500 -
2025/03/08 18:57:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:57:30] "[31m[1mPOST /users HTTP/1.1[0m" 405 -
2025/03/08 18:57:35 flask_api app.py[875] log_exception() ERROR: Exception on /api/users [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 46, in create_user
    new_user = user_schema.load(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_pwd': ['Not a valid string.']}
2025/03/08 18:57:35 root __init__.py[130] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/08 18:57:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:57:35] "[35m[1mPOST /api/users HTTP/1.1[0m" 500 -
2025/03/08 18:58:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:58:05] "[36mGET / HTTP/1.1[0m" 304 -
2025/03/08 18:58:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:58:06] "[36mGET /static/css/app.949a0224.css HTTP/1.1[0m" 304 -
2025/03/08 18:58:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:58:06] "[36mGET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1[0m" 304 -
2025/03/08 18:58:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:58:06] "[36mGET /static/css/chunk-libs.3dfb7769.css HTTP/1.1[0m" 304 -
2025/03/08 18:58:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:58:06] "[36mGET /static/js/app.a7289db6.js HTTP/1.1[0m" 304 -
2025/03/08 18:58:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:58:06] "[36mGET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1[0m" 304 -
2025/03/08 18:58:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:58:06] "[36mGET /static/js/chunk-libs.4a5831c0.js HTTP/1.1[0m" 304 -
2025/03/08 18:58:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:58:06] "[36mGET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1[0m" 304 -
2025/03/08 18:58:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:58:06] "[36mGET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1[0m" 304 -
2025/03/08 18:58:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:58:06] "[36mGET /static/fonts/element-icons.535877f5.woff HTTP/1.1[0m" 304 -
2025/03/08 18:58:11 flask_api app.py[875] log_exception() ERROR: Exception on /api/users [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 46, in create_user
    new_user = user_schema.load(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_pwd': ['Not a valid string.']}
2025/03/08 18:58:11 root __init__.py[130] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/08 18:58:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:58:11] "[35m[1mPOST /api/users HTTP/1.1[0m" 500 -
2025/03/08 18:58:12 flask_api app.py[875] log_exception() ERROR: Exception on /api/users [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 46, in create_user
    new_user = user_schema.load(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_pwd': ['Not a valid string.']}
2025/03/08 18:58:12 root __init__.py[130] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/08 18:58:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:58:12] "[35m[1mPOST /api/users HTTP/1.1[0m" 500 -
2025/03/08 18:59:09 flask_api app.py[875] log_exception() ERROR: Exception on /api/users [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 46, in create_user
    new_user = user_schema.load(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_pwd': ['Not a valid string.']}
2025/03/08 18:59:09 root __init__.py[130] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/08 18:59:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 18:59:09] "[35m[1mPOST /api/users HTTP/1.1[0m" 500 -
2025/03/08 19:01:53 flask_api app.py[875] log_exception() ERROR: Exception on /api/users [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 46, in create_user
    app.logger.info(f"Received user creation request with data: {data}")
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_pwd': ['Missing data for required field.'], 'u_role': ['Missing data for required field.']}
2025/03/08 19:01:53 root __init__.py[130] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/08 19:01:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:01:53] "[35m[1mPOST /api/users HTTP/1.1[0m" 500 -
2025/03/08 19:04:58 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 19:05:27 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 19:05:27 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 19:05:27 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 19:05:35 flask_api app.py[875] log_exception() ERROR: Exception on /api/users [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 46, in create_user
    if not data:
           ^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_pwd': ['Not a valid string.']}
2025/03/08 19:05:35 root __init__.py[130] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/08 19:05:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:05:35] "[35m[1mPOST /api/users HTTP/1.1[0m" 500 -
2025/03/08 19:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:05:44] "[36mGET / HTTP/1.1[0m" 304 -
2025/03/08 19:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:05:44] "[36mGET /static/css/app.949a0224.css HTTP/1.1[0m" 304 -
2025/03/08 19:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:05:44] "[36mGET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1[0m" 304 -
2025/03/08 19:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:05:44] "[36mGET /static/css/chunk-libs.3dfb7769.css HTTP/1.1[0m" 304 -
2025/03/08 19:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:05:44] "[36mGET /static/js/app.a7289db6.js HTTP/1.1[0m" 304 -
2025/03/08 19:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:05:44] "[36mGET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1[0m" 304 -
2025/03/08 19:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:05:44] "[36mGET /static/js/chunk-libs.4a5831c0.js HTTP/1.1[0m" 304 -
2025/03/08 19:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:05:44] "[36mGET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1[0m" 304 -
2025/03/08 19:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:05:44] "[36mGET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1[0m" 304 -
2025/03/08 19:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:05:44] "[36mGET /static/fonts/element-icons.535877f5.woff HTTP/1.1[0m" 304 -
2025/03/08 19:06:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:06:01] "GET /api/users HTTP/1.1" 200 -
2025/03/08 19:06:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:06:04] "GET /api/users/1 HTTP/1.1" 200 -
2025/03/08 19:06:16 flask_api app.py[875] log_exception() ERROR: Exception on /api/users [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\try\try\vue-admin-template\api\app\users\routes.py", line 46, in create_user
    if not data:
           ^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow_sqlalchemy\load_instance_mixin.py", line 147, in load
    return cast(ma.Schema, super()).load(_cast_data(data), **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 792, in load
    return self._do_load(
           ~~~~~~~~~~~~~^
        data, many=many, partial=partial, unknown=unknown, postprocess=True
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\marshmallow\schema.py", line 999, in _do_load
    raise exc
marshmallow.exceptions.ValidationError: {'u_pwd': ['Not a valid string.']}
2025/03/08 19:06:16 root __init__.py[130] server_error() ERROR: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025/03/08 19:06:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:06:16] "[35m[1mPOST /api/users HTTP/1.1[0m" 500 -
2025/03/08 19:06:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:06:24] "[35m[1mPOST /api/users HTTP/1.1[0m" 201 -
2025/03/08 19:06:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:06:44] "GET /api/users/1 HTTP/1.1" 200 -
2025/03/08 19:07:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:07:08] "[31m[1mPOST /api/users/1 HTTP/1.1[0m" 405 -
2025/03/08 19:07:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:07:18] "PUT /api/users/1 HTTP/1.1" 200 -
2025/03/08 19:07:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:07:23] "GET /api/users/1 HTTP/1.1" 200 -
2025/03/08 19:07:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:07:41] "[35m[1mDELETE /api/users/1 HTTP/1.1[0m" 204 -
2025/03/08 19:07:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:07:44] "[31m[1mDELETE /api/users HTTP/1.1[0m" 405 -
2025/03/08 19:07:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:07:49] "GET /api/users HTTP/1.1" 200 -
2025/03/08 19:08:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:08:11] "[31m[1mPOST /api/register HTTP/1.1[0m" 415 -
2025/03/08 19:08:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:08:45] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/03/08 19:08:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:08:58] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/08 19:09:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:09:45] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/08 19:09:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:09:49] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/08 19:09:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:09:56] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/08 19:10:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:10:17] "GET /api/users HTTP/1.1" 200 -
2025/03/08 19:12:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:12:11] "GET /api/users HTTP/1.1" 200 -
2025/03/08 19:12:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:12:21] "[31m[1mGET /api/bikes HTTP/1.1[0m" 401 -
2025/03/08 19:18:43 flask_api __init__.py[124] not_found() ERROR: Route not found: /api/login
2025/03/08 19:18:43 root __init__.py[125] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/08 19:18:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:18:43] "[33mGET /api/login HTTP/1.1[0m" 404 -
2025/03/08 19:18:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:18:48] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/08 19:19:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:19:06] "[31m[1mGET /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:19:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:19:18] "GET /api/bikes HTTP/1.1" 200 -
2025/03/08 19:19:36 flask_api __init__.py[124] not_found() ERROR: Route not found: /api/bikes/1
2025/03/08 19:19:36 root __init__.py[125] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/08 19:19:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:19:36] "[33mGET /api/bikes/1 HTTP/1.1[0m" 404 -
2025/03/08 19:19:58 flask_api __init__.py[124] not_found() ERROR: Route not found: /api/bikes/1
2025/03/08 19:19:58 root __init__.py[125] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/08 19:19:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:19:58] "[33mGET /api/bikes/1 HTTP/1.1[0m" 404 -
2025/03/08 19:21:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:21:00] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:21:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:21:19] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/03/08 19:29:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:29:09] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:29:29 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 19:29:29 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 19:29:29 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 19:29:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:29:33] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:31:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:31:38] "GET /api/bikes HTTP/1.1" 200 -
2025/03/08 19:31:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:31:49] "GET /api/bikes/1 HTTP/1.1" 200 -
2025/03/08 19:32:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:32:30] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:32:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:32:43] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:33:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:33:03] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:35:20 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 19:35:20 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 19:35:20 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 19:35:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:35:28] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:35:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:35:31] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:36:34 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 19:36:34 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 19:36:34 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 19:36:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:36:37] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/03/08 19:36:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:36:45] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:45:43 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 19:45:43 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 19:45:43 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 19:45:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:45:47] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:46:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:46:39] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:46:46 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 19:46:46 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 19:46:46 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 19:46:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:46:50] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/08 19:48:36 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 19:48:36 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 19:48:36 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 19:48:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:48:40] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/03/08 19:54:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:54:02] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 19:54:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:54:02] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 19:54:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:54:29] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 19:54:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 19:54:29] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 20:00:34 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 20:00:34 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 20:00:34 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 20:00:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:00:38] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 20:00:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:00:38] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 20:02:12 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 20:02:12 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 20:02:12 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 20:02:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:02:20] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 20:02:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:02:20] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 20:04:11 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 20:04:11 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 20:04:11 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 20:04:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:04:25] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 20:04:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:04:25] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 20:06:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:06:02] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/03/08 20:06:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:06:02] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/08 20:07:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:07:15] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 20:07:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:07:15] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 20:09:57 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 20:10:48 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 20:11:29 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 20:11:29 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 20:11:29 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 20:11:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:11:31] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 20:11:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:11:31] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/08 20:11:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:11:40] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 20:11:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:11:46] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 20:12:48 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 20:12:48 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 20:12:48 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 20:12:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:12:51] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 20:12:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:12:58] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 20:13:01 flask_api __init__.py[131] not_found() ERROR: 路由未找到: /api/login, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua: "Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"

Sec-Ch-Ua-Mobile: ?0

Sec-Ch-Ua-Platform: "Windows"

Upgrade-Insecure-Requests: 1

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7

Sec-Fetch-Site: none

Sec-Fetch-Mode: navigate

Sec-Fetch-User: ?1

Sec-Fetch-Dest: document

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6

Cookie: vue_admin_template_token=admin-token; sidebarStatus=1




2025/03/08 20:13:01 root __init__.py[132] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/03/08 20:13:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:13:01] "[33mGET /api/login HTTP/1.1[0m" 404 -
2025/03/08 20:16:27 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 20:16:27 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 20:16:27 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 20:16:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:30] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 20:16:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:35] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 20:16:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:41] "[32mGET /api/login HTTP/1.1[0m" 302 -
2025/03/08 20:16:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:42] "[36mGET / HTTP/1.1[0m" 304 -
2025/03/08 20:16:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:42] "[36mGET /static/css/app.949a0224.css HTTP/1.1[0m" 304 -
2025/03/08 20:16:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:42] "[36mGET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1[0m" 304 -
2025/03/08 20:16:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:42] "[36mGET /static/css/chunk-libs.3dfb7769.css HTTP/1.1[0m" 304 -
2025/03/08 20:16:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:42] "[36mGET /static/js/app.a7289db6.js HTTP/1.1[0m" 304 -
2025/03/08 20:16:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:42] "[36mGET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1[0m" 304 -
2025/03/08 20:16:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:42] "[36mGET /static/js/chunk-libs.4a5831c0.js HTTP/1.1[0m" 304 -
2025/03/08 20:16:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:42] "[36mGET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1[0m" 304 -
2025/03/08 20:16:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:42] "[36mGET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1[0m" 304 -
2025/03/08 20:16:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:16:42] "[36mGET /static/fonts/element-icons.535877f5.woff HTTP/1.1[0m" 304 -
2025/03/08 20:18:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:18:20] "[36mGET /?imageView2/1/w/80/h/80 HTTP/1.1[0m" 304 -
2025/03/08 20:18:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:18:20] "[36mGET /static/css/chunk-a8539586.94702ff7.css HTTP/1.1[0m" 304 -
2025/03/08 20:18:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:18:20] "[36mGET /static/js/chunk-a8539586.06bed4b6.js HTTP/1.1[0m" 304 -
2025/03/08 20:18:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:18:26] "GET /static/js/chunk-2d0e4b0c.dd2c0753.js HTTP/1.1" 200 -
2025/03/08 20:18:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:18:54] "GET /static/css/chunk-31dba1a5.c58e968f.css HTTP/1.1" 200 -
2025/03/08 20:18:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 20:18:54] "GET /static/js/chunk-31dba1a5.b0b4da8e.js HTTP/1.1" 200 -
2025/03/08 21:02:00 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 21:02:00 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 21:02:00 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 21:02:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:02:50] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 21:08:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:08:56] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 21:09:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:09:06] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 21:11:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:38] "[36mGET / HTTP/1.1[0m" 304 -
2025/03/08 21:11:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:38] "[36mGET /static/css/app.949a0224.css HTTP/1.1[0m" 304 -
2025/03/08 21:11:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:38] "[36mGET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1[0m" 304 -
2025/03/08 21:11:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:38] "[36mGET /static/css/chunk-libs.3dfb7769.css HTTP/1.1[0m" 304 -
2025/03/08 21:11:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:38] "[36mGET /static/js/app.a7289db6.js HTTP/1.1[0m" 304 -
2025/03/08 21:11:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:38] "[36mGET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1[0m" 304 -
2025/03/08 21:11:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:38] "[36mGET /static/js/chunk-libs.4a5831c0.js HTTP/1.1[0m" 304 -
2025/03/08 21:11:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:38] "[36mGET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1[0m" 304 -
2025/03/08 21:11:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:38] "[36mGET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1[0m" 304 -
2025/03/08 21:11:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:38] "[36mGET /static/fonts/element-icons.535877f5.woff HTTP/1.1[0m" 304 -
2025/03/08 21:11:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:40] "[36mGET /?imageView2/1/w/80/h/80 HTTP/1.1[0m" 304 -
2025/03/08 21:11:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:40] "[36mGET /static/css/chunk-a8539586.94702ff7.css HTTP/1.1[0m" 304 -
2025/03/08 21:11:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:11:40] "[36mGET /static/js/chunk-a8539586.06bed4b6.js HTTP/1.1[0m" 304 -
2025/03/08 21:12:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:12:00] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 21:16:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:04] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 21:16:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:15] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 21:16:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:21] "[32mGET /api/login HTTP/1.1[0m" 302 -
2025/03/08 21:16:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:21] "[36mGET / HTTP/1.1[0m" 304 -
2025/03/08 21:16:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:21] "[36mGET /static/css/app.949a0224.css HTTP/1.1[0m" 304 -
2025/03/08 21:16:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:21] "[36mGET /static/css/chunk-elementUI.68c70ad5.css HTTP/1.1[0m" 304 -
2025/03/08 21:16:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:21] "[36mGET /static/css/chunk-libs.3dfb7769.css HTTP/1.1[0m" 304 -
2025/03/08 21:16:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:21] "[36mGET /static/js/app.a7289db6.js HTTP/1.1[0m" 304 -
2025/03/08 21:16:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:21] "[36mGET /static/js/chunk-elementUI.6f38d267.js HTTP/1.1[0m" 304 -
2025/03/08 21:16:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:21] "[36mGET /static/js/chunk-libs.4a5831c0.js HTTP/1.1[0m" 304 -
2025/03/08 21:16:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:21] "[36mGET /static/css/chunk-630a64ed.9a9361c6.css HTTP/1.1[0m" 304 -
2025/03/08 21:16:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:21] "[36mGET /static/js/chunk-630a64ed.0bed9e47.js HTTP/1.1[0m" 304 -
2025/03/08 21:16:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:21] "[36mGET /static/fonts/element-icons.535877f5.woff HTTP/1.1[0m" 304 -
2025/03/08 21:16:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:41] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 21:16:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:16:49] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 21:18:56 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/08 21:18:56 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/08 21:18:56 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/08 21:19:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:19:01] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 21:19:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:19:01] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/08 21:22:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:22:19] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/08 21:22:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [08/Mar/2025 21:22:19] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
