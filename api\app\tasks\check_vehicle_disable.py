"""
车辆禁用管理工具

提供禁用车辆、解除禁用等功能，并记录禁用历史。
使用统一的状态定义，确保前后端一致性。
"""

from app.utils.constants import VehicleStatus
from datetime import datetime
from app import db
from app.violations.models import VehicleDisableRecord, ViolationRecord
from app.bikes.models import Bikes
from flask import current_app
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def disable_vehicle(bike_id, violation_id=None, reason=None, operator_id=None):
    """
    禁用车辆

    Args:
        bike_id: 车辆ID
        violation_id: 关联的违规记录ID（可选）
        reason: 禁用原因（可选，当没有关联违规记录时使用）
        operator_id: 操作人ID（可选）

    Returns:
        tuple: (是否成功, 消息, 禁用记录ID)
    """
    try:
        # 查找车辆
        bike = Bikes.query.get(bike_id)
        if not bike:
            message = f"未找到车辆 ID: {bike_id}，无法禁用"
            logger.error(message)
            return False, message, None

        # 检查车辆当前状态
        if bike.status == VehicleStatus.DISABLED:
            # 检查是否已有禁用记录
            existing_record = VehicleDisableRecord.query.filter_by(
                bike_id=bike_id,
                is_active=True
            ).first()

            if existing_record:
                message = f"车辆 {bike.b_num} (ID: {bike.b_id}) 已经处于禁用状态"
                logger.warning(message)
                return False, message, existing_record.id

        # 更新车辆状态为禁用状态
        bike.status = VehicleStatus.DISABLED

        # 验证违规记录是否存在
        if violation_id:
            violation = ViolationRecord.query.get(violation_id)
            if not violation:
                message = f"未找到违规记录 ID: {violation_id}，将创建无关联违规记录的禁用记录"
                logger.warning(message)
                # 继续创建禁用记录，但不关联违规记录
                violation_id = None

        # 创建禁用记录
        disable_record = VehicleDisableRecord(
            bike_id=bike_id,
            violation_id=violation_id if violation_id else None,
            disable_start_time=datetime.now(),
            is_active=True,
            reason=reason,
            operator_id=operator_id
        )
        db.session.add(disable_record)

        # 提交更改
        db.session.commit()
        message = f"已禁用车辆 {bike.b_num} (ID: {bike.b_id})"
        logger.info(message)
        return True, message, disable_record.id

    except Exception as e:
        message = f"禁用车辆失败: {str(e)}"
        logger.error(message)
        db.session.rollback()
        return False, message, None

def enable_vehicle(bike_id, operator_id=None, reason=None):
    """
    解除车辆禁用

    Args:
        bike_id: 车辆ID
        operator_id: 操作人ID（可选）
        reason: 解除禁用原因（可选）

    Returns:
        tuple: (是否成功, 消息, 禁用记录ID列表)
    """
    try:
        # 查找车辆
        bike = Bikes.query.get(bike_id)
        if not bike:
            message = f"未找到车辆 ID: {bike_id}，无法解除禁用"
            logger.error(message)
            return False, message, []

        # 检查车辆当前状态
        if bike.status != VehicleStatus.DISABLED:
            message = f"车辆 {bike.b_num} (ID: {bike.b_id}) 当前状态不是禁用状态，无需解除"
            logger.warning(message)
            return False, message, []

        # 更新车辆状态为可用状态
        bike.status = VehicleStatus.AVAILABLE

        # 更新禁用记录
        disable_records = VehicleDisableRecord.query.filter_by(
            bike_id=bike_id,
            is_active=True
        ).all()

        if not disable_records:
            message = f"车辆 {bike.b_num} (ID: {bike.b_id}) 没有有效的禁用记录，但状态为禁用，已更正为可用"
            logger.warning(message)
            db.session.commit()
            return True, message, []

        record_ids = []
        for record in disable_records:
            record.is_active = False
            record.enable_reason = reason
            record.enable_operator_id = operator_id
            record_ids.append(record.id)

        # 提交更改
        db.session.commit()
        message = f"已解除车辆 {bike.b_num} (ID: {bike.b_id}) 的禁用状态"
        logger.info(message)
        return True, message, record_ids

    except Exception as e:
        message = f"解除车辆禁用失败: {str(e)}"
        logger.error(message)
        db.session.rollback()
        return False, message, []

def get_vehicle_disable_records(bike_id=None, active_only=False, limit=100):
    """
    获取车辆禁用记录

    Args:
        bike_id: 车辆ID（可选，如果不提供则获取所有记录）
        active_only: 是否只获取有效的禁用记录
        limit: 最大记录数

    Returns:
        list: 禁用记录列表
    """
    try:
        query = VehicleDisableRecord.query

        if bike_id:
            query = query.filter_by(bike_id=bike_id)

        if active_only:
            query = query.filter_by(is_active=True)

        # 按时间倒序排序，最新的记录在前面
        query = query.order_by(VehicleDisableRecord.disable_start_time.desc())

        # 限制记录数
        if limit:
            query = query.limit(limit)

        records = query.all()
        return records

    except Exception as e:
        logger.error(f"获取车辆禁用记录失败: {str(e)}")
        return []

def check_vehicle_disable_status(bike_id):
    """
    检查车辆禁用状态

    Args:
        bike_id: 车辆ID

    Returns:
        tuple: (是否禁用, 禁用原因, 禁用记录)
    """
    try:
        # 查找车辆
        bike = Bikes.query.get(bike_id)
        if not bike:
            return False, "车辆不存在", None

        # 检查车辆状态
        if bike.status != VehicleStatus.DISABLED:
            return False, None, None

        # 查找有效的禁用记录
        disable_record = VehicleDisableRecord.query.filter_by(
            bike_id=bike_id,
            is_active=True
        ).first()

        if not disable_record:
            # 状态为废弃但没有禁用记录，可能是数据不一致
            logger.warning(f"车辆 {bike.b_num} (ID: {bike.b_id}) 状态为废弃，但没有有效的禁用记录")
            return True, "车辆已禁用，但无详细原因", None

        # 获取关联的违规记录
        if disable_record.violation_id:
            violation = ViolationRecord.query.get(disable_record.violation_id)
            if violation:
                reason = f"违规原因: {violation.violation_type}, 地点: {violation.location}, 时间: {violation.violation_time}"
                return True, reason, disable_record

        # 使用记录中的原因
        if disable_record.reason:
            return True, disable_record.reason, disable_record

        return True, "车辆已禁用，但无法获取详细原因", disable_record

    except Exception as e:
        logger.error(f"检查车辆禁用状态失败: {str(e)}")
        return False, f"检查禁用状态出错: {str(e)}", None

if __name__ == "__main__":
    # 示例：解除车辆禁用
    # success, message, record_ids = enable_vehicle(1)
    # print(f"解除禁用结果: {success}, 消息: {message}, 记录ID: {record_ids}")

    # 示例：禁用车辆
    # success, message, record_id = disable_vehicle(1, 2)
    # print(f"禁用结果: {success}, 消息: {message}, 记录ID: {record_id}")

    # 示例：检查车辆禁用状态
    # is_disabled, reason, record = check_vehicle_disable_status(1)
    # print(f"车辆是否禁用: {is_disabled}, 原因: {reason}")

    print("请在代码中取消注释相应的函数调用来执行操作")
