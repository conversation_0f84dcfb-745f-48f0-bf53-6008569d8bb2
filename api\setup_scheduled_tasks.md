# 设置定期数据一致性检查任务

本文档提供了如何设置定期数据一致性检查任务的指南，以确保停车场系统数据的准确性和一致性。

## Windows 计划任务设置

### 1. 创建批处理文件

首先，创建一个批处理文件 `run_scheduled_tasks.bat`，内容如下：

```batch
@echo off
cd /d D:\try\try\vue-admin-template\api
python scheduled_tasks.py >> scheduled_tasks_output.log 2>&1
```

将此文件保存在 `D:\try\try\vue-admin-template\api` 目录下。

### 2. 使用任务计划程序创建定期任务

1. 打开 Windows 任务计划程序（按 Win+R，输入 `taskschd.msc`，然后按 Enter）
2. 在右侧面板中，点击"创建基本任务"
3. 输入任务名称（如"停车场数据一致性检查"）和描述
4. 选择触发器：
   - 对于日常检查，选择"每天"，并设置开始时间（如凌晨 2:00）
   - 对于更频繁的检查，选择"每天"，然后在高级设置中设置重复间隔（如每 4 小时）
5. 选择操作："启动程序"
6. 程序/脚本：浏览并选择之前创建的 `run_scheduled_tasks.bat` 文件
7. 完成向导

### 3. 高级设置（可选）

1. 创建任务后，右键点击任务并选择"属性"
2. 在"常规"选项卡中，选择"使用最高权限运行"
3. 在"条件"选项卡中，根据需要调整设置（如"只有在计算机使用交流电源时才启动此任务"）
4. 在"设置"选项卡中，选择"如果任务运行时间超过以下时间，则停止任务"并设置为合理的时间（如 30 分钟）

## Linux Cron 任务设置（如果使用 Linux 服务器）

如果您的系统运行在 Linux 服务器上，可以使用 cron 任务来设置定期检查：

1. 打开终端并编辑 crontab：
   ```bash
   crontab -e
   ```

2. 添加以下行来设置每天凌晨 2 点运行检查：
   ```
   0 2 * * * cd /path/to/api && python scheduled_tasks.py >> scheduled_tasks_output.log 2>&1
   ```

3. 保存并退出编辑器

## 检查日志

任务运行后，可以查看以下日志文件来监控执行情况：

- `scheduled_tasks.log`：包含详细的执行日志
- `scheduled_tasks_output.log`：包含批处理文件的输出

## 手动运行检查

如果需要手动运行数据一致性检查，可以：

1. 打开命令提示符或 PowerShell
2. 导航到 API 目录：
   ```
   cd D:\try\try\vue-admin-template\api
   ```
3. 运行脚本：
   ```
   python scheduled_tasks.py
   ```

## 故障排除

如果计划任务未按预期运行，请检查：

1. 批处理文件路径是否正确
2. Python 是否在系统 PATH 中
3. 任务计划程序日志中是否有错误
4. `scheduled_tasks.log` 文件中是否有错误信息

## 注意事项

- 建议在系统负载较低的时间运行此任务（如凌晨）
- 确保运行任务的用户具有足够的权限访问数据库和文件
- 定期检查日志文件，确保任务正常运行
