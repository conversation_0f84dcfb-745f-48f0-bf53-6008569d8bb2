import request from '@/utils/request'

// 获取停车记录统计数据
export function getParkingStats(params) {
  return request({
    url: '/api/parking-records/stats',
    method: 'get',
    params
  })
}

// 获取停车场使用率统计
export function getParkingLotUtilization(params) {
  return request({
    url: '/api/parking-lots/utilization',
    method: 'get',
    params
  })
}

// 获取按日期统计的停车记录数量
export function getDailyParkingStats(params) {
  return request({
    url: '/api/parking-records/daily-stats',
    method: 'get',
    params
  })
}

// 获取按车辆类型统计的停车记录
export function getVehicleTypeStats(params) {
  return request({
    url: '/api/parking-records/vehicle-type-stats',
    method: 'get',
    params
  })
}

// 获取用户停车记录统计
export function getUserParkingStats(userId, params) {
  return request({
    url: `/api/parking-records/user-stats${userId ? `/${userId}` : ''}`,
    method: 'get',
    params
  })
}

// 导出停车记录报表
export function exportParkingRecords(params) {
  return request({
    url: '/api/parking-records/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出停车场使用率报表
export function exportParkingLotUtilization(params) {
  return request({
    url: '/api/parking-lots/export-utilization',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取仪表盘概览数据
export function getDashboardOverview() {
  return request({
    url: '/api/dashboard/overview',
    method: 'get'
  })
}
