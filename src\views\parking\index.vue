<template>
  <div class="app-container">
    <permission-wrapper :allowed-roles="['admin']" message="您没有权限访问停车场管理数据，请联系管理员获取权限。">
    <div class="dashboard-header">
      <div class="dashboard-title">
        <h2>停车场管理中心</h2>
        <p class="subtitle">管理和监控所有停车场的实时状态</p>
      </div>
      <div class="dashboard-actions">
        <el-input
          v-model="listQuery.search"
          placeholder="搜索停车场"
          prefix-icon="el-icon-search"
          clearable
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.status"
          placeholder="状态"
          clearable
          style="width: 120px; margin-left: 10px;"
          class="filter-item"
          @change="handleFilter"
        >
          <el-option label="正常运营" :value="1" />
          <el-option label="暂停使用" :value="0" />
        </el-select>
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          v-if="checkPermission(['admin'])"
          type="success"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          添加停车场
        </el-button>
        <el-button
          type="warning"
          icon="el-icon-refresh"
          @click="fetchData"
        >
          刷新
        </el-button>
        <el-button
          v-if="checkPermission(['admin'])"
          type="info"
          icon="el-icon-check"
          @click="checkConsistency"
        >
          数据一致性检查
        </el-button>
      </div>
    </div>

    <!-- 停车场统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <div class="data-card">
          <i class="el-icon-office-building data-icon"></i>
          <div class="data-title">停车场总数</div>
          <div class="data-value">{{ totalLots }}</div>
          <div class="data-desc">当前系统中的停车场数量</div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <div class="data-card">
          <i class="el-icon-s-grid data-icon"></i>
          <div class="data-title">车位总数</div>
          <div class="data-value">{{ totalSpaces }}</div>
          <div class="data-desc">所有停车场的车位总数</div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <div class="data-card">
          <i class="el-icon-s-data data-icon"></i>
          <div class="data-title">已占用车位</div>
          <div class="data-value">{{ totalOccupied }}</div>
          <div class="data-desc">当前已被占用的车位数量</div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <div class="data-card">
          <i class="el-icon-data-analysis data-icon"></i>
          <div class="data-title">平均利用率</div>
          <div class="data-value">{{ avgUtilization }}%</div>
          <div class="data-desc">所有停车场的平均利用率</div>
        </div>
      </el-col>
    </el-row>

    <!-- 已移除视图切换按钮，只保留卡片视图 -->

    <!-- 停车场卡片视图 -->
    <div class="parking-cards-container">
      <el-row :gutter="20" v-loading="listLoading" element-loading-text="加载中...">
        <el-col v-for="item in list" :key="item.id" :xs="24" :sm="12" :md="8" :lg="6" class="card-col">
          <div class="parking-card" @click="handleViewDetails(item)">
            <div class="card-header">
              <h3 class="title">{{ item.name }}</h3>
              <div class="status-tag" :class="item.status === 1 ? 'operating' : 'paused'">
                <span class="dot" :class="item.status === 1 ? 'operating' : 'paused'"></span>
                {{ item.status === 1 ? '正常运营' : '暂停使用' }}
              </div>
            </div>
            <div class="card-body">
              <p class="address"><i class="el-icon-location"></i> {{ item.address }}</p>

              <div class="utilization-info">
                <div class="utilization-header">
                  <span>车位利用率</span>
                  <span class="utilization-value" :class="getUtilizationClass(item)">
                    {{ calculateUtilization(item) }}%
                  </span>
                </div>
                <el-progress
                  :percentage="calculateUtilization(item)"
                  :status="getUtilizationStatus(item)"
                  :stroke-width="10"
                  :show-text="false"
                />
                <div class="space-count">
                  <span>已用: <b>{{ item.occupied_spaces }}</b></span>
                  <span>总数: <b>{{ item.total_spaces }}</b></span>
                  <span>空闲: <b>{{ item.total_spaces - item.occupied_spaces }}</b></span>
                </div>
              </div>
            </div>
            <div class="card-footer">
              <el-button size="mini" type="primary" icon="el-icon-view" @click.stop="handleViewDetails(item)">查看</el-button>
              <el-button v-if="checkPermission(['admin'])" size="mini" type="success" icon="el-icon-edit" @click.stop="handleUpdate(item)">编辑</el-button>
              <el-button
                v-if="checkPermission(['admin'])"
                size="mini"
                type="danger"
                icon="el-icon-delete"
                :disabled="item.occupied_spaces > 0"
                @click.stop="handleDelete(item)"
              >删除</el-button>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 空数据提示 -->
      <el-empty v-if="list.length === 0 && !listLoading" description="没有找到停车场数据"></el-empty>
    </div>

    <!-- 已移除表格视图，只保留卡片视图 -->

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="fetchData"
    />

    <!-- 添加/编辑停车场对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '添加停车场' : '编辑停车场'" :visible.sync="dialogFormVisible" width="650px">
      <el-form ref="dataForm" :model="temp" :rules="rules" label-position="left" label-width="100px" style="width: 550px; margin-left:30px;">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="名称" prop="name">
              <el-input v-model="temp.name" placeholder="请输入停车场名称" />
            </el-form-item>
            <el-form-item label="地址" prop="address">
              <el-input v-model="temp.address" placeholder="请输入地址" />
            </el-form-item>
            <el-form-item label="车位数量" prop="total_spaces">
              <el-input-number v-model="temp.total_spaces" :min="1" :max="1000" />
            </el-form-item>
            <el-form-item label="状态">
              <el-radio-group v-model="temp.status">
                <el-radio :label="1">正常运营</el-radio>
                <el-radio :label="0">暂停使用</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="开放时间">
              <el-input v-model="temp.opening_hours" placeholder="例如：24小时" />
            </el-form-item>
            <el-form-item label="描述">
              <el-input v-model="temp.description" type="textarea" :rows="2" placeholder="请输入描述信息" />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="区域信息" name="location">
            <el-form-item label="校区" prop="campus">
              <el-select v-model="temp.campus" placeholder="请选择校区" style="width: 100%">
                <el-option
                  v-for="campus in campusOptions"
                  :key="campus.value"
                  :label="campus.label"
                  :value="campus.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="区域" prop="area">
              <el-select v-model="temp.area" placeholder="请选择区域" style="width: 100%">
                <el-option
                  v-for="area in areaOptions"
                  :key="area.value"
                  :label="area.label"
                  :value="area.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="经度" prop="longitude">
              <el-input v-model="temp.longitude" placeholder="选填" />
            </el-form-item>
            <el-form-item label="纬度" prop="latitude">
              <el-input v-model="temp.latitude" placeholder="选填" />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="联系信息" name="contact">
            <el-form-item label="管理员" prop="manager">
              <el-input v-model="temp.manager" placeholder="请输入管理员姓名" />
            </el-form-item>
            <el-form-item label="联系电话" prop="contact">
              <el-input v-model="temp.contact" placeholder="请输入联系电话" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">确认</el-button>
      </div>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="确认删除"
      :visible.sync="dialogDeleteVisible"
      width="30%"
    >
      <span>确定要删除停车场 "{{ temp.name }}" 吗？此操作不可恢复！</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDeleteVisible = false">取消</el-button>
        <el-button type="primary" @click="deleteData">确定</el-button>
      </span>
    </el-dialog>
    </permission-wrapper>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import request from '@/utils/request'
import { checkParkingLotsConsistency } from '@/api/parkinglot'
import PermissionWrapper from '@/components/PermissionWrapper'
import { CampusTypes, AreaTypes } from '@/utils/constants'

export default {
  name: 'Parking',
  components: { Pagination, PermissionWrapper },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      // 已移除视图切换功能，只保留卡片视图
      listQuery: {
        page: 1,
        limit: 10,
        search: '',
        status: '',
        sort_field: 'id',
        sort_order: 'asc'
      },
      totalLots: 0,
      totalSpaces: 0,
      totalOccupied: 0,
      avgUtilization: 0,
      dialogFormVisible: false,
      dialogDeleteVisible: false,
      dialogStatus: '',
      activeTab: 'basic',
      temp: {
        id: undefined,
        name: '',
        address: '',
        total_spaces: 10,
        status: 1,
        description: '',
        longitude: null,
        latitude: null,
        opening_hours: '24小时',
        campus: '',
        area: '',
        manager: '',
        contact: ''
      },
      rules: {
        name: [{ required: true, message: '停车场名称不能为空', trigger: 'blur' }],
        address: [{ required: true, message: '地址不能为空', trigger: 'blur' }],
        total_spaces: [{ required: true, message: '车位数量不能为空', trigger: 'blur' }]
      },
      // 校区和区域选项（使用常量定义）
      campusOptions: CampusTypes.OPTIONS,
      areaOptions: AreaTypes.OPTIONS
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'userId'
    ])
  },
  created() {
    this.fetchData()
  },
  methods: {
    checkPermission(permissionRoles) {
      if (this.roles.includes('admin')) {
        return true
      }
      if (!permissionRoles) return false
      return permissionRoles.some(role => this.roles.includes(role))
    },
    fetchData() {
      console.log('开始获取停车场列表数据...')
      this.listLoading = true
      // 使用直接请求，避免依赖特定的API路径
      request({
        url: '/api/parkinglots',
        method: 'get',
        params: this.listQuery
      }).then(response => {
        console.log('获取停车场列表成功:', response)

        // 在控制台输出完整的响应结构，帮助调试
        console.log('响应完整数据结构:', JSON.stringify(response))

        // 处理响应数据
        let responseData = response
        if (response.data) {
          responseData = response.data
        }

        // 特殊处理后端返回的数据格式
        if (responseData.items) {
          this.list = responseData.items
          this.total = responseData.pagination?.total || responseData.items.length
          console.log(`成功加载${this.list.length}条停车场数据(从items提取)`)
        } else if (Array.isArray(responseData)) {
          this.list = responseData
          this.total = responseData.length
          console.log(`成功加载${this.list.length}条停车场数据(直接数组)`)
        } else {
          // 检查其他可能的数据结构
          console.log('尝试查找其他位置的数据:', responseData)

          if (Array.isArray(responseData.data)) {
            // 如果data字段本身是数组
            this.list = responseData.data
            this.total = responseData.data.length
            console.log(`成功加载${this.list.length}条停车场数据(data是数组)`)
          } else if (responseData.data && Array.isArray(responseData.data.items)) {
            // data.items是数组的情况
            this.list = responseData.data.items
            this.total = responseData.data.pagination?.total || responseData.data.items.length
            console.log(`成功加载${this.list.length}条停车场数据(从data.items提取)`)
          } else if (responseData.data && responseData.data.data && Array.isArray(responseData.data.data)) {
            // 某些API可能会有嵌套的data.data
            this.list = responseData.data.data
            this.total = responseData.data.data.length
            console.log(`成功加载${this.list.length}条停车场数据(从data.data提取)`)
          } else {
            // 尝试将所有属性都打印出来，查看是否有其他可能的数据结构
            console.log('响应对象的所有属性:')
            for (const key in responseData) {
              console.log(`- ${key}:`, responseData[key])
              if (responseData[key] && typeof responseData[key] === 'object') {
                for (const subKey in responseData[key]) {
                  console.log(`  - ${key}.${subKey}:`, responseData[key][subKey])
                }
              }
            }

            console.error('无法解析停车场数据，返回格式:', responseData)
            this.list = []
            this.total = 0
          }
        }

        // 计算统计信息
        this.calculateStats()

        this.listLoading = false
      }).catch(error => {
        console.error('获取停车场列表失败 (/api/parkinglots)', error)

        // 尝试备用API路径
        console.log('尝试备用API路径 /api/parking-lots')
        request({
          url: '/api/parking-lots',
          method: 'get',
          params: this.listQuery
        }).then(response => {
          console.log('备用API获取停车场列表成功:', response)

          // 处理响应数据
          let responseData = response
          if (response.data) {
            responseData = response.data
          }

          // 特殊处理后端返回的数据格式
          if (responseData.items) {
            this.list = responseData.items
            this.total = responseData.pagination?.total || responseData.items.length
            console.log(`成功加载${this.list.length}条停车场数据(从items提取)`)
          } else if (Array.isArray(responseData)) {
            this.list = responseData
            this.total = responseData.length
            console.log(`成功加载${this.list.length}条停车场数据(直接数组)`)
          } else {
            // 检查其他可能的数据结构
            console.log('尝试查找其他位置的数据:', responseData)

            if (Array.isArray(responseData.data)) {
              this.list = responseData.data
              this.total = responseData.data.length
            } else {
              console.error('无法解析停车场数据，返回格式:', responseData)
              this.list = []
              this.total = 0
            }
          }

          // 计算统计信息
          this.calculateStats()

          this.listLoading = false
        }).catch(secondError => {
          console.error('备用API获取停车场列表也失败', secondError)
          // 显示更友好的错误消息
          let errorMsg = '获取停车场列表失败'
          if (secondError.response && secondError.response.data && secondError.response.data.message) {
            errorMsg += ': ' + secondError.response.data.message
          } else if (secondError.message) {
            errorMsg += ': ' + secondError.message
          }
          this.$message.error(errorMsg)
          this.list = []
          this.total = 0
          this.listLoading = false
        })
      })
    },
    calculateStats() {
      this.totalLots = this.list.length
      this.totalSpaces = this.list.reduce((sum, lot) => sum + lot.total_spaces, 0)
      this.totalOccupied = this.list.reduce((sum, lot) => sum + lot.occupied_spaces, 0)
      this.avgUtilization = this.totalSpaces > 0
        ? Math.round((this.totalOccupied / this.totalSpaces) * 100)
        : 0
    },
    calculateUtilization(row) {
      return row.total_spaces > 0
        ? Math.round((row.occupied_spaces / row.total_spaces) * 100)
        : 0
    },
    getUtilizationStatus(row) {
      const rate = this.calculateUtilization(row)
      if (rate >= 90) return 'exception'
      if (rate >= 70) return 'warning'
      return 'success'
    },
    getUtilizationClass(row) {
      const rate = this.calculateUtilization(row)
      if (rate >= 90) return 'high'
      if (rate >= 70) return 'medium'
      return 'low'
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        address: '',
        total_spaces: 10,
        status: 1,
        description: '',
        longitude: null,
        latitude: null,
        opening_hours: '24小时',
        campus: '',
        area: '',
        manager: '',
        contact: ''
      }
      this.activeTab = 'basic'
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDelete(row) {
      this.temp = Object.assign({}, row)
      this.dialogDeleteVisible = true
    },
    handleViewDetails(row) {
      this.$router.push({ path: `/parking/details/${row.id}` })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.listLoading = true
          request({
            url: '/api/parkinglots',
            method: 'post',
            data: this.temp
          }).then(response => {
            // 处理响应数据
            let responseData = response
            if (response.data) {
              responseData = response.data
            }

            // 关闭对话框
            this.dialogFormVisible = false

            // 创建成功提示，显示更多详细信息
            this.$message({
              message: `停车场"${responseData.name}"创建成功，总车位数：${responseData.total_spaces}`,
              type: 'success',
              duration: 5000 // 显示时间延长为5秒
            })

            // 直接将新创建的停车场添加到列表中，避免重新加载整个列表
            if (responseData.id) {
              // 创建包含完整字段的对象
              const newParkingLot = {
                id: responseData.id,
                name: responseData.name,
                address: responseData.address,
                total_spaces: responseData.total_spaces,
                occupied_spaces: responseData.occupied_spaces || 0,
                status: responseData.status,
                description: responseData.description,
                longitude: responseData.longitude,
                latitude: responseData.latitude,
                opening_hours: responseData.opening_hours,
                campus: responseData.campus,
                area: responseData.area,
                manager: responseData.manager,
                contact: responseData.contact
              }

              // 添加到当前列表的开头
              this.list.unshift(newParkingLot)

              // 重新计算统计信息
              this.calculateStats()

              // 如果列表已经达到分页大小，则重新加载数据以确保分页正确
              if (this.list.length > this.listQuery.limit) {
                this.fetchData()
              } else {
                // 增加总数
                this.total += 1
              }
            } else {
              // 如果响应中没有完整数据，则回退到刷新整个列表
              this.fetchData()
            }
          }).catch(error => {
            console.error('创建停车场失败', error)
            // 显示更友好的错误消息
            let errorMsg = '创建停车场失败'
            if (error.response && error.response.data && error.response.data.message) {
              errorMsg += ': ' + error.response.data.message
            } else if (error.message) {
              errorMsg += ': ' + error.message
            }
            this.$message.error(errorMsg)
          }).finally(() => {
            this.listLoading = false
          })
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.listLoading = true
          const tempData = Object.assign({}, this.temp)
          request({
            url: `/api/parkinglots/${tempData.id}`,
            method: 'put',
            data: tempData
          }).then(response => {
            // 处理响应数据
            let responseData = response
            if (response.data) {
              responseData = response.data
            }

            // 关闭对话框
            this.dialogFormVisible = false

            // 更新成功提示
            this.$message({
              message: `停车场"${tempData.name}"信息更新成功`,
              type: 'success',
              duration: 3000
            })

            // 直接更新当前列表中的数据，避免重新加载
            if (responseData.id) {
              // 查找当前列表中的对应项
              const index = this.list.findIndex(item => item.id === responseData.id)

              if (index !== -1) {
                // 更新对象的所有字段
                this.$set(this.list, index, {
                  id: responseData.id,
                  name: responseData.name,
                  address: responseData.address,
                  total_spaces: responseData.total_spaces,
                  occupied_spaces: responseData.occupied_spaces,
                  status: responseData.status,
                  description: responseData.description,
                  longitude: responseData.longitude,
                  latitude: responseData.latitude,
                  opening_hours: responseData.opening_hours,
                  campus: responseData.campus,
                  area: responseData.area,
                  manager: responseData.manager,
                  contact: responseData.contact
                })

                // 重新计算统计信息
                this.calculateStats()
              } else {
                // 如果在当前页面找不到，可能是分页或过滤导致，刷新整个列表
                this.fetchData()
              }
            } else {
              // 如果响应中没有完整数据，则回退到刷新整个列表
              this.fetchData()
            }
          }).catch(error => {
            console.error('更新停车场失败', error)
            // 显示更友好的错误消息
            let errorMsg = '更新停车场失败'
            if (error.response && error.response.data && error.response.data.message) {
              errorMsg += ': ' + error.response.data.message
            } else if (error.message) {
              errorMsg += ': ' + error.message
            }
            this.$message.error(errorMsg)
          }).finally(() => {
            this.listLoading = false
          })
        }
      })
    },
    deleteData() {
      this.listLoading = true
      request({
        url: `/api/parkinglots/${this.temp.id}`,
        method: 'delete'
      }).then(response => {
        // 处理响应数据
        // let responseData = response
        // if (response.data) {
        //   responseData = response.data
        // }

        // 关闭对话框
        this.dialogDeleteVisible = false

        // 删除成功提示
        this.$message({
          message: `停车场"${this.temp.name}"删除成功`,
          type: 'success'
        })

        // 从列表中移除已删除的项
        const deletedId = this.temp.id
        const index = this.list.findIndex(item => item.id === deletedId)

        if (index !== -1) {
          // 从列表中移除
          this.list.splice(index, 1)

          // 更新总数和统计
          this.total = Math.max(0, this.total - 1)
          this.calculateStats()

          // 如果当前页面为空但还有其他页，则加载前一页
          if (this.list.length === 0 && this.currentPage > 1) {
            this.currentPage -= 1
            this.fetchData()
          }
        } else {
          // 如果在当前页面找不到，可能是ID不匹配，刷新整个列表
          this.fetchData()
        }
      }).catch(error => {
        console.error('删除停车场失败', error)
        // 显示更友好的错误消息
        let errorMsg = '删除停车场失败'
        if (error.response && error.response.data && error.response.data.message) {
          errorMsg += ': ' + error.response.data.message
        } else if (error.message) {
          errorMsg += ': ' + error.message
        }
        this.$message.error(errorMsg)
      }).finally(() => {
        this.listLoading = false
      })
    },

    // 触发停车场数据一致性检查
    checkConsistency() {
      // 显示确认对话框
      this.$confirm('确定要执行停车场数据一致性检查吗？这将修复车位利用率信息与数据库不一致的问题。', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认，执行检查
        const loading = this.$loading({
          lock: true,
          text: '正在执行数据一致性检查...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        // 调用后端API
        checkParkingLotsConsistency().then(response => {
          // 处理响应数据
          let responseData = response
          if (response.data) {
            responseData = response.data
          }

          // 关闭加载提示
          loading.close()

          // 显示成功消息
          this.$message({
            message: `数据一致性检查完成，修复了 ${responseData.fixed_lots || 0} 个停车场和 ${responseData.fixed_spaces || 0} 个车位`,
            type: 'success',
            duration: 5000
          })

          // 刷新数据
          this.fetchData()
        }).catch(error => {
          // 关闭加载提示
          loading.close()

          console.error('数据一致性检查失败', error)
          // 显示更友好的错误消息
          let errorMsg = '数据一致性检查失败'
          if (error.response && error.response.data && error.response.data.message) {
            errorMsg += ': ' + error.response.data.message
          } else if (error.message) {
            errorMsg += ': ' + error.message
          }
          this.$message.error(errorMsg)
        })
      }).catch(() => {
        // 用户取消，不执行任何操作
        this.$message({
          type: 'info',
          message: '已取消数据一致性检查'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 顶部标题和搜索区域
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;

  .dashboard-title {
    h2 {
      margin: 0 0 5px 0;
      font-size: 24px;
      color: #303133;
    }

    .subtitle {
      margin: 0;
      font-size: 14px;
      color: #909399;
    }
  }

  .dashboard-actions {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;

    .filter-item {
      margin-bottom: 0;
    }

    .add-btn {
      margin-left: 10px;
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;

    .dashboard-title {
      margin-bottom: 15px;
    }

    .dashboard-actions {
      width: 100%;
    }
  }
}

// 统计卡片行
.stats-row {
  margin-bottom: 20px;
}

// 已移除视图切换相关样式

// 停车场卡片容器
.parking-cards-container {
  margin-bottom: 20px;

  .card-col {
    margin-bottom: 20px;
  }

  .parking-card {
    height: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    background-color: #fff;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.15);
    }

    .card-header {
      padding: 15px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .status-tag {
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        display: flex;
        align-items: center;

        &.operating {
          background-color: rgba(103, 194, 58, 0.1);
          color: #67c23a;
        }

        &.paused {
          background-color: rgba(144, 147, 153, 0.1);
          color: #909399;
        }

        .dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          margin-right: 5px;

          &.operating {
            background-color: #67c23a;
          }

          &.paused {
            background-color: #909399;
          }
        }
      }
    }

    .card-body {
      padding: 15px;
      flex: 1;

      .address {
        margin: 0 0 15px 0;
        color: #606266;
        font-size: 14px;
        display: flex;
        align-items: center;

        i {
          margin-right: 5px;
          color: #409eff;
        }
      }

      .utilization-info {
        margin-top: 10px;

        .utilization-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 5px;
          font-size: 14px;
          color: #606266;

          .utilization-value {
            font-weight: 600;

            &.low {
              color: #67c23a;
            }

            &.medium {
              color: #e6a23c;
            }

            &.high {
              color: #f56c6c;
            }
          }
        }

        .space-count {
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
          font-size: 12px;
          color: #909399;

          b {
            color: #606266;
          }
        }
      }
    }

    .card-footer {
      padding: 10px 15px;
      border-top: 1px solid #ebeef5;
      display: flex;
      justify-content: flex-end;
      background-color: #f9f9f9;

      .el-button {
        padding: 7px 12px;
        font-size: 12px;
      }

      .el-button + .el-button {
        margin-left: 5px;
      }
    }
  }
}

// 表格样式
.responsive-table {
  margin-bottom: 20px;
}

// 兼容旧样式
.filter-container {
  padding-bottom: 15px;
  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
    margin-right: 10px;
  }
  .el-button {
    margin-left: 0;
    margin-right: 10px;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .parking-cards-container .parking-card {
    .card-header {
      flex-direction: column;
      align-items: flex-start;

      .status-tag {
        margin-top: 5px;
      }
    }

    .card-body .utilization-info .space-count {
      flex-direction: column;
      gap: 5px;
    }

    .card-footer {
      flex-wrap: wrap;
      gap: 5px;

      .el-button {
        flex: 1;
        margin-left: 0 !important;
      }
    }
  }
}
</style>
