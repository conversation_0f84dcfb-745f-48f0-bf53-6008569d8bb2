from app import db
from datetime import datetime

# 充电故障模型
class ChargingFault(db.Model):
    __tablename__ = 'charging_faults'
    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 停车场ID
    parking_lot_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('parking_lots.id', ondelete='CASCADE'), nullable=False)
    # 车位ID
    space_id = db.Column(db.Integer, db.<PERSON>ey('parking_spaces.id', ondelete='CASCADE'), nullable=False)
    # 故障类型：connector(充电接口故障), startup(设备无法启动), interruption(充电中断), other(其他故障)
    fault_type = db.Column(db.String(20), nullable=False)
    # 故障严重程度：low(轻微), medium(一般), high(严重), critical(紧急)
    severity = db.Column(db.String(20), nullable=False)
    # 报修人姓名
    reporter_name = db.Column(db.String(50), nullable=False)
    # 报修人电话
    reporter_phone = db.Column(db.String(20), nullable=True)
    # 故障描述
    fault_description = db.Column(db.Text, nullable=False)
    # 故障状态：0待处理，1处理中，2已完成，3已关闭
    status = db.Column(db.Integer, nullable=False, default=0)
    # 处理人
    processor = db.Column(db.String(50), nullable=True)
    # 处理时间
    process_time = db.Column(db.DateTime, nullable=True)
    # 处理结果
    process_result = db.Column(db.Text, nullable=True)
    # 报修时间
    report_time = db.Column(db.DateTime, default=datetime.now, nullable=False)
    # 创建时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __init__(self, parking_lot_id, space_id, fault_type, severity, reporter_name, fault_description, reporter_phone=None):
        # 确保 parking_lot_id 是整数
        if not isinstance(parking_lot_id, int):
            try:
                parking_lot_id = int(parking_lot_id)
                print(f"__init__: 已将 parking_lot_id 转换为整数: {parking_lot_id}")
            except (ValueError, TypeError) as e:
                print(f"__init__: 无法将 parking_lot_id 转换为整数: {parking_lot_id}, 错误: {str(e)}")
                raise ValueError(f"parking_lot_id 必须是整数: {parking_lot_id}")

        # 确保 space_id 是整数
        if not isinstance(space_id, int):
            try:
                space_id = int(space_id)
                print(f"__init__: 已将 space_id 转换为整数: {space_id}")
            except (ValueError, TypeError) as e:
                print(f"__init__: 无法将 space_id 转换为整数: {space_id}, 错误: {str(e)}")
                raise ValueError(f"space_id 必须是整数: {space_id}")

        self.parking_lot_id = parking_lot_id
        self.space_id = space_id
        self.fault_type = fault_type
        self.severity = severity
        self.reporter_name = reporter_name
        self.reporter_phone = reporter_phone
        self.fault_description = fault_description
        self.status = 0  # 待处理
        self.report_time = datetime.now()
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

        print(f"__init__: 充电故障对象创建成功，parking_lot_id={self.parking_lot_id}({type(self.parking_lot_id)}), space_id={self.space_id}({type(self.space_id)})")

    def __repr__(self):
        return f'<ChargingFault {self.id}: {self.fault_type}>'

    # 获取故障详情
    def get_details(self, include_relations=False):
        """获取故障详情"""
        status_map = {0: '待处理', 1: '处理中', 2: '已完成', 3: '已关闭'}
        fault_type_map = {
            'connector': '充电接口故障',
            'startup': '设备无法启动',
            'interruption': '充电中断',
            'other': '其他故障'
        }
        severity_map = {
            'low': '轻微',
            'medium': '一般',
            'high': '严重',
            'critical': '紧急'
        }

        data = {
            'id': self.id,
            'parking_lot_id': self.parking_lot_id,
            'space_id': self.space_id,
            'fault_type': self.fault_type,
            'fault_type_text': fault_type_map.get(self.fault_type, self.fault_type),
            'severity': self.severity,
            'severity_text': severity_map.get(self.severity, self.severity),
            'reporter_name': self.reporter_name,
            'reporter_phone': self.reporter_phone,
            'fault_description': self.fault_description,
            'status': self.status,
            'status_text': status_map.get(self.status, '未知'),
            'processor': self.processor,
            'process_time': self.process_time.isoformat() if self.process_time else None,
            'process_result': self.process_result,
            'report_time': self.report_time.isoformat(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

        if include_relations:
            # 获取关联的停车场信息
            from app.parkinglots.models import ParkingLot
            parking_lot = ParkingLot.query.get(self.parking_lot_id)
            if parking_lot:
                data['parking_lot'] = {
                    'id': parking_lot.id,
                    'name': parking_lot.name,
                    'address': parking_lot.address
                }

            # 获取关联的车位信息
            from app.parkinglots.models import ParkingSpace
            space = ParkingSpace.query.get(self.space_id)
            if space:
                data['space'] = {
                    'id': space.id,
                    'space_number': space.space_number,
                    'type': space.type,
                    'type_text': {1: '普通车位', 2: '残疾人车位', 3: '充电车位'}.get(space.type, '未知')
                }

        return data

    # 创建故障记录
    def create(self):
        """创建故障记录"""
        try:
            # 打印对象属性，用于调试
            print(f"创建充电故障记录，属性: parking_lot_id={self.parking_lot_id}, space_id={self.space_id}, fault_type={self.fault_type}")
            print(f"属性类型: parking_lot_id={type(self.parking_lot_id)}, space_id={type(self.space_id)}")

            # 验证关键属性
            if not isinstance(self.parking_lot_id, int):
                print(f"警告: parking_lot_id 不是整数类型: {type(self.parking_lot_id)}")
                try:
                    self.parking_lot_id = int(self.parking_lot_id)
                    print(f"已将 parking_lot_id 转换为整数: {self.parking_lot_id}")
                except (ValueError, TypeError) as e:
                    print(f"无法将 parking_lot_id 转换为整数: {self.parking_lot_id}, 错误: {str(e)}")
                    raise ValueError(f"parking_lot_id 必须是整数: {self.parking_lot_id}")

            if not isinstance(self.space_id, int):
                print(f"警告: space_id 不是整数类型: {type(self.space_id)}")
                try:
                    self.space_id = int(self.space_id)
                    print(f"已将 space_id 转换为整数: {self.space_id}")
                except (ValueError, TypeError) as e:
                    print(f"无法将 space_id 转换为整数: {self.space_id}, 错误: {str(e)}")
                    raise ValueError(f"space_id 必须是整数: {self.space_id}")

            # 添加到会话并提交
            db.session.add(self)
            db.session.commit()
            print(f"充电故障记录创建成功，ID: {self.id}")
            return self
        except Exception as e:
            db.session.rollback()
            import traceback
            error_traceback = traceback.format_exc()
            print(f"创建充电故障记录失败: {str(e)}\n{error_traceback}")
            # 重新抛出异常，以便上层函数可以捕获并处理
            raise

    # 开始处理故障
    def start_process(self, processor):
        """开始处理故障"""
        try:
            self.status = 1  # 处理中
            self.processor = processor
            self.process_time = datetime.now()
            self.updated_at = datetime.now()
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"开始处理充电故障失败: {str(e)}")
            return False

    # 完成故障处理
    def complete_process(self, process_result):
        """完成故障处理"""
        try:
            self.status = 2  # 已完成
            self.process_result = process_result
            self.updated_at = datetime.now()
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"完成充电故障处理失败: {str(e)}")
            return False

    # 关闭故障
    def close(self):
        """关闭故障"""
        try:
            self.status = 3  # 已关闭
            self.updated_at = datetime.now()
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"关闭充电故障失败: {str(e)}")
            return False

# 充电记录模型
class ChargingRecord(db.Model):
    __tablename__ = 'charging_records'
    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 关联的停车记录ID
    parking_record_id = db.Column(db.Integer, db.ForeignKey('parking_records.id'), nullable=False)
    # 车辆ID
    vehicle_id = db.Column(db.Integer, db.ForeignKey('bikes.b_id'), nullable=False)
    # 用户ID
    user_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=False)
    # 停车场ID
    parking_lot_id = db.Column(db.Integer, db.ForeignKey('parking_lots.id'), nullable=False)
    # 车位ID
    parking_space_id = db.Column(db.Integer, db.ForeignKey('parking_spaces.id'), nullable=False)
    # 开始充电时间
    start_time = db.Column(db.DateTime, nullable=False, default=datetime.now)
    # 结束充电时间
    end_time = db.Column(db.DateTime, nullable=True)
    # 充电时长（分钟）
    duration = db.Column(db.Integer, nullable=True)

    # 充电功率（kW）
    power = db.Column(db.Float, nullable=True)
    # 充电状态：0进行中，1已完成，2异常
    status = db.Column(db.Integer, nullable=False, default=0)
    # 创建时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    # 备注
    remarks = db.Column(db.Text)

    def __init__(self, parking_record_id, vehicle_id, user_id, parking_lot_id, parking_space_id, power=None):
        self.parking_record_id = parking_record_id
        self.vehicle_id = vehicle_id
        self.user_id = user_id
        self.parking_lot_id = parking_lot_id
        self.parking_space_id = parking_space_id

        # 使用当前时间
        current_time = datetime.now()
        print(f"创建充电记录，设置开始时间为当前时间: {current_time}")

        self.start_time = current_time
        self.end_time = None
        # 初始化时不设置时长，将在首次获取详情时计算
        self.duration = None
        self.power = power  # 充电功率
        self.status = 0  # 进行中
        self.created_at = current_time  # 使用相同的时间戳
        self.updated_at = current_time  # 使用相同的时间戳
        self.remarks = None

    def __repr__(self):
        return f'<ChargingRecord {self.id}: {self.vehicle_id}>'

    # 计算充电时长（分钟）
    def calculate_duration(self):
        """计算充电时长（分钟）"""
        if not self.end_time:
            # 如果尚未结束，计算到当前时间的时长
            duration = (datetime.now() - self.start_time).total_seconds() / 60
        else:
            # 如果已结束，计算开始到结束的时长
            duration = (self.end_time - self.start_time).total_seconds() / 60
        # 保持与 end_charging 方法一致，四舍五入到两位小数
        return round(duration, 2)

    # 格式化充电时长
    def format_duration(self, minutes):
        """将分钟数格式化为可读的时间格式"""
        if minutes is None:
            return None

        hours = int(minutes // 60)
        mins = int(minutes % 60)
        if hours > 0:
            return f"{hours}小时{mins}分钟"
        else:
            return f"{mins}分钟"

    # 结束充电
    def end_charging(self, remarks=None):
        """结束充电记录"""
        try:
            # 更新充电记录状态
            # 使用当前时间
            current_time = datetime.now()
            print(f"结束充电记录，设置结束时间为当前时间: {current_time}")

            self.end_time = current_time
            self.status = 1  # 已完成

            # 计算充电时长（分钟）
            duration = (self.end_time - self.start_time).total_seconds() / 60
            self.duration = round(duration, 2)
            print(f"计算并存储充电时长: {self.duration}分钟")

            if remarks:
                self.remarks = remarks

            self.updated_at = datetime.now()

            # 保存更改
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"结束充电记录失败: {str(e)}")
            return False

    # 获取充电记录详情
    def get_details(self, include_relations=False):
        """获取充电记录详情"""
        status_map = {0: '进行中', 1: '已完成', 2: '异常'}

        # 根据状态处理时长
        if self.status == 0:
            # 进行中的充电记录，不计算时长，返回null
            duration = None
            print(f"充电记录进行中，不计算时长")
        elif self.status == 1 and self.duration is not None:
            # 已完成的充电记录，使用存储的时长
            duration = self.duration
            print(f"使用存储的时长: {duration}分钟")
        else:
            # 其他状态（如异常状态）
            if self.duration is not None:
                duration = self.duration
                print(f"使用数据库中的时长: {duration}分钟")
            else:
                # 如果没有存储的时长，计算一次
                duration = self.calculate_duration()
                print(f"计算的当前时长: {duration}分钟")

        # 移除充电类型映射

        data = {
            'id': self.id,
            'parking_record_id': self.parking_record_id,
            'vehicle_id': self.vehicle_id,
            'user_id': self.user_id,
            'parking_lot_id': self.parking_lot_id,
            'parking_space_id': self.parking_space_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration': round(duration, 2) if duration is not None else None,  # 充电时长（分钟）
            'duration_formatted': self.format_duration(duration) if duration is not None else None,  # 格式化的充电时长
            'power': self.power,  # 充电功率
            'status': self.status,
            'status_text': status_map.get(self.status, '未知'),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'remarks': self.remarks
        }

        if include_relations:
            # 获取关联的车辆信息
            from app.bikes.models import Bikes
            vehicle = Bikes.query.get(self.vehicle_id)
            if vehicle:
                data['vehicle'] = {
                    'id': vehicle.b_id,
                    'number': vehicle.b_num,
                    'brand': vehicle.brand,
                    'color': vehicle.color,
                    'type': vehicle.b_type
                }

            # 获取关联的用户信息
            from app.users.models import Users
            user = Users.query.get(self.user_id)
            if user:
                data['user'] = {
                    'id': user.u_id,
                    'name': user.u_name,
                    'phone': user.u_phone
                }

            # 获取关联的停车场信息
            from app.parkinglots.models import ParkingLot
            parking_lot = ParkingLot.query.get(self.parking_lot_id)
            if parking_lot:
                data['parking_lot'] = {
                    'id': parking_lot.id,
                    'name': parking_lot.name,
                    'address': parking_lot.address
                }

            # 获取关联的车位信息
            from app.parkinglots.models import ParkingSpace
            space = ParkingSpace.query.get(self.parking_space_id)
            if space:
                data['parking_space'] = {
                    'id': space.id,
                    'number': space.space_number,
                    'space_number': space.space_number,  # 添加与前端兼容的字段名
                    'type': space.type,
                    'type_text': {1: '普通车位', 2: '残疾人车位', 3: '充电车位'}.get(space.type, '未知')
                }

        return data

    # 创建充电记录
    def create(self):
        """创建充电记录"""
        try:
            # 开始事务
            db.session.add(self)

            # 计算初始时长（如果需要）
            if self.duration is None and self.status == 0:
                self.duration = self.calculate_duration()
                print(f"创建充电记录时计算初始时长: {self.duration}分钟")

            db.session.commit()
            return self
        except Exception as e:
            db.session.rollback()
            print(f"创建充电记录失败: {str(e)}")
            return None

# 充电异常模型
class ChargingException(db.Model):
    __tablename__ = 'charging_exceptions'
    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 关联的充电记录ID（可为空，允许直接关联到故障）
    charging_record_id = db.Column(db.Integer, db.ForeignKey('charging_records.id', ondelete='SET NULL'), nullable=True)
    # 关联的充电故障ID（可为空，允许只关联到充电记录）
    fault_id = db.Column(db.Integer, db.ForeignKey('charging_faults.id', ondelete='SET NULL'), nullable=True)
    # 车位ID（冗余字段，方便查询）
    space_id = db.Column(db.Integer, db.ForeignKey('parking_spaces.id', ondelete='SET NULL'), nullable=True)
    # 停车场ID（冗余字段，方便查询）
    parking_lot_id = db.Column(db.Integer, db.ForeignKey('parking_lots.id', ondelete='SET NULL'), nullable=True)
    # 异常类型：connector(充电接口故障), startup(设备无法启动), interruption(充电中断), other(其他故障)
    exception_type = db.Column(db.String(20), nullable=False)
    # 异常描述
    description = db.Column(db.Text, nullable=False)
    # 异常时间
    time = db.Column(db.DateTime, default=datetime.now, nullable=False)
    # 处理状态：0未处理，1已处理
    status = db.Column(db.Integer, nullable=False, default=0)
    # 处理人
    processor = db.Column(db.String(50), nullable=True)
    # 处理时间
    process_time = db.Column(db.DateTime, nullable=True)
    # 处理结果
    process_result = db.Column(db.Text, nullable=True)
    # 创建时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __init__(self, exception_type, description, charging_record_id=None, fault_id=None, space_id=None, parking_lot_id=None):
        # 设置关联ID
        self.charging_record_id = charging_record_id
        self.fault_id = fault_id
        self.space_id = space_id
        self.parking_lot_id = parking_lot_id

        # 如果提供了充电记录ID，确保它是整数
        if charging_record_id is not None and not isinstance(charging_record_id, int):
            try:
                self.charging_record_id = int(charging_record_id)
                print(f"ChargingException.__init__: 已将 charging_record_id 转换为整数: {self.charging_record_id}")
            except (ValueError, TypeError) as e:
                print(f"ChargingException.__init__: 无法将 charging_record_id 转换为整数: {charging_record_id}, 错误: {str(e)}")
                raise ValueError(f"charging_record_id 必须是整数: {charging_record_id}")

        # 如果提供了故障ID，确保它是整数
        if fault_id is not None and not isinstance(fault_id, int):
            try:
                self.fault_id = int(fault_id)
                print(f"ChargingException.__init__: 已将 fault_id 转换为整数: {self.fault_id}")
            except (ValueError, TypeError) as e:
                print(f"ChargingException.__init__: 无法将 fault_id 转换为整数: {fault_id}, 错误: {str(e)}")
                raise ValueError(f"fault_id 必须是整数: {fault_id}")

        # 如果提供了车位ID，确保它是整数
        if space_id is not None and not isinstance(space_id, int):
            try:
                self.space_id = int(space_id)
                print(f"ChargingException.__init__: 已将 space_id 转换为整数: {self.space_id}")
            except (ValueError, TypeError) as e:
                print(f"ChargingException.__init__: 无法将 space_id 转换为整数: {space_id}, 错误: {str(e)}")
                raise ValueError(f"space_id 必须是整数: {space_id}")

        # 如果提供了停车场ID，确保它是整数
        if parking_lot_id is not None and not isinstance(parking_lot_id, int):
            try:
                self.parking_lot_id = int(parking_lot_id)
                print(f"ChargingException.__init__: 已将 parking_lot_id 转换为整数: {self.parking_lot_id}")
            except (ValueError, TypeError) as e:
                print(f"ChargingException.__init__: 无法将 parking_lot_id 转换为整数: {parking_lot_id}, 错误: {str(e)}")
                raise ValueError(f"parking_lot_id 必须是整数: {parking_lot_id}")

        self.exception_type = exception_type
        self.description = description
        self.time = datetime.now()
        self.status = 0  # 未处理
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

        print(f"ChargingException.__init__: 充电异常对象创建成功，charging_record_id={self.charging_record_id}, fault_id={self.fault_id}, space_id={self.space_id}")

    def __repr__(self):
        return f'<ChargingException {self.id}: {self.exception_type}>'

    # 获取异常详情
    def get_details(self, include_relations=False):
        """获取异常详情"""
        status_map = {0: '未处理', 1: '已处理'}
        exception_type_map = {
            'connector': '充电接口故障',
            'startup': '设备无法启动',
            'interruption': '充电中断',
            'other': '其他故障'
        }

        data = {
            'id': self.id,
            'charging_record_id': self.charging_record_id,
            'fault_id': self.fault_id,
            'space_id': self.space_id,
            'parking_lot_id': self.parking_lot_id,
            'type': self.exception_type,
            'type_text': exception_type_map.get(self.exception_type, self.exception_type),
            'description': self.description,
            'time': self.time.isoformat(),
            'status': self.status,
            'status_text': status_map.get(self.status, '未知'),
            'handled': self.status == 1,  # 兼容前端
            'processor': self.processor,
            'process_time': self.process_time.isoformat() if self.process_time else None,
            'process_result': self.process_result,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

        if include_relations:
            # 获取关联的充电记录信息（如果有）
            if self.charging_record_id:
                charging_record = ChargingRecord.query.get(self.charging_record_id)
                if charging_record:
                    # 获取车辆信息
                    from app.bikes.models import Bikes
                    vehicle = Bikes.query.get(charging_record.vehicle_id)
                    if vehicle:
                        data['vehicle'] = {
                            'id': vehicle.b_id,
                            'number': vehicle.b_num,  # 使用正确的属性名 b_num 而不是 bike_number
                            'brand': vehicle.brand,
                            'color': vehicle.color
                        }

                    # 获取用户信息
                    from app.users.models import Users
                    user = Users.query.get(charging_record.user_id)
                    if user:
                        data['user'] = {
                            'u_id': user.u_id,
                            'name': user.u_name,
                            'phone': user.u_phone
                        }

            # 获取车位信息（优先使用直接关联的车位ID）
            from app.parkinglots.models import ParkingSpace
            space = None

            if self.space_id:
                # 直接使用关联的车位ID
                space = ParkingSpace.query.get(self.space_id)
            elif self.charging_record_id:
                # 通过充电记录获取车位
                charging_record = ChargingRecord.query.get(self.charging_record_id)
                if charging_record and charging_record.parking_space_id:
                    space = ParkingSpace.query.get(charging_record.parking_space_id)

            if space:
                data['parking_space'] = {
                    'id': space.id,
                    'space_number': space.space_number,
                    'type': space.type,
                    'type_text': {1: '普通车位', 2: '残疾人车位', 3: '充电车位'}.get(space.type, '未知')
                }

            # 获取停车场信息（优先使用直接关联的停车场ID）
            from app.parkinglots.models import ParkingLot
            parking_lot = None

            if self.parking_lot_id:
                # 直接使用关联的停车场ID
                parking_lot = ParkingLot.query.get(self.parking_lot_id)
            elif self.charging_record_id:
                # 通过充电记录获取停车场
                charging_record = ChargingRecord.query.get(self.charging_record_id)
                if charging_record and charging_record.parking_lot_id:
                    parking_lot = ParkingLot.query.get(charging_record.parking_lot_id)

            if parking_lot:
                data['parking_lot'] = {
                    'id': parking_lot.id,
                    'name': parking_lot.name,
                    'address': parking_lot.address
                }

            # 获取关联的充电故障信息（如果有）
            if self.fault_id:
                fault = ChargingFault.query.get(self.fault_id)
                if fault:
                    data['fault'] = {
                        'id': fault.id,
                        'fault_type': fault.fault_type,
                        'fault_type_text': {
                            'connector': '充电接口故障',
                            'startup': '设备无法启动',
                            'interruption': '充电中断',
                            'other': '其他故障'
                        }.get(fault.fault_type, fault.fault_type),
                        'severity': fault.severity,
                        'severity_text': {
                            'low': '轻微',
                            'medium': '一般',
                            'high': '严重',
                            'critical': '紧急'
                        }.get(fault.severity, fault.severity),
                        'reporter_name': fault.reporter_name,
                        'fault_description': fault.fault_description,
                        'status': fault.status,
                        'status_text': {0: '待处理', 1: '处理中', 2: '已完成', 3: '已关闭'}.get(fault.status, '未知'),
                        'report_time': fault.report_time.isoformat() if hasattr(fault, 'report_time') and fault.report_time else None,
                        'process_result': fault.process_result
                    }
            # 如果没有直接关联的故障，但有车位信息，查找相关的故障
            elif space and (self.parking_lot_id or (parking_lot and parking_lot.id)):
                lot_id = self.parking_lot_id or parking_lot.id
                related_faults = ChargingFault.query.filter_by(
                    space_id=space.id,
                    parking_lot_id=lot_id
                ).order_by(ChargingFault.report_time.desc()).all()

                if related_faults:
                    data['related_faults'] = []
                    for fault in related_faults:
                        data['related_faults'].append({
                            'id': fault.id,
                            'fault_type': fault.fault_type,
                            'fault_type_text': {
                                'connector': '充电接口故障',
                                'startup': '设备无法启动',
                                'interruption': '充电中断',
                                'other': '其他故障'
                            }.get(fault.fault_type, fault.fault_type),
                            'severity': fault.severity,
                            'severity_text': {
                                'low': '轻微',
                                'medium': '一般',
                                'high': '严重',
                                'critical': '紧急'
                            }.get(fault.severity, fault.severity),
                            'reporter_name': fault.reporter_name,
                            'fault_description': fault.fault_description,
                            'status': fault.status,
                            'status_text': {0: '待处理', 1: '处理中', 2: '已完成', 3: '已关闭'}.get(fault.status, '未知'),
                            'report_time': fault.report_time.isoformat() if hasattr(fault, 'report_time') and fault.report_time else None,
                            'process_result': fault.process_result
                        })

        return data

    # 创建异常记录
    def create(self):
        """创建异常记录"""
        try:
            # 打印对象属性，用于调试
            print(f"创建充电异常记录，属性: charging_record_id={self.charging_record_id}, fault_id={self.fault_id}, space_id={self.space_id}, exception_type={self.exception_type}")

            # 验证至少有一个关联ID
            if self.charging_record_id is None and self.fault_id is None and self.space_id is None:
                print("警告: 充电异常记录没有任何关联ID (charging_record_id, fault_id, space_id)")
                # 不抛出异常，允许创建没有关联ID的记录

            # 添加到会话并提交
            db.session.add(self)
            db.session.commit()
            print(f"充电异常记录创建成功，ID: {self.id}")
            return self
        except Exception as e:
            db.session.rollback()
            import traceback
            error_traceback = traceback.format_exc()
            print(f"创建充电异常记录失败: {str(e)}\n{error_traceback}")
            # 重新抛出异常，以便上层函数可以捕获并处理
            raise

    # 处理异常
    def process(self, processor, process_result):
        """处理异常"""
        try:
            self.status = 1  # 已处理
            self.processor = processor
            self.process_time = datetime.now()
            self.process_result = process_result
            self.updated_at = datetime.now()
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"处理充电异常失败: {str(e)}")
            return False

# 充电预约模型
class ChargingReservation(db.Model):
    __tablename__ = 'charging_reservations'
    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 用户ID
    user_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=False)
    # 车辆ID
    vehicle_id = db.Column(db.Integer, db.ForeignKey('bikes.b_id'), nullable=False)
    # 停车场ID
    parking_lot_id = db.Column(db.Integer, db.ForeignKey('parking_lots.id'), nullable=False)
    # 预约开始时间
    start_time = db.Column(db.DateTime, nullable=False)
    # 预约结束时间
    end_time = db.Column(db.DateTime, nullable=False)
    # 预约状态：0待使用，1已使用，2已取消，3已过期
    status = db.Column(db.Integer, nullable=False, default=0)
    # 创建时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    # 备注
    remarks = db.Column(db.Text)

    def __init__(self, user_id, vehicle_id, parking_lot_id, start_time, end_time, remarks=None):
        self.user_id = user_id
        self.vehicle_id = vehicle_id
        self.parking_lot_id = parking_lot_id
        self.start_time = start_time
        self.end_time = end_time
        self.status = 0  # 待使用
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.remarks = remarks

    def __repr__(self):
        return f'<ChargingReservation {self.id}: {self.user_id}>'

    # 获取预约详情
    def get_details(self, include_relations=False):
        """获取预约详情"""
        status_map = {0: '待使用', 1: '已使用', 2: '已取消', 3: '已过期'}

        data = {
            'id': self.id,
            'user_id': self.user_id,
            'vehicle_id': self.vehicle_id,
            'parking_lot_id': self.parking_lot_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'status': self.status,
            'status_text': status_map.get(self.status, '未知'),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'remarks': self.remarks
        }

        if include_relations:
            # 获取关联的车辆信息
            from app.bikes.models import Bikes
            vehicle = Bikes.query.get(self.vehicle_id)
            if vehicle:
                data['vehicle'] = {
                    'id': vehicle.b_id,
                    'number': vehicle.b_num,
                    'brand': vehicle.brand,
                    'color': vehicle.color,
                    'type': vehicle.b_type
                }

            # 获取关联的用户信息
            from app.users.models import Users
            user = Users.query.get(self.user_id)
            if user:
                data['user'] = {
                    'id': user.u_id,
                    'name': user.u_name,
                    'phone': user.u_phone
                }

            # 获取关联的停车场信息
            from app.parkinglots.models import ParkingLot
            parking_lot = ParkingLot.query.get(self.parking_lot_id)
            if parking_lot:
                data['parking_lot'] = {
                    'id': parking_lot.id,
                    'name': parking_lot.name,
                    'address': parking_lot.address
                }

        return data

    # 创建预约
    def create(self):
        """创建预约"""
        try:
            db.session.add(self)
            db.session.commit()
            return self
        except Exception as e:
            db.session.rollback()
            print(f"创建充电预约失败: {str(e)}")
            return None

    # 取消预约
    def cancel(self):
        """取消预约"""
        try:
            self.status = 2  # 已取消
            self.updated_at = datetime.now()
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"取消充电预约失败: {str(e)}")
            return False

    # 标记为已使用
    def mark_as_used(self):
        """标记预约为已使用"""
        try:
            self.status = 1  # 已使用
            self.updated_at = datetime.now()
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"标记充电预约为已使用失败: {str(e)}")
            return False

    # 检查预约是否已过期
    def check_expired(self):
        """检查预约是否已过期"""
        if self.status == 0 and datetime.now() > self.end_time:
            try:
                self.status = 3  # 已过期
                self.updated_at = datetime.now()
                db.session.commit()
                return True
            except Exception as e:
                db.session.rollback()
                print(f"标记充电预约为已过期失败: {str(e)}")
                return False
        return False

    # 检查预约时间是否冲突
    @staticmethod
    def check_conflict(user_id, start_time, end_time, exclude_id=None):
        """检查用户在指定时间段内是否已有预约"""
        query = ChargingReservation.query.filter(
            ChargingReservation.user_id == user_id,
            ChargingReservation.status == 0,  # 只检查待使用的预约
            # 检查时间段重叠
            db.or_(
                # 新预约的开始时间在已有预约的时间段内
                db.and_(
                    ChargingReservation.start_time <= start_time,
                    ChargingReservation.end_time > start_time
                ),
                # 新预约的结束时间在已有预约的时间段内
                db.and_(
                    ChargingReservation.start_time < end_time,
                    ChargingReservation.end_time >= end_time
                ),
                # 新预约完全包含已有预约
                db.and_(
                    ChargingReservation.start_time >= start_time,
                    ChargingReservation.end_time <= end_time
                )
            )
        )

        # 如果是更新预约，排除自身
        if exclude_id:
            query = query.filter(ChargingReservation.id != exclude_id)

        return query.first() is not None


