# api/app/players/routes.py

from flask import request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from app import db
from app.players import players_bp
from app.players.models import Players
from app.utils.response import api_response
from app.utils.password_utils import verify_password, hash_password, get_salt_if_none
from datetime import datetime, timedelta
import sys

# 直接导入Users类 (而不是从users.models导入)
# 这样避免循环导入问题
Users = None

def setup_users_model(_Users):
    """设置Users模型类的引用"""
    global Users
    Users = _Users

# 定义players模块使用的辅助函数
def get_player_by_id(player_id):
    """通过ID获取Players对象"""
    return Players.query.get(player_id)

def get_user_by_id(user_id):
    """通过ID获取Users对象"""
    if Users and user_id:
        return Users.query.get(user_id)
    return None

def check_is_admin(user):
    """检查用户是否为管理员"""
    return user and user.u_role == 'admin'

# 初始化auth_helpers
def init_auth_helpers():
    """初始化认证辅助函数"""
    from app.utils.auth_helpers import set_auth_helper_functions
    set_auth_helper_functions(get_player_by_id, get_user_by_id, check_is_admin)

# 注册
@players_bp.route('/register', methods=['POST'])
def register():
    """注册新用户，同时创建Players和Users记录"""
    data = request.get_json()

    # 验证基础数据
    if not data or not data.get('username') or not data.get('password'):
        return api_response(message="用户名和密码不能为空", status="error", code=400)

    # 检查用户名是否已存在
    if Players.query.filter_by(username=data['username']).first():
        return api_response(message="用户名已存在", status="error", code=409)

    try:
        # 创建Players记录（认证账户）
        hashed_password = Players.generate_hash(data['password'])
        new_player = Players(username=data['username'], password=hashed_password)

        # 创建关联的Users记录（用户信息）
        new_user = Users(
            u_name=data['username'],
            u_pwd='通过Players认证',  # 实际密码存储在Players表
            u_role=data.get('u_role', 'user'),
            u_belong=data.get('u_belong'),
            u_phone=data.get('u_phone')
        )

        # 保存Users记录
        db.session.add(new_user)
        db.session.flush()  # 获取生成的u_id

        # 关联Players和Users
        new_player.user_id = new_user.u_id
        db.session.add(new_player)
        db.session.commit()

        return api_response(
            data={"player_id": new_player.id, "user_id": new_user.u_id},
            message="注册成功",
            code=201
        )
    except Exception as e:
        db.session.rollback()
        print(f"注册失败: {str(e)}", file=sys.stderr)
        return api_response(message=f"注册失败: {str(e)}", status="error", code=500)

# 登录
@players_bp.route('/login', methods=['POST'])
def login():
    """用户登录，返回JWT令牌"""
    data = request.get_json()

    print(f"Players登录接口收到数据: {data}")

    if not data or not data.get('username') or not data.get('password'):
        return api_response(message="用户名和密码不能为空", status="error", code=400)

    # 查找用户
    player = Players.query.filter_by(username=data['username']).first()

    print(f"找到玩家: {player.username if player else 'None'}")

    # 如果在Players表中找到用户，尝试验证密码
    if player and Players.verify_hash(data['password'], player.password):
        print(f"在Players表中验证成功: 用户名={data['username']}")
        # 继续使用Players表的验证逻辑
    else:
        # 如果Players表中没有找到用户或密码验证失败，尝试在Users表中查找
        print(f"在Players表中验证失败，尝试在Users表中查找: 用户名={data['username']}")
        user = Users.query.filter_by(u_name=data['username']).first()

        if user:
            print(f"在Users表中找到用户: {user.u_name}, ID: {user.u_id}")
            print(f"用户盐值: {user.salt}")

            # 使用用户的盐值和输入的密码生成哈希
            salt = user.salt or ''
            input_password_hash = hash_password(data['password'], salt)
            print(f"输入密码哈希: {input_password_hash[:10]}...")
            print(f"存储的密码哈希: {user.u_pwd[:10]}...")

            # 验证密码
            if input_password_hash == user.u_pwd:
                print("在Users表中密码验证成功!")

                # 生成令牌
                access_token = create_access_token(
                    identity=user.u_id,
                    additional_claims={
                        "role": user.u_role,
                        "sub": str(user.u_id)  # 添加标准的 sub 字段，确保是字符串类型
                    },
                    expires_delta=timedelta(hours=24)
                )

                # 构建响应数据
                response_data = {
                    'access_token': access_token,
                    'user': {
                        'u_id': user.u_id,
                        'u_name': user.u_name,
                        'u_role': user.u_role,
                        'u_belong': user.u_belong,
                        'u_phone': user.u_phone
                    }
                }

                # 返回响应
                return jsonify(response_data), 200
            else:
                print("在Users表中密码验证失败!")

        # 如果所有验证都失败，返回错误
        return api_response(message="用户名或密码错误", status="error", code=401)

    # 查找关联的用户信息
    user = None
    user_role = "user"  # 默认角色
    if player.user_id:
        user = Users.query.get(player.user_id)
        if user:
            user_role = user.u_role  # 获取用户角色
            print(f"[DEBUG] 用户 '{user.u_name}' 的角色: '{user_role}'")
        else:
            print(f"[DEBUG] 未找到用户ID {player.user_id} 对应的Users记录")
    else:
        print(f"[DEBUG] 玩家 '{player.username}' 没有关联用户ID")

    # 创建访问令牌（有效期24小时）
    additional_claims = {
        "role": user_role  # 添加角色信息到JWT
    }
    print(f"[DEBUG] 添加到JWT的额外声明: {additional_claims}")

    # 使用user_id作为身份，而不是player.id
    # 这样可以确保JWT令牌中的身份与用户ID一致
    identity = player.user_id if player.user_id else player.id
    print(f"[DEBUG] 使用身份ID: {identity}")

    # 确保 additional_claims 包含 sub 字段，并且是字符串类型
    if "sub" not in additional_claims:
        additional_claims["sub"] = str(identity)
    elif not isinstance(additional_claims["sub"], str):
        additional_claims["sub"] = str(additional_claims["sub"])

    access_token = create_access_token(
        identity=identity,
        additional_claims=additional_claims,
        expires_delta=timedelta(hours=24)
    )
    print(f"[DEBUG] 生成的访问令牌: {access_token[:20]}...")

    # 构建响应数据 - 确保access_token位于响应的顶层，便于前端获取
    response_data = {
        'access_token': access_token,  # 将token放在顶层
        'player': {
            'id': player.id,
            'username': player.username
        }
    }

    # 如果有关联的用户信息，添加到响应中
    if user:
        response_data['user'] = {
            'u_id': user.u_id,
            'u_name': user.u_name,
            'u_role': user.u_role,
            'u_belong': user.u_belong,
            'u_phone': user.u_phone
        }

    # 使用Flask的jsonify直接返回，不使用api_response
    # 这确保前端store/modules/user.js中能找到access_token
    return jsonify(response_data), 200

# 修改密码
@players_bp.route('/change-password', methods=['PUT'])
@jwt_required()
def change_password():
    """修改当前用户密码"""
    player_id = get_jwt_identity()
    player = Players.query.get(player_id)

    if not player:
        return api_response(message="用户不存在", status="error", code=404)

    data = request.get_json()
    if not data or not data.get('old_password') or not data.get('new_password'):
        return api_response(message="旧密码和新密码不能为空", status="error", code=400)

    # 验证旧密码
    if not Players.verify_hash(data['old_password'], player.password):
        return api_response(message="旧密码不正确", status="error", code=401)

    # 验证新密码长度
    if len(data['new_password']) < 6:
        return api_response(message="新密码长度不能少于6个字符", status="error", code=400)

    try:
        # 更新密码
        player.password = Players.generate_hash(data['new_password'])
        db.session.commit()

        return api_response(message="密码修改成功")
    except Exception as e:
        db.session.rollback()
        return api_response(message=f"密码修改失败: {str(e)}", status="error", code=500)
