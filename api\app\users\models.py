from app import db
from datetime import datetime
import hashlib
import uuid
from app.users.service import get_salt_if_none

# 用户信息模型
class Users(db.Model):
    __tablename__ = 'users'
    # 主键
    u_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 用户名
    u_name = db.Column(db.String(50), nullable=False, unique=True)
    # 密码（使用哈希存储）
    u_pwd = db.Column(db.String(255), nullable=False)
    # 盐值 - 用于密码哈希
    salt = db.Column(db.String(36), nullable=False)
    # 用户角色：admin-管理员，user-普通用户
    u_role = db.Column(db.String(20), nullable=False, default='user')
    # 所属学院/单位
    u_belong = db.Column(db.String(50))
    # 手机号码（改为字符串类型）
    u_phone = db.Column(db.String(20))
    # 电子邮箱
    u_email = db.Column(db.String(100))
    # 头像URL
    avatar = db.Column(db.String(255))
    # 创建时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    # 版本控制字段
    version = db.Column(db.Integer, default=1)
    
    # 定义与bikes表的一对多关系
    bikes = db.relationship('Bikes', backref='owner', lazy=True, cascade="all, delete-orphan")
    
    # 注意：player_account关系已经在Players模型中定义，这里不需要重复定义

    def __init__(self, u_name, u_pwd='默认密码', u_role='user', u_belong=None, u_phone=None, u_email=None, u_num=None):
        self.u_name = u_name
        # 使用默认盐值，但不哈希密码 - 允许外部设置这些值
        self.salt = get_salt_if_none()
        self.u_pwd = u_pwd  # 直接使用传入的密码值，可能已经哈希过
        self.u_role = u_role
        self.u_belong = u_belong
        self.u_phone = u_phone
        self.u_email = u_email
        self.avatar = None
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.version = 1  # 初始化版本号

    def __repr__(self):
        return f'<User {self.u_name} ({self.u_id})>'

    # 创建user信息接口
    def create(self):
        db.session.add(self)
        db.session.commit()
        return self
    
    # 更新user信息
    def update(self, **kwargs):
        for key, value in kwargs.items():
            if key == 'u_pwd' and value:
                # 如果更新密码，先哈希处理
                self.u_pwd = self.hash_password(value)
            elif hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.now()
        self.version += 1  # 更新版本号
        db.session.commit()
        return self
    
    # 哈希密码
    def hash_password(self, password):
        """使用盐值哈希密码"""
        # 将密码和盐值组合后进行哈希
        return hashlib.sha256(f"{password}{self.salt}".encode()).hexdigest()
    
    # 验证密码
    def verify_password(self, password):
        """验证密码是否正确"""
        print(f"验证密码: 输入[{password}]")
        print(f"当前用户: {self.u_name}, 盐值: {self.salt}")
        print(f"存储密码哈希: {self.u_pwd}")
        
        hashed = hashlib.sha256(f"{password}{self.salt}".encode()).hexdigest()
        print(f"计算得到的哈希: {hashed}")
        
        # 检查计算的哈希与存储的哈希是否匹配
        result = hashed == self.u_pwd
        print(f"密码验证结果: {'成功' if result else '失败'}")
        return result
    
    # 获取用户详细信息（包括相关的车辆）
    def get_details(self, include_bikes=False):
        """获取用户详细信息（包括关联的车辆）"""
        user_data = {
            'id': self.u_id,
            'username': self.u_name,
            'role': self.u_role,
            'department': self.u_belong,
            'phone': self.u_phone,
            'email': self.u_email,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'version': self.version
        }
        
        if include_bikes:
            from app.bikes.models import Bikes
            user_data['bikes'] = []
            bikes = Bikes.query.filter_by(belong_to=self.u_id).all()
            for bike in bikes:
                user_data['bikes'].append({
                    'id': bike.b_id,
                    'bike_number': bike.b_num,
                    'brand': bike.brand,
                    'color': bike.color,
                    'type': bike.b_type,
                    'status': bike.status,
                    'created_at': bike.created_at.isoformat() if bike.created_at else None
                })
                
        return user_data
        
    # 静态方法：从用户名查找用户
    @staticmethod
    def find_by_username(username):
        """通过用户名查找用户"""
        return Users.query.filter_by(u_name=username).first()
    
    # 静态方法：哈希密码（用于注册新用户）
    @staticmethod
    def generate_hash(password):
        """生成随机盐值并哈希密码，返回盐值和哈希值"""
        salt = str(uuid.uuid4())
        print(f"生成新盐值: {salt}")
        print(f"需要哈希的密码: {password}")
        hashed = hashlib.sha256(f"{password}{salt}".encode()).hexdigest()
        print(f"哈希结果: {hashed[:10]}...")
        return salt, hashed
