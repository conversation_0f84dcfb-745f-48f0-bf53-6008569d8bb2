import os
import logging
from dotenv import load_dotenv

basedir = os.path.abspath(os.path.dirname(__file__))
print(f"基础目录: {basedir}")

# load env
load_dotenv(os.path.join(basedir, '.flaskenv'))

# log dir
log_dir = os.path.join(basedir, os.getenv('LOG_DIR', 'app/logs'))
# 创建日志目录（如果不存在）
if not os.path.exists(log_dir):
  os.makedirs(log_dir)


class Config(object):
  # 使用绝对路径确保数据库文件位置正确
  # 数据库文件放在api目录下
  DB_PATH = os.path.join(os.path.dirname(basedir), 'sys.db')
  print(f"数据库文件路径: {DB_PATH}")
  SQLALCHEMY_DATABASE_URI = f'sqlite:///{DB_PATH}'
  SQLALCHEMY_TRACK_MODIFICATIONS = False
  LOG_TO_STDOUT = os.environ.get('LOG_TO_STDOUT', 'false').lower() == 'true'
  LOG_LEVEL = logging.INFO
  SQLALCHEMY_ECHO = True  # 启用SQL日志，方便调试
  SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'
  JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-string'
  JWT_ACCESS_TOKEN_EXPIRES = 86400  # 1天
