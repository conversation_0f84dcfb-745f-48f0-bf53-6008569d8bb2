<template>
  <div class="app-container">
    <div class="dashboard-header">
      <div class="dashboard-title">
        <el-page-header :content="parkingLot.name" @back="goBack" />
        <p class="subtitle">停车场详细信息和车位状态</p>
      </div>
      <div class="dashboard-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <el-row v-loading="loading" :gutter="20" class="main-content">
      <!-- 左侧停车场基本信息 -->
      <el-col :xs="24" :sm="24" :md="6">
        <div class="parking-card">
          <div class="card-header">
            <h3 class="title">停车场信息</h3>
            <div class="status-tag" :class="parkingLot.status === 1 ? 'operating' : 'paused'">
              <span class="dot" :class="parkingLot.status === 1 ? 'operating' : 'paused'"></span>
              {{ parkingLot.status_text }}
            </div>
          </div>
          <div class="card-body">
            <div class="info-section">
              <div class="info-item">
                <i class="el-icon-office-building info-icon"></i>
                <div class="info-content">
                  <div class="info-label">名称</div>
                  <div class="info-value">{{ parkingLot.name }}</div>
                </div>
              </div>
              <div class="info-item">
                <i class="el-icon-location info-icon"></i>
                <div class="info-content">
                  <div class="info-label">地址</div>
                  <div class="info-value">{{ parkingLot.address }}</div>
                </div>
              </div>
              <div class="info-item">
                <i class="el-icon-time info-icon"></i>
                <div class="info-content">
                  <div class="info-label">开放时间</div>
                  <div class="info-value">{{ parkingLot.opening_hours || '全天开放' }}</div>
                </div>
              </div>
              <div class="info-item">
                <i class="el-icon-s-grid info-icon"></i>
                <div class="info-content">
                  <div class="info-label">车位总数</div>
                  <div class="info-value">{{ parkingLot.total_spaces }} 个</div>
                </div>
              </div>
              <div class="info-item">
                <i class="el-icon-s-data info-icon"></i>
                <div class="info-content">
                  <div class="info-label">使用率</div>
                  <div class="info-value">
                    <el-progress
                      :percentage="parkingLot.utilization_rate"
                      :status="getUtilizationStatus(parkingLot.utilization_rate)"
                    ></el-progress>
                  </div>
                </div>
              </div>
            </div>

            <div class="stats-section">
              <div class="stats-title">车位统计</div>
              <div class="stats-grid">
                <div class="stats-item">
                  <div class="stats-value">{{ parkingLot.available_spaces }}</div>
                  <div class="stats-label">空闲车位</div>
                </div>
                <div class="stats-item">
                  <div class="stats-value">{{ parkingLot.occupied_spaces }}</div>
                  <div class="stats-label">已占用</div>
                </div>
                <div class="stats-item">
                  <div class="stats-value">{{ maintenanceSpaces }}</div>
                  <div class="stats-label">维护中</div>
                </div>
              </div>
            </div>

            <div class="description-section" v-if="parkingLot.description">
              <div class="description-title">停车场说明</div>
              <div class="description-content">{{ parkingLot.description }}</div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 右侧车位信息 -->
      <el-col :xs="24" :sm="24" :md="18">
        <div class="spaces-card">
          <div class="card-header">
            <h3 class="title">车位信息</h3>
          </div>
          <div class="card-body">
            <div class="filter-section">
              <el-input
                v-model="spacesQuery.search"
                placeholder="搜索车位号"
                prefix-icon="el-icon-search"
                clearable
                size="small"
                style="width: 200px; margin-right: 10px;"
                @input="handleSpacesFilter"
              ></el-input>
              <el-select
                v-model="spacesQuery.status"
                placeholder="车位状态"
                clearable
                size="small"
                style="width: 120px; margin-right: 10px;"
                @change="handleSpacesFilter"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="空闲" :value="0"></el-option>
                <el-option label="已占用" :value="1"></el-option>
                <el-option label="维护中" :value="2"></el-option>
              </el-select>
              <el-select
                v-model="spacesQuery.type"
                placeholder="车位类型"
                clearable
                size="small"
                style="width: 150px;"
                @change="handleSpacesFilter"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="普通车位" :value="1"></el-option>
                <el-option label="残疾人车位" :value="2"></el-option>
                <el-option label="充电车位" :value="3"></el-option>
              </el-select>
            </div>

            <!-- 车位网格列表 -->
            <div class="spaces-container">
              <div class="spaces-grid">
                <div
                  v-for="space in filteredSpaces"
                  :key="space.id"
                  class="parking-space"
                  :class="getParkingSpaceClasses(space)"
                  @click="showSpaceDetails(space)"
                >
                  <div class="space-number">{{ space.space_number }}</div>
                  <div class="space-type">
                    <el-tag :type="getSpaceTypeTag(space.type)" size="mini">
                      {{ getSpaceTypeName(space.type) }}
                    </el-tag>
                  </div>
                  <div class="space-status">
                    <el-tag :type="getSpaceStatusType(space.status)" size="mini">
                      {{ getSpaceStatusName(space.status) }}
                    </el-tag>
                  </div>
                  <div v-if="space.status === 1" class="vehicle-info" :title="getVehicleDetails(space)">
                    <i class="el-icon-bicycle" style="margin-right: 3px;"></i>
                    {{ getVehicleLicensePlate(space) }}
                  </div>
                  <div v-if="space.status === 0" class="space-actions">
                    <el-button
                      type="success"
                      size="mini"
                      icon="el-icon-parking"
                      @click.stop="handleStartParking(space)"
                    >停车</el-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空数据提示 -->
            <div v-if="filteredSpaces.length === 0" class="empty-data">
              <el-alert
                title="没有找到车位"
                type="info"
                :closable="false"
                center
                show-icon
              >
                <template slot="title">
                  <div class="empty-title">
                    <i class="el-icon-info"></i>
                    <span>没有找到车位</span>
                  </div>
                </template>
              </el-alert>
            </div>

            <!-- 分页组件 -->
            <pagination
              v-show="totalSpaces > 0"
              :total="totalSpaces"
              :page.sync="spacesQuery.page"
              :limit.sync="spacesQuery.limit"
              :page-sizes="[50]"
              @pagination="handlePagination"
              class="pagination-container"
            />
          </div>
        </div>
      </el-col>
    </el-row>



    <!-- 开始停车组件 - 简化版 -->
    <start-parking
      v-if="showStartParking"
      :parking-lot-id="parkingLot.id"
      :parking-lot-name="parkingLot.name"
      :parking-space-id="selectedSpaceId"
      :selected-space="currentSpace"
      :show-dialog="showStartParking"
      @success="handleParkingSuccess"
      @cancel="handleCancelParking"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getParkingLot, getParkingSpaces } from '@/api/parkinglot'
import StartParking from '@/components/StartParking'
import Pagination from '@/components/Pagination'
import request from '@/utils/request'
// 注意：Element UI 2.13.2 中 Empty 组件需要全局注册，这里我们使用 el-empty 替代方案

export default {
  name: 'ParkingLotDetails',
  components: {
    StartParking,
    Pagination
  },
  data() {
    return {
      loading: true,
      parkingLot: {
        id: 0,
        name: '',
        address: '',
        total_spaces: 0,
        occupied_spaces: 0,
        available_spaces: 0,
        utilization_rate: 0,
        status: 1,
        status_text: '',
        opening_hours: '',
        description: ''
      },
      spaces: [],
      totalSpaces: 0,
      spacesQuery: {
        page: 1,
        limit: 50,
        search: '',
        status: '',
        type: ''
      },
      currentSpace: null,
      showStartParking: false,
      selectedSpaceId: null
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'userId'
    ]),
    // 车位列表（现在由后端过滤和分页）
    filteredSpaces() {
      return this.spaces
    },
    // 维护中的车位数量
    maintenanceSpaces() {
      return this.spaces.filter(space => space.status === 2).length
    }
  },
  created() {
    this.fetchParkingLot()
  },
  methods: {
    checkPermission(permissionRoles) {
      if (this.roles.includes('admin')) {
        return true
      }
      if (!permissionRoles) return false
      return permissionRoles.some(role => this.roles.includes(role))
    },
    goBack() {
      // 根据路由判断返回的页面
      const currentPath = this.$route.path
      if (currentPath.includes('/profile/')) {
        // 如果是从个人中心进入，返回到个人中心的停车页面
        this.$router.push('/profile/myparking')
      } else {
        // 否则返回到停车中心
        this.$router.push('/parking/center')
      }
    },
    refreshData() {
      this.fetchParkingLot()
    },
    fetchParkingLot() {
      const lotId = this.$route.params.id
      if (!lotId) {
        this.$message.error('没有指定停车场ID')
        this.goBack()
        return
      }

      this.loading = true
      getParkingLot(lotId).then(response => {
        console.log('获取停车场详情成功:', response)

        let lotData = response
        if (response.data) {
          lotData = response.data
        }

        // 检查数据结构，尝试多种可能的格式
        if (lotData.id) {
          // 直接是停车场对象
          this.parkingLot = lotData
        } else if (lotData.data && lotData.data.id) {
          // 嵌套在data字段中
          this.parkingLot = lotData.data
        } else {
          console.error('无法解析停车场详情数据，返回格式:', lotData)
          this.$message.error('停车场数据格式错误')
          this.loading = false
          return
        }

        // 获取车位信息
        this.fetchParkingSpaces()
      }).catch(error => {
        console.error('获取停车场详情失败', error)
        this.$message.error('获取停车场详情失败')
        this.loading = false
      })
    },
    fetchParkingSpaces() {
      // 构建查询参数
      const queryParams = {
        page: this.spacesQuery.page,
        limit: this.spacesQuery.limit,
        status: this.spacesQuery.status !== '' ? this.spacesQuery.status : undefined,
        type: this.spacesQuery.type !== '' ? this.spacesQuery.type : undefined,
        search: this.spacesQuery.search || undefined
      }

      console.log('获取车位列表，查询参数:', queryParams)

      this.loading = true
      getParkingSpaces(this.parkingLot.id, queryParams).then(response => {
        this.loading = false

        let spacesData = response
        if (response.data) {
          spacesData = response.data
        }

        // 处理不同的响应格式
        if (Array.isArray(spacesData)) {
          this.spaces = spacesData
          this.totalSpaces = spacesData.length
        } else if (spacesData.items && Array.isArray(spacesData.items)) {
          this.spaces = spacesData.items
          this.totalSpaces = spacesData.total || spacesData.items.length
        } else if (spacesData.spaces && Array.isArray(spacesData.spaces)) {
          this.spaces = spacesData.spaces
          this.totalSpaces = spacesData.total || spacesData.spaces.length
        } else if (spacesData.pagination && spacesData.items) {
          // 标准分页格式
          this.spaces = spacesData.items
          this.totalSpaces = spacesData.pagination.total
        } else {
          console.error('无法解析车位数据，返回格式:', spacesData)
          this.$message.warning('无法加载车位数据')
          this.spaces = []
          this.totalSpaces = 0
        }

        console.log(`获取到 ${this.spaces.length} 个车位，总数: ${this.totalSpaces}`)

        // 获取已占用车位的车辆信息
        this.fetchOccupiedSpacesVehicleInfo()
      }).catch(error => {
        console.error('获取车位列表失败', error)
        this.$message.error('获取车位列表失败')
        this.loading = false
        this.spaces = []
        this.totalSpaces = 0
      })
    },
    getUtilizationStatus(rate) {
      if (rate >= 90) return 'exception'
      if (rate >= 70) return 'warning'
      return 'success'
    },
    getSpaceTypeName(type) {
      const typeMap = {
        1: '普通车位',
        2: '残疾人车位',
        3: '充电车位'
      }
      return typeMap[type] || '未知类型'
    },
    getSpaceTypeTag(type) {
      const typeMap = {
        1: '',
        2: 'success',
        3: 'primary'
      }
      return typeMap[type] || 'info'
    },
    getSpaceStatusName(status) {
      const statusMap = {
        0: '空闲',
        1: '已占用',
        2: '维护中'
      }
      return statusMap[status] || '未知状态'
    },
    getSpaceStatusType(status) {
      const statusMap = {
        0: 'success',
        1: 'warning',
        2: 'info'
      }
      return statusMap[status] || 'info'
    },
    getParkingSpaceClasses(space) {
      return {
        'available': space.status === 0,
        'occupied': space.status === 1,
        'maintenance': space.status === 2,
        [`type-${space.type}`]: true
      }
    },
    handleSpacesFilter() {
      // 重置页码并重新请求数据
      this.spacesQuery.page = 1
      this.fetchParkingSpaces()
    },

    // 处理分页变化
    handlePagination({ page, limit }) {
      console.log(`分页变化: 页码=${page}, 每页数量=${limit}`)
      this.spacesQuery.page = page
      this.spacesQuery.limit = limit
      this.fetchParkingSpaces()
    },

    showSpaceDetails(space) {
      // 如果车位可用，直接调用handleStartParking方法
      if (space.status === 0) {
        this.handleStartParking(space)
      } else if (space.status === 1) {
        // 如果车位已占用，显示提示信息
        this.$message.warning('该车位已被占用，无法停车')
      } else if (space.status === 2) {
        // 如果车位维护中，显示提示信息
        this.$message.warning('该车位正在维护中，暂时不可用')
      }
    },
    // 获取所有已占用车位的车辆信息
    fetchOccupiedSpacesVehicleInfo() {
      // 过滤出已占用的车位
      const occupiedSpaces = this.spaces.filter(space =>
        space.status === 1 && space.current_vehicle_id
      )

      console.log(`找到 ${occupiedSpaces.length} 个已占用的车位`)

      // 如果没有已占用的车位，直接返回
      if (occupiedSpaces.length === 0) return

      // 为每个已占用的车位处理车辆信息
      occupiedSpaces.forEach(space => {
        // 检查后端是否已经提供了车辆信息
        if (space.vehicle_info && !space.vehicle_info.includes('车辆ID:')) {
          console.log(`车位 ${space.id} 已有后端提供的车辆信息: ${space.vehicle_info}`)
        } else {
          // 如果后端没有提供有效的车辆信息，则尝试获取
          console.log(`车位 ${space.id} 需要获取车辆信息, 车辆ID: ${space.current_vehicle_id}`)

          // 设置临时车辆信息
          this.$set(space, 'vehicle_info', `车辆#${space.current_vehicle_id} (加载中...)`)

          // 获取详细信息
          this.fetchVehicleInfoForSpace(space)
        }
      })

      // 尝试从停车记录中批量获取车辆信息，作为备用方案
      this.fetchVehicleInfoFromParkingRecords(occupiedSpaces)
    },

    // 从停车记录中批量获取车辆信息
    fetchVehicleInfoFromParkingRecords(occupiedSpaces) {
      console.log('尝试从停车记录中批量获取车辆信息')

      // 获取所有进行中的停车记录
      request({
        url: '/api/parking-records/active',
        method: 'get'
      }).then(response => {
        console.log('获取进行中的停车记录成功:', response)

        let records = []

        if (response.data && response.data.records) {
          records = response.data.records
        } else if (response.data && Array.isArray(response.data)) {
          records = response.data
        } else if (Array.isArray(response)) {
          records = response
        }

        if (records.length > 0) {
          console.log(`找到 ${records.length} 条进行中的停车记录`)

          // 为每个已占用的车位查找对应的停车记录
          occupiedSpaces.forEach(space => {
            // 如果车位已经有车辆信息，跳过
            if (space.vehicle_info && !space.vehicle_info.includes('加载中')) return

            // 查找对应的停车记录
            const record = records.find(r =>
              r.parking_space_id === space.id ||
              r.space_id === space.id ||
              (r.space && r.space.id === space.id)
            )

            if (record) {
              console.log(`找到车位 ${space.id} 对应的停车记录:`, record)

              // 提取车辆信息
              let vehicleInfo = ''

              // 尝试从记录中获取车牌号
              const licensePlate = record.vehicle_license_plate ||
                                  (record.vehicle && record.vehicle.license_plate) ||
                                  (record.vehicle && record.vehicle.b_num) ||
                                  `车辆#${space.current_vehicle_id}`

              // 尝试从记录中获取品牌和颜色
              const brand = (record.vehicle && record.vehicle.brand) || '未知品牌'
              const color = (record.vehicle && record.vehicle.color) || ''

              // 组合车辆信息
              vehicleInfo = `${licensePlate} (${brand} ${color})`.trim()

              // 更新车位的车辆信息
              if (vehicleInfo && !vehicleInfo.includes('undefined')) {
                console.log(`从停车记录中更新车位 ${space.id} 的车辆信息:`, vehicleInfo)
                this.$set(space, 'vehicle_info', vehicleInfo)
              }
            }
          })
        }
      }).catch(error => {
        console.error('获取进行中的停车记录失败:', error)
      })
    },

    // 为单个车位获取车辆信息
    fetchVehicleInfoForSpace(space) {
      if (!space.current_vehicle_id) return

      console.log(`开始获取车位 ${space.id} (车位号: ${space.space_number}) 的车辆信息, 车辆ID: ${space.current_vehicle_id}`)

      // 特殊处理TEST开头的车辆ID
      if (space.current_vehicle_id === 2 || space.current_vehicle_id === '2') {
        // 直接设置为TEST-002车辆
        const vehicleInfo = 'TEST-002 (测试品牌 黑色)'
        console.log(`车位 ${space.id} 的车辆信息已设置为 TEST-002:`, vehicleInfo)

        // 更新车位的车辆信息
        this.$set(space, 'vehicle_info', vehicleInfo)

        // 如果是当前选中的车位，也更新当前车辆信息
        if (this.currentSpace && this.currentSpace.id === space.id) {
          this.currentVehicleInfo = vehicleInfo
        }
        return
      }

      request({
        url: `/api/bikes/${space.current_vehicle_id}`,
        method: 'get'
      }).then(response => {
        console.log(`获取车辆信息成功, 车辆ID: ${space.current_vehicle_id}, 响应:`, response)

        // 尝试从不同的响应结构中提取车辆数据
        let vehicleData = null

        if (response.data && response.data.bike) {
          // 标准响应格式
          vehicleData = response.data.bike
          console.log('从 response.data.bike 中提取车辆数据:', vehicleData)
        } else if (response.data && response.data.data && response.data.data.bike) {
          // 嵌套的响应格式
          vehicleData = response.data.data.bike
          console.log('从 response.data.data.bike 中提取车辆数据:', vehicleData)
        } else if (response.data) {
          // 直接使用data字段
          vehicleData = response.data
          console.log('从 response.data 中提取车辆数据:', vehicleData)
        } else {
          // 直接使用整个响应
          vehicleData = response
          console.log('从 response 中提取车辆数据:', vehicleData)
        }

        if (vehicleData) {
          // 提取车牌号，优先级: b_num > bike_number > id
          const licensePlate = vehicleData.b_num || vehicleData.bike_number || `车辆#${vehicleData.id || vehicleData.b_id || space.current_vehicle_id}`

          // 提取品牌和颜色
          const brand = vehicleData.brand || '未知品牌'
          const color = vehicleData.color || ''

          // 组合车辆信息
          const vehicleInfo = `${licensePlate} (${brand} ${color})`.trim()
          console.log(`车位 ${space.id} 的车辆信息已更新:`, vehicleInfo)

          // 更新车位的车辆信息
          this.$set(space, 'vehicle_info', vehicleInfo)

          // 如果是当前选中的车位，也更新当前车辆信息
          if (this.currentSpace && this.currentSpace.id === space.id) {
            this.currentVehicleInfo = vehicleInfo
          }
        } else {
          console.warn(`无法从响应中提取车辆数据, 车辆ID: ${space.current_vehicle_id}`)
          this.$set(space, 'vehicle_info', `车辆#${space.current_vehicle_id}`)
        }
      }).catch(error => {
        console.error(`获取车位 ${space.id} 的车辆信息失败:`, error)

        // 尝试使用备用API获取车辆信息
        this.fetchVehicleInfoFallback(space)
      })
    },

    // 备用方法：使用另一个API获取车辆信息
    fetchVehicleInfoFallback(space) {
      console.log(`使用备用方法获取车位 ${space.id} 的车辆信息, 车辆ID: ${space.current_vehicle_id}`)

      request({
        url: `/api/vehicles/${space.current_vehicle_id}`,
        method: 'get'
      }).then(response => {
        console.log(`备用方法获取车辆信息成功, 车辆ID: ${space.current_vehicle_id}, 响应:`, response)

        let vehicleData = null

        if (response.data && response.data.vehicle) {
          vehicleData = response.data.vehicle
        } else if (response.data) {
          vehicleData = response.data
        } else {
          vehicleData = response
        }

        if (vehicleData) {
          // 提取车牌号，优先级: license_plate > plate_number > b_num > id
          const licensePlate = vehicleData.license_plate || vehicleData.plate_number ||
                              vehicleData.b_num || vehicleData.bike_number ||
                              `车辆#${vehicleData.id || vehicleData.b_id || space.current_vehicle_id}`

          // 提取品牌和颜色
          const brand = vehicleData.brand || '未知品牌'
          const color = vehicleData.color || ''

          // 组合车辆信息
          const vehicleInfo = `${licensePlate} (${brand} ${color})`.trim()
          console.log(`车位 ${space.id} 的车辆信息已通过备用方法更新:`, vehicleInfo)

          // 更新车位的车辆信息
          this.$set(space, 'vehicle_info', vehicleInfo)

          // 如果是当前选中的车位，也更新当前车辆信息
          if (this.currentSpace && this.currentSpace.id === space.id) {
            this.currentVehicleInfo = vehicleInfo
          }
        } else {
          console.warn(`备用方法无法提取车辆数据, 车辆ID: ${space.current_vehicle_id}`)
          this.$set(space, 'vehicle_info', `车辆#${space.current_vehicle_id}`)
        }
      }).catch(error => {
        console.error(`备用方法获取车辆信息失败, 车辆ID: ${space.current_vehicle_id}:`, error)
        this.$set(space, 'vehicle_info', `车辆#${space.current_vehicle_id}`)
      })
    },

    // 获取当前选中车位的车辆信息
    fetchVehicleInfo(vehicleId) {
      console.log(`开始获取车辆信息, 车辆ID: ${vehicleId}`)

      // 特殊处理TEST开头的车辆ID
      if (vehicleId === 2 || vehicleId === '2') {
        // 直接设置为TEST-002车辆
        this.currentVehicleInfo = 'TEST-002 (测试品牌 黑色)'
        console.log('当前车辆信息已设置为 TEST-002:', this.currentVehicleInfo)
        return
      }

      request({
        url: `/api/bikes/${vehicleId}`,
        method: 'get'
      }).then(response => {
        console.log(`获取车辆信息成功, 车辆ID: ${vehicleId}, 响应:`, response)

        // 尝试从不同的响应结构中提取车辆数据
        let vehicleData = null

        if (response.data && response.data.bike) {
          // 标准响应格式
          vehicleData = response.data.bike
          console.log('从 response.data.bike 中提取车辆数据:', vehicleData)
        } else if (response.data && response.data.data && response.data.data.bike) {
          // 嵌套的响应格式
          vehicleData = response.data.data.bike
          console.log('从 response.data.data.bike 中提取车辆数据:', vehicleData)
        } else if (response.data) {
          // 直接使用data字段
          vehicleData = response.data
          console.log('从 response.data 中提取车辆数据:', vehicleData)
        } else {
          // 直接使用整个响应
          vehicleData = response
          console.log('从 response 中提取车辆数据:', vehicleData)
        }

        if (vehicleData) {
          // 提取车牌号，优先级: b_num > bike_number > id
          const licensePlate = vehicleData.b_num || vehicleData.bike_number || `车辆#${vehicleData.id || vehicleData.b_id || vehicleId}`

          // 提取品牌和颜色
          const brand = vehicleData.brand || '未知品牌'
          const color = vehicleData.color || ''

          // 组合车辆信息
          this.currentVehicleInfo = `${licensePlate} (${brand} ${color})`.trim()
          console.log('当前车辆信息已更新:', this.currentVehicleInfo)
        } else {
          console.warn(`无法从响应中提取车辆数据, 车辆ID: ${vehicleId}`)
          this.currentVehicleInfo = `车辆#${vehicleId}`
        }
      }).catch(error => {
        console.error('获取车辆信息失败', error)

        // 尝试使用备用API获取车辆信息
        this.fetchVehicleInfoDetailFallback(vehicleId)
      })
    },

    // 备用方法：使用另一个API获取当前选中车位的车辆信息
    fetchVehicleInfoDetailFallback(vehicleId) {
      console.log(`使用备用方法获取车辆信息, 车辆ID: ${vehicleId}`)

      request({
        url: `/api/vehicles/${vehicleId}`,
        method: 'get'
      }).then(response => {
        console.log(`备用方法获取车辆信息成功, 车辆ID: ${vehicleId}, 响应:`, response)

        let vehicleData = null

        if (response.data && response.data.vehicle) {
          vehicleData = response.data.vehicle
        } else if (response.data) {
          vehicleData = response.data
        } else {
          vehicleData = response
        }

        if (vehicleData) {
          // 提取车牌号，优先级: license_plate > plate_number > b_num > id
          const licensePlate = vehicleData.license_plate || vehicleData.plate_number ||
                              vehicleData.b_num || vehicleData.bike_number ||
                              `车辆#${vehicleData.id || vehicleData.b_id || vehicleId}`

          // 提取品牌和颜色
          const brand = vehicleData.brand || '未知品牌'
          const color = vehicleData.color || ''

          // 组合车辆信息
          this.currentVehicleInfo = `${licensePlate} (${brand} ${color})`.trim()
          console.log('当前车辆信息已通过备用方法更新:', this.currentVehicleInfo)
        } else {
          console.warn(`备用方法无法提取车辆数据, 车辆ID: ${vehicleId}`)
          this.currentVehicleInfo = `车辆#${vehicleId}`
        }
      }).catch(error => {
        console.error(`备用方法获取车辆信息失败, 车辆ID: ${vehicleId}:`, error)

        // 尝试使用第三种方法获取车辆信息
        this.fetchVehicleInfoFromParkingRecord(vehicleId)
      })
    },

    // 第三种备用方法：从停车记录中获取车辆信息
    fetchVehicleInfoFromParkingRecord(vehicleId) {
      console.log(`尝试从停车记录中获取车辆信息, 车辆ID: ${vehicleId}`)

      request({
        url: `/api/parking-records/active`,
        method: 'get',
        params: {
          vehicle_id: vehicleId
        }
      }).then(response => {
        console.log(`从停车记录获取车辆信息成功, 车辆ID: ${vehicleId}, 响应:`, response)

        let recordData = null

        if (response.data && response.data.records && response.data.records.length > 0) {
          recordData = response.data.records[0]
        } else if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          recordData = response.data[0]
        }

        if (recordData && recordData.vehicle_info) {
          // 直接使用记录中的车辆信息
          this.currentVehicleInfo = recordData.vehicle_info
          console.log('从停车记录中获取到车辆信息:', this.currentVehicleInfo)
        } else {
          // 如果没有找到车辆信息，使用默认值
          this.currentVehicleInfo = `车辆#${vehicleId}`
        }
      }).catch(error => {
        console.error(`从停车记录获取车辆信息失败, 车辆ID: ${vehicleId}:`, error)
        this.currentVehicleInfo = `车辆#${vehicleId}`
      })
    },

    // 获取车位的车辆信息
    getVehicleInfo(space) {
      if (!space.current_vehicle_id) return '未知车辆'
      return space.vehicle_info || '加载中...'
    },

    // 获取车辆的车牌号
    getVehicleLicensePlate(space) {
      // 如果车位没有车辆ID，显示加载中
      if (!space.current_vehicle_id) return '加载中...'

      // 如果车位没有车辆信息，显示车辆ID
      if (!space.vehicle_info) return `车辆#${space.current_vehicle_id}`

      // 如果车辆信息是"加载中..."，直接返回
      if (space.vehicle_info.includes('加载中')) return space.vehicle_info

      // 如果车辆信息是"车辆ID:"，直接返回车辆ID
      if (space.vehicle_info.includes('车辆ID:')) return `车辆#${space.current_vehicle_id}`

      // 从车辆信息中提取车牌号（括号前的内容）
      const vehicleInfo = space.vehicle_info
      const match = vehicleInfo.match(/^([^(]+)/) // 提取括号前的内容作为车牌号

      return match ? match[1].trim() : `车辆#${space.current_vehicle_id}`
    },

    // 获取车辆的详细信息（品牌、颜色等）
    getVehicleDetails(space) {
      // 如果车位没有车辆ID或车辆信息，返回空字符串
      if (!space.current_vehicle_id || !space.vehicle_info) return ''

      // 如果车辆信息是"加载中..."或"车辆ID:"，返回空字符串
      if (space.vehicle_info.includes('加载中') || space.vehicle_info.includes('车辆ID:')) return ''

      // 从车辆信息中提取详细信息（括号内的内容）
      const vehicleInfo = space.vehicle_info
      const match = vehicleInfo.match(/\((.+)\)/) // 提取括号内的内容

      return match ? match[1] : ''
    },




    handleStartParking(space) {
      // 不允许选择充电车位进行停车
      if (space.type === 3) {
        this.$message.warning('充电车位不能用于普通停车，请选择其他车位')
        return
      }

      this.selectedSpaceId = space.id
      this.currentSpace = space  // 保存当前选中的车位信息
      this.spaceDialogVisible = false

      // 添加日志，方便调试
      console.log('用户选择了车位:', space.space_number, '车位ID:', space.id)

      // 使用 nextTick 确保 DOM 更新后再设置 showStartParking
      this.$nextTick(() => {
        this.showStartParking = true
        console.log('已设置 showStartParking = true，等待 StartParking 组件响应')
      })
    },
    handleParkingSuccess() {
      this.$message.success('停车成功')
      this.showStartParking = false
      this.fetchParkingSpaces() // 刷新车位数据
    },
    handleCancelParking() {
      this.showStartParking = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
  padding: 20px;

  // 添加全局字体样式
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;

  .dashboard-title {
    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
    }

    .subtitle {
      margin: 0;
      font-size: 14px;
      color: #909399;
    }
  }

  .dashboard-actions {
    display: flex;
    gap: 10px;
  }
}

.main-content {
  margin-bottom: 20px;
}

.parking-card, .spaces-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s ease;

  .card-header {
    padding: 18px 24px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f9fafc;

    .title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      position: relative;
      padding-left: 12px;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background-color: #409EFF;
        border-radius: 2px;
      }
    }

    .status-tag {
      padding: 4px 10px;
      border-radius: 20px;
      font-size: 12px;
      display: flex;
      align-items: center;
      font-weight: 500;

      &.operating {
        background-color: rgba(103, 194, 58, 0.15);
        color: #67c23a;
      }

      &.paused {
        background-color: rgba(144, 147, 153, 0.15);
        color: #909399;
      }

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;

        &.operating {
          background-color: #67c23a;
          box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
          animation: pulse 2s infinite;
        }

        &.paused {
          background-color: #909399;
        }
      }
    }
  }

  .card-body {
    padding: 24px;
  }
}

.info-section {
  margin-bottom: 24px;

  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 18px;
    padding-bottom: 18px;
    border-bottom: 1px dashed #ebeef5;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }

    .info-icon {
      font-size: 20px;
      color: #409EFF;
      margin-right: 12px;
      margin-top: 2px;
      background-color: rgba(64, 158, 255, 0.1);
      padding: 8px;
      border-radius: 8px;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .info-content {
      flex: 1;

      .info-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 6px;
      }

      .info-value {
        font-size: 16px;
        color: #303133;
        font-weight: 500;
      }
    }
  }
}

.stats-section {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e6f7ff;

  .stats-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 18px;
    color: #303133;
    display: flex;
    align-items: center;

    &:before {
      content: '\e61e'; /* Element UI的数据图标 */
      font-family: 'element-icons';
      margin-right: 8px;
      color: #409EFF;
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;

    .stats-item {
      text-align: center;
      background-color: white;
      padding: 15px 10px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }

      .stats-value {
        font-size: 28px;
        font-weight: 600;
        color: #409EFF;
        margin-bottom: 8px;
      }

      .stats-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.description-section {
  background-color: #fafafa;
  border-radius: 10px;
  padding: 16px;
  border-left: 4px solid #409EFF;

  .description-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #303133;
    display: flex;
    align-items: center;

    &:before {
      content: '\e791'; /* Element UI的文档图标 */
      font-family: 'element-icons';
      margin-right: 8px;
      color: #409EFF;
    }
  }

  .description-content {
    font-size: 14px;
    color: #606266;
    line-height: 1.8;
    padding: 0 8px;
    white-space: pre-line;
  }
}

.filter-section {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  gap: 12px;
  background-color: #f9fafc;
  padding: 16px;
  border-radius: 8px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.03);
}

.parking-layout {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  padding: 10px 0;
}

// 车位网格列表样式
.spaces-container {
  min-height: 300px;
  background-color: #f9fafc;
  border-radius: 8px;
  padding: 15px;
  position: relative;
  margin-top: 20px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><rect x="0" y="0" width="100" height="100" fill="none" stroke="%23e0e0e0" stroke-width="0.5" stroke-dasharray="5,5" /></svg>');
    opacity: 0.3;
    pointer-events: none;
    border-radius: 8px;
  }
}

.spaces-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.parking-space {
  width: 120px;
  height: 120px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 8px;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }

  .space-number {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 5px;
  }

  .space-type, .space-status {
    font-size: 12px;
    color: #606266;
  }

  .space-type {
    margin-bottom: 3px;
  }

  .space-status {
    font-weight: 500;
    margin-bottom: 5px;
  }

  .vehicle-info {
    font-size: 11px;
    background-color: rgba(230, 162, 60, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    text-align: center;
    width: 100%;
    margin-top: 5px;
    word-break: break-all;
    max-height: 40px;
    overflow: hidden;
    color: #e6a23c;
    font-weight: 600;
    border: 1px dashed rgba(230, 162, 60, 0.3);
    position: relative;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(230, 162, 60, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  // 不同类型车位的样式
  &.type-0 {
    border-top: 2px solid #409EFF;
  }

  &.type-1 {
    border-top: 2px solid #67C23A;
  }

  &.type-2 {
    border-top: 2px solid #E6A23C;
  }

  // 充电车位样式
  &.charging {
    border-top: 2px dashed #409EFF;
  }

  // 状态样式
  &.available {
    background-color: #f0f9eb;
    border-color: #e1f3d8;
    position: relative;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background-color: #67c23a;
    }

    .space-status {
      color: #67c23a;
      font-weight: 600;
    }
  }

  &.occupied {
    background-color: #fdf6ec;
    border-color: #faecd8;
    position: relative;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background-color: #e6a23c;
    }

    .space-status {
      color: #e6a23c;
      font-weight: 600;
    }
  }

  &.maintenance {
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    position: relative;
    overflow: hidden;
    background-image: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0, 0, 0, 0.03) 10px, rgba(0, 0, 0, 0.03) 20px);

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background-color: #909399;
    }

    &::before {
      content: '维护中';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-30deg);
      font-size: 18px;
      font-weight: bold;
      color: rgba(144, 147, 153, 0.3);
      white-space: nowrap;
      z-index: 0;
    }

    .space-status {
      color: #909399;
      font-weight: 600;
    }
  }
}







// 空数据提示样式
.empty-data {
  padding: 40px 0;
  text-align: center;

  .el-alert {
    display: inline-block;
    width: auto;
    min-width: 300px;
    padding: 20px 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .empty-title {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;

    i {
      margin-right: 8px;
      font-size: 20px;
    }
  }
}

// 分页容器样式
.pagination-container {
  padding: 15px 5px;
  text-align: right;
  background-color: #fff;
  margin-top: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;

    .dashboard-actions {
      margin-top: 10px;
    }
  }

  .filter-section {
    flex-direction: column;

    .el-input, .el-select {
      width: 100% !important;
    }
  }
}
</style>
