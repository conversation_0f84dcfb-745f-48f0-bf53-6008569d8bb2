from app import db
from datetime import datetime

class Announcement(db.Model):
    __tablename__ = 'announcements'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(20), nullable=False, default='info')  # info, success, warning, error
    priority = db.Column(db.Integer, nullable=False, default=1)  # 1-低, 2-中, 3-高
    target_roles = db.Column(db.String(100))  # 目标角色，逗号分隔，空表示所有用户
    is_active = db.Column(db.<PERSON>, nullable=False, default=True)  # 是否启用
    start_time = db.Column(db.DateTime)  # 生效开始时间
    end_time = db.Column(db.DateTime)  # 生效结束时间
    created_by = db.Column(db.Integer, db.<PERSON><PERSON>('users.u_id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'type': self.type,
            'priority': self.priority,
            'target_roles': self.target_roles.split(',') if self.target_roles else [],
            'is_active': self.is_active,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def is_visible_to_user(self, user_role):
        """检查公告是否对指定用户角色可见"""
        # 检查是否启用
        if not self.is_active:
            return False

        # 检查时间范围
        now = datetime.now()
        if self.start_time and now < self.start_time:
            return False
        if self.end_time and now > self.end_time:
            return False

        # 检查目标角色
        if not self.target_roles:  # 空表示所有用户
            return True

        target_roles = self.target_roles.split(',')
        return user_role in target_roles

    @classmethod
    def get_active_announcements_for_user(cls, user_role, limit=10):
        """获取对指定用户角色可见的活跃公告"""
        now = datetime.now()

        # 基础查询：启用的公告
        query = cls.query.filter(cls.is_active == True)

        # 时间范围过滤
        query = query.filter(
            db.or_(
                cls.start_time.is_(None),
                cls.start_time <= now
            )
        ).filter(
            db.or_(
                cls.end_time.is_(None),
                cls.end_time >= now
            )
        )

        # 角色过滤
        query = query.filter(
            db.or_(
                cls.target_roles.is_(None),
                cls.target_roles == '',
                cls.target_roles.like(f'%{user_role}%')
            )
        )

        # 按优先级和创建时间排序
        query = query.order_by(cls.priority.desc(), cls.created_at.desc())

        if limit:
            query = query.limit(limit)

        return query.all()