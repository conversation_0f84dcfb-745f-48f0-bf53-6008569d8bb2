2025/04/06 00:06:51 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/06 00:06:51 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/06 00:06:51 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 00:07:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:07:29] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 00:07:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:07:35] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 00:07:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:07:42] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 00:07:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:07:42] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 00:07:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:07:42] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 00:09:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:09:58] "POST /api/login HTTP/1.1" 200 -
2025/04/06 00:09:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:09:58] "[31m[1mGET /api/users HTTP/1.1[0m" 422 -
2025/04/06 00:10:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:10:45] "POST /api/login HTTP/1.1" 200 -
2025/04/06 00:10:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:10:45] "[31m[1mGET /api/users HTTP/1.1[0m" 422 -
2025/04/06 00:11:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:11:34] "POST /api/login HTTP/1.1" 200 -
2025/04/06 00:11:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:11:34] "[31m[1mGET /api/users HTTP/1.1[0m" 422 -
2025/04/06 00:12:32 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/04/06 00:12:32 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/04/06 00:12:32 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 00:12:32 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 00:12:34 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/04/06 00:12:34 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 00:12:34 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 00:12:42 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\test_login.py', reloading
2025/04/06 00:12:43 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 00:12:44 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/04/06 00:12:45 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 00:12:45 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 00:12:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:12:56] "POST /api/login HTTP/1.1" 200 -
2025/04/06 00:12:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:12:56] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:13:16 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 00:13:16 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 00:13:18 flask_api __init__.py[51] create_app() INFO: Flask Rest Api startup
2025/04/06 00:13:18 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 00:13:18 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 00:13:27 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\__init__.py', reloading
2025/04/06 00:13:27 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 00:13:29 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/06 00:13:29 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 00:13:29 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 00:13:37 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\utils\\auth_helpers.py', reloading
2025/04/06 00:13:37 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 00:13:40 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/06 00:13:40 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 00:13:40 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 00:14:42 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/06 00:14:42 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/06 00:14:42 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 00:15:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:15:23] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 00:15:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:15:29] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 00:15:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:15:29] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 00:15:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:15:29] "[35m[1mPOST /api/register HTTP/1.1[0m" 500 -
2025/04/06 00:15:57 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/06 00:16:03 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/06 00:16:11 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/06 00:16:15 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/06 00:16:16 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/06 00:16:16 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 00:16:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:16:28] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 00:16:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:16:33] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 00:16:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:16:33] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 00:16:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:16:33] "[35m[1mPOST /api/register HTTP/1.1[0m" 500 -
2025/04/06 00:17:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:17:31] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:17:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:17:31] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:17:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:17:56] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:17:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:17:56] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:18:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:18:37] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:18:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:18:37] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:19:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:19:01] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:19:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:19:01] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:19:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:19:31] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:19:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:19:31] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:19:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:19:37] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:19:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:19:37] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 00:21:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 00:21:03] "[35m[1mPOST /api/register HTTP/1.1[0m" 500 -
2025/04/06 14:01:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:01:35] "[35m[1mPOST /api/register HTTP/1.1[0m" 500 -
2025/04/06 14:02:59 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/06 14:02:59 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/04/06 14:02:59 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 14:02:59 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:03:01 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/06 14:03:01 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:03:01 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:05:22 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/04/06 14:05:22 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/06 14:05:22 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 14:05:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:05:49] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:05:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:05:52] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:05:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:05:52] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 14:05:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:05:52] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 14:07:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:07:09] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 14:07:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:07:38] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 14:07:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:07:45] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:08:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:08:13] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:08:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:08:20] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:08:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:08:20] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 14:08:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:08:59] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:08:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:08:59] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 14:08:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:08:59] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:09:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:09:39] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:10:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:10:07] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:10:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:10:07] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 14:10:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:10:07] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:10:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:10:14] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:11:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:11:02] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:11:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:11:34] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:12:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:12:55] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:13:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:13:53] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:14:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:14:26] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:15:34 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:15:34 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/04/06 14:15:34 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 14:15:34 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:15:36 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:15:36 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:15:36 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:16:21 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:16:21 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:16:23 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:16:23 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:16:23 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:16:51 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:16:51 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:16:54 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:16:54 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:16:54 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:17:01 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:17:01 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:17:03 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:17:03 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:17:03 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:17:08 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:17:08 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:17:11 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:17:11 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:17:11 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:17:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:17:16] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:17:27 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:17:27 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:17:29 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:17:29 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:17:29 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:17:33 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:17:34 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:17:36 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:17:36 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:17:36 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:17:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:17:41] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:17:52 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\users\\views.py', reloading
2025/04/06 14:17:53 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:17:57 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:17:57 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:17:57 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:18:01 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\users\\views.py', reloading
2025/04/06 14:18:02 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:18:05 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:18:05 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:18:05 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:18:53 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\test_login.py', reloading
2025/04/06 14:18:53 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:18:55 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:18:55 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:18:55 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:19:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:19:23] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:19:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:19:23] "[31m[1mGET /api/users HTTP/1.1[0m" 403 -
2025/04/06 14:19:56 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\test_login.py', reloading
2025/04/06 14:19:56 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:19:58 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:19:58 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:19:58 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:20:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:20:06] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:20:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:20:06] "[31m[1mGET /api/users HTTP/1.1[0m" 403 -
2025/04/06 14:22:28 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:22:28 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:22:30 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:22:30 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:22:30 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:22:42 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\utils\\responses.py', reloading
2025/04/06 14:22:42 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:22:44 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:22:44 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:22:44 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:22:52 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\utils\\responses.py', reloading
2025/04/06 14:22:52 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:22:54 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:22:54 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:22:54 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:23:02 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\utils\\responses.py', reloading
2025/04/06 14:23:02 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:23:04 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:23:04 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:23:04 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:23:06 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:23:06 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:23:08 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:23:09 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:23:09 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:23:13 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\utils\\responses.py', reloading
2025/04/06 14:23:13 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:23:15 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:23:15 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:23:15 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:23:23 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\utils\\responses.py', reloading
2025/04/06 14:23:23 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:23:25 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:23:25 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:23:25 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:23:46 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:23:46 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:23:48 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:23:48 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:23:48 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:23:56 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\utils\\responses.py', reloading
2025/04/06 14:23:56 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:23:58 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:23:58 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:23:58 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:24:10 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\utils\\responses.py', reloading
2025/04/06 14:24:10 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:24:12 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:24:12 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:24:12 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:24:38 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:24:39 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:24:41 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:24:41 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:24:41 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:24:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:24:55] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:24:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:24:55] "[31m[1mGET /api/users HTTP/1.1[0m" 403 -
2025/04/06 14:25:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:25:07] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:25:21 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:25:21 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:25:23 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:25:23 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:25:23 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:25:31 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:25:31 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:25:33 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:25:33 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:25:33 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:25:43 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:25:43 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:25:45 flask_api __init__.py[57] create_app() INFO: Flask Rest Api startup
2025/04/06 14:25:45 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:25:45 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:26:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:26:03] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 14:26:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:26:09] "POST /api/check-username HTTP/1.1" 200 -
2025/04/06 14:26:23 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\__init__.py', reloading
2025/04/06 14:26:23 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:26:25 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:26:25 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:26:25 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:26:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:26:34] "POST /api/check-username HTTP/1.1" 200 -
2025/04/06 14:26:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:26:40] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:27:14 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:27:14 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:27:16 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:27:16 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:27:16 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:27:50 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:27:51 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:27:53 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:27:53 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:27:53 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:28:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:28:02] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:28:17 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:28:17 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:28:19 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:28:19 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:28:19 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:28:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:28:28] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:28:39 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\__init__.py', reloading
2025/04/06 14:28:39 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:28:41 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:28:41 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:28:41 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:28:48 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\app\\__init__.py', reloading
2025/04/06 14:28:49 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:28:50 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:28:50 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:28:50 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:28:59 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:28:59 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:29:01 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:29:01 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:29:01 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:29:15 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\frontend_adapter.py', reloading
2025/04/06 14:29:15 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:29:17 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:29:17 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:29:17 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:29:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:29:40] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:30:42 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:30:42 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:30:44 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:30:44 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:30:44 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:31:30 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:31:30 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/06 14:31:30 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 14:32:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:32:19] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:32:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:32:23] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:32:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:32:26] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:32:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:32:27] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 14:32:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:32:27] "[31m[1mPOST /api/register HTTP/1.1[0m" 409 -
2025/04/06 14:32:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:32:32] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:32:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:32:33] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:32:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:32:33] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 14:32:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:32:33] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 14:33:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:33:36] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 14:33:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:33:44] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:33:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:33:50] "POST /api/check-username HTTP/1.1" 200 -
2025/04/06 14:33:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:33:57] "POST /api/check-username HTTP/1.1" 200 -
2025/04/06 14:34:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:34:04] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 14:34:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:34:11] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:34:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:34:17] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 14:34:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:34:25] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/06 14:34:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:34:32] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:34:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:34:32] "[31m[1mGET /api/users HTTP/1.1[0m" 403 -
2025/04/06 14:34:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:34:39] "POST /api/logout HTTP/1.1" 200 -
2025/04/06 14:34:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:34:48] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:34:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:34:57] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:37:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:37:00] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:39:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:39:50] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:39:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:39:57] "POST /api/check-username HTTP/1.1" 200 -
2025/04/06 14:40:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:40:03] "POST /api/logout HTTP/1.1" 200 -
2025/04/06 14:40:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:40:24] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 14:41:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:41:14] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:41:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:41:14] "[31m[1mGET /api/users HTTP/1.1[0m" 403 -
2025/04/06 14:41:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:41:22] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:41:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:41:22] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 14:42:18 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:42:18 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/06 14:42:18 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 14:46:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:07] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:46:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:14] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:46:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:14] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:46:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:14] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 14:46:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:14] "[31m[1mPOST /api/register HTTP/1.1[0m" 409 -
2025/04/06 14:46:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:19] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:46:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:19] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:46:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:19] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 14:46:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:19] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 14:46:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:41] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:46:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:45] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:46:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:45] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 14:46:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:46:45] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 14:47:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:47:03] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:47:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:47:09] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:47:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:47:09] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 14:47:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:47:09] "[31m[1mPOST /api/register HTTP/1.1[0m" 409 -
2025/04/06 14:47:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:47:12] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:47:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:47:57] "[31m[1mPOST /api/register HTTP/1.1[0m" 409 -
2025/04/06 14:48:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:48:04] "[31m[1mPOST /api/register HTTP/1.1[0m" 409 -
2025/04/06 14:48:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:48:12] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:48:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:48:17] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:48:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:48:27] "POST /api/check-username HTTP/1.1" 200 -
2025/04/06 14:48:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:48:34] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:51:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:51:47] "POST /api/login HTTP/1.1" 200 -
2025/04/06 14:53:03 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:53:03 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/06 14:53:03 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 14:53:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:53:05] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:53:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:53:06] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 14:53:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:53:06] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 14:53:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:53:06] "[31m[1mPOST /api/register HTTP/1.1[0m" 409 -
2025/04/06 14:55:28 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:55:28 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025/04/06 14:55:28 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 14:55:28 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:55:30 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:55:30 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:55:30 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:58:02 werkzeug _internal.py[97] _log() INFO:  * Detected change in 'D:\\try\\try\\vue-admin-template\\api\\main.py', reloading
2025/04/06 14:58:03 werkzeug _internal.py[97] _log() INFO:  * Restarting with stat
2025/04/06 14:58:05 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 14:58:05 werkzeug _internal.py[97] _log() WARNING:  * Debugger is active!
2025/04/06 14:58:05 werkzeug _internal.py[97] _log() INFO:  * Debugger PIN: 700-732-964
2025/04/06 14:58:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 14:58:17] "POST /api/login HTTP/1.1" 200 -
2025/04/06 15:01:18 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 15:01:18 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/06 15:01:18 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 15:06:32 flask_api __init__.py[58] create_app() INFO: Flask Rest Api startup
2025/04/06 15:06:32 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/06 15:06:32 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/06 15:26:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:26:44] "OPTIONS /api/user/info?token=aba-token-1743924404337 HTTP/1.1" 200 -
2025/04/06 15:26:44 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/user/info, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer aba-token-1743924404337

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/06 15:26:44 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/06 15:26:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:26:44] "[33mGET /api/user/info?token=aba-token-1743924404337 HTTP/1.1[0m" 404 -
2025/04/06 15:26:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:26:53] "OPTIONS /api/user/info?token=aba-token-1743924413358 HTTP/1.1" 200 -
2025/04/06 15:26:53 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/user/info, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer aba-token-1743924413358

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/06 15:26:53 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/06 15:26:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:26:53] "[33mGET /api/user/info?token=aba-token-1743924413358 HTTP/1.1[0m" 404 -
2025/04/06 15:27:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:27:22] "OPTIONS /api/user/info?token=aba-token-1743924442450 HTTP/1.1" 200 -
2025/04/06 15:27:22 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/user/info, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer aba-token-1743924442450

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/06 15:27:22 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/06 15:27:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:27:22] "[33mGET /api/user/info?token=aba-token-1743924442450 HTTP/1.1[0m" 404 -
2025/04/06 15:29:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:29:14] "OPTIONS /api/user/info?token=aba-token-1743924554869 HTTP/1.1" 200 -
2025/04/06 15:29:14 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/user/info, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer aba-token-1743924554869

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/06 15:29:14 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/06 15:29:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:29:14] "[33mGET /api/user/info?token=aba-token-1743924554869 HTTP/1.1[0m" 404 -
2025/04/06 15:29:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:29:25] "OPTIONS /api/user/info?token=aba-token-1743924565315 HTTP/1.1" 200 -
2025/04/06 15:29:26 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/user/info, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer aba-token-1743924565315

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/06 15:29:26 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/06 15:29:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:29:26] "[33mGET /api/user/info?token=aba-token-1743924565315 HTTP/1.1[0m" 404 -
2025/04/06 15:31:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:31:44] "OPTIONS /api/users/info?token=aba-token-1743924704512 HTTP/1.1" 200 -
2025/04/06 15:31:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:31:45] "OPTIONS /api/users/info?token=aba-token-1743924704512 HTTP/1.1" 200 -
2025/04/06 15:37:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:37:28] "OPTIONS /api/users/info?token=aba-token-1743925047098 HTTP/1.1" 200 -
2025/04/06 15:37:28 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/users/info, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer aba-token-1743925047098

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/06 15:37:28 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/06 15:37:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:37:28] "[33mGET /api/users/info?token=aba-token-1743925047098 HTTP/1.1[0m" 404 -
2025/04/06 15:38:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:38:08] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 15:38:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:38:13] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 15:38:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:38:14] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 15:38:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:38:14] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 15:38:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:38:14] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 15:39:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:39:41] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 15:39:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:39:54] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 15:39:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:39:54] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 15:39:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:39:54] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 15:39:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:39:54] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 15:46:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:46:19] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 15:46:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:46:23] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 15:46:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:46:24] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 15:46:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:46:32] "OPTIONS /api/users/info?token=user-token-3-1743925590619 HTTP/1.1" 200 -
2025/04/06 15:46:32 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/users/info, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer user-token-3-1743925590619

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/06 15:46:32 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/06 15:46:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 15:46:32] "[33mGET /api/users/info?token=user-token-3-1743925590619 HTTP/1.1[0m" 404 -
2025/04/06 16:03:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:03:13] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/06 16:03:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:03:13] "POST /api/logout HTTP/1.1" 200 -
2025/04/06 16:03:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:03:18] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 16:03:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:03:27] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 16:03:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:03:27] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 16:14:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:14:02] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/06 16:14:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:14:02] "POST /api/logout HTTP/1.1" 200 -
2025/04/06 16:14:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:14:09] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 16:14:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:14:12] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 16:21:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:21:42] "OPTIONS /api/users/info?token=user-token-5-1743927255044 HTTP/1.1" 200 -
2025/04/06 16:21:42 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/users/info, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer user-token-5-1743927255044

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/06 16:21:42 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/06 16:21:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:21:42] "[33mGET /api/users/info?token=user-token-5-1743927255044 HTTP/1.1[0m" 404 -
2025/04/06 16:22:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:22:02] "OPTIONS /api/users/info?token=user-token-5-1743927255044 HTTP/1.1" 200 -
2025/04/06 16:22:02 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/users/info, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer user-token-5-1743927255044

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/06 16:22:02 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/06 16:22:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:22:02] "[33mGET /api/users/info?token=user-token-5-1743927255044 HTTP/1.1[0m" 404 -
2025/04/06 16:22:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:22:16] "OPTIONS /api/users/info?token=user-token-5-1743927255044 HTTP/1.1" 200 -
2025/04/06 16:22:16 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/users/info, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer user-token-5-1743927255044

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/06 16:22:16 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/06 16:22:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:22:16] "[33mGET /api/users/info?token=user-token-5-1743927255044 HTTP/1.1[0m" 404 -
2025/04/06 16:22:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:22:36] "OPTIONS /api/users/info?token=user-token-5-1743927255044 HTTP/1.1" 200 -
2025/04/06 16:22:36 flask_api __init__.py[215] not_found() ERROR: 路由未找到: /api/users/info, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer user-token-5-1743927255044

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/06 16:22:36 root __init__.py[216] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/06 16:22:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:22:36] "[33mGET /api/users/info?token=user-token-5-1743927255044 HTTP/1.1[0m" 404 -
2025/04/06 16:22:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:22:48] "OPTIONS /api/users/5 HTTP/1.1" 200 -
2025/04/06 16:22:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:22:48] "[31m[1mGET /api/users/5 HTTP/1.1[0m" 422 -
2025/04/06 16:23:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:23:07] "OPTIONS /api/users/5 HTTP/1.1" 200 -
2025/04/06 16:23:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:23:07] "[31m[1mGET /api/users/5 HTTP/1.1[0m" 422 -
2025/04/06 16:23:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:23:35] "OPTIONS /api/users/5 HTTP/1.1" 200 -
2025/04/06 16:23:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:23:35] "[31m[1mGET /api/users/5 HTTP/1.1[0m" 422 -
2025/04/06 16:23:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:23:57] "OPTIONS /api/users/5 HTTP/1.1" 200 -
2025/04/06 16:23:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:23:57] "[31m[1mGET /api/users/5 HTTP/1.1[0m" 422 -
2025/04/06 16:24:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:24:42] "OPTIONS /api/users/5 HTTP/1.1" 200 -
2025/04/06 16:24:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:24:42] "[31m[1mGET /api/users/5 HTTP/1.1[0m" 422 -
2025/04/06 16:26:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:22] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 16:26:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:25] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/06 16:26:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:25] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/06 16:26:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:25] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/06 16:26:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:26] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/06 16:26:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:26] "POST /api/login HTTP/1.1" 200 -
2025/04/06 16:26:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:27] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:26:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:27] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:26:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:31] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:26:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:32] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:26:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:32] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:26:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:33] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:26:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:33] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:26:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:33] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/06 16:26:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:33] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:26:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:33] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/06 16:26:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:33] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:26:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:26:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:36] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:26:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:38] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:26:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:38] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:26:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:38] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/06 16:26:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:38] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:26:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:38] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:26:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:38] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/06 16:26:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:38] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:26:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:42] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:26:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:26:43] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:01] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:27:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:01] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:27:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:02] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/06 16:27:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:02] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:02] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:02] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/06 16:27:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:02] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:12] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:12] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:12] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:22] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:22] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:22] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:32] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:32] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:32] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:42] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:42] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:42] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:52] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:52] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:27:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:27:52] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:02] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:02] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:02] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:12] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:12] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:12] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:23] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:23] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:23] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:25] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:28:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:25] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:28:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:26] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:26] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:28:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:27] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/06 16:28:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:27] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:27] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/06 16:28:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:27] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:37] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:37] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:37] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:47] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:47] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:47] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:57] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:57] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:28:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:28:57] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:29:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:07] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:29:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:07] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:29:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:08] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/06 16:29:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:08] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:29:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:08] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:29:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:08] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/06 16:29:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:08] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:29:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:18] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:29:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:18] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:29:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:18] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:29:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:28] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:29:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:28] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:29:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:29:28] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:06] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:30:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:06] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:30:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:06] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/06 16:30:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:06] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:06] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:06] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/06 16:30:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:06] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:17] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:17] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:17] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:27] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:27] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:27] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:37] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:37] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:37] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:44] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:44] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:44] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/06 16:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:44] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/06 16:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:47] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:30:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:48] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:30:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:49] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:49] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:30:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:49] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:30:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:50] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/06 16:30:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:50] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:50] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:50] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/06 16:30:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:50] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:30:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:54] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:30:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:58] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/06 16:30:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:30:58] "POST /api/logout HTTP/1.1" 200 -
2025/04/06 16:31:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:02] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/06 16:31:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:02] "POST /api/login HTTP/1.1" 200 -
2025/04/06 16:31:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:03] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:31:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:03] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:31:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:06] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:31:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:07] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:07] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/06 16:31:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:07] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:07] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/06 16:31:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:07] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:17] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:17] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:17] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:19] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:31:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:19] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:31:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:19] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:21] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:31:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:21] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:21] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/06 16:31:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:21] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/06 16:31:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:21] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:31] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:31] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:31] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:41] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:41] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:41] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:51] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:51] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:31:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:31:51] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:01] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:01] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:01] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:11] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:11] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:11] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:21] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:21] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:21] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:31] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:31] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:31] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:41] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:41] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:41] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:51] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:51] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:32:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:32:51] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:01] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:01] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:01] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:10] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:33:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:10] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:33:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:10] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/04/06 16:33:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:10] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:10] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:10] "[31m[1mGET /api/users/1 HTTP/1.1[0m" 403 -
2025/04/06 16:33:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:10] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:20] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:20] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:20] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:40] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:33:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:40] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:33:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:41] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:41] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:33:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:41] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:41] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:51] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:51] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:33:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:33:51] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:01] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:01] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:01] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:09] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:34:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:10] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:34:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:10] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:10] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:34:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:10] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:10] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:20] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:20] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:20] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:23] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/06 16:34:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:23] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:34:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:23] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:23] "GET /api/users/me HTTP/1.1" 200 -
2025/04/06 16:34:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:23] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:34] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:34] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:34] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:54] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:54] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:34:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:34:54] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:35:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:35:04] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:35:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:35:04] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:35:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:35:04] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:35:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:35:14] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:35:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:35:14] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:35:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:35:14] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:35:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:35:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:35:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:35:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:35:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:35:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:36:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:36:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:36:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:36:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:36:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:36:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:37:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:37:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:37:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:37:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:37:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:37:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:38:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:38:23] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:38:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:38:23] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:38:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:38:23] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:38:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:38:34] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:38:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:38:34] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:38:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:38:34] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:38:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:38:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:38:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:38:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:38:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:38:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:38:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:38:54] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:38:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:38:54] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:38:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:38:54] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:39:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:39:04] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:39:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:39:04] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:39:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:39:04] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:39:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:39:14] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:39:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:39:14] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:39:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:39:14] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:39:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:39:24] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:39:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:39:24] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:39:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:39:24] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:39:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:39:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:39:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:40:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:40:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:40:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:40:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:40:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:40:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:41:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:41:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:41:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:41:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:41:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:41:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:42:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:42:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:42:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:42:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:42:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:42:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:43:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:43:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:43:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:43:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:43:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:43:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:44:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:44:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:44:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:44:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:44:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:44:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:45:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:45:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:45:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:45:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:45:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:45:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:46:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:46:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:46:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:46:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:46:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:46:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:47:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:47:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:47:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:47:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:47:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:47:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:48:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:48:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:48:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:48:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:48:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:48:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:49:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:49:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:49:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:49:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:49:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:49:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:50:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:50:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:50:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:50:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:50:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:50:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:51:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:51:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:51:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:51:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:51:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:51:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:52:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:52:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:52:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:52:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:52:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:52:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:53:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:53:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:53:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:53:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:53:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:53:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:54:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:54:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:54:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:54:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:54:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:54:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:55:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:55:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:55:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:55:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:55:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:55:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:56:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:56:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:56:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:56:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:56:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:56:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:57:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:57:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:57:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:57:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:57:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:57:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:58:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:58:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:58:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:58:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:58:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:58:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:59:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:59:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:59:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:59:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 16:59:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 16:59:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:00:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:00:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:00:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:00:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:00:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:00:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:01:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:01:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:01:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:01:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:01:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:01:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:02:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:02:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:02:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:02:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:02:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:02:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:03:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:03:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:03:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:03:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:03:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:03:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:04:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:04:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:04:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:04:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:04:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:04:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:05:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:05:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:05:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:05:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:06:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:06:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:06:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:06:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:06:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:06:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:07:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:07:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:07:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:07:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:07:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:07:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:08:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:08:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:08:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:08:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:08:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:08:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:09:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:09:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:09:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:09:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:09:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:09:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:10:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:10:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:10:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:10:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:10:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:10:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:11:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:11:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:11:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:11:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:11:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:11:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:12:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:12:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:12:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:12:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:12:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:12:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:13:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:13:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:13:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:13:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:13:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:13:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:14:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:14:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:14:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:14:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:14:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:14:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:15:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:15:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:15:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:15:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:15:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:15:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:16:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:16:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:16:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:16:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:16:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:16:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:17:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:17:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:17:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:17:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:17:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:17:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:18:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:18:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:18:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:18:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:18:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:18:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:19:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:19:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:19:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:19:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:19:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:19:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:20:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:20:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:20:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:20:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:21:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:21:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:21:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:21:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:21:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:21:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:22:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:22:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:22:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:22:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:22:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:22:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:23:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:23:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:23:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:23:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:23:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:23:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:24:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:24:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:24:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:24:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:24:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:24:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:25:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:25:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:25:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:25:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:25:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:25:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:26:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:26:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:26:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:26:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:26:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:26:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:27:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:27:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:27:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:27:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:27:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:27:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:28:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:28:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:28:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:28:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:28:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:28:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:29:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:29:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:29:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:29:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:29:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:29:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:30:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:30:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:30:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:30:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:31:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:31:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:31:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:31:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:31:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:31:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:32:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:32:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:32:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:32:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:32:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:32:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:33:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:33:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:33:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:33:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:33:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:33:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:34:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:34:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:34:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:34:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:34:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:34:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:35:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:35:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:35:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:35:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:35:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:35:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:36:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:36:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:36:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:36:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:36:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:36:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:37:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:37:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:37:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:37:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:37:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:37:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:38:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:38:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:38:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:38:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:38:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:38:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:39:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:39:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:39:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:40:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:40:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:40:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:40:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:40:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:40:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:41:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:41:44] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:41:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:41:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 17:41:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 17:41:44] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 18:12:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 18:12:18] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 18:12:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 18:12:18] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 18:12:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 18:12:21] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 18:12:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 18:12:30] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 18:12:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 18:12:30] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/04/06 18:12:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [06/Apr/2025 18:12:30] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
