from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db, socketio
from app.parking_records import parking_records_bp
from app.parking_records.models import ParkingRecord
from app.utils.response import api_response
from app.utils.auth_helpers import get_current_user, get_current_user_id, is_admin
from app.utils.vehicle_status import check_vehicle_for_operation, is_vehicle_disabled
from datetime import datetime
import logging
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_

# 获取用户的停车记录
@parking_records_bp.route('/user', methods=['GET'])
@jwt_required()
def get_user_parking_records():
    """获取当前用户的停车记录"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status', type=int)  # 0进行中，1已完成，2异常

        # 获取排序参数
        sort_by = request.args.get('sort_by', 'id')
        sort_order = request.args.get('sort_order', 'asc')

        # 构建查询
        query = ParkingRecord.query.filter_by(user_id=user_id)

        # 应用过滤条件
        if status is not None:
            query = query.filter(ParkingRecord.status == status)

        # 根据排序参数进行排序
        if sort_by == 'entry_time':
            if sort_order.lower() == 'desc':
                query = query.order_by(ParkingRecord.entry_time.desc())
            else:
                query = query.order_by(ParkingRecord.entry_time.asc())
        elif sort_by == 'exit_time':
            if sort_order.lower() == 'desc':
                query = query.order_by(ParkingRecord.exit_time.desc())
            else:
                query = query.order_by(ParkingRecord.exit_time.asc())
        else:
            # 默认按ID排序
            if sort_order.lower() == 'desc':
                query = query.order_by(ParkingRecord.id.desc())
            else:
                query = query.order_by(ParkingRecord.id.asc())

        # 分页
        pagination = query.paginate(page=page, per_page=per_page)
        items = pagination.items

        # 获取记录详情
        records = [record.get_details(include_relations=True) for record in items]

        return api_response(
            data={
                'items': records,
                'total': pagination.total,
                'page': page,
                'per_page': per_page,
                'pages': pagination.pages
            },
            message="获取停车记录成功"
        )
    except Exception as e:
        return api_response(message=f"获取停车记录失败: {str(e)}", status="error", code=500)

# 获取停车记录详情
@parking_records_bp.route('/<int:record_id>', methods=['GET'])
@jwt_required()
def get_parking_record(record_id):
    """获取停车记录详情"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 查找记录
        record = ParkingRecord.query.get(record_id)
        if not record:
            return api_response(message="停车记录不存在", status="error", code=404)

        # 检查权限：只有记录的所有者或管理员可以查看
        if record.user_id != user_id and not is_admin():
            return api_response(message="没有权限查看此记录", status="error", code=403)

        # 获取详情
        record_data = record.get_details(include_relations=True)

        return api_response(
            data=record_data,
            message="获取停车记录详情成功"
        )
    except Exception as e:
        return api_response(message=f"获取停车记录详情失败: {str(e)}", status="error", code=500)

# 创建停车记录（开始停车）
@parking_records_bp.route('', methods=['POST'])
@jwt_required()
def create_parking_record():
    """创建停车记录（开始停车）"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        data = request.get_json()

        # 验证必要字段
        required_fields = ['vehicle_id', 'parking_lot_id', 'parking_space_id']
        for field in required_fields:
            if field not in data:
                return api_response(message=f"缺少必要字段: {field}", status="error", code=400)

        # 检查是否已有进行中的记录
        existing_record = ParkingRecord.query.filter_by(
            vehicle_id=data['vehicle_id'],
            status=0  # 进行中
        ).first()

        if existing_record:
            return api_response(
                message="此车辆已有进行中的停车记录",
                status="error",
                code=400
            )

        # 检查车位是否可用
        from app.parkinglots.models import ParkingSpace
        space = ParkingSpace.query.get(data['parking_space_id'])
        if not space:
            return api_response(message="车位不存在", status="error", code=404)

        if space.status != 0:  # 非空闲状态
            return api_response(message="车位已被占用或维护中", status="error", code=400)

        # 检查车辆是否存在
        from app.bikes.models import Bikes
        vehicle = Bikes.query.get(data['vehicle_id'])
        if not vehicle:
            return api_response(message="车辆不存在", status="error", code=404)

        # 检查车辆所有权
        if vehicle.belong_to != user_id and not is_admin():
            return api_response(message="没有操作此车辆的权限", status="error", code=403)

        # 检查车辆是否被禁用
        is_disabled, reason, _ = is_vehicle_disabled(data['vehicle_id'])
        if is_disabled:
            return api_response(
                message=f"车辆已被禁用，无法停车。{reason}",
                status="error",
                code=403
            )

        # 检查车辆是否可用于停车操作
        can_operate, operation_reason = check_vehicle_for_operation(data['vehicle_id'], "parking")
        if not can_operate:
            return api_response(
                message=operation_reason,
                status="error",
                code=403
            )

        try:
            # 使用事务锁定车位，防止并发问题
            # 再次检查车位状态，使用锁定查询
            from app.parkinglots.models import ParkingSpace
            space = ParkingSpace.query.filter(
                and_(
                    ParkingSpace.id == data['parking_space_id'],
                    ParkingSpace.status == 0  # 必须是空闲状态
                )
            ).with_for_update().first()

            if not space:
                return api_response(message="车位不存在或已被占用", status="error", code=409)

            # 创建记录
            record = ParkingRecord(
                vehicle_id=data['vehicle_id'],
                user_id=user_id,
                parking_lot_id=data['parking_lot_id'],
                parking_space_id=data['parking_space_id']
            )

            # 如果有备注信息，添加到记录中
            if 'notes' in data and data['notes']:
                record.remarks = data['notes']

            # 调用创建方法，其中包含了更新车位状态的逻辑
            record = record.create()

            # 记录日志
            current_app.logger.info(f"用户 {user_id} 成功创建停车记录 ID: {record.id}, 车辆ID: {record.vehicle_id}, 车位ID: {record.parking_space_id}")

            # 获取记录详情
            record_details = record.get_details(include_relations=True)

            # 发送WebSocket通知
            try:
                # 通知停车场房间
                socketio.emit('parking_space_updated', {
                    'parking_lot_id': data['parking_lot_id'],
                    'parking_space_id': data['parking_space_id'],
                    'status': 1,  # 已占用
                    'vehicle_id': data['vehicle_id']
                }, room=f'parking_lot_{data["parking_lot_id"]}')

                # 通知用户
                socketio.emit('parking_record_created', {
                    'record': record_details
                }, room=f'user_{user_id}')

                # 通知管理员
                socketio.emit('admin_notification', {
                    'type': 'parking_record_created',
                    'record': record_details,
                    'user_id': user_id
                }, room='admin')

                current_app.logger.info(f"实时通知发送成功: 停车记录创建 ID={record.id}")
            except Exception as ws_error:
                current_app.logger.error(f"发送WebSocket通知失败: {str(ws_error)}")

            return api_response(
                data=record_details,
                message="停车记录创建成功",
                code=201
            )

        except ValueError as ve:
            # 特定的业务验证错误
            db.session.rollback()
            current_app.logger.warning(f"创建停车记录验证失败: {str(ve)}")
            return api_response(message=str(ve), status="error", code=400)

        except SQLAlchemyError as se:
            # 数据库错误
            db.session.rollback()
            current_app.logger.error(f"创建停车记录数据库错误: {str(se)}")
            return api_response(message="数据库操作失败，请重试", status="error", code=500)

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建停车记录未知错误: {str(e)}")
        return api_response(message=f"创建停车记录失败: {str(e)}", status="error", code=500)

# 结束停车记录
@parking_records_bp.route('/<int:record_id>/end', methods=['PUT'])
@jwt_required()
def end_parking_record(record_id):
    """结束停车记录"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 查找记录
        record = ParkingRecord.query.get(record_id)
        if not record:
            return api_response(message="停车记录不存在", status="error", code=404)

        # 检查权限：只有记录的所有者或管理员可以操作
        if record.user_id != user_id and not is_admin():
            return api_response(message="没有权限操作此记录", status="error", code=403)

        # 检查记录状态
        if record.status != 0:  # 不是进行中状态
            return api_response(message="此停车记录已结束，无法再次结束", status="error", code=400)

        try:
            # 获取备注信息
            data = request.get_json() or {}
            remarks = data.get('remarks')

            # 使用锁定查询确保记录不被并发修改
            locked_record = ParkingRecord.query.filter(
                and_(
                    ParkingRecord.id == record_id,
                    ParkingRecord.status == 0  # 必须是进行中状态
                )
            ).with_for_update().first()

            if not locked_record:
                return api_response(message="停车记录不存在或已结束", status="error", code=409)

            # 结束停车
            locked_record.end_parking(remarks)

            # 记录日志
            current_app.logger.info(f"用户 {user_id} 成功结束停车记录 ID: {record_id}, 车辆ID: {locked_record.vehicle_id}, 车位ID: {locked_record.parking_space_id}")

            # 获取详细信息
            record_details = locked_record.get_details(include_relations=True)

            # 发送WebSocket通知
            try:
                # 通知停车场房间
                socketio.emit('parking_space_updated', {
                    'parking_lot_id': locked_record.parking_lot_id,
                    'parking_space_id': locked_record.parking_space_id,
                    'status': 0,  # 空闲
                    'vehicle_id': None
                }, room=f'parking_lot_{locked_record.parking_lot_id}')

                # 通知用户
                socketio.emit('parking_record_ended', {
                    'record': record_details
                }, room=f'user_{user_id}')

                # 通知管理员
                socketio.emit('admin_notification', {
                    'type': 'parking_record_ended',
                    'record': record_details,
                    'user_id': user_id
                }, room='admin')

                current_app.logger.info(f"实时通知发送成功: 停车记录结束 ID={record_id}")
            except Exception as ws_error:
                current_app.logger.error(f"发送WebSocket通知失败: {str(ws_error)}")

            return api_response(
                data=record_details,
                message="停车记录已成功结束"
            )

        except ValueError as ve:
            # 特定的业务验证错误
            db.session.rollback()
            current_app.logger.warning(f"结束停车记录验证失败: {str(ve)}")
            return api_response(message=str(ve), status="error", code=400)

        except SQLAlchemyError as se:
            # 数据库错误
            db.session.rollback()
            current_app.logger.error(f"结束停车记录数据库错误: {str(se)}")
            return api_response(message="数据库操作失败，请重试", status="error", code=500)

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"结束停车记录未知错误: {str(e)}")
        return api_response(message=f"结束停车记录失败: {str(e)}", status="error", code=500)

# 获取所有停车记录
@parking_records_bp.route('', methods=['GET'])
@jwt_required()
def get_all_parking_records():
    """获取所有停车记录"""
    try:
        # 获取当前用户ID和角色
        user_id = get_current_user_id()
        is_admin_user = is_admin()

        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status', type=int)
        query_user_id = request.args.get('user_id', type=int)
        parking_lot_id = request.args.get('parking_lot_id', type=int)
        vehicle_id = request.args.get('vehicle_id', type=int)

        # 获取排序参数
        sort_by = request.args.get('sort_by', 'id')
        sort_order = request.args.get('sort_order', 'asc')

        # 构建查询
        query = ParkingRecord.query

        # 非管理员只能查看自己的记录
        if not is_admin_user:
            query = query.filter(ParkingRecord.user_id == user_id)
        # 管理员可以按用户ID筛选
        elif query_user_id is not None:
            query = query.filter(ParkingRecord.user_id == query_user_id)

        # 应用其他过滤条件
        if status is not None:
            query = query.filter(ParkingRecord.status == status)
        if parking_lot_id is not None:
            query = query.filter(ParkingRecord.parking_lot_id == parking_lot_id)
        if vehicle_id is not None:
            query = query.filter(ParkingRecord.vehicle_id == vehicle_id)

        # 根据排序参数进行排序
        if sort_by == 'entry_time':
            if sort_order.lower() == 'desc':
                query = query.order_by(ParkingRecord.entry_time.desc())
            else:
                query = query.order_by(ParkingRecord.entry_time.asc())
        elif sort_by == 'exit_time':
            if sort_order.lower() == 'desc':
                query = query.order_by(ParkingRecord.exit_time.desc())
            else:
                query = query.order_by(ParkingRecord.exit_time.asc())
        else:
            # 默认按ID排序
            if sort_order.lower() == 'desc':
                query = query.order_by(ParkingRecord.id.desc())
            else:
                query = query.order_by(ParkingRecord.id.asc())

        # 分页
        pagination = query.paginate(page=page, per_page=per_page)
        items = pagination.items

        # 获取记录详情
        records = [record.get_details(include_relations=True) for record in items]

        return api_response(
            data={
                'items': records,
                'total': pagination.total,
                'page': page,
                'per_page': per_page,
                'pages': pagination.pages
            },
            message="获取停车记录成功"
        )
    except Exception as e:
        current_app.logger.error(f"获取停车记录失败: {str(e)}")
        return api_response(message=f"获取停车记录失败: {str(e)}", status="error", code=500)

# 管理员获取所有停车记录 (保留旧路径以兼容)
@parking_records_bp.route('/all', methods=['GET'])
@jwt_required()
def get_all_parking_records_legacy():
    """管理员获取所有停车记录（兼容旧路径）"""
    return get_all_parking_records()

# 删除停车记录
@parking_records_bp.route('/<int:record_id>', methods=['DELETE'])
@jwt_required()
def delete_parking_record(record_id):
    """删除停车记录（仅管理员可用）"""
    try:
        # 检查用户权限
        if not is_admin():
            return api_response(message="需要管理员权限才能删除停车记录", status="error", code=403)

        # 查找记录
        record = ParkingRecord.query.get(record_id)
        if not record:
            return api_response(message="停车记录不存在", status="error", code=404)

        # 如果记录状态为进行中，需要先结束停车
        if record.status == 0:
            try:
                # 获取车位信息
                from app.parkinglots.models import ParkingSpace
                space = ParkingSpace.query.get(record.parking_space_id)
                if space:
                    # 更新车位状态为空闲
                    space.status = 0
                    space.current_vehicle_id = None
                    db.session.flush()
            except Exception as e:
                current_app.logger.error(f"更新车位状态失败: {str(e)}")

        # 删除记录
        db.session.delete(record)
        db.session.commit()

        current_app.logger.info(f"管理员成功删除停车记录 ID: {record_id}")

        return api_response(
            message="停车记录已成功删除",
            code=200
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除停车记录失败: {str(e)}")
        return api_response(message=f"删除停车记录失败: {str(e)}", status="error", code=500)

# 检查车辆是否有进行中的停车记录
@parking_records_bp.route('/check-active', methods=['GET'])
@jwt_required()
def check_vehicle_active_parking():
    """检查车辆是否有进行中的停车记录"""
    try:
        # 获取当前用户ID
        user_id = get_current_user_id()
        if not user_id:
            return api_response(message="未登录或无法获取用户信息", status="error", code=401)

        # 获取车辆ID
        vehicle_id = request.args.get('vehicle_id', type=int)
        if not vehicle_id:
            return api_response(message="缺少车辆ID参数", status="error", code=400)

        # 检查车辆是否存在
        from app.bikes.models import Bikes
        vehicle = Bikes.query.get(vehicle_id)
        if not vehicle:
            return api_response(message="车辆不存在", status="error", code=404)

        # 检查权限：只有车辆的所有者或管理员可以查询
        if vehicle.belong_to != user_id and not is_admin():
            return api_response(message="没有权限查询此车辆", status="error", code=403)

        # 检查车辆是否被禁用（仅提供信息，不阻止查询）
        is_disabled, reason, _ = is_vehicle_disabled(vehicle_id)
        disabled_info = {
            "is_disabled": is_disabled,
            "reason": reason if is_disabled else None
        }

        # 查询车辆的进行中停车记录
        active_record = ParkingRecord.query.filter_by(
            vehicle_id=vehicle_id,
            status=0  # 进行中
        ).first()

        if active_record:
            # 获取记录详情
            record_details = active_record.get_details(include_relations=True)

            return api_response(
                data={
                    'hasActiveParking': True,
                    'record': record_details,
                    'vehicle_status': disabled_info
                },
                message="车辆有进行中的停车记录"
            )
        else:
            return api_response(
                data={
                    'hasActiveParking': False,
                    'record': None,
                    'vehicle_status': disabled_info
                },
                message="车辆没有进行中的停车记录"
            )
    except Exception as e:
        current_app.logger.error(f"检查车辆停车状态失败: {str(e)}")
        return api_response(message=f"检查车辆停车状态失败: {str(e)}", status="error", code=500)

# 获取停车统计信息
@parking_records_bp.route('/stats', methods=['GET', 'OPTIONS'])
def get_parking_stats():
    """
    获取停车统计信息
    此接口不需要JWT验证，允许未登录用户访问
    """
    # 记录请求信息
    current_app.logger.info(f"收到停车统计请求: {request.args}")
    current_app.logger.info(f"请求头: {request.headers}")

    # 此接口不需要JWT验证，允许未登录用户访问
    user_id = None
    is_admin_user = False
    current_app.logger.info("以匿名用户身份访问停车统计API")

    # 允许所有用户查看全局统计信息

    try:
        # 获取查询参数
        scope = request.args.get('scope', 'all' if user_id is None else 'user')  # 未登录用户默认查看全局统计
        date_range = request.args.get('date_range', 'week')  # 默认查询一周
        parking_lot_id = request.args.get('parking_lot_id', type=int)  # 可选停车场筛选
        status_filter = request.args.get('status', type=int)  # 可选状态筛选

        # 获取前端传递的用户ID，如果有的话优先使用
        request_user_id = request.args.get('user_id', type=int)
        if request_user_id and (is_admin_user or (user_id is not None and request_user_id == user_id)):
            user_id = request_user_id

        current_app.logger.info(f"统计API参数: user_id={user_id}, scope={scope}, date_range={date_range}, status={status_filter}")

        # 构建查询
        query = ParkingRecord.query

        # 如果不是管理员或者范围是用户，只查询当前用户的记录
        if user_id is not None and (not is_admin_user or scope == 'user'):
            query = query.filter(ParkingRecord.user_id == user_id)
            current_app.logger.info(f"用户筛选已应用: user_id={user_id}")

        # 如果指定了状态筛选，进行筛选
        if status_filter is not None:
            query = query.filter(ParkingRecord.status == status_filter)
            current_app.logger.info(f"状态筛选已应用: status={status_filter}")

        # 如果指定了停车场，进行筛选
        if parking_lot_id:
            query = query.filter(ParkingRecord.parking_lot_id == parking_lot_id)

        # 基本统计信息
        total_records_count = query.count()
        active_records_count = query.filter(ParkingRecord.status == 0).count()
        completed_records_count = query.filter(ParkingRecord.status == 1).count()
        abnormal_records_count = query.filter(ParkingRecord.status == 2).count()

        # 时间范围统计
        from sqlalchemy import func
        today = datetime.now().date()

        # 根据日期范围参数设置查询时间范围
        from datetime import timedelta
        if date_range == 'today':
            start_date = today
        elif date_range == 'week':
            start_date = today - timedelta(days=6)  # 过去7天
        elif date_range == 'month':
            start_date = today.replace(day=1)  # 本月第一天
        elif date_range == 'year':
            start_date = today.replace(month=1, day=1)  # 本年第一天
        else:  # 默认为今天
            start_date = today

        # 在时间范围内的记录 - 使用SQLite兼容的日期函数
        start_date_str = start_date.strftime('%Y-%m-%d')
        today_str = today.strftime('%Y-%m-%d')

        period_records = query.filter(
            func.date(ParkingRecord.entry_time) >= start_date_str,
            func.date(ParkingRecord.entry_time) <= today_str
        ).count()

        # 今日统计 - 使用SQLite兼容的日期函数
        today_records = query.filter(
            func.date(ParkingRecord.entry_time) == today_str
        ).count()

        # 按停车场统计
        from app.parkinglots.models import ParkingLot
        lots = ParkingLot.query.all()
        lots_stats = []

        for lot in lots:
            # 使用与主查询相同的筛选条件 (用于统计用户相关的记录数)
            lot_query = query.filter(ParkingRecord.parking_lot_id == lot.id)
            lot_records = lot_query.count()

            # 今日停车记录 - 使用SQLite兼容的日期函数 (用户筛选)
            lot_today_query = lot_query.filter(func.date(ParkingRecord.entry_time) == today_str)
            lot_today = lot_today_query.count()

            # 计算全局停车场使用率 (不受用户筛选影响)
            from app.parkinglots.models import ParkingSpace
            total_spaces = ParkingSpace.query.filter_by(parking_lot_id=lot.id).count()
            occupied_spaces = ParkingSpace.query.filter_by(parking_lot_id=lot.id, status=1).count()

            # 全局活跃停车记录数 (不受用户筛选影响)
            global_lot_active = ParkingRecord.query.filter_by(
                parking_lot_id=lot.id,
                status=0  # 进行中
            ).count()

            # 确保总车位数不为0，避免除以0错误
            utilization_rate = 0
            if total_spaces > 0:
                # 使用全局活跃记录数量计算使用率，确保显示真实的停车场使用情况
                utilization_rate = round(global_lot_active / total_spaces * 100, 2)
                current_app.logger.info(f"停车场 {lot.name} 的使用率计算: {global_lot_active}/{total_spaces} = {utilization_rate}%")

            # 更新停车场的车位数据，确保前端显示正确
            if lot.total_spaces != total_spaces or lot.occupied_spaces != occupied_spaces:
                lot.total_spaces = total_spaces
                lot.occupied_spaces = occupied_spaces
                db.session.add(lot)
                db.session.flush()
                current_app.logger.info(f"更新停车场 {lot.name} 的车位数据: 总车位={total_spaces}, 已占用={occupied_spaces}")

            lots_stats.append({
                'id': lot.id,
                'name': lot.name,
                'total_records': lot_records,
                'active_records': global_lot_active,
                'today_records': lot_today,
                'total_spaces': total_spaces,
                'occupied_spaces': occupied_spaces,
                'utilization_rate': utilization_rate
            })

        # 按车辆类型统计
        from app.bikes.models import Bikes
        from sqlalchemy import desc

        # 构建基本查询
        vehicle_stats_query = db.session.query(
            func.coalesce(Bikes.b_type, '未知类型').label('b_type'),
            func.count(ParkingRecord.id).label('count')
        ).join(
            ParkingRecord, ParkingRecord.vehicle_id == Bikes.b_id
        )

        # 如果不是管理员或者范围是用户，只查询当前用户的记录
        if user_id is not None and (not is_admin_user or scope == 'user'):
            vehicle_stats_query = vehicle_stats_query.filter(ParkingRecord.user_id == user_id)

        # 如果指定了状态筛选，进行筛选
        if status_filter is not None:
            vehicle_stats_query = vehicle_stats_query.filter(ParkingRecord.status == status_filter)

        # 如果指定了停车场，进行筛选
        if parking_lot_id:
            vehicle_stats_query = vehicle_stats_query.filter(ParkingRecord.parking_lot_id == parking_lot_id)

        # 完成查询
        vehicle_stats = vehicle_stats_query.group_by(
            func.coalesce(Bikes.b_type, '未知类型')
        ).order_by(
            desc('count')
        ).all()

        vehicle_type_stats = [
            {'type': type_, 'count': count} for type_, count in vehicle_stats
        ]

        # 最近7天的停车记录数量
        from datetime import timedelta
        last_week = today - timedelta(days=6)

        # 使用SQL聚合查询一次性获取所有日期的数据
        from sqlalchemy import func, case

        # 构建基本查询
        # 注意：SQLAlchemy 2.0+ 中 case() 函数的语法已更改，不再接受列表作为参数
        # 使用 func.date() 返回的是字符串格式的日期，不是日期对象
        current_app.logger.info("构建每日统计查询")
        daily_stats_query = db.session.query(
            func.date(ParkingRecord.entry_time).label('date'),  # 这会返回字符串格式的日期
            func.count().label('count'),
            func.sum(
                case((ParkingRecord.status == 1, 1), else_=0)
            ).label('completed'),
            func.sum(
                case((ParkingRecord.status == 0, 1), else_=0)
            ).label('active')
        )

        # 应用与主查询相同的筛选条件
        if user_id is not None and (not is_admin_user or scope == 'user'):
            daily_stats_query = daily_stats_query.filter(ParkingRecord.user_id == user_id)

        if parking_lot_id:
            daily_stats_query = daily_stats_query.filter(ParkingRecord.parking_lot_id == parking_lot_id)

        # 如果指定了状态筛选，应用筛选
        if status_filter is not None:
            daily_stats_query = daily_stats_query.filter(ParkingRecord.status == status_filter)

        # 限制日期范围 - 使用SQLite兼容的日期函数
        last_week_str = last_week.strftime('%Y-%m-%d')
        today_str = today.strftime('%Y-%m-%d')

        # 使用SQLite的日期函数进行过滤
        daily_stats_query = daily_stats_query.filter(
            func.date(ParkingRecord.entry_time) >= last_week_str,
            func.date(ParkingRecord.entry_time) <= today_str
        )

        # 记录查询条件
        current_app.logger.info(f"日期范围过滤: {last_week_str} 至 {today_str}")

        # 按日期分组并获取结果
        daily_stats_results = daily_stats_query.group_by(func.date(ParkingRecord.entry_time)).all()

        # 记录查询结果
        current_app.logger.info(f"每日统计查询结果数量: {len(daily_stats_results)}")
        for row in daily_stats_results:
            current_app.logger.info(f"日期: {row.date}, 类型: {type(row.date)}, 总数: {row.count}, 已完成: {row.completed}, 进行中: {row.active}")

        # 将查询结果转换为字典列表
        daily_stats_dict = {}
        for row in daily_stats_results:
            # 检查日期类型并适当处理
            if isinstance(row.date, str):
                date_str = row.date  # 如果已经是字符串，直接使用
            else:
                # 如果是日期对象，则格式化为字符串
                date_str = row.date.strftime('%Y-%m-%d')

            daily_stats_dict[date_str] = {
                'date': date_str,
                'count': row.count,
                'completed': row.completed or 0,  # 处理 None 值
                'active': row.active or 0  # 处理 None 值
            }
            current_app.logger.info(f"处理日期 {date_str}: count={row.count}, completed={row.completed}, active={row.active}")

        # 确保所有日期都有数据
        daily_stats = []
        current_app.logger.info(f"生成最近7天的每日统计数据")
        current_app.logger.info(f"日期字典中的键: {list(daily_stats_dict.keys())}")

        for i in range(7):
            day = last_week + timedelta(days=i)
            day_str = day.strftime('%Y-%m-%d')

            # 检查日期是否在字典中
            if day_str in daily_stats_dict:
                current_app.logger.info(f"日期 {day_str} 有数据: {daily_stats_dict[day_str]}")
                daily_stats.append(daily_stats_dict[day_str])
            else:
                current_app.logger.info(f"日期 {day_str} 没有数据，添加默认值")
                # 如果没有数据，添加默认值
                daily_stats.append({
                    'date': day_str,
                    'count': 0,
                    'completed': 0,
                    'active': 0
                })

        # 记录日志
        current_app.logger.info(f"生成的每日统计数据: {daily_stats}")

        # 添加时间范围统计信息
        period_stats = {
            'label': {
                'today': '今日',
                'week': '本周',
                'month': '本月',
                'year': '本年'
            }.get(date_range, date_range),
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': today.strftime('%Y-%m-%d'),
            'total_records': period_records
        }

        # 添加平均停车时长统计
        # 首先检查是否有已完成的记录
        current_app.logger.info(f"检查已完成的停车记录数量: {completed_records_count}")

        # 设置默认值
        avg_duration_hours = 0

        if completed_records_count > 0:
            # 只有在有已完成记录的情况下才执行平均时长查询
            # 使用直接SQL查询计算平均停车时长，避免SQLAlchemy的复杂转换可能导致的问题
            current_app.logger.info("使用直接SQL查询计算平均停车时长")

            try:
                # 导入text函数
                from sqlalchemy import text

                # 构建基本SQL查询
                sql_query = text("""
                    SELECT AVG((julianday(exit_time) - julianday(entry_time)) * 24) as avg_hours
                    FROM parking_records
                    WHERE status = 1
                """)

                # 构建查询参数字典
                params = {}

                # 添加用户筛选
                if user_id is not None and (not is_admin_user or scope == 'user'):
                    sql_query = text(sql_query.text + " AND user_id = :user_id")
                    params['user_id'] = user_id

                # 添加停车场筛选
                if parking_lot_id:
                    sql_query = text(sql_query.text + " AND parking_lot_id = :parking_lot_id")
                    params['parking_lot_id'] = parking_lot_id

                # 记录SQL查询
                current_app.logger.info(f"平均停车时长SQL查询: {sql_query}, 参数: {params}")

                # 执行查询
                result = db.session.execute(sql_query, params).scalar()
                current_app.logger.info(f"SQL查询结果: {result}")

                # 如果结果为None，设置为0
                if result is None:
                    current_app.logger.info("SQL查询返回None，设置平均停车时长为0")
                else:
                    avg_duration_hours = round(float(result), 2)
                    current_app.logger.info(f"计算得到的平均停车时长: {avg_duration_hours} 小时")
            except Exception as e:
                current_app.logger.error(f"执行SQL查询时出错: {str(e)}")
                avg_duration_hours = 0
        else:
            # 如果没有已完成记录，直接设置为0
            current_app.logger.info("没有已完成的停车记录，平均停车时长设为0")

        # 添加高峰时段统计
        peak_hour_query = db.session.query(
            func.extract('hour', ParkingRecord.entry_time).label('hour'),
            func.count(ParkingRecord.id).label('count')
        ).filter(ParkingRecord.status == 1)  # 只考虑已完成的停车记录

        # 应用相同的筛选条件
        if user_id is not None and (not is_admin_user or scope == 'user'):
            peak_hour_query = peak_hour_query.filter(ParkingRecord.user_id == user_id)
        if parking_lot_id:
            peak_hour_query = peak_hour_query.filter(ParkingRecord.parking_lot_id == parking_lot_id)
        if status_filter is not None:
            peak_hour_query = peak_hour_query.filter(ParkingRecord.status == status_filter)

        # 按小时分组并获取结果
        hourly_stats_query = peak_hour_query.group_by('hour').order_by('hour').all()

        # 创建24小时的统计数据
        hourly_stats = []
        for hour in range(24):
            hourly_stats.append({
                'hour': hour,
                'count': 0
            })

        # 填充实际数据
        for hour_stat in hourly_stats_query:
            hour, count = hour_stat
            if hour is not None and 0 <= int(hour) < 24:
                hourly_stats[int(hour)]['count'] = count

        # 记录小时统计数据
        current_app.logger.info(f"小时统计数据: {hourly_stats}")

        # 获取高峰时段
        peak_hour_stats = peak_hour_query.group_by('hour').order_by(desc('count')).limit(1).all()

        # 确定高峰时段
        peak_hour = None
        if peak_hour_stats and len(peak_hour_stats) > 0:
            hour, _ = peak_hour_stats[0]
            if hour is not None:
                peak_hour = f"{int(hour)}:00-{int(hour)+1}:00"

        # 添加停车时长分布统计
        # 构建基本查询，只查询已完成的停车记录
        duration_query = query.filter(ParkingRecord.status == 1)

        # 获取所有已完成的停车记录
        completed_parking_records = duration_query.all()

        # 初始化时长分布统计
        duration_stats = [
            { 'name': '<1小时', 'value': 0 },
            { 'name': '1-3小时', 'value': 0 },
            { 'name': '3-6小时', 'value': 0 },
            { 'name': '6-12小时', 'value': 0 },
            { 'name': '12-24小时', 'value': 0 },
            { 'name': '1天以上', 'value': 0 }
        ]

        # 遍历记录，统计时长分布
        for record in completed_parking_records:
            if record.exit_time and record.entry_time:
                # 计算停车时长（小时）
                delta = record.exit_time - record.entry_time
                hours = delta.total_seconds() / 3600

                # 分类统计
                if hours < 1:
                    duration_stats[0]['value'] += 1
                elif hours < 3:
                    duration_stats[1]['value'] += 1
                elif hours < 6:
                    duration_stats[2]['value'] += 1
                elif hours < 12:
                    duration_stats[3]['value'] += 1
                elif hours < 24:
                    duration_stats[4]['value'] += 1
                else:
                    duration_stats[5]['value'] += 1

        # 移除平均停车时长计算功能
        current_app.logger.info("平均停车时长功能已移除")

        return api_response(
            data={
                'overview': {
                    'total_records': total_records_count,
                    'active_records': active_records_count,
                    'completed_records': completed_records_count,
                    'abnormal_records': abnormal_records_count,
                    'today_records': today_records
                },
                'period_stats': period_stats,
                'parking_lots': lots_stats,
                'vehicle_types': vehicle_type_stats,
                'daily_stats': daily_stats,
                'duration_stats': duration_stats,
                'peak_hour': peak_hour,
                'hourly_stats': hourly_stats  # 添加小时统计数据
            },
            message="获取停车统计信息成功"
        )
    except Exception as e:
        # 记录详细错误信息
        import traceback
        error_traceback = traceback.format_exc()
        current_app.logger.error(f"获取停车统计信息失败: {str(e)}\n{error_traceback}")

        # 返回错误响应
        return api_response(message=f"获取停车统计信息失败: {str(e)}", status="error", code=500)