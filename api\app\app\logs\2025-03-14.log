2025/03/14 14:18:42 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:18:48 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:19:52 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:21:16 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:23:17 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:26:35 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:30:35 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:30:42 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:30:46 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:39:14 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:39:48 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:41:08 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:41:21 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:41:52 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:42:01 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:43:08 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:43:50 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:44:10 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:45:26 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:45:26 flask_migrate __init__.py[113] wrapped() ERROR: Error: Directory migrations already exists and is not empty
2025/03/14 14:45:46 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:45:57 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:47:00 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:47:12 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:47:18 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:47:23 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:50:42 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 14:50:42 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 14:50:42 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 14:50:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:50:45] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 14:50:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:50:45] "[33mPOST /api/login HTTP/1.1[0m" 404 -
2025/03/14 14:50:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:50:58] "OPTIONS /api/check-username HTTP/1.1" 200 -
2025/03/14 14:50:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:50:58] "POST /api/check-username HTTP/1.1" 200 -
2025/03/14 14:51:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:04] "OPTIONS /api/check-username HTTP/1.1" 200 -
2025/03/14 14:51:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:04] "POST /api/check-username HTTP/1.1" 200 -
2025/03/14 14:51:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:04] "OPTIONS /api/register HTTP/1.1" 200 -
2025/03/14 14:51:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:04] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/03/14 14:51:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:07] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 14:51:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:07] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 14:51:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:07] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 14:51:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:07] "GET /api/users HTTP/1.1" 200 -
2025/03/14 14:51:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:11] "GET /api/users HTTP/1.1" 200 -
2025/03/14 14:51:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:11] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 14:51:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:11] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 14:51:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:23] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 14:51:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:23] "GET /api/users HTTP/1.1" 200 -
2025/03/14 14:51:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:25] "GET /api/users HTTP/1.1" 200 -
2025/03/14 14:51:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:26] "GET /api/users HTTP/1.1" 200 -
2025/03/14 14:51:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:28] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 14:51:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:51:28] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 14:52:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:52:03] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 14:52:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:52:03] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/14 14:52:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:52:09] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 14:52:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:52:09] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/14 14:52:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:52:22] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 14:52:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:52:22] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/14 14:53:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:53:36] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 14:53:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:53:36] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 14:53:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:53:36] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 14:53:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:53:38] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 14:53:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:53:38] "GET /api/users HTTP/1.1" 200 -
2025/03/14 14:53:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:53:39] "GET /api/users HTTP/1.1" 200 -
2025/03/14 14:53:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:53:47] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 14:53:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:53:47] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 14:53:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:53:49] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 14:53:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:53:49] "GET /api/users HTTP/1.1" 200 -
2025/03/14 14:59:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:59:00] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 14:59:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 14:59:00] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:01:37 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:01:53 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:02:00 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:02:36 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:02:36 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 15:02:36 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 15:02:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:44] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 15:02:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:44] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 15:02:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:44] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:02:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:44] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:02:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:44] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:02:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:50] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:02:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:50] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:02:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:53] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:02:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:53] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:02:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:55] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:02:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:57] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:02:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:57] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:02:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:57] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:02:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:58] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:02:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:02:58] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:03:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:03:07] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:03:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:03:07] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/14 15:09:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:09:21] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:09:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:09:21] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:10:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:10:00] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:10:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:10:00] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:10:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:10:44] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:10:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:10:44] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:11:26 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:11:26 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 15:11:26 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 15:11:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:37] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 15:11:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:37] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 15:11:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:37] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:11:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:37] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:11:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:37] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:11:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:37] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:11:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:45] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:11:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:45] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:11:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:50] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:11:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:50] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:11:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:52] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:11:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:52] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:11:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:58] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:11:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:11:58] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:12:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:12:25] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:12:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:12:25] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/14 15:14:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:14:06] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:14:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:14:07] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:25:38 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:25:38 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 15:25:38 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 15:25:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:25:50] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 15:25:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:25:50] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 15:25:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:25:50] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:25:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:25:50] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:25:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:25:51] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:25:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:25:55] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:25:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:25:55] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:26:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:05] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:26:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:05] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/14 15:26:19 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:26:24 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:26:29 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:26:29 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 15:26:29 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 15:26:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:41] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 15:26:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:41] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 15:26:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:41] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:26:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:41] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:26:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:41] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:26:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:41] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:26:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:43] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:26:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:45] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:26:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:46] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:26:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:46] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:26:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:49] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:26:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:49] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:26:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:57] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:26:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:26:57] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/14 15:31:14 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:31:19 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:31:23 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:31:23 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 15:31:23 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 15:31:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:31:28] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:31:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:31:28] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:31:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:31:38] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 15:31:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:31:38] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 15:31:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:31:38] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:31:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:31:39] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:31:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:31:39] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:31:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:31:39] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:31:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:31:41] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:31:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:31:42] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:31:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:31:55] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:31:55 flask_api routes.py[50] create_user() INFO: Received user creation request with data: {'u_name': 'wc', 'u_role': 'admin', 'u_pwd': '123456', 'u_belong': 'jg', 'u_phone': 18852182417}
2025/03/14 15:31:55 flask_api routes.py[90] create_user() INFO: User created successfully: {'u_id': 2, 'u_name': 'wc', 'u_role': 'admin', 'u_belong': 'jg', 'u_phone': 18852182417, 'bikes': []}
2025/03/14 15:31:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:31:55] "[35m[1mPOST /api/users HTTP/1.1[0m" 201 -
2025/03/14 15:32:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:32:04] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:32:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:32:04] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:32:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:32:06] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:32:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:32:07] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:32:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:32:07] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:32:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:32:16] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:32:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:32:16] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/14 15:32:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:32:23] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:32:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:32:23] "[31m[1mPOST /api/bikes HTTP/1.1[0m" 422 -
2025/03/14 15:36:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:36:00] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:36:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:36:00] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:36:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:36:55] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:36:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:36:55] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:38:10 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:38:14 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 15:38:14 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 15:38:14 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 15:38:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:38:29] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 15:38:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:38:29] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 15:38:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:38:29] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:38:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:38:29] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:38:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:38:29] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:38:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:38:29] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:38:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:38:32] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:38:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:38:33] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:38:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:38:41] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:38:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:38:41] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/03/14 15:38:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:38:41] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:39:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:39:46] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:39:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:39:46] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:39:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:39:51] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:39:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:39:51] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:40:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:40:29] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:40:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:40:29] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:40:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:40:54] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:40:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:40:54] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:41:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:41:08] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:41:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:41:08] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 15:46:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:46:02] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:46:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:46:02] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:46:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:46:13] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 15:46:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:46:13] "GET /api/users HTTP/1.1" 200 -
2025/03/14 15:55:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:55:47] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 15:55:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 15:55:47] "[31m[1mGET /api/bikes HTTP/1.1[0m" 401 -
2025/03/14 16:18:45 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 16:18:45 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 16:18:45 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 16:18:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:18:53] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 16:18:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:18:53] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 16:18:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:18:53] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:18:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:18:53] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:19:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:19:03] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:19:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:19:03] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:19:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:19:16] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:19:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:19:16] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:19:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:19:43] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:19:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:19:43] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:19:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:19:43] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:19:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:19:43] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:20:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:20:10] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:20:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:20:10] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:22:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:22:46] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:22:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:22:46] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:26:16 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 16:26:16 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 16:26:16 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 16:26:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:26:25] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 16:26:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:26:25] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 16:26:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:26:25] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:26:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:26:25] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:26:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:26:26] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:26:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:26:26] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:26:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:26:39] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:26:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:26:39] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:26:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:26:39] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:26:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:26:39] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:27:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:27:06] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:27:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:27:06] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:27:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:27:22] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:27:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:27:22] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:27:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:27:44] "OPTIONS /api/users/ HTTP/1.1" 200 -
2025/03/14 16:27:44 flask_api __init__.py[146] handle_exception() ERROR: 未处理的异常: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025/03/14 16:27:44 flask_api __init__.py[148] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.

2025/03/14 16:27:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:27:44] "[35m[1mPUT /api/users/ HTTP/1.1[0m" 500 -
2025/03/14 16:33:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:33:01] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:33:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:33:01] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:33:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:33:50] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:33:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:33:50] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:36:29 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 16:36:29 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 16:36:29 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 16:36:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:36:38] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 16:36:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:36:38] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 16:36:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:36:38] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:36:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:36:38] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:36:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:36:44] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:36:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:36:44] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:38:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:38:06] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:38:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:38:11] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:38:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:38:11] "OPTIONS /api/register HTTP/1.1" 200 -
2025/03/14 16:38:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:38:11] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/03/14 16:38:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:38:13] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 16:38:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:38:13] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 16:38:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:38:13] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:38:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:38:13] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:38:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:38:18] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:38:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:38:18] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:40:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:40:49] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 16:40:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:40:49] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 16:40:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:40:49] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:40:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:40:49] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:45:27 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 16:45:27 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 16:45:27 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 16:46:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:05] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:46:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:09] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:46:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:09] "OPTIONS /api/register HTTP/1.1" 200 -
2025/03/14 16:46:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:09] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/03/14 16:46:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:18] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 16:46:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:18] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 16:46:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:18] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:46:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:18] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:46:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:24] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 16:46:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:24] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 16:46:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:24] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:46:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:24] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:46:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:41] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 16:46:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:41] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 16:46:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:41] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:46:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:46:41] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:47:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:47:28] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:47:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:47:31] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:47:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:47:34] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:47:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:47:38] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:47:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:47:38] "OPTIONS /api/register HTTP/1.1" 200 -
2025/03/14 16:47:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:47:38] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/03/14 16:47:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:47:41] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 16:47:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:47:41] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 16:47:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:47:41] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:47:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:47:41] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:50:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:50:42] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:50:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:50:44] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:50:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:50:44] "OPTIONS /api/register HTTP/1.1" 200 -
2025/03/14 16:50:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:50:44] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/03/14 16:50:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:50:47] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 16:50:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:50:47] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 16:50:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:50:47] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:50:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:50:47] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:50:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:50:58] "OPTIONS /api/bikes?user_id=6 HTTP/1.1" 200 -
2025/03/14 16:50:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:50:58] "GET /api/bikes?user_id=6 HTTP/1.1" 200 -
2025/03/14 16:51:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:08] "OPTIONS /api/users/6 HTTP/1.1" 200 -
2025/03/14 16:51:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:08] "OPTIONS /api/bikes?belong_to=6 HTTP/1.1" 200 -
2025/03/14 16:51:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:08] "GET /api/users/6 HTTP/1.1" 200 -
2025/03/14 16:51:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:08] "GET /api/bikes?belong_to=6 HTTP/1.1" 200 -
2025/03/14 16:51:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:24] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:51:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:24] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:51:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:34] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 16:51:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:34] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 16:51:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:34] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:51:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:35] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:51:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:37] "OPTIONS /api/users/6 HTTP/1.1" 200 -
2025/03/14 16:51:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:37] "OPTIONS /api/bikes?belong_to=6 HTTP/1.1" 200 -
2025/03/14 16:51:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:37] "GET /api/users/6 HTTP/1.1" 200 -
2025/03/14 16:51:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:37] "GET /api/bikes?belong_to=6 HTTP/1.1" 200 -
2025/03/14 16:51:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:41] "GET /api/users/6 HTTP/1.1" 200 -
2025/03/14 16:51:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:41] "GET /api/bikes?belong_to=6 HTTP/1.1" 200 -
2025/03/14 16:51:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:43] "OPTIONS /api/bikes?user_id=6 HTTP/1.1" 200 -
2025/03/14 16:51:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:51:43] "GET /api/bikes?user_id=6 HTTP/1.1" 200 -
2025/03/14 16:52:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:52:02] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:52:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:52:02] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:52:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:52:10] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 16:52:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:52:10] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 16:52:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:52:10] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 16:52:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:52:10] "GET /api/users HTTP/1.1" 200 -
2025/03/14 16:52:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:52:10] "OPTIONS /api/bikes?user_id=6 HTTP/1.1" 200 -
2025/03/14 16:52:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:52:10] "GET /api/bikes?user_id=6 HTTP/1.1" 200 -
2025/03/14 16:53:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:53:19] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:53:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:53:19] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 16:53:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:53:32] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 16:53:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 16:53:32] "GET /api/bikes HTTP/1.1" 200 -
2025/03/14 17:01:44 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 17:01:44 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 17:01:44 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 17:01:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:01:53] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 17:01:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:01:53] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 17:01:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:01:53] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 17:01:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:01:54] "GET /api/users HTTP/1.1" 200 -
2025/03/14 17:01:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:01:58] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/03/14 17:01:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:01:58] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/03/14 17:02:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:02:02] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/03/14 17:02:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:02:02] "OPTIONS /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/03/14 17:02:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:02:02] "GET /api/users/1 HTTP/1.1" 200 -
2025/03/14 17:02:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:02:02] "GET /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/03/14 17:02:34 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 17:02:34 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 17:02:34 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 17:02:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:02:41] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 17:02:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:02:41] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 17:02:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:02:41] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 17:02:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:02:41] "GET /api/users HTTP/1.1" 200 -
2025/03/14 17:02:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:02:45] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/03/14 17:02:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:02:45] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/03/14 17:02:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:02:52] "OPTIONS /api/bikes/1 HTTP/1.1" 200 -
2025/03/14 17:02:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:02:52] "[31m[1mPUT /api/bikes/1 HTTP/1.1[0m" 403 -
2025/03/14 17:03:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:03:08] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/03/14 17:03:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:03:08] "OPTIONS /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/03/14 17:03:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:03:08] "GET /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/03/14 17:03:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:03:08] "GET /api/users/1 HTTP/1.1" 200 -
2025/03/14 17:04:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:04:52] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/03/14 17:04:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:04:52] "OPTIONS /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/03/14 17:04:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:04:52] "GET /api/users/1 HTTP/1.1" 200 -
2025/03/14 17:04:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:04:52] "GET /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/03/14 17:05:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:07] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/03/14 17:05:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:07] "OPTIONS /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/03/14 17:05:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:07] "GET /api/users/1 HTTP/1.1" 200 -
2025/03/14 17:05:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:07] "GET /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/03/14 17:05:41 flask_api __init__.py[52] create_app() INFO: Flask Rest Api startup
2025/03/14 17:05:41 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/03/14 17:05:41 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/03/14 17:05:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:46] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 17:05:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:46] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 17:05:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:46] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 17:05:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:46] "GET /api/users HTTP/1.1" 200 -
2025/03/14 17:05:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:49] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/03/14 17:05:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:49] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/03/14 17:05:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:51] "OPTIONS /api/users/1 HTTP/1.1" 200 -
2025/03/14 17:05:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:51] "OPTIONS /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/03/14 17:05:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:51] "GET /api/users/1 HTTP/1.1" 200 -
2025/03/14 17:05:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:51] "GET /api/bikes?belong_to=1 HTTP/1.1" 200 -
2025/03/14 17:05:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:55] "OPTIONS /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/03/14 17:05:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:55] "GET /api/bikes?user_id=1 HTTP/1.1" 200 -
2025/03/14 17:05:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:58] "OPTIONS /api/bikes/1 HTTP/1.1" 200 -
2025/03/14 17:05:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:05:58] "GET /api/bikes/1 HTTP/1.1" 200 -
2025/03/14 17:06:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:12] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 17:06:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:12] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 17:06:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:12] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 17:06:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:12] "GET /api/users HTTP/1.1" 200 -
2025/03/14 17:06:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:13] "OPTIONS /api/bikes?user_id=6 HTTP/1.1" 200 -
2025/03/14 17:06:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:13] "GET /api/bikes?user_id=6 HTTP/1.1" 200 -
2025/03/14 17:06:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:15] "OPTIONS /api/bikes?belong_to=6 HTTP/1.1" 200 -
2025/03/14 17:06:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:15] "OPTIONS /api/users/6 HTTP/1.1" 200 -
2025/03/14 17:06:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:15] "GET /api/bikes?belong_to=6 HTTP/1.1" 200 -
2025/03/14 17:06:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:16] "GET /api/users/6 HTTP/1.1" 200 -
2025/03/14 17:06:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:32] "OPTIONS /api/login HTTP/1.1" 200 -
2025/03/14 17:06:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:32] "[35m[1mPOST /api/login HTTP/1.1[0m" 201 -
2025/03/14 17:06:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:32] "OPTIONS /api/users HTTP/1.1" 200 -
2025/03/14 17:06:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:32] "GET /api/users HTTP/1.1" 200 -
2025/03/14 17:06:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:35] "OPTIONS /api/bikes?user_id=6 HTTP/1.1" 200 -
2025/03/14 17:06:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:35] "GET /api/bikes?user_id=6 HTTP/1.1" 200 -
2025/03/14 17:06:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:45] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/03/14 17:06:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:45] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/03/14 17:06:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:45] "OPTIONS /api/bikes?user_id=6 HTTP/1.1" 200 -
2025/03/14 17:06:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:45] "GET /api/bikes?user_id=6 HTTP/1.1" 200 -
2025/03/14 17:06:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:48] "OPTIONS /api/users/6 HTTP/1.1" 200 -
2025/03/14 17:06:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:48] "OPTIONS /api/bikes?belong_to=6 HTTP/1.1" 200 -
2025/03/14 17:06:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:48] "GET /api/bikes?belong_to=6 HTTP/1.1" 200 -
2025/03/14 17:06:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [14/Mar/2025 17:06:48] "GET /api/users/6 HTTP/1.1" 200 -
