<template>
  <div class="app-container">
    <div class="dashboard-header">
      <div class="dashboard-title">
        <el-page-header :content="parkingLot.name" @back="goBack" />
        <p class="subtitle">编辑停车场车位信息</p>
      </div>
      <div class="dashboard-actions">
        <el-button type="primary" icon="el-icon-check" @click="saveChanges" :loading="saveLoading">保存更改</el-button>
        <el-button icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
        <el-button icon="el-icon-back" @click="goBack">返回</el-button>
      </div>
    </div>

    <el-row v-loading="loading" :gutter="20" class="main-content">
      <!-- 左侧停车场基本信息 -->
      <el-col :xs="24" :sm="24" :md="6">
        <div class="parking-card">
          <div class="card-header">
            <h3 class="title">停车场信息</h3>
            <div class="status-tag" :class="parkingLot.status === 1 ? 'operating' : 'paused'">
              <span class="dot" :class="parkingLot.status === 1 ? 'operating' : 'paused'"></span>
              {{ parkingLot.status_text }}
            </div>
          </div>
          <div class="card-body">
            <div class="info-section">
              <div class="info-item">
                <i class="el-icon-office-building info-icon"></i>
                <div class="info-content">
                  <div class="info-label">名称</div>
                  <div class="info-value">{{ parkingLot.name }}</div>
                </div>
              </div>
              <div class="info-item">
                <i class="el-icon-location info-icon"></i>
                <div class="info-content">
                  <div class="info-label">地址</div>
                  <div class="info-value">{{ parkingLot.address }}</div>
                </div>
              </div>
              <div class="info-item">
                <i class="el-icon-s-grid info-icon"></i>
                <div class="info-content">
                  <div class="info-label">车位数量</div>
                  <div class="info-value">{{ parkingLot.total_spaces }} 个</div>
                </div>
              </div>
              <div class="info-item">
                <i class="el-icon-time info-icon"></i>
                <div class="info-content">
                  <div class="info-label">开放时间</div>
                  <div class="info-value">{{ parkingLot.opening_hours || '24小时' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 批量操作区域 -->
        <div class="parking-card">
          <div class="card-header">
            <h3 class="title">批量操作</h3>
          </div>
          <div class="card-body">
            <div class="batch-actions">
              <p>已选择 <span class="selected-count">{{ selectedSpaces.length }}</span> 个车位</p>

              <el-divider content-position="left">修改车位类型</el-divider>
              <div class="action-group">
                <el-button
                  size="small"
                  :disabled="selectedSpaces.length === 0"
                  @click="batchUpdateType(1)"
                >设为普通车位</el-button>
                <el-button
                  size="small"
                  type="success"
                  :disabled="selectedSpaces.length === 0"
                  @click="batchUpdateType(2)"
                >设为残疾人车位</el-button>
                <el-button
                  size="small"
                  type="primary"
                  :disabled="selectedSpaces.length === 0"
                  @click="batchUpdateType(3)"
                >设为充电车位</el-button>
              </div>

              <el-divider content-position="left">修改车位状态</el-divider>
              <div class="action-group">
                <el-button
                  size="small"
                  type="success"
                  :disabled="selectedSpaces.length === 0 || hasOccupiedSpaces"
                  @click="batchUpdateStatus(0)"
                >设为空闲</el-button>
                <el-button
                  size="small"
                  type="info"
                  :disabled="selectedSpaces.length === 0 || hasOccupiedSpaces"
                  @click="batchUpdateStatus(2)"
                >设为维护中</el-button>
              </div>

              <el-divider content-position="left">选择操作</el-divider>
              <div class="action-group">
                <el-button
                  size="small"
                  @click="selectAll"
                >全选</el-button>
                <el-button
                  size="small"
                  @click="deselectAll"
                  :disabled="selectedSpaces.length === 0"
                >取消选择</el-button>
                <el-button
                  size="small"
                  @click="invertSelection"
                >反选</el-button>
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 右侧车位管理 -->
      <el-col :xs="24" :sm="24" :md="18">
        <div class="parking-card">
          <div class="card-header">
            <h3 class="title">车位管理</h3>
            <div class="filter-actions">
              <el-input
                v-model="spacesQuery.search"
                placeholder="搜索车位编号"
                style="width: 200px;"
                clearable
                @clear="handleSpacesFilter"
                @keyup.enter.native="handleSpacesFilter"
              >
                <el-button slot="append" icon="el-icon-search" @click="handleSpacesFilter"></el-button>
              </el-input>
              <el-select v-model="spacesQuery.type" placeholder="车位类型" style="width: 140px;" @change="handleSpacesFilter" clearable>
                <el-option label="全部类型" value=""></el-option>
                <el-option label="普通车位" :value="1"></el-option>
                <el-option label="残疾人车位" :value="2"></el-option>
                <el-option label="充电车位" :value="3"></el-option>
              </el-select>
              <el-select v-model="spacesQuery.status" placeholder="车位状态" style="width: 140px;" @change="handleSpacesFilter" clearable>
                <el-option label="全部状态" value=""></el-option>
                <el-option label="空闲" :value="0"></el-option>
                <el-option label="已占用" :value="1"></el-option>
                <el-option label="维护中" :value="2"></el-option>
              </el-select>
            </div>
          </div>

          <div class="card-body">
            <div class="parking-spaces-grid">
              <div
                v-for="space in spaces"
                :key="space.id"
                class="parking-space-item"
                :class="[
                  getSpaceStatusClass(space.status),
                  { 'selected': isSpaceSelected(space.id) }
                ]"
                @click="toggleSpaceSelection(space)"
              >
                <div class="space-number">{{ space.space_number }}</div>
                <div class="space-type">
                  <el-tag :type="getSpaceTypeTag(space.type)" size="mini">
                    {{ getSpaceTypeName(space.type) }}
                  </el-tag>
                </div>
                <div class="space-status">
                  <el-tag :type="getSpaceStatusType(space.status)" size="mini">
                    {{ getSpaceStatusName(space.status) }}
                  </el-tag>
                </div>
                <div v-if="space.current_vehicle_id" class="space-vehicle">
                  <i class="el-icon-bicycle"></i> 已停车
                </div>
              </div>
            </div>

            <!-- 空数据提示 -->
            <el-empty v-if="spaces.length === 0 && !loading" description="没有找到车位数据"></el-empty>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-show="totalSpaces > 0"
                background
                layout="total, sizes, prev, pager, next"
                :page-sizes="[20, 50, 100]"
                :page-size="spacesQuery.limit"
                :total="totalSpaces"
                :current-page.sync="spacesQuery.page"
                @size-change="val => handlePagination({page: spacesQuery.page, limit: val})"
                @current-change="val => handlePagination({page: val, limit: spacesQuery.limit})"
              />
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import request from '@/utils/request'
import { getParkingLot, getParkingSpaces, updateSpaceType, updateSpaceStatus, batchUpdateSpaceTypes, batchUpdateParkingSpaceStatus } from '@/api/parkinglot'

export default {
  name: 'ParkingEdit',
  data() {
    return {
      loading: true,
      saveLoading: false,
      parkingLot: {
        id: 0,
        name: '',
        address: '',
        total_spaces: 0,
        occupied_spaces: 0,
        available_spaces: 0,
        utilization_rate: 0,
        status: 1,
        status_text: '',
        opening_hours: '',
        description: ''
      },
      spaces: [],
      totalSpaces: 0,
      spacesQuery: {
        page: 1,
        limit: 50,
        search: '',
        status: '',
        type: ''
      },
      selectedSpaces: [], // 存储选中的车位ID
      changedSpaces: {}, // 存储已修改的车位信息
      hasChanges: false // 是否有未保存的更改
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'roles'
    ]),
    hasOccupiedSpaces() {
      // 检查选中的车位中是否有已占用的车位
      return this.selectedSpaces.some(spaceId => {
        const space = this.spaces.find(s => s.id === spaceId)
        return space && space.status === 1
      })
    }
  },
  created() {
    this.fetchParkingLot()
  },
  methods: {
    checkPermission(permissionRoles) {
      if (this.roles.includes('admin')) {
        return true
      }
      if (!permissionRoles) return false
      return permissionRoles.some(role => this.roles.includes(role))
    },
    goBack() {
      this.$router.push(`/parking/details/${this.$route.params.id}`)
    },
    refreshData() {
      this.fetchParkingLot()
      this.fetchParkingSpaces()
    },
    fetchParkingLot() {
      const lotId = this.$route.params.id
      if (!lotId) {
        this.$message.error('没有指定停车场ID')
        this.goBack()
        return
      }

      this.loading = true
      getParkingLot(lotId).then(response => {
        let lotData = response
        if (response.data) {
          lotData = response.data
        }

        this.parkingLot = lotData

        // 获取车位信息
        this.fetchParkingSpaces()
      }).catch(error => {
        console.error('获取停车场详情失败', error)
        this.$message.error('获取停车场详情失败')
        this.loading = false
      })
    },
    fetchParkingSpaces() {
      const lotId = this.parkingLot.id
      if (!lotId) return

      this.loading = true
      getParkingSpaces(lotId, this.spacesQuery).then(response => {
        let data = response
        if (response.data) {
          data = response.data
        }

        if (Array.isArray(data)) {
          this.spaces = data
          this.totalSpaces = data.length
        } else if (data.items && Array.isArray(data.items)) {
          this.spaces = data.items
          this.totalSpaces = data.total || data.items.length
        } else if (data.data && Array.isArray(data.data)) {
          this.spaces = data.data
          this.totalSpaces = data.total || data.data.length
        } else {
          console.error('无法识别的车位数据格式:', data)
          this.spaces = []
          this.totalSpaces = 0
        }

        this.loading = false
      }).catch(error => {
        console.error('获取车位列表失败', error)
        this.$message.error('获取车位列表失败')
        this.loading = false
        this.spaces = []
        this.totalSpaces = 0
      })
    },
    getSpaceStatusType(status) {
      const statusMap = {
        0: 'success',
        1: 'warning',
        2: 'info'
      }
      return statusMap[status] || 'info'
    },
    getSpaceTypeName(type) {
      const typeMap = {
        1: '普通车位',
        2: '残疾人车位',
        3: '充电车位'
      }
      return typeMap[type] || '未知类型'
    },
    getSpaceTypeTag(type) {
      const typeMap = {
        1: '',
        2: 'success',
        3: 'primary'
      }
      return typeMap[type] || 'info'
    },
    getSpaceStatusName(status) {
      const statusMap = {
        0: '空闲',
        1: '已占用',
        2: '维护中'
      }
      return statusMap[status] || '未知状态'
    },
    getSpaceStatusClass(status) {
      const statusMap = {
        0: 'status-available',
        1: 'status-occupied',
        2: 'status-maintenance'
      }
      return statusMap[status] || ''
    },
    handleSpacesFilter() {
      // 重置页码并重新请求数据
      this.spacesQuery.page = 1
      this.fetchParkingSpaces()
    },
    handlePagination({ page, limit }) {
      this.spacesQuery.page = page
      this.spacesQuery.limit = limit
      this.fetchParkingSpaces()
    },
    // 选择相关方法
    isSpaceSelected(spaceId) {
      return this.selectedSpaces.includes(spaceId)
    },
    toggleSpaceSelection(space) {
      const index = this.selectedSpaces.indexOf(space.id)
      if (index === -1) {
        // 添加到选中列表
        this.selectedSpaces.push(space.id)
      } else {
        // 从选中列表中移除
        this.selectedSpaces.splice(index, 1)
      }
    },
    selectAll() {
      this.selectedSpaces = this.spaces.map(space => space.id)
    },
    deselectAll() {
      this.selectedSpaces = []
    },
    invertSelection() {
      const allSpaceIds = this.spaces.map(space => space.id)
      this.selectedSpaces = allSpaceIds.filter(id => !this.selectedSpaces.includes(id))
    },
    // 批量更新车位类型
    batchUpdateType(type) {
      if (this.selectedSpaces.length === 0) {
        this.$message.warning('请先选择要修改的车位')
        return
      }

      const typeNames = {
        1: '普通车位',
        2: '残疾人车位',
        3: '充电车位'
      }

      this.$confirm(`确定将选中的 ${this.selectedSpaces.length} 个车位类型修改为${typeNames[type]}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true

        // 更新本地数据
        this.spaces.forEach(space => {
          if (this.selectedSpaces.includes(space.id)) {
            space.type = type
          }
        })

        // 记录更改，但不立即提交到服务器
        this.selectedSpaces.forEach(spaceId => {
          if (!this.changedSpaces[spaceId]) {
            this.changedSpaces[spaceId] = {}
          }
          this.changedSpaces[spaceId].type = type
        })

        this.hasChanges = true
        this.loading = false
        this.$message.success(`已将 ${this.selectedSpaces.length} 个车位类型修改为${typeNames[type]}，点击"保存更改"按钮提交到服务器`)
      }).catch(() => {
        // 用户取消操作
      })
    },
    // 批量更新车位状态
    batchUpdateStatus(status) {
      if (this.selectedSpaces.length === 0) {
        this.$message.warning('请先选择要修改的车位')
        return
      }

      // 检查是否有已占用的车位
      const hasOccupied = this.selectedSpaces.some(spaceId => {
        const space = this.spaces.find(s => s.id === spaceId)
        return space && space.status === 1
      })

      if (hasOccupied) {
        this.$message.error('选中的车位中包含已占用的车位，无法修改状态')
        return
      }

      const statusNames = {
        0: '空闲',
        2: '维护中'
      }

      this.$confirm(`确定将选中的 ${this.selectedSpaces.length} 个车位状态修改为${statusNames[status]}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true

        // 更新本地数据
        this.spaces.forEach(space => {
          if (this.selectedSpaces.includes(space.id) && space.status !== 1) {
            space.status = status
          }
        })

        // 记录更改，但不立即提交到服务器
        this.selectedSpaces.forEach(spaceId => {
          const space = this.spaces.find(s => s.id === spaceId)
          if (space && space.status !== 1) {
            if (!this.changedSpaces[spaceId]) {
              this.changedSpaces[spaceId] = {}
            }
            this.changedSpaces[spaceId].status = status
          }
        })

        this.hasChanges = true
        this.loading = false
        this.$message.success(`已将 ${this.selectedSpaces.length} 个车位状态修改为${statusNames[status]}，点击"保存更改"按钮提交到服务器`)
      }).catch(() => {
        // 用户取消操作
      })
    },
    // 保存所有更改
    saveChanges() {
      if (!this.hasChanges) {
        this.$message.info('没有需要保存的更改')
        return
      }

      this.$confirm('确定要保存所有更改吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.saveLoading = true

        // 将更改分组为类型更改和状态更改
        const typeChanges = {}
        const statusChanges = {}

        Object.entries(this.changedSpaces).forEach(([spaceId, changes]) => {
          if ('type' in changes) {
            const type = changes.type
            if (!typeChanges[type]) {
              typeChanges[type] = []
            }
            typeChanges[type].push(parseInt(spaceId))
          }

          if ('status' in changes) {
            const status = changes.status
            if (!statusChanges[status]) {
              statusChanges[status] = []
            }
            statusChanges[status].push(parseInt(spaceId))
          }
        })

        // 创建批量更新请求
        const promises = []

        // 批量更新类型
        Object.entries(typeChanges).forEach(([type, spaceIds]) => {
          if (spaceIds.length > 0) {
            promises.push(
              batchUpdateSpaceTypes(spaceIds, parseInt(type))
                .catch(error => {
                  console.error(`批量更新车位类型失败 (类型=${type})`, error)
                  throw error
                })
            )
          }
        })

        // 批量更新状态
        Object.entries(statusChanges).forEach(([status, spaceIds]) => {
          if (spaceIds.length > 0) {
            promises.push(
              batchUpdateParkingSpaceStatus(spaceIds, parseInt(status))
                .catch(error => {
                  console.error(`批量更新车位状态失败 (状态=${status})`, error)
                  throw error
                })
            )
          }
        })

        // 执行所有请求
        Promise.all(promises)
          .then(() => {
            this.$message.success('所有更改已成功保存')
            this.changedSpaces = {}
            this.hasChanges = false
            this.refreshData() // 刷新数据
          })
          .catch(error => {
            this.$message.error('保存更改失败: ' + (error.message || '未知错误'))
          })
          .finally(() => {
            this.saveLoading = false
          })
      }).catch(() => {
        // 用户取消操作
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .dashboard-title {
    .subtitle {
      margin-top: 5px;
      color: #606266;
      font-size: 14px;
    }
  }

  .dashboard-actions {
    display: flex;
    gap: 10px;
  }
}

.main-content {
  margin-bottom: 20px;
}

.parking-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;

  .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .status-tag {
      display: flex;
      align-items: center;
      font-size: 14px;
      padding: 2px 8px;
      border-radius: 4px;

      &.operating {
        background-color: rgba(103, 194, 58, 0.1);
        color: #67c23a;
      }

      &.paused {
        background-color: rgba(144, 147, 153, 0.1);
        color: #909399;
      }

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;

        &.operating {
          background-color: #67c23a;
        }

        &.paused {
          background-color: #909399;
        }
      }
    }

    .filter-actions {
      display: flex;
      gap: 10px;
    }
  }

  .card-body {
    padding: 20px;
  }
}

.info-section {
  .info-item {
    display: flex;
    margin-bottom: 15px;

    .info-icon {
      font-size: 18px;
      color: $primaryColor;
      margin-right: 10px;
      margin-top: 2px;
    }

    .info-content {
      flex: 1;

      .info-label {
        font-size: 13px;
        color: #909399;
        margin-bottom: 4px;
      }

      .info-value {
        font-size: 14px;
        color: #303133;
      }
    }
  }
}

.batch-actions {
  .selected-count {
    font-weight: bold;
    color: $primaryColor;
  }

  .action-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
  }

  .el-divider {
    margin: 15px 0;
  }
}

.parking-spaces-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;

  .parking-space-item {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    &.selected {
      border: 2px solid $primaryColor;
      background-color: rgba($primaryColor, 0.05);
    }

    &.status-available {
      background-color: rgba(103, 194, 58, 0.1);
    }

    &.status-occupied {
      background-color: rgba(230, 162, 60, 0.1);
    }

    &.status-maintenance {
      background-color: rgba(144, 147, 153, 0.1);
    }

    .space-number {
      font-weight: bold;
      font-size: 16px;
      margin-bottom: 8px;
    }

    .space-type, .space-status {
      margin-bottom: 5px;
    }

    .space-vehicle {
      margin-top: 8px;
      font-size: 12px;
      color: #e6a23c;
    }
  }
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}
</style>
