<!DOCTYPE html>
<html>
<head>
    <title>登录流程测试</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>登录流程测试</h1>
    <button onclick="testLogin()">测试登录</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>正在测试登录...</p>';

            try {
                console.log('=== 开始登录测试 ===');

                // 1. 测试登录API
                const loginData = {
                    username: 'admin',
                    password: 'admin123'
                };

                console.log('发送登录请求:', loginData);

                const response = await axios.post('http://127.0.0.1:5000/api/login', loginData, {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log('登录响应:', response);
                console.log('响应数据:', response.data);

                // 2. 模拟前端的数据提取逻辑
                console.log('=== 模拟前端数据提取 ===');

                // 模拟 extractToken
                function extractToken(data) {
                    if (!data) return null;
                    if (data.token) return data.token;
                    if (data.access_token) return data.access_token;
                    if (data.data) {
                        if (data.data.token) return data.data.token;
                        if (data.data.access_token) return data.data.access_token;
                    }
                    return null;
                }

                // 模拟 extractUserInfo
                function extractUserInfo(data) {
                    if (!data) return null;
                    if (data.user) return data.user;
                    if (data.data && data.data.user) return data.data.user;
                    if (data.username || data.u_name || data.id || data.u_id) return data;
                    return null;
                }

                // 模拟 extractUserName
                function extractUserName(userInfo, defaultName = '') {
                    if (!userInfo) return defaultName;
                    return userInfo.u_name || userInfo.name || userInfo.username || defaultName;
                }

                const data = response.data;
                const token = extractToken(data);
                const userInfo = extractUserInfo(data);
                const userName = extractUserName(userInfo);

                console.log('提取到的Token:', token);
                console.log('提取到的用户信息:', userInfo);
                console.log('提取到的用户名:', userName);

                // 3. 检查提取结果
                if (token && userInfo && userName) {
                    console.log('✅ 所有数据提取成功');

                    resultDiv.innerHTML = `
                        <h2>✅ 登录测试成功</h2>
                        <h3>提取结果:</h3>
                        <p><strong>Token:</strong> ${token.substring(0, 50)}...</p>
                        <p><strong>用户名:</strong> ${userName}</p>
                        <p><strong>用户ID:</strong> ${userInfo.u_id}</p>
                        <p><strong>角色:</strong> ${userInfo.u_role}</p>

                        <h3>原始响应:</h3>
                        <pre>${JSON.stringify(response.data, null, 2)}</pre>

                        <h3>提取到的用户信息:</h3>
                        <pre>${JSON.stringify(userInfo, null, 2)}</pre>
                    `;
                } else {
                    console.error('❌ 数据提取失败');
                    console.error('Token:', token);
                    console.error('UserInfo:', userInfo);
                    console.error('UserName:', userName);

                    resultDiv.innerHTML = `
                        <h2>❌ 数据提取失败</h2>
                        <p><strong>Token:</strong> ${token || '未提取到'}</p>
                        <p><strong>用户信息:</strong> ${userInfo ? '已提取' : '未提取到'}</p>
                        <p><strong>用户名:</strong> ${userName || '未提取到'}</p>

                        <h3>原始响应:</h3>
                        <pre>${JSON.stringify(response.data, null, 2)}</pre>
                    `;
                }

            } catch (error) {
                console.error('❌ 登录请求失败:', error);
                resultDiv.innerHTML = `
                    <h2>❌ 登录请求失败</h2>
                    <p>${error.message}</p>
                    <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre>
                `;
            }
        }
    </script>
</body>
</html>
