<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 进行中的充电标签页 -->
      <el-tab-pane label="进行中的充电" name="active">
        <div v-loading="activeLoading" class="charging-records-container">
          <div v-if="activeChargingRecords.length === 0" class="empty-container">
            <el-empty description="没有进行中的充电记录" />
            <div class="empty-tip">您可以点击下方按钮查看可用充电站点</div>
            <div class="empty-buttons">
              <el-button type="primary" icon="el-icon-plus" @click="activeTab = 'stations'">
                查看充电站点
              </el-button>
            </div>
          </div>

          <el-row v-else :gutter="20">
            <el-col v-for="record in activeChargingRecords" :key="record.id" :xs="24" :sm="12" :md="8" :lg="8" :xl="6">
              <charging-card
                :record="record"
                :loading="endingChargingId === record.id"
                @end-charging="handleEndCharging"
              />
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>

      <!-- 历史充电记录标签页 -->
      <el-tab-pane label="历史充电记录" name="history">
        <div v-loading="historyLoading" class="charging-records-container">
          <div class="filter-container">
            <el-input
              v-model="listQuery.license"
              placeholder="车牌号"
              style="width: 200px;"
              class="filter-item"
              @keyup.enter.native="handleFilter"
            />
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 350px;"
              class="filter-item"
              value-format="yyyy-MM-dd"
              @change="handleDateRangeChange"
            />
            <el-select
              v-model="listQuery.sort_field"
              placeholder="排序字段"
              style="width: 120px;"
              class="filter-item"
              @change="handleSortChange"
            >
              <el-option label="ID" value="id" />
              <el-option label="开始时间" value="start_time" />
              <el-option label="结束时间" value="end_time" />
            </el-select>
            <el-select
              v-model="listQuery.sort_order"
              placeholder="排序方式"
              style="width: 120px;"
              class="filter-item"
              @change="handleSortChange"
            >
              <el-option label="升序" value="asc" />
              <el-option label="降序" value="desc" />
            </el-select>
            <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
              搜索
            </el-button>
            <el-button v-waves class="filter-item" type="info" icon="el-icon-refresh" @click="resetFilter">
              重置
            </el-button>
          </div>

          <div v-if="historyChargingRecords.length === 0" class="empty-container">
            <el-empty description="没有历史充电记录" />
          </div>

          <el-table
            v-else
            :data="historyChargingRecords"
            border
            style="width: 100%"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
            @sort-change="handleTableSortChange"
          >
            <el-table-column label="充电ID" prop="id" width="80" align="center" sortable="custom" :sort-orders="['ascending', 'descending']" />
            <el-table-column label="车牌号" width="120" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.vehicle.number }}</span>
              </template>
            </el-table-column>
            <el-table-column label="车辆信息" width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.vehicle.brand }} {{ scope.row.vehicle.color }}</span>
              </template>
            </el-table-column>
            <el-table-column label="停车场" width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.parking_lot.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="车位" width="100" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.parking_space.space_number }}</span>
              </template>
            </el-table-column>
            <el-table-column label="充电功率" width="120" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.power || 7 }}kW</span>
              </template>
            </el-table-column>
            <el-table-column label="开始时间" prop="start_time" width="170" sortable="custom" :sort-orders="['ascending', 'descending']">
              <template slot-scope="scope">
                <span>{{ formatDateTime(scope.row.start_time) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="结束时间" prop="end_time" width="170" sortable="custom" :sort-orders="['ascending', 'descending']">
              <template slot-scope="scope">
                <span>{{ formatDateTime(scope.row.end_time) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="充电时长" width="120" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.duration_formatted }}</span>
              </template>
            </el-table-column>

            <el-table-column label="状态" width="100" align="center">
              <template slot-scope="scope">
                <el-tag :type="getStatusTagType(scope.row.status)">
                  {{ scope.row.status_text }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container">
            <el-pagination
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="listQuery.page"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="listQuery.limit"
              layout="total, sizes, prev, pager, next, jumper"
              :total="historyTotal"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 充电站点标签页 -->
      <el-tab-pane label="充电站点" name="stations">
        <div v-loading="lotsLoading" class="charging-lots-container">
          <div class="section-header">
            <div class="section-title">
              <i class="el-icon-location"></i>
              <span>充电站点</span>
            </div>
            <div class="section-filter">
              <el-input
                v-model="lotsQuery.search"
                placeholder="搜索停车场名称"
                prefix-icon="el-icon-search"
                clearable
                size="small"
                @keyup.enter.native="handleLotsFilter"
                style="width: 200px; margin-right: 10px;"
              />
              <el-select
                v-model="lotsQuery.campus"
                placeholder="选择校区"
                clearable
                size="small"
                style="width: 120px; margin-right: 10px;"
                @change="handleLotsFilter"
              >
                <el-option
                  v-for="item in campusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-button type="primary" size="small" icon="el-icon-search" @click="handleLotsFilter">筛选</el-button>
              <el-button type="info" size="small" icon="el-icon-refresh" @click="resetLotsFilter">重置</el-button>
            </div>
          </div>

          <div v-if="filteredParkingLots.length === 0" class="empty-container">
            <el-empty description="没有找到充电站点" />
          </div>

          <el-row v-else :gutter="20" type="flex" justify="start">
            <el-col
              v-for="lot in filteredParkingLots"
              :key="lot.id"
              :xs="24"
              :sm="12"
              :md="8"
              :lg="6"
              class="lot-col"
            >
              <el-card class="lot-card" :body-style="{ padding: '0px' }" shadow="hover">
                <div class="lot-header">
                  <div class="lot-name">{{ lot.name }}</div>
                  <div class="lot-status" :class="{ 'status-active': lot.status === 1, 'status-inactive': lot.status !== 1 }">
                    {{ lot.status === 1 ? '正常运营' : '暂停使用' }}
                  </div>
                </div>

                <div class="lot-content">
                  <div class="lot-tags">
                    <el-tag v-if="lot.campus" size="mini" type="primary">{{ lot.campus }}</el-tag>
                    <el-tag v-if="lot.area" size="mini" type="success" style="margin-left: 5px;">{{ lot.area }}</el-tag>
                  </div>

                  <div class="lot-address">
                    <i class="el-icon-location"></i>
                    <span>{{ lot.address }}</span>
                  </div>

                  <div class="charging-stats">
                    <div class="utilization-rate">
                      <div class="rate-label">充电车位利用率</div>
                      <div class="rate-value">{{ getUtilizationRate(lot) }}%</div>
                      <el-progress
                        :percentage="getUtilizationRate(lot)"
                        :color="getProgressColor(getUtilizationRate(lot))"
                      ></el-progress>
                    </div>

                    <div class="spaces-count">
                      <div class="count-item">
                        <div class="count-label">已用</div>
                        <div class="count-value">{{ getOccupiedSpaces(lot) }}</div>
                      </div>
                      <div class="count-item">
                        <div class="count-label">总数</div>
                        <div class="count-value">{{ getTotalSpaces(lot) }}</div>
                      </div>
                      <div class="count-item">
                        <div class="count-label">空闲</div>
                        <div class="count-value">{{ getAvailableSpaces(lot) }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="lot-footer">
                  <el-button
                    type="success"
                    size="small"
                    :disabled="getAvailableSpaces(lot) === 0 || lot.status !== 1"
                    @click="handleStartCharging(lot)"
                    style="width: 100%"
                  >
                    我要充电
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>



    <!-- 充电车位选择对话框 -->
    <el-dialog
      :title="`${selectedLot ? selectedLot.name : ''} - 充电车位选择`"
      :visible.sync="spaceDialogVisible"
      width="60%"
      class="charging-space-dialog"
    >
      <div v-if="selectedLot" class="space-dialog-content">
        <div v-loading="spacesLoading" class="spaces-grid-container">
          <div v-if="!spacesLoading && chargingSpaces.length > 0" class="spaces-grid">
            <charging-space
              v-for="space in chargingSpaces"
              :key="space.id"
              :space="space"
              :selected="space.id === selectedSpaceId"
              @click="handleSpaceClick"
            />
          </div>
          <div v-else-if="!spacesLoading && chargingSpaces.length === 0" class="no-spaces">
            <div class="empty-content">
              <i class="el-icon-warning-outline" style="font-size: 48px; color: #909399; margin-bottom: 10px;"></i>
              <div class="empty-text">该停车场没有充电车位</div>
            </div>
          </div>
        </div>

        <div v-if="selectedSpace" class="space-info-container">
          <div class="space-info-header">
            <div class="space-number">{{ selectedSpace.space_number }}</div>
          </div>

          <div class="space-info-content">
            <div class="info-item">
              <div class="label">充电功率</div>
              <div class="value">{{ selectedSpace.power || '-' }} kW</div>
            </div>



            <el-form
              ref="chargingForm"
              :model="chargingForm"
              :rules="chargingFormRules"
              label-width="80px"
              size="small"
            >
              <el-form-item label="车辆" prop="vehicle_id">
                <el-select
                  v-model="chargingForm.vehicle_id"
                  placeholder="请选择车辆"
                  style="width: 100%"
                >
                  <el-option
                    v-for="vehicle in availableVehicles"
                    :key="vehicle.b_id"
                    :label="`${vehicle.number} (${vehicle.brand} ${vehicle.color})`"
                    :value="vehicle.b_id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="充电时长" prop="estimated_duration">
                <el-select
                  v-model="chargingForm.estimated_duration"
                  placeholder="请选择充电时长"
                  style="width: 100%"
                >
                  <el-option label="1小时" :value="1" />
                  <el-option label="2小时" :value="2" />
                  <el-option label="3小时" :value="3" />
                  <el-option label="4小时" :value="4" />
                  <el-option label="5小时" :value="5" />
                  <el-option label="6小时" :value="6" />
                </el-select>
              </el-form-item>

              <el-form-item label="备注" prop="remarks">
                <el-input
                  v-model="chargingForm.remarks"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入备注信息（选填）"
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" :loading="startingCharging" @click="submitStartCharging">开始充电</el-button>
                <el-button @click="resetChargingForm">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div v-else class="space-info-placeholder">
          <div class="placeholder-content">
            <i class="el-icon-s-opportunity"></i>
            <div class="placeholder-text">请选择一个充电车位</div>
          </div>
        </div>
      </div>

      <div class="space-dialog-legend">
        <div class="legend-item">
          <div class="legend-color available"></div>
          <div class="legend-text">空闲</div>
        </div>
        <div class="legend-item">
          <div class="legend-color occupied"></div>
          <div class="legend-text">已占用</div>
        </div>
        <div class="legend-item">
          <div class="legend-color maintenance"></div>
          <div class="legend-text">维护中</div>
        </div>
        <div class="legend-item">
          <div class="legend-color selected"></div>
          <div class="legend-text">已选择</div>
        </div>
      </div>
    </el-dialog>

    <!-- 结束充电确认对话框 -->
    <el-dialog title="结束充电" :visible.sync="endChargingDialogVisible" width="400px">
      <p>确定要结束当前充电吗？</p>
      <div v-if="selectedChargingRecord" class="end-charging-info">
        <div class="info-item">
          <span class="info-label">车牌号:</span>
          <span class="info-value">{{ selectedChargingRecord.vehicle.number }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">停车场:</span>
          <span class="info-value">{{ selectedChargingRecord.parking_lot.name }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">车位:</span>
          <span class="info-value">{{ selectedChargingRecord.parking_space.space_number }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">充电时长:</span>
          <span class="info-value">{{ selectedChargingRecord.duration_formatted }}</span>
        </div>

      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="endChargingDialogVisible = false">取消</el-button>
        <el-button type="danger" :loading="endingChargingLoading" @click="confirmEndCharging">确认结束</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getActiveChargingRecords,
  getHistoryChargingRecords,
  endCharging,
  startCharging
} from '@/api/charging'
import { getMyVehicles } from '@/api/vehicles'
import { getChargingParkingLots } from '@/api/charging'
import { getParkingSpaces } from '@/api/parkinglot'
import { checkVehicleActiveParking } from '@/api/parking'
import request from '@/utils/request'
import waves from '@/directive/waves'
import { parseTime } from '@/utils'
import ChargingCard from '@/components/ChargingCard'
import ChargingSpace from '@/components/ChargingSpace'

export default {
  name: 'ChargingCenter',
  components: {
    ChargingCard,
    ChargingSpace
  },
  directives: { waves },
  data() {
    return {
      activeTab: 'active',
      activeLoading: false,
      historyLoading: false,
      reservationsLoading: false,

      // 进行中的充电记录
      activeChargingRecords: [],
      // 定时刷新计时器
      refreshTimer: null,

      // 历史充电记录
      historyChargingRecords: [],
      historyTotal: 0,
      listQuery: {
        page: 1,
        limit: 10,
        license: '',
        start_date: '',
        end_date: '',
        sort_field: 'id',
        sort_order: 'asc'  // 确保按id升序排序
      },
      dateRange: [],

      // 可用车辆和停车场
      availableVehicles: [],
      parkingLots: [],

      // 充电站点
      lotsLoading: false,
      lotsQuery: {
        search: '',
        campus: '',
        area: '',
        status: ''
      },
      campusOptions: [
        { label: '主校区', value: '主校区' },
        { label: '东校区', value: '东校区' },
        { label: '南校区', value: '南校区' },
        { label: '北校区', value: '北校区' }
      ],
      areaOptions: [
        { label: '教学区', value: '教学区' },
        { label: '宿舍区', value: '宿舍区' },
        { label: '图书馆区', value: '图书馆区' },
        { label: '食堂区', value: '食堂区' },
        { label: '体育区', value: '体育区' },
        { label: '行政区', value: '行政区' }
      ],

      // 充电车位选择
      spaceDialogVisible: false,
      selectedLot: null,
      spacesLoading: false,
      chargingSpaces: [],
      selectedSpaceId: null,
      selectedSpace: null,

      // 开始充电表单
      chargingForm: {
        vehicle_id: '',
        estimated_duration: 3,
        remarks: ''
      },
      chargingFormRules: {
        vehicle_id: [
          { required: true, message: '请选择车辆', trigger: 'change' }
        ],
        estimated_duration: [
          { required: true, message: '请选择充电时长', trigger: 'change' }
        ]
      },
      startingCharging: false,

      // 结束充电
      endChargingDialogVisible: false,
      selectedChargingRecord: null,
      endingChargingId: null,
      endingChargingLoading: false
    }
  },
  computed: {
    // 筛选后的停车场列表
    filteredParkingLots() {
      if (!this.parkingLots.length) return []

      let result = [...this.parkingLots]

      // 应用筛选条件
      if (this.lotsQuery.search) {
        const searchTerm = this.lotsQuery.search.toLowerCase()
        result = result.filter(lot =>
          (lot.name && lot.name.toLowerCase().includes(searchTerm)) ||
          (lot.address && lot.address.toLowerCase().includes(searchTerm))
        )
      }

      if (this.lotsQuery.campus) {
        result = result.filter(lot => lot.campus === this.lotsQuery.campus)
      }

      if (this.lotsQuery.area) {
        result = result.filter(lot => lot.area === this.lotsQuery.area)
      }

      if (this.lotsQuery.status !== '') {
        result = result.filter(lot => lot.status === parseInt(this.lotsQuery.status))
      }

      return result
    }
  },
  created() {
    this.fetchActiveChargingRecords()
    this.fetchHistoryChargingRecords()
    this.fetchVehicles()
    this.fetchParkingLots()

    // 设置定时刷新进行中的充电记录
    this.startRefreshTimer()
  },

  beforeDestroy() {
    // 清除定时器
    this.clearRefreshTimer()
  },
  methods: {
    fetchActiveChargingRecords() {
      this.activeLoading = true
      getActiveChargingRecords().then(response => {
        console.log('进行中的充电记录完整响应:', response)
        console.log('进行中的充电记录数据:', response.data)

        // 尝试直接使用响应数据
        let recordsArray = null

        if (response.data) {
          // 检查是否有 data 字段
          if (response.data.data) {
            console.log('进行中的充电记录 data 字段:', response.data.data)
            // 检查 data 字段是否为数组
            if (Array.isArray(response.data.data)) {
              recordsArray = response.data.data
            } else if (response.data.data.items && Array.isArray(response.data.data.items)) {
              // 检查是否有 items 字段
              console.log('进行中的充电记录 items 字段:', response.data.data.items)
              recordsArray = response.data.data.items
            }
          } else if (response.data.items && Array.isArray(response.data.items)) {
            // 检查是否直接有 items 字段
            console.log('进行中的充电记录 items 字段:', response.data.items)
            recordsArray = response.data.items
          } else if (Array.isArray(response.data)) {
            // 检查 response.data 是否直接是数组
            recordsArray = response.data
          } else if (response.status === 'success' && Array.isArray(response.data)) {
            // 检查 response 是否直接包含 data 数组
            recordsArray = response.data
          }
        } else if (Array.isArray(response)) {
          // 检查 response 是否直接是数组
          recordsArray = response
        } else if (response.status === 'success' && Array.isArray(response.data)) {
          // 检查 response 是否直接包含 data 数组
          recordsArray = response.data
        }

        if (recordsArray) {
          // 记录原始数据格式，便于调试
          if (recordsArray.length > 0) {
            console.log('充电记录示例数据:', recordsArray[0])
            if (recordsArray[0].start_time) {
              console.log('充电记录开始时间(原始):', recordsArray[0].start_time, '类型:', typeof recordsArray[0].start_time)
            }
          }

          // 确保每个记录都有必要的嵌套对象
          this.activeChargingRecords = recordsArray.map(record => {
            // 记录每条数据的开始时间，便于调试
            if (record.start_time) {
              console.log(`记录ID ${record.id} 的开始时间:`, record.start_time)
            }

            // 确保vehicle对象存在
            if (!record.vehicle) {
              record.vehicle = {
                number: '未知车牌',
                brand: '未知品牌',
                color: '未知颜色'
              }
            }
            // 确保parking_lot对象存在
            if (!record.parking_lot) {
              record.parking_lot = { name: '未知停车场' }
            }
            // 确保parking_space对象存在
            if (!record.parking_space) {
              record.parking_space = { space_number: '未知车位' }
            }
            return record
          })
        } else {
          console.error('进行中的充电记录数据格式不正确 - 无法找到记录数组:', response.data)
          this.activeChargingRecords = []
        }

        this.activeLoading = false

        // 更新可用车辆列表
        if (this.availableVehicles.length > 0) {
          this.filterAvailableVehicles(this.availableVehicles)
        }
      }).catch((error) => {
        console.error('获取进行中的充电记录失败:', error)
        this.activeChargingRecords = []
        this.activeLoading = false
      })
    },
    fetchHistoryChargingRecords() {
      this.historyLoading = true
      // 使用用户选择的排序参数
      const query = {
        ...this.listQuery
      }

      // 添加调试信息
      console.log('发送历史充电记录查询参数:', query)
      getHistoryChargingRecords(query).then(response => {
        console.log('历史充电记录完整响应:', response)
        console.log('历史充电记录数据:', response.data)

        // 尝试直接使用响应数据
        let recordsArray = null
        let totalRecords = 0

        if (response.data) {
          // 检查是否有 data 字段
          if (response.data.data) {
            console.log('历史充电记录 data 字段:', response.data.data)

            // 检查 data 字段是否包含 items 数组
            if (response.data.data.items && Array.isArray(response.data.data.items)) {
              console.log('历史充电记录 items 字段:', response.data.data.items)
              recordsArray = response.data.data.items
              totalRecords = response.data.data.total || recordsArray.length
            } else if (Array.isArray(response.data.data)) {
              recordsArray = response.data.data
              totalRecords = recordsArray.length
            }
          } else if (response.data.items && Array.isArray(response.data.items)) {
            // 检查是否直接有 items 字段
            console.log('历史充电记录 items 字段:', response.data.items)
            recordsArray = response.data.items
            totalRecords = response.data.total || recordsArray.length
          } else if (Array.isArray(response.data)) {
            // 检查 response.data 是否直接是数组
            recordsArray = response.data
            totalRecords = recordsArray.length
          }
        } else if (response.status === 'success' && response.data) {
          // 检查 response 是否直接包含 data 对象
          if (response.data.items && Array.isArray(response.data.items)) {
            recordsArray = response.data.items
            totalRecords = response.data.total || recordsArray.length
          }
        }

        if (recordsArray) {
          // 确保每个记录都有必要的嵌套对象
          this.historyChargingRecords = recordsArray.map(record => {
            // 确保vehicle对象存在
            if (!record.vehicle) {
              record.vehicle = {
                number: '未知车牌',
                brand: '未知品牌',
                color: '未知颜色'
              }
            }
            // 确保parking_lot对象存在
            if (!record.parking_lot) {
              record.parking_lot = { name: '未知停车场' }
            }
            // 确保parking_space对象存在
            if (!record.parking_space) {
              record.parking_space = { space_number: '未知车位' }
            }
            return record
          })
          this.historyTotal = totalRecords
        } else {
          console.error('历史充电记录数据格式不正确 - 无法找到记录数组:', response.data)
          this.historyChargingRecords = []
          this.historyTotal = 0
        }

        this.historyLoading = false
      }).catch((error) => {
        console.error('获取历史充电记录失败:', error)
        this.historyChargingRecords = []
        this.historyTotal = 0
        this.historyLoading = false
      })
    },

    fetchVehicles() {
      getMyVehicles().then(response => {
        console.log('用户车辆数据完整响应:', response)
        console.log('用户车辆数据:', response.data)

        // 尝试直接使用响应数据
        let allVehicles = []

        if (response.data) {
          // 检查是否有 data 字段
          if (response.data.data) {
            console.log('用户车辆 data 字段:', response.data.data)
            // 检查 data 字段是否包含 bikes 字段
            if (response.data.data.bikes && Array.isArray(response.data.data.bikes)) {
              console.log('用户车辆 bikes 字段:', response.data.data.bikes)
              allVehicles = response.data.data.bikes.map(bike => ({
                b_id: bike.id,
                number: bike.bike_number,
                brand: bike.brand,
                color: bike.color
              }))
            } else if (Array.isArray(response.data.data)) {
              allVehicles = response.data.data.map(bike => ({
                b_id: bike.id,
                number: bike.bike_number,
                brand: bike.brand,
                color: bike.color
              }))
            } else {
              console.error('用户车辆数据格式不正确 - data 字段不包含 bikes 数组:', response.data)
              allVehicles = []
            }
          } else if (response.data.bikes && Array.isArray(response.data.bikes)) {
            // 检查是否直接有 bikes 字段
            console.log('用户车辆 bikes 字段:', response.data.bikes)
            allVehicles = response.data.bikes.map(bike => ({
              b_id: bike.id,
              number: bike.bike_number,
              brand: bike.brand,
              color: bike.color
            }))
          } else if (Array.isArray(response.data)) {
            // 检查 response.data 是否直接是数组
            allVehicles = response.data.map(bike => ({
              b_id: bike.id,
              number: bike.bike_number,
              brand: bike.brand,
              color: bike.color
            }))
          } else {
            console.error('用户车辆数据格式不正确 - 无法找到车辆数组:', response.data)
            allVehicles = []
          }
        } else {
          console.error('用户车辆数据格式不正确 - 无数据:', response)
          allVehicles = []
        }

        // 获取进行中的充电记录，以便过滤掉已经在充电的车辆
        this.filterAvailableVehicles(allVehicles)
      }).catch(error => {
        console.error('获取用户车辆失败:', error)
        this.availableVehicles = []
      })
    },

    // 过滤出没有正在充电和没有正在停车的车辆
    async filterAvailableVehicles(allVehicles) {
      // 显示加载状态
      this.lotsLoading = true

      try {
        // 获取所有正在充电的车辆ID
        const chargingVehicleIds = this.activeChargingRecords.map(record => record.vehicle_id)
        console.log('正在充电的车辆ID:', chargingVehicleIds)

        // 首先过滤掉正在充电的车辆
        let filteredVehicles = allVehicles.filter(vehicle => !chargingVehicleIds.includes(vehicle.b_id))
        console.log('过滤掉正在充电的车辆后:', filteredVehicles.length)

        // 检查每个车辆是否有进行中的停车记录
        const vehiclesWithParkingStatus = await Promise.all(
          filteredVehicles.map(async (vehicle) => {
            try {
              // 检查车辆是否有进行中的停车记录
              const response = await checkVehicleActiveParking(vehicle.b_id)
              const hasActiveParking = response?.data?.hasActiveParking || false

              return {
                ...vehicle,
                hasActiveParking
              }
            } catch (error) {
              console.error(`检查车辆 ${vehicle.b_id} 停车状态失败:`, error)
              return {
                ...vehicle,
                hasActiveParking: false // 出错时默认为没有进行中的停车记录
              }
            }
          })
        )

        // 过滤掉有进行中停车记录的车辆
        this.availableVehicles = vehiclesWithParkingStatus.filter(vehicle => !vehicle.hasActiveParking)

        // 记录被过滤掉的车辆
        const parkingVehicleIds = vehiclesWithParkingStatus
          .filter(vehicle => vehicle.hasActiveParking)
          .map(vehicle => vehicle.b_id)

        console.log('正在停车的车辆ID:', parkingVehicleIds)
        console.log('最终可用于充电的车辆:', this.availableVehicles)

        // 如果没有可用车辆，显示提示
        if (this.availableVehicles.length === 0 && allVehicles.length > 0) {
          this.$message.warning('您的所有车辆都在充电或停车中，暂时无法开始新的充电')
        }
      } catch (error) {
        console.error('过滤可用车辆失败:', error)
        this.$message.error('获取可用车辆失败')
        this.availableVehicles = []
      } finally {
        this.lotsLoading = false
      }
    },
    fetchParkingLots() {
      getChargingParkingLots().then(response => {
        console.log('充电停车场完整响应:', response)
        console.log('充电停车场数据:', response.data)

        // 尝试直接使用响应数据
        if (response.data) {
          // 检查是否有 data 字段
          if (response.data.data) {
            console.log('充电停车场 data 字段:', response.data.data)
            // 检查 data 字段是否为数组
            if (Array.isArray(response.data.data)) {
              this.parkingLots = response.data.data.filter(lot => lot.charging_spaces_total > 0)
            } else if (response.data.data.items && Array.isArray(response.data.data.items)) {
              // 检查是否有 items 字段
              console.log('充电停车场 items 字段:', response.data.data.items)
              this.parkingLots = response.data.data.items.filter(lot => lot.charging_spaces_total > 0)
            } else {
              console.error('充电停车场数据格式不正确 - data 字段不是数组:', response.data)
              this.parkingLots = []
            }
          } else if (response.data.items && Array.isArray(response.data.items)) {
            // 检查是否直接有 items 字段
            console.log('充电停车场 items 字段:', response.data.items)
            this.parkingLots = response.data.items.filter(lot => lot.charging_spaces_total > 0)
          } else if (Array.isArray(response.data)) {
            // 检查 response.data 是否直接是数组
            this.parkingLots = response.data.filter(lot => lot.charging_spaces_total > 0)
          } else {
            console.error('充电停车场数据格式不正确 - 无法找到停车场数组:', response.data)
            this.parkingLots = []
          }
        } else {
          console.error('充电停车场数据格式不正确 - 无数据:', response)
          this.parkingLots = []
        }
      }).catch(error => {
        console.error('获取充电停车场失败:', error)
        this.parkingLots = []
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.fetchHistoryChargingRecords()
    },

    // 处理下拉框排序变化
    handleSortChange() {
      console.log('下拉框排序变化:', this.listQuery.sort_field, this.listQuery.sort_order)

      // 确保排序参数是字符串类型
      this.listQuery.sort_field = String(this.listQuery.sort_field)
      this.listQuery.sort_order = String(this.listQuery.sort_order)

      // 添加时间戳，确保请求不会被缓存
      this.listQuery._t = new Date().getTime()

      // 重置页码并重新获取数据
      this.listQuery.page = 1

      // 打印最终的查询参数
      console.log('最终排序查询参数:', JSON.stringify(this.listQuery))

      // 使用延时，确保UI更新后再发送请求
      setTimeout(() => {
        this.fetchHistoryChargingRecords()
      }, 100)
    },

    // 处理表格排序变化
    handleTableSortChange({ column, prop, order }) {
      console.log('表格排序变化:', prop, order)

      if (!prop || !order) {
        return
      }

      // 映射排序字段
      const fieldMap = {
        'id': 'id',
        'start_time': 'start_time',
        'end_time': 'end_time'
      }

      // 映射排序顺序
      const orderMap = {
        'ascending': 'asc',
        'descending': 'desc'
      }

      // 设置排序参数
      this.listQuery.sort_field = fieldMap[prop] || 'id'
      this.listQuery.sort_order = orderMap[order] || 'asc'

      // 添加时间戳，确保请求不会被缓存
      this.listQuery._t = new Date().getTime()

      // 重置页码并重新获取数据
      this.listQuery.page = 1

      // 打印最终的查询参数
      console.log('表格排序最终查询参数:', JSON.stringify(this.listQuery))

      // 使用延时，确保UI更新后再发送请求
      setTimeout(() => {
        this.fetchHistoryChargingRecords()
      }, 100)
    },
    resetFilter() {
      this.listQuery = {
        page: 1,
        limit: 10,
        license: '',
        start_date: '',
        end_date: '',
        sort_field: 'id',
        sort_order: 'asc'
      }
      this.dateRange = []

      // 打印重置后的查询参数
      console.log('重置后的查询参数:', JSON.stringify(this.listQuery))

      // 重新获取数据
      this.fetchHistoryChargingRecords()
    },
    // 启动定时刷新
    startRefreshTimer() {
      // 清除可能存在的旧定时器
      this.clearRefreshTimer()

      // 设置新的定时器，每30秒刷新一次
      this.refreshTimer = setInterval(() => {
        // 只有当前标签页是"active"时才刷新
        if (this.activeTab === 'active' && this.activeChargingRecords.length > 0) {
          this.refreshActiveChargingRecords()
        }
      }, 30000) // 30秒
    },

    // 清除定时刷新
    clearRefreshTimer() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    // 刷新进行中的充电记录（不显示加载状态）
    refreshActiveChargingRecords() {
      getActiveChargingRecords().then(response => {
        console.log('刷新充电记录完整响应:', response)
        let recordsArray = null

        if (response.data) {
          if (response.data.data) {
            if (Array.isArray(response.data.data)) {
              recordsArray = response.data.data
            } else if (response.data.data.items && Array.isArray(response.data.data.items)) {
              recordsArray = response.data.data.items
            }
          } else if (response.data.items && Array.isArray(response.data.items)) {
            recordsArray = response.data.items
          } else if (Array.isArray(response.data)) {
            recordsArray = response.data
          }
        }

        if (recordsArray) {
          // 记录原始数据格式，便于调试
          if (recordsArray.length > 0) {
            console.log('刷新充电记录示例数据:', recordsArray[0])
            if (recordsArray[0].start_time) {
              console.log('刷新充电记录开始时间(原始):', recordsArray[0].start_time, '类型:', typeof recordsArray[0].start_time)
            }
          }

          this.activeChargingRecords = recordsArray.map(record => {
            // 记录每条数据的开始时间，便于调试
            if (record.start_time) {
              console.log(`刷新记录ID ${record.id} 的开始时间:`, record.start_time)
            }

            if (!record.vehicle) {
              record.vehicle = {
                number: '未知车牌',
                brand: '未知品牌',
                color: '未知颜色'
              }
            }
            if (!record.parking_lot) {
              record.parking_lot = { name: '未知停车场' }
            }
            if (!record.parking_space) {
              record.parking_space = { space_number: '未知车位' }
            }
            return record
          })
        }
      }).catch(error => {
        console.error('刷新进行中的充电记录失败:', error)
      })
    },

    handleDateRangeChange(val) {
      if (val) {
        this.listQuery.start_date = val[0]
        this.listQuery.end_date = val[1]
      } else {
        this.listQuery.start_date = ''
        this.listQuery.end_date = ''
      }
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.fetchHistoryChargingRecords()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.fetchHistoryChargingRecords()
    },
    formatDateTime(dateTime) {
      if (!dateTime) return '未设置'
      return parseTime(dateTime, '{y}-{m}-{d} {h}:{i}:{s}')
    },
    getStatusTagType(status) {
      switch (status) {
        case 0: return 'primary'
        case 1: return 'success'
        case 2: return 'danger'
        default: return 'info'
      }
    },



    // 处理结束充电
    handleEndCharging(record) {
      this.selectedChargingRecord = record
      this.endChargingDialogVisible = true
    },

    // 确认结束充电
    confirmEndCharging() {
      if (!this.selectedChargingRecord) return

      this.endingChargingId = this.selectedChargingRecord.id
      this.endingChargingLoading = true

      endCharging(this.selectedChargingRecord.id).then(response => {
        this.$message({
          message: '充电已成功结束',
          type: 'success'
        })
        this.endChargingDialogVisible = false

        // 刷新数据
        this.fetchActiveChargingRecords()
        this.fetchHistoryChargingRecords()
      }).catch(error => {
        this.$message({
          message: error.response?.data?.message || '结束充电失败，请重试',
          type: 'error'
        })
      }).finally(() => {
        this.endingChargingId = null
        this.endingChargingLoading = false
      })
    },
    goToChargingLots() {
      // 切换到充电预约标签页
      this.activeTab = 'reservations'
    },

    // 前往充电站点页面
    goToChargingStations() {
      this.$router.push('/charging/lots')
    },
    handleCreateReservation() {
      this.reservationForm = {
        vehicleId: '',
        parkingLotId: '',
        timeRange: [],
        remarks: ''
      }
      this.reservationDialogVisible = true
    },
    // 提交预约表单
    submitReservation() {
      this.$refs.reservationForm.validate(valid => {
        if (!valid) {
          return
        }

        this.creatingReservation = true

        const reservationData = {
          vehicle_id: this.reservationForm.vehicleId,
          parking_lot_id: this.reservationForm.parkingLotId,
          start_time: this.reservationForm.timeRange[0],
          end_time: this.reservationForm.timeRange[1],
          remarks: this.reservationForm.remarks
        }

        createChargingReservation(reservationData).then(response => {
          this.$message({
            message: '充电预约创建成功',
            type: 'success'
          })
          this.reservationDialogVisible = false
          this.fetchChargingReservations()

          // 如果预约时间是当前时间，提示用户可以立即开始充电
          const now = new Date()
          const startTime = new Date(reservationData.start_time)
          const timeDiff = Math.abs(startTime - now) / (1000 * 60) // 分钟差

          if (timeDiff < 30) { // 如果预约时间在30分钟内
            this.$confirm('预约已创建，是否立即开始充电?', '提示', {
              confirmButtonText: '是',
              cancelButtonText: '否',
              type: 'info'
            }).then(() => {
              // 切换到充电预约标签页
              this.activeTab = 'reservations'
              // 刷新预约列表
              this.fetchChargingReservations()
            }).catch(() => {})
          }
        }).catch(error => {
          this.$message({
            message: error.response?.data?.message || '创建预约失败，请重试',
            type: 'error'
          })
        }).finally(() => {
          this.creatingReservation = false
        })
      })
    },
    calculateDuration(startTime, endTime) {
      const start = new Date(startTime)
      const end = new Date(endTime)
      const diffMs = end - start
      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60))
      const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))

      return `${diffHrs}小时${diffMins}分钟`
    },
    getReservationStatusText(reservation) {
      const now = new Date()
      const startTime = new Date(reservation.start_time)
      const endTime = new Date(reservation.end_time)

      if (now < startTime) {
        return '未开始'
      } else if (now >= startTime && now <= endTime) {
        return '进行中'
      } else {
        return '已结束'
      }
    },
    getReservationStatusTagType(reservation) {
      const status = this.getReservationStatusText(reservation)
      switch (status) {
        case '未开始': return 'primary'
        case '进行中': return 'success'
        case '已结束': return 'info'
        default: return 'info'
      }
    },
    canCancelReservation(reservation) {
      const now = new Date()
      const startTime = new Date(reservation.start_time)

      // 只有未开始的预约可以取消
      return now < startTime
    },

    canStartCharging(reservation) {
      // 只有当前有效的预约才能开始充电
      const now = new Date()
      const startTime = new Date(reservation.start_time)
      const endTime = new Date(reservation.end_time)

      // 预约状态为未开始或进行中，且当前时间在预约时间范围内
      return (reservation.status === 0 || reservation.status === 1) &&
             now >= startTime && now <= endTime
    },

    // 从预约开始充电
    handleStartChargingFromReservation(reservation) {
      this.$confirm('确定要开始充电吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 获取预约对应的停车场
        const parkingLotId = reservation.parking_lot.id

        // 先获取该停车场的充电车位
        this.spacesLoading = true
        getParkingSpaces(parkingLotId, { type: 2 })
          .then(response => {
            const spaces = response.data || []
            // 找到一个可用的充电车位
            const availableSpace = spaces.find(space => space.status === 0)

            if (!availableSpace) {
              this.$message.error('当前没有可用的充电车位，请稍后再试')
              this.spacesLoading = false
              return
            }

            // 构建请求数据
            const data = {
              vehicle_id: reservation.vehicle.b_id,
              parking_lot_id: parkingLotId,
              parking_space_id: availableSpace.id,
              estimated_duration: 3, // 默认3小时
              remarks: `来自预约 #${reservation.id}`,
              reservation_id: reservation.id // 关联预约ID
            }

            // 发送开始充电请求
            startCharging(data)
              .then(response => {
                this.$message.success('开始充电成功')
                // 刷新充电记录和预约
                this.fetchActiveChargingRecords()
                this.fetchChargingReservations()
                this.spacesLoading = false
              })
              .catch(error => {
                console.error('开始充电失败', error)
                this.$message.error(error.response?.data?.message || '开始充电失败')
                this.spacesLoading = false
              })
          })
          .catch(error => {
            console.error('获取充电车位失败', error)
            this.$message.error('获取充电车位失败')
            this.spacesLoading = false
          })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        })
      })
    },
    handleCancelReservation(reservation) {
      this.$confirm('确定要取消该充电预约吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.reservationsLoading = true
        cancelChargingReservation(reservation.id).then(response => {
          this.$message({
            message: '充电预约已取消',
            type: 'success'
          })
          this.fetchChargingReservations()
        }).catch(error => {
          console.error('取消充电预约失败:', error)
          this.$message({
            message: error.response?.data?.message || '取消预约失败，请重试',
            type: 'error'
          })
          this.reservationsLoading = false
        })
      }).catch(() => {})
    },

    // 充电站点相关方法
    handleLotsFilter() {
      // 筛选逻辑已在计算属性中实现
    },

    resetLotsFilter() {
      this.lotsQuery = {
        search: '',
        campus: '',
        area: '',
        status: ''
      }
    },

    // 获取停车场利用率
    getUtilizationRate(lot) {
      if (!lot.charging_spaces_total || lot.charging_spaces_total === 0) return 0

      const occupied = this.getOccupiedSpaces(lot)
      const total = this.getTotalSpaces(lot)

      return Math.round((occupied / total) * 100)
    },

    // 获取已占用车位数量
    getOccupiedSpaces(lot) {
      return lot.charging_spaces_occupied || 0
    },

    // 获取总车位数量
    getTotalSpaces(lot) {
      return lot.charging_spaces_total || 0
    },

    // 获取可用车位数量
    getAvailableSpaces(lot) {
      const total = this.getTotalSpaces(lot)
      const occupied = this.getOccupiedSpaces(lot)
      return total - occupied
    },

    // 获取进度条颜色
    getProgressColor(rate) {
      if (rate < 60) return '#67c23a'  // 绿色
      if (rate < 80) return '#e6a23c'  // 黄色
      return '#f56c6c'  // 红色
    },

    // 根据车辆ID获取车辆名称
    getVehicleName(vehicleId) {
      const vehicle = this.availableVehicles.find(v => v.b_id === vehicleId)
      if (vehicle) {
        return `${vehicle.number} (${vehicle.brand} ${vehicle.color})`
      }
      return '未知车辆'
    },



    // 开始充电
    handleStartCharging(lot) {
      this.selectedLot = lot
      this.selectedSpaceId = null
      this.selectedSpace = null
      this.spaceDialogVisible = true
      this.fetchChargingSpaces(lot.id)

      // 设置对话框标题，表明是开始充电模式
      this.$nextTick(() => {
        const dialogHeader = document.querySelector('.charging-space-dialog .el-dialog__header')
        if (dialogHeader) {
          dialogHeader.querySelector('.el-dialog__title').textContent = `${lot.name} - 选择充电车位`
        }
      })
    },

    // 获取充电车位
    fetchChargingSpaces(lotId) {
      this.spacesLoading = true
      this.chargingSpaces = []

      // 使用充电专用API获取充电车位
      // 首先尝试使用充电专用API
      request({
        url: `/api/charging/parking-lots/${lotId}/spaces`,
        method: 'get'
      }).catch(error => {
        console.error('使用充电专用API获取充电车位失败，尝试使用通用API', error)
        // 如果充电专用API失败，尝试使用通用API
        return getParkingSpaces(lotId, { type: 3 }) // 类型3表示充电车位
      })
        .then(response => {
          console.log('获取充电车位响应:', response)

          // 处理不同的响应格式
          let spacesData = null

          if (response.data) {
            if (Array.isArray(response.data)) {
              spacesData = response.data
            } else if (response.data.data && Array.isArray(response.data.data)) {
              spacesData = response.data.data
            } else if (response.data.items && Array.isArray(response.data.items)) {
              spacesData = response.data.items
            }
          } else if (Array.isArray(response)) {
            spacesData = response
          }

          if (spacesData) {
            // 确保每个车位都有必要的属性
            this.chargingSpaces = spacesData.map(space => {
              // 如果没有功率，设置默认值
              if (!space.power) {
                space.power = 7.0 // 默认7kW
              }

              // 确保类型是数字
              space.status = Number(space.status)
              space.power = Number(space.power)

              return space
            })
          } else {
            console.error('无法解析充电车位数据:', response)
            this.chargingSpaces = []
          }

          this.spacesLoading = false
        })
        .catch(error => {
          console.error('获取充电车位失败', error)
          this.$message.error('获取充电车位失败')
          this.spacesLoading = false
        })
    },

    // 处理车位点击
    handleSpaceClick(space) {
      if (space.status !== 0) {
        if (space.status === 1) {
          this.$message.warning('该充电车位已被占用')
        } else if (space.status === 2) {
          this.$message.warning('该充电车位正在维护中')
        }
        return
      }

      this.selectedSpaceId = space.id
      this.selectedSpace = space

      // 检查是否有可用车辆
      if (this.availableVehicles.length === 0) {
        this.$message.warning('您没有可用于充电的车辆，可能是因为所有车辆都在停车或充电中')
        return
      }

      // 如果有车辆，默认选择第一个
      if (!this.chargingForm.vehicle_id) {
        this.chargingForm.vehicle_id = this.availableVehicles[0].b_id
      }
    },

    // 提交开始充电表单
    async submitStartCharging() {
      this.$refs.chargingForm.validate(async valid => {
        if (!valid) {
          return
        }

        this.startingCharging = true

        // 构建请求数据 - 使用字符串ID
        const data = {
          vehicle_id: this.chargingForm.vehicle_id,
          parking_lot_id: this.selectedLot.id,
          parking_space_id: this.selectedSpace.id,
          estimated_duration: this.chargingForm.estimated_duration,
          remarks: this.chargingForm.remarks || ''
        }

        console.log('开始充电请求数据:', data)

        // 检查数据是否存在
        if (!data.vehicle_id || !data.parking_lot_id || !data.parking_space_id || !data.estimated_duration) {
          this.$message.error('请确保所有必填字段都已填写')
          this.startingCharging = false
          return
        }

        try {
          // 再次检查车辆是否有进行中的停车记录
          const response = await checkVehicleActiveParking(data.vehicle_id)
          const hasActiveParking = response?.data?.hasActiveParking || false

          if (hasActiveParking) {
            this.$message.error('该车辆有进行中的停车记录，无法开始充电')
            this.startingCharging = false
            return
          }

          // 显示确认信息
          this.$confirm(`确认开始充电？
            <div style="margin-top: 10px; font-size: 14px; color: #606266;">
              <div>车辆：${this.getVehicleName(data.vehicle_id)}</div>
              <div>停车场：${this.selectedLot.name}</div>
              <div>车位：${this.selectedSpace.space_number}</div>
              <div>充电时长：${data.estimated_duration}小时</div>
            </div>
          `, '开始充电', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'info',
            dangerouslyUseHTMLString: true
          }).then(() => {
            // 发送请求
            startCharging(data)
              .then(response => {
                this.$message({
                  message: '开始充电成功',
                  type: 'success',
                  duration: 5000
                })
                this.spaceDialogVisible = false
                this.resetChargingForm()
                this.startingCharging = false

                // 刷新充电记录
                this.fetchActiveChargingRecords()

                // 切换到进行中的充电标签页
                this.activeTab = 'active'
              })
              .catch(error => {
                console.error('开始充电失败', error)

                // 提取错误信息
                let errorMessage = '开始充电失败，请检查参数'

                if (error.response && error.response.data) {
                  if (error.response.data.message) {
                    errorMessage = error.response.data.message
                  } else if (error.response.data.error) {
                    errorMessage = error.response.data.error
                  } else if (typeof error.response.data === 'string') {
                    errorMessage = error.response.data
                  }
                } else if (error.message) {
                  errorMessage = error.message
                }

                this.$message({
                  message: errorMessage,
                  type: 'error',
                  duration: 5000
                })
                this.startingCharging = false
              })
          }).catch(() => {
            this.startingCharging = false
          })
        } catch (error) {
          console.error('检查车辆停车状态失败:', error)
          this.$message.error('检查车辆停车状态失败，请重试')
          this.startingCharging = false
        }
      })
    },

    // 重置充电表单
    resetChargingForm() {
      this.$refs.chargingForm.resetFields()
      this.selectedSpaceId = null
      this.selectedSpace = null
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-records-container,
.charging-reservations-container {
  padding: 20px 0;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;

  .el-button {
    margin-top: 20px;
  }

  .empty-tip {
    margin-top: 15px;
    color: #909399;
    font-size: 14px;
  }

  .empty-buttons {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    justify-content: center;
  }
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .el-button {
    margin-left: 0;
  }
}

.charging-card {
  margin-bottom: 20px;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .vehicle-info {
      display: flex;
      flex-direction: column;

      .license-plate {
        font-size: 16px;
        font-weight: bold;
      }

      .vehicle-details {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .card-content {
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      i {
        margin-right: 10px;
        color: #909399;
        width: 16px;
      }
    }
  }

  .card-footer {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.end-charging-info {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;

  .info-item {
    display: flex;
    margin-bottom: 8px;

    .info-label {
      width: 80px;
      color: #909399;
    }

    .info-value {
      flex: 1;
    }
  }
}

// 充电站点相关样式
.charging-lots-section,
.my-reservations-section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .section-title {
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }

  .section-filter,
  .section-actions {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
  }
}

.charging-lots-container {
  margin-bottom: 30px;
}

.lot-col {
  margin-bottom: 20px;
  display: flex;

  /* 确保每列的高度相同 */
  .el-card {
    width: 100%;
    height: 100%;
  }
}

.lot-card {
  height: 100%;
  transition: all 0.3s;
  /* 确保所有卡片具有相同的最小宽度和高度 */
  min-width: 280px;
  min-height: 300px;
  width: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .lot-header {
    padding: 15px;
    background-color: #f5f7fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebeef5;

    .lot-name {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }

    .lot-status {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 10px;

      &.status-active {
        background-color: #f0f9eb;
        color: #67c23a;
      }

      &.status-inactive {
        background-color: #fef0f0;
        color: #f56c6c;
      }
    }
  }

  .lot-content {
    padding: 15px;
    /* 确保内容区域有固定的高度 */
    flex: 1;
    display: flex;
    flex-direction: column;

    .lot-tags {
      margin-bottom: 10px;
    }

    .lot-address {
      display: flex;
      align-items: flex-start;
      margin-bottom: 15px;
      color: #606266;

      i {
        margin-right: 5px;
        margin-top: 3px;
      }
    }

    .charging-stats {
      background-color: #f5f7fa;
      border-radius: 4px;
      padding: 10px;
      margin-bottom: 15px;
      /* 确保统计区域有固定的高度 */
      min-height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .utilization-rate {
        margin-bottom: 10px;

        .rate-label {
          font-size: 14px;
          color: #606266;
          margin-bottom: 5px;
        }

        .rate-value {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }
      }

      .spaces-count {
        display: flex;
        justify-content: space-between;

        .count-item {
          text-align: center;
          flex: 1;

          .count-label {
            font-size: 12px;
            color: #909399;
          }

          .count-value {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
          }
        }
      }
    }
  }

  .lot-footer {
    padding: 10px 15px;
    border-top: 1px solid #ebeef5;
    display: flex;
    justify-content: center;
    /* 确保底部区域有固定的高度 */
    min-height: 60px;
    align-items: center;

    .el-button {
      margin-left: 10px;
      width: 100%;
    }
  }
}

// 充电车位选择对话框样式
.charging-space-dialog {
  .space-dialog-content {
    display: flex;
    min-height: 400px;

    .spaces-grid-container {
      flex: 1;
      padding-right: 20px;
      border-right: 1px solid #ebeef5;
      min-height: 400px;
      display: flex;
      flex-direction: column;

      .no-spaces {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
      }

      .spaces-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 8px;
        padding: 10px;

        .space-item {
          aspect-ratio: 1;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s;
          position: relative;

          &.status-available {
            background-color: rgba(103, 194, 58, 0.1);
            border: 2px solid #67c23a;

            &:hover {
              background-color: rgba(103, 194, 58, 0.2);
              transform: translateY(-2px);
            }
          }

          &.status-occupied {
            background-color: rgba(245, 108, 108, 0.1);
            border: 2px solid #f56c6c;
            cursor: not-allowed;
          }

          &.status-maintenance {
            background-color: rgba(230, 162, 60, 0.1);
            border: 2px solid #e6a23c;
            cursor: not-allowed;
          }

          &.selected {
            background-color: rgba(64, 158, 255, 0.1);
            border: 2px solid #409EFF;

            &::after {
              content: '✓';
              position: absolute;
              top: -10px;
              right: -10px;
              width: 20px;
              height: 20px;
              background-color: #409EFF;
              color: white;
              border-radius: 50%;
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 12px;
            }
          }

          .space-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            padding: 5px;

            .space-number {
              font-size: 14px;
              font-weight: bold;
              color: #303133;
              margin-bottom: 2px;
            }

            .space-icon {
              font-size: 18px;
              color: #606266;
              margin-bottom: 2px;
            }

            .space-power {
              font-size: 12px;
              color: #606266;
              margin-bottom: 0;
            }

            .space-type {
              font-size: 11px;
              color: #909399;
            }
          }
        }
      }
    }

    .space-info-container {
      width: 250px;
      padding-left: 15px;

      .space-info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .space-number {
          font-size: 20px;
          font-weight: bold;
          color: #303133;
        }

        .space-type {
          font-size: 14px;
          color: #409EFF;
          background-color: #ecf5ff;
          padding: 2px 8px;
          border-radius: 10px;
        }
      }

      .space-info-content {
        .info-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 15px;

          .label {
            color: #909399;
          }

          .value {
            color: #303133;
            font-weight: bold;
          }
        }
      }
    }

    .space-info-placeholder {
      width: 250px;
      padding-left: 15px;
      display: flex;
      justify-content: center;
      align-items: center;

      .placeholder-content {
        text-align: center;
        color: #909399;

        i {
          font-size: 48px;
          margin-bottom: 10px;
        }

        .placeholder-text {
          font-size: 14px;
        }
      }
    }
  }

  .space-dialog-legend {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;

    .legend-item {
      display: flex;
      align-items: center;
      margin: 0 15px;

      .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 3px;
        margin-right: 5px;

        &.available {
          background-color: #67c23a;
        }

        &.occupied {
          background-color: #f56c6c;
        }

        &.maintenance {
          background-color: #e6a23c;
        }

        &.selected {
          background-color: #409EFF;
        }
      }

      .legend-text {
        font-size: 12px;
        color: #606266;
      }
    }
  }
}

@media (max-width: 1200px) {
  .charging-space-dialog {
    .space-dialog-content {
      .spaces-grid-container {
        .spaces-grid {
          grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
        }
      }
    }
  }
}

@media (max-width: 992px) {
  .charging-space-dialog {
    .space-dialog-content {
      .spaces-grid-container {
        .spaces-grid {
          grid-template-columns: repeat(auto-fill, minmax(65px, 1fr));
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .charging-space-dialog {
    .space-dialog-content {
      flex-direction: column;

      .spaces-grid-container {
        padding-right: 0;
        border-right: none;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 15px;
        margin-bottom: 15px;

        .spaces-grid {
          grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
        }
      }

      .space-info-container,
      .space-info-placeholder {
        width: 100%;
        padding-left: 0;
      }
    }
  }
}

// 自定义空状态样式
.empty-container {
  text-align: center;
  padding: 30px 0;

  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;

    .empty-text {
      font-size: 14px;
      color: #909399;
      margin-top: 10px;
    }
  }

  .empty-tip {
    color: #909399;
    margin: 10px 0 20px;
    font-size: 14px;
  }
}

.no-spaces {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .empty-text {
      font-size: 14px;
      color: #909399;
      margin-top: 10px;
    }
  }
}
</style>
