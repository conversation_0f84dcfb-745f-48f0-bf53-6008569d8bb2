from app import db
from datetime import datetime
from sqlalchemy.orm import foreign, remote
from app.bikes.models import Bikes
from app.users.models import Users

# 违规类型模型
class ViolationType(db.Model):
    __tablename__ = 'violation_types'

    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 类型名称
    name = db.Column(db.String(50), nullable=False, unique=True)
    # 类型描述
    description = db.Column(db.Text, nullable=True)
    # 是否需要管理员处理（0-不需要，1-需要）
    needs_admin = db.Column(db.Integer, nullable=False, default=0)
    # 创建时间
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<ViolationType {self.id}: {self.name}'

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'needs_admin': self.needs_admin,
            'created_at': str(self.created_at) if self.created_at else None,
            'updated_at': str(self.updated_at) if self.updated_at else None
        }

# 违规记录模型
class ViolationRecord(db.Model):
    __tablename__ = 'violation_records'

    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 车牌号
    bike_number = db.Column(db.String(50), nullable=False)
    # 车辆ID
    bike_id = db.Column(db.Integer, db.ForeignKey('bikes.b_id'), nullable=True)
    # 车主ID
    user_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=False)
    # 违规时间
    violation_time = db.Column(db.DateTime, nullable=False, default=datetime.now)
    # 违规地点
    location = db.Column(db.String(100), nullable=False)
    # 违规类型
    violation_type = db.Column(db.String(50), nullable=False)
    # 违规类型ID
    violation_type_id = db.Column(db.Integer, db.ForeignKey('violation_types.id'), nullable=True)
    # 违规描述
    description = db.Column(db.Text, nullable=True)
    # 处理状态：0-待审核，1-已处理，2-申诉中，3-已撤销
    # 使用 ViolationStatus 类中的常量定义
    status = db.Column(db.Integer, nullable=False, default=0)
    # 处理结果
    result = db.Column(db.Text, nullable=True)
    # 录入人ID（保安）
    recorder_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=False)
    # 处理人ID（管理员）
    handler_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=True)
    # 创建时间
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

    # 关联关系
    bike = db.relationship('Bikes', backref='violations', foreign_keys=[bike_id])
    user = db.relationship('Users', backref='violations_received', foreign_keys=[user_id])
    recorder = db.relationship('Users', backref='violations_recorded', foreign_keys=[recorder_id])
    handler = db.relationship('Users', backref='violations_handled', foreign_keys=[handler_id])
    appeals = db.relationship('Appeal', backref='violation', cascade="all, delete-orphan")
    evidences = db.relationship('Evidence', backref='violation',
                               primaryjoin="and_(foreign(Evidence.related_id)==ViolationRecord.id, "
                                          "Evidence.related_type=='violation')",
                               cascade="all, delete-orphan",
                               viewonly=True)

    def __repr__(self):
        return f'<ViolationRecord {self.id}: {self.bike_number}>'

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'bike_number': self.bike_number,
            'bike_id': self.bike_id,
            'user_id': self.user_id,
            'violation_time': str(self.violation_time) if self.violation_time else None,
            'location': self.location,
            'violation_type': self.violation_type,
            'description': self.description,
            'status': self.status,
            'status_text': self.get_status_text(),
            'result': self.result,
            'recorder_id': self.recorder_id,
            'handler_id': self.handler_id,
            'created_at': str(self.created_at) if self.created_at else None,
            'updated_at': str(self.updated_at) if self.updated_at else None,
            'user_name': self.user.u_name if self.user else None,
            'recorder_name': self.recorder.u_name if self.recorder else None,
            'handler_name': self.handler.u_name if self.handler else None
        }

    def get_status_text(self):
        """获取状态文本"""
        from app.utils.violation_status import ViolationStatus
        return ViolationStatus.get_text(self.status)

# 申诉记录模型
class Appeal(db.Model):
    __tablename__ = 'appeals'

    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 违规记录ID（添加唯一约束，确保一个违规记录只能有一个申诉）
    violation_id = db.Column(db.Integer, db.ForeignKey('violation_records.id'), nullable=False, unique=True)
    # 申诉用户ID
    user_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=False)
    # 申诉理由
    reason = db.Column(db.Text, nullable=False)
    # 申诉状态：0-待审核，1-已通过，2-未通过
    # 使用 AppealStatus 类中的常量定义
    status = db.Column(db.Integer, nullable=False, default=0)
    # 处理意见
    comment = db.Column(db.Text, nullable=True)
    # 处理人ID
    handler_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=True)
    # 创建时间
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

    # 关联关系
    user = db.relationship('Users', backref='appeals', foreign_keys=[user_id])
    handler = db.relationship('Users', backref='appeals_handled', foreign_keys=[handler_id])
    evidences = db.relationship('Evidence', backref='appeal',
                               primaryjoin="and_(foreign(Evidence.related_id)==Appeal.id, "
                                          "Evidence.related_type=='appeal')",
                               cascade="all, delete-orphan",
                               viewonly=True)

    def __repr__(self):
        return f'<Appeal {self.id} for Violation {self.violation_id}>'

    def get_status_text(self):
        """获取申诉状态文本"""
        from app.utils.violation_status import AppealStatus
        return AppealStatus.get_text(self.status)

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'violation_id': self.violation_id,
            'user_id': self.user_id,
            'reason': self.reason,
            'status': self.status,
            'status_text': self.get_status_text(),
            'comment': self.comment,
            'handler_id': self.handler_id,
            'created_at': str(self.created_at) if self.created_at else None,
            'updated_at': str(self.updated_at) if self.updated_at else None,
            'user_name': self.user.u_name if self.user else None,
            'handler_name': self.handler.u_name if self.handler else None
        }

    def get_status_text(self):
        """获取状态文本"""
        from app.utils.violation_status import AppealStatus
        return AppealStatus.get_text(self.status)

# 证据模型
class Evidence(db.Model):
    __tablename__ = 'evidences'

    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 关联ID（违规记录ID或申诉ID）
    related_id = db.Column(db.Integer, nullable=False)
    # 关联类型（violation或appeal）
    related_type = db.Column(db.String(20), nullable=False)
    # 证据类型（image或video）
    evidence_type = db.Column(db.String(20), nullable=False)
    # 文件路径
    file_path = db.Column(db.String(255), nullable=False)
    # 上传人ID
    uploader_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=False)
    # 上传时间
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)

    # 关联关系
    uploader = db.relationship('Users', backref='evidences')

    def __repr__(self):
        return f'<Evidence {self.id} for {self.related_type} {self.related_id}>'

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'related_id': self.related_id,
            'related_type': self.related_type,
            'evidence_type': self.evidence_type,
            'file_path': self.file_path,
            'uploader_id': self.uploader_id,
            'created_at': str(self.created_at) if self.created_at else None,
            'uploader_name': self.uploader.u_name if self.uploader else None
        }

# 状态变更日志模型
class StatusChangeLog(db.Model):
    __tablename__ = 'status_change_logs'

    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 实体类型（violation, appeal）
    entity_type = db.Column(db.String(20), nullable=False)
    # 实体ID
    entity_id = db.Column(db.Integer, nullable=False)
    # 旧状态
    old_status = db.Column(db.Integer, nullable=False)
    # 新状态
    new_status = db.Column(db.Integer, nullable=False)
    # 操作人ID
    operator_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=False)
    # 变更时间
    change_time = db.Column(db.DateTime, nullable=False, default=datetime.now)
    # 相关实体（JSON字符串）
    related_entities = db.Column(db.Text, nullable=True)

    # 关联关系
    operator = db.relationship('Users', backref='status_changes')

    def __repr__(self):
        return f'<StatusChangeLog {self.id} for {self.entity_type} {self.entity_id}>'

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'entity_type': self.entity_type,
            'entity_id': self.entity_id,
            'old_status': self.old_status,
            'new_status': self.new_status,
            'operator_id': self.operator_id,
            'change_time': str(self.change_time) if self.change_time else None,
            'related_entities': self.related_entities,
            'operator_name': self.operator.u_name if self.operator else None
        }

# 车辆禁用记录模型
class VehicleDisableRecord(db.Model):
    __tablename__ = 'vehicle_disable_records'

    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 车辆ID
    bike_id = db.Column(db.Integer, db.ForeignKey('bikes.b_id'), nullable=False)
    # 关联的违规记录ID（可为空，表示手动禁用）
    violation_id = db.Column(db.Integer, db.ForeignKey('violation_records.id'), nullable=True)
    # 禁用开始时间
    disable_start_time = db.Column(db.DateTime, nullable=False, default=datetime.now)
    # 禁用结束时间（解除禁用时设置）
    disable_end_time = db.Column(db.DateTime, nullable=True)
    # 是否激活（用于标记当前是否有效）
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    # 禁用原因（当没有关联违规记录时使用）
    reason = db.Column(db.Text, nullable=True)
    # 禁用操作人ID
    operator_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=True)
    # 解除禁用原因
    enable_reason = db.Column(db.Text, nullable=True)
    # 解除禁用操作人ID
    enable_operator_id = db.Column(db.Integer, db.ForeignKey('users.u_id'), nullable=True)
    # 创建时间
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)
    # 更新时间
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

    # 关联关系
    bike = db.relationship('Bikes', backref='disable_records')
    violation = db.relationship('ViolationRecord', backref='disable_records')
    operator = db.relationship('Users', backref='disable_operations', foreign_keys=[operator_id])
    enable_operator = db.relationship('Users', backref='enable_operations', foreign_keys=[enable_operator_id])

    def __repr__(self):
        return f'<VehicleDisableRecord {self.id} for Bike {self.bike_id}>'

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'bike_id': self.bike_id,
            'violation_id': self.violation_id,
            'disable_start_time': str(self.disable_start_time) if self.disable_start_time else None,
            'disable_end_time': str(self.disable_end_time) if self.disable_end_time else None,
            'is_active': self.is_active,
            'reason': self.reason,
            'operator_id': self.operator_id,
            'enable_reason': self.enable_reason,
            'enable_operator_id': self.enable_operator_id,
            'created_at': str(self.created_at) if self.created_at else None,
            'updated_at': str(self.updated_at) if self.updated_at else None,
            'bike_number': self.bike.b_num if self.bike else None,
            'operator_name': self.operator.u_name if self.operator else None,
            'enable_operator_name': self.enable_operator.u_name if self.enable_operator else None,
            'violation_type': self.violation.violation_type if self.violation else None,
            'violation_status': self.violation.status if self.violation else None,
            'violation_status_text': self.violation.get_status_text() if self.violation else None
        }

    def get_disable_reason(self):
        """获取禁用原因"""
        if self.reason:
            return self.reason
        elif self.violation:
            return f"违规原因: {self.violation.violation_type}, 地点: {self.violation.location}, 时间: {self.violation.violation_time}"
        else:
            return "未知原因"
