<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>违规记录管理</span>
      </div>

      <violation-list
        :fetch-list-method="fetchAllViolations"
        :show-create-button="false"
        :show-date-range="true"
        :show-recorder="true"
        :show-handler="true"
        :can-appeal="false"
        :can-handle="true"
        @detail="handleDetail"
        @process="handleProcess"
      />
    </el-card>
  </div>
</template>

<script>
import { getAllViolations, handleViolation } from '@/api/violations'
import ViolationList from '../components/ViolationList'

export default {
  name: 'AdminViolationRecords',
  components: {
    ViolationList
  },
  data() {
    return {
      processDialogVisible: false,
      currentViolation: null,
      processForm: {
        status: 1,
        fine_amount: 0,
        result: ''
      }
    }
  },
  methods: {
    fetchAllViolations(params) {
      return getAllViolations(params)
    },

    handleDetail(row) {
      this.$router.push({ name: 'AdminViolationDetail', params: { id: row.id }})
    },
    handleProcess(row) {
      this.$router.push({ name: 'AdminViolationDetail', params: { id: row.id }})
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
</style>
