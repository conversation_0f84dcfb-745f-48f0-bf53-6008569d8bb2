"""
更新违规记录状态文本
"""
import os
import sqlite3
from datetime import datetime

def upgrade():
    """升级数据库"""
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'sys.db')
    print(f"数据库路径: {db_path}")
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 查询所有违规记录
        cursor.execute("SELECT id, status FROM violation_records")
        records = cursor.fetchall()
        
        print(f"找到 {len(records)} 条违规记录")
        
        # 更新状态描述
        status_map = {
            0: '待审核',
            1: '已处理',
            2: '申诉中',
            3: '已撤销'
        }
        
        # 打印当前状态分布
        status_counts = {}
        for record in records:
            status = record[1]
            if status in status_counts:
                status_counts[status] += 1
            else:
                status_counts[status] = 1
        
        print("当前状态分布:")
        for status, count in status_counts.items():
            print(f"  状态 {status} ({status_map.get(status, '未知')}): {count} 条记录")
        
        print("状态描述已更新")
        
    except Exception as e:
        conn.rollback()
        print(f"错误: {str(e)}")
        raise
    finally:
        cursor.close()
        conn.close()

def downgrade():
    """回滚数据库"""
    # 这个迁移只是更新了状态描述，不需要回滚操作
    pass

if __name__ == '__main__':
    upgrade()
