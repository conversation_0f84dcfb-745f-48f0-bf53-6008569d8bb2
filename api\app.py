import os
import sys
from app import create_app, db
from app.parkinglots.models import ParkingLot, ParkingSpace
from app.charging.models import Charging<PERSON><PERSON>, ChargingException

app = create_app()

# 重置数据库并创建测试数据
def reset_and_create_test_data():
    """重置数据库并创建测试数据"""
    try:
        with app.app_context():
            print("清空停车场表...")
            # 删除所有车位
            ParkingSpace.query.delete()
            # 删除所有停车场
            ParkingLot.query.delete()
            db.session.commit()

            print("添加测试停车场数据...")
            test_lots = [
                {
                    'name': '北门停车场',
                    'address': '校园北门附近',
                    'total_spaces': 20,
                    'status': 1,
                    'description': '位于学校北门，靠近图书馆'
                },
                {
                    'name': '东区停车场',
                    'address': '东区教学楼旁',
                    'total_spaces': 30,
                    'status': 1,
                    'description': '东区主停车场，靠近食堂'
                },
                {
                    'name': '科研楼停车场',
                    'address': '科研楼南侧',
                    'total_spaces': 15,
                    'status': 1,
                    'description': '仅供科研人员使用'
                }
            ]

            for lot_data in test_lots:
                new_lot = ParkingLot(
                    name=lot_data['name'],
                    address=lot_data['address'],
                    total_spaces=lot_data['total_spaces'],
                    status=lot_data['status'],
                    description=lot_data['description']
                )
                db.session.add(new_lot)
                db.session.flush()  # 获取ID但不提交

                # 添加车位
                for i in range(1, lot_data['total_spaces'] + 1):
                    space_number = f"{new_lot.id}-{i:03d}"
                    space = ParkingSpace(
                        parking_lot_id=new_lot.id,
                        space_number=space_number,
                        type=1,  # 普通车位
                        status=0  # 空闲
                    )
                    db.session.add(space)

            # 提交所有更改
            db.session.commit()

            # 验证数据是否添加成功
            final_count = ParkingLot.query.count()
            print(f"现在数据库中有 {final_count} 个停车场")

            # 输出所有停车场记录
            all_lots = ParkingLot.query.all()
            print("所有停车场记录:")
            for lot in all_lots:
                print(f"  ID: {lot.id}, 名称: {lot.name}, 地址: {lot.address}")
                spaces_count = ParkingSpace.query.filter_by(parking_lot_id=lot.id).count()
                print(f"    关联车位数: {spaces_count}")

            print("测试数据添加成功")
    except Exception as e:
        print(f"重置数据库和创建测试数据出错: {e}")
        db.session.rollback()

if __name__ == '__main__':
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--reset':
        print("收到重置数据库参数，准备重置数据库...")
        reset_and_create_test_data()

    # 无论是否重置，都检查是否有数据
    with app.app_context():
        # 确保所有表都已创建
        print("确保所有数据库表都已创建...")
        db.create_all()

        total_count = ParkingLot.query.count()
        if total_count == 0:
            print("数据库中没有停车场记录，添加测试数据...")
            reset_and_create_test_data()
        else:
            print(f"数据库中已有 {total_count} 个停车场，不添加测试数据")

    # 启动应用
    app.run(debug=True, host='127.0.0.1', port=5000)