<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="6">
        <!-- 用户信息卡片 -->
        <el-card class="profile-card info-section" shadow="hover">
          <div class="avatar-container">
            <el-avatar :size="120" :src="userInfo.avatar || defaultAvatar" />
            <h3 class="user-name">{{ userInfo.name || userInfo.username }}</h3>
            <el-tag
              class="role-tag"
              :type="userInfo.role === 'admin' ? 'danger' : userInfo.role === 'security' ? 'warning' : 'success'"
            >
              {{ getRoleName(userInfo.role) }}
            </el-tag>
            <p class="user-id">ID: {{ userInfo.id }}</p>
          </div>

          <div class="card-footer">
            <el-button v-if="!isEditing" type="primary" plain round icon="el-icon-edit" @click="startEdit">
              编辑资料
            </el-button>
            <el-button type="text" @click="showChangePassword = true">修改密码</el-button>
          </div>
        </el-card>

        <!-- 统计信息卡片 -->
        <el-card class="profile-card stat-section" shadow="hover">
          <div slot="header" class="clearfix">
            <span class="section-title">账号信息</span>
          </div>
          <div class="stat-container">
            <div class="stat-item">
              <el-tooltip content="账号注册时间" placement="top">
                <div class="stat-icon">
                  <i class="el-icon-time" />
                </div>
              </el-tooltip>
              <div class="stat-info">
                <div class="stat-title">注册时间</div>
                <div class="stat-value">{{ formatTime(userInfo.created_at) }}</div>
              </div>
            </div>

            <div v-if="userInfo.updated_at && userInfo.created_at !== userInfo.updated_at" class="stat-item">
              <el-tooltip content="最后更新资料时间" placement="top">
                <div class="stat-icon">
                  <i class="el-icon-refresh" />
                </div>
              </el-tooltip>
              <div class="stat-info">
                <div class="stat-title">最后更新</div>
                <div class="stat-value">{{ formatTime(userInfo.updated_at) }}</div>
              </div>
            </div>

            <div class="stat-item">
              <el-tooltip content="数据同步状态" placement="top">
                <div class="stat-icon">
                  <i class="el-icon-connection" />
                </div>
              </el-tooltip>
              <div class="stat-info">
                <div class="stat-title">数据同步</div>
                <div class="stat-value">
                  <el-tag v-if="lastUserUpdate" size="small" type="success">
                    已同步
                  </el-tag>
                  <el-tag v-else size="small" type="info">
                    未同步
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="16" :lg="18" :xl="18">
        <!-- 用户详细信息卡片 -->
        <el-card class="profile-card detail-section" shadow="hover">
          <div slot="header" class="clearfix">
            <span class="section-title">{{ isEditing ? '编辑个人信息' : '个人信息' }}</span>
            <div class="header-btns">
              <el-button v-if="lastUserUpdate && !isEditing" size="mini" type="text">
                最后更新: {{ formatLastUpdateTime() }}
              </el-button>
            </div>
          </div>

          <!-- 查看模式 -->
          <div v-if="!isEditing" class="user-detail-container">
            <div class="detail-item">
              <div class="detail-icon"><i class="el-icon-user" /></div>
              <div class="detail-label">用户名：</div>
              <div class="detail-value">{{ userInfo.username }}</div>
            </div>

            <div class="detail-item">
              <div class="detail-icon"><i class="el-icon-user" /></div>
              <div class="detail-label">姓名：</div>
              <div class="detail-value">{{ userInfo.name || '未设置' }}</div>
            </div>

            <div class="detail-item">
              <div class="detail-icon"><i class="el-icon-school" /></div>
              <div class="detail-label">学院：</div>
              <div class="detail-value">{{ userInfo.department || '未设置' }}</div>
            </div>

            <div class="detail-item">
              <div class="detail-icon"><i class="el-icon-phone-outline" /></div>
              <div class="detail-label">联系方式：</div>
              <div class="detail-value">{{ userInfo.phone || '未设置' }}</div>
            </div>

            <div class="detail-item">
              <div class="detail-icon"><i class="el-icon-message" /></div>
              <div class="detail-label">邮箱：</div>
              <div class="detail-value">{{ userInfo.email || '未设置' }}</div>
            </div>
          </div>

          <!-- 编辑模式 -->
          <el-form
            v-else
            ref="userForm"
            :model="userForm"
            :rules="rules"
            label-width="100px"
            class="user-form"
          >
            <div class="avatar-upload">
              <el-upload
                class="avatar-uploader"
                action="/api/users/me/avatar"
                :headers="uploadHeaders"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="userForm.avatar" :src="userForm.avatar" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon" />
              </el-upload>
              <div class="avatar-tip">点击上传新头像（支持JPG/PNG格式，最大10MB）</div>
            </div>

            <el-form-item label="用户名" prop="username">
              <el-input v-model="userForm.username" disabled />
              <div class="form-tip">用户名不可修改</div>
            </el-form-item>

            <el-form-item label="姓名" prop="name">
              <el-input v-model="userForm.name" placeholder="请输入您的真实姓名" />
            </el-form-item>

            <el-form-item label="学院" prop="department">
              <el-input v-model="userForm.department" placeholder="请输入您所在的学院" />
            </el-form-item>

            <el-form-item label="联系方式" prop="phone">
              <el-input v-model="userForm.phone" placeholder="请输入您的手机号码" />
            </el-form-item>

            <el-form-item label="邮箱" prop="email">
              <el-input v-model="userForm.email" placeholder="请输入您的电子邮箱" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" :loading="loading" round @click="saveUserInfo">
                {{ loading ? '保存中...' : '保存' }}
              </el-button>
              <el-button :disabled="loading" round @click="cancelEdit">取消</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      :visible.sync="showChangePassword"
      width="400px"
      :close-on-click-modal="false"
    >


      <el-form
        ref="passwordForm"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button round @click="showChangePassword = false">取消</el-button>
        <el-button type="primary" :loading="passwordLoading" round @click="changePassword">
          确认修改
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserInfo, updateUserInfo, changePassword } from '@/api/user'
import { mapGetters } from 'vuex'
import request from '@/utils/request'

export default {
  name: 'UserProfile',
  data() {
    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback() // 允许为空
        return
      }
      // 中国手机号验证规则
      if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error('请输入正确的手机号码'))
      } else {
        callback()
      }
    }

    const validateEmail = (rule, value, callback) => {
      if (!value) {
        callback() // 允许为空
        return
      }
      // 邮箱格式验证
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        callback(new Error('请输入正确的邮箱地址'))
      } else {
        callback()
      }
    }

    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入密码'))
        return
      }
      // 密码强度验证：至少8位，包含字母和数字
      if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/.test(value)) {
        callback(new Error('密码至少8位，必须包含字母和数字'))
      } else {
        callback()
      }
    }

    const validateConfirmPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请再次输入密码'))
        return
      }
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }

    return {
      defaultAvatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
      userInfo: {
        id: '',
        username: '',
        name: '',
        department: '',
        phone: '',
        email: '',
        role: '',
        avatar: '',
        created_at: null,
        updated_at: null
      },
      userForm: {
        id: '',
        username: '',
        name: '',
        department: '',
        phone: '',
        email: '',
        avatar: ''
      },
      rules: {
        name: [
          { max: 20, message: '姓名不能超过20个字符', trigger: 'blur' }
        ],
        phone: [
          { validator: validatePhone, trigger: 'blur' }
        ],
        email: [
          { validator: validateEmail, trigger: 'blur' }
        ]
      },
      isEditing: false,
      loading: false,
      lastUserUpdate: null,
      showChangePassword: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      passwordLoading: false,
      uploadHeaders: {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      },
      refreshTimer: null,
      refreshInterval: 30000
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'roles'
    ])
  },
  created() {
    // 检查路由参数中是否包含userId
    const routeUserId = this.$route.params.userId || this.$route.query.userId
    if (routeUserId) {
      console.log('从路由参数获取用户ID:', routeUserId)
      this.fetchUserInfo(parseInt(routeUserId, 10))
    } else {
      // 获取当前登录用户信息
      this.fetchUserInfo()
    }
  },
  mounted() {
    this.fetchUserInfo()
    this.startAutoRefresh()
  },
  beforeDestroy() {
    this.stopAutoRefresh()
  },
  methods: {
    async fetchUserInfo(userId) {
      this.loading = true
      console.log('当前登录用户ID:', this.$store.state.user.userId, '请求的用户ID:', userId)

      // 如果提供的userId与当前登录用户不同，且不是管理员，直接使用当前用户ID
      const roles = this.$store.getters.roles || []
      const isAdmin = Array.isArray(roles) ? roles.includes('admin') : roles === 'admin'
      const currentUserId = this.$store.state.user.userId

      if (userId && userId !== currentUserId && !isAdmin) {
        console.log('非管理员用户尝试查看其他用户信息，改为查看自己')
        userId = currentUserId
        this.$message.warning('您只能查看自己的个人信息')
      }

      try {
        console.log('开始获取用户信息，用户ID:', userId || this.$store.state.user.userId)

        try {
          // 尝试从API获取用户信息
          let response

          // 对于当前登录的用户，使用/me端点，这样不会有权限问题
          if (!userId || userId === currentUserId) {
            console.log('获取当前用户信息，使用/api/users/me端点')
            response = await request({
              url: '/api/users/me',
              method: 'get'
            })
          } else {
            // 管理员查看其他用户信息
            console.log('获取其他用户信息，使用/api/users/{id}端点')
            response = await getUserInfo(userId)
          }

          console.log('获取用户信息响应:', response)

          // 处理响应数据
          let userData = null
          if (response && response.data && response.data.user) {
            userData = response.data.user
          } else if (response && response.data) {
            userData = response.data
          } else if (response && response.user) {
            userData = response.user
          } else {
            userData = response
          }

          // 规范化用户数据
          const normalizedData = this.normalizeUserData(userData)
          console.log('规范化后的用户数据:', normalizedData)

          // 更新用户信息
          this.userInfo = {
            id: normalizedData.id || userId || this.$store.state.user.userId,
            username: normalizedData.username || this.$store.state.user.name,
            name: normalizedData.name || normalizedData.username || this.$store.state.user.name,
            department: normalizedData.department || '',
            phone: normalizedData.phone || '',
            email: normalizedData.email || '',
            role: normalizedData.role || (this.$store.state.user.roles && this.$store.state.user.roles[0]) || 'user',
            avatar: normalizedData.avatar || '',
            created_at: normalizedData.created_at || new Date().toISOString(),
            updated_at: normalizedData.updated_at || new Date().toISOString()
          }

          // 复制信息到表单
          this.userForm = { ...this.userInfo }

          console.log('用户信息更新完成:', this.userInfo)
        } catch (apiError) {
          console.warn('API获取用户信息失败:', apiError)

          // 显示错误信息但不要中断
          if (apiError.message && apiError.message.includes('权限')) {
            this.$message.error('权限错误: ' + apiError.message)
            // 如果是权限问题，确保使用当前登录用户的信息
            userId = this.$store.state.user.userId
          } else {
            this.$message.warning('获取详细信息失败，将使用基本信息')
          }

          // 从store中获取基本信息
          this.userInfo = {
            id: this.$store.state.user.userId,
            username: this.$store.state.user.name,
            name: this.$store.state.user.name,
            role: (this.$store.state.user.roles && this.$store.state.user.roles[0]) || 'user',
            avatar: this.$store.state.user.avatar || '',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }

          // 复制信息到表单
          this.userForm = { ...this.userInfo }
        }

        // 强制刷新页面数据
        this.$forceUpdate()
        this.loading = false
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.$message.error('获取用户信息失败: ' + (error.message || '未知错误'))
        this.loading = false
      }
    },

    getRoleName(role) {
      if (!role) return '普通用户'
      const roleMap = {
        'admin': '管理员',
        'user': '普通用户'
      }
      return roleMap[role] || role || '普通用户'
    },

    formatTime(timestamp) {
      if (!timestamp) return '未知时间'

      try {
        const date = new Date(timestamp)
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } catch (e) {
        return timestamp
      }
    },

    startEdit() {
      this.isEditing = true
    },

    cancelEdit() {
      this.isEditing = false
      this.userForm = { ...this.userInfo }
    },

    saveUserInfo() {
      this.$refs.userForm.validate(async valid => {
        if (valid) {
          this.loading = true
          try {
            // 准备数据，使用前端字段名
            const userData = {
              id: this.userForm.id,
              name: this.userForm.name,
              department: this.userForm.department,
              phone: this.userForm.phone,
              email: this.userForm.email,
              avatar: this.userForm.avatar
            }

            console.log('准备提交用户信息:', userData)

            // 调用API更新用户信息
            const response = await updateUserInfo(this.userForm.id, userData)
            console.log('更新用户信息响应:', response)

            // 提取更新后的用户信息并确保字段名称统一
            let updatedUser = {}
            if (response && response.data && response.data.user) {
              updatedUser = this.normalizeUserData(response.data.user)
            } else if (response && response.data) {
              updatedUser = this.normalizeUserData(response.data)
            } else if (response && response.user) {
              updatedUser = this.normalizeUserData(response.user)
            } else {
              updatedUser = this.normalizeUserData(response)
            }

            console.log('规范化后的用户数据:', updatedUser)

            // 确保所有字段正确更新
            this.userInfo = {
              id: updatedUser.id || this.userInfo.id,
              username: updatedUser.username || this.userInfo.username,
              name: updatedUser.name || this.userInfo.name,
              department: updatedUser.department || this.userForm.department || this.userInfo.department,
              phone: updatedUser.phone || this.userForm.phone || this.userInfo.phone,
              email: updatedUser.email || this.userForm.email || this.userInfo.email,
              role: updatedUser.role || this.userInfo.role,
              avatar: updatedUser.avatar || this.userForm.avatar || this.userInfo.avatar,
              created_at: updatedUser.created_at || this.userInfo.created_at,
              updated_at: updatedUser.updated_at || new Date().toISOString()
            }

            // 更新表单数据，确保下次编辑时使用最新值
            this.userForm = { ...this.userInfo }

            // 记录最近一次修改的时间戳
            this.lastUserUpdate = Date.now()

            this.$message.success('个人信息更新成功')
            this.isEditing = false

            // 先更新Vuex store中的用户信息
            await this.$store.dispatch('user/getInfo')

            // 然后重新获取用户信息以确保显示最新数据
            await this.fetchUserInfo(this.userInfo.id)

            // 强制刷新页面数据
            this.$forceUpdate()
          } catch (error) {
            console.error('更新用户信息失败:', error)

            let errorMessage = '更新用户信息失败'

            if (error.response) {
              if (error.response.status === 422 && error.response.data) {
                const data = error.response.data
                if (data.errors) {
                  const firstField = Object.keys(data.errors)[0]
                  if (firstField && data.errors[firstField][0]) {
                    errorMessage += `: ${firstField} - ${data.errors[firstField][0]}`
                  } else {
                    errorMessage += ': ' + data.message
                  }
                } else if (data.message) {
                  errorMessage += ': ' + data.message
                }
              } else if (error.response.data && error.response.data.message) {
                errorMessage += ': ' + error.response.data.message
              } else {
                errorMessage += ` (HTTP ${error.response.status})`
              }
            } else if (error.message) {
              errorMessage += ': ' + error.message
            }

            this.$message.error(errorMessage)
          } finally {
            this.loading = false
          }
        }
      })
    },

    // 规范化用户数据，处理不同的字段命名
    normalizeUserData(userData) {
      if (!userData) return {}

      console.log('开始规范化用户数据:', userData)

      // 字段名映射
      const fieldMap = {
        u_id: 'id',
        u_name: 'username',
        u_belong: 'department',
        u_phone: 'phone',
        u_email: 'email',
        u_role: 'role',
        u_avatar: 'avatar'
      }

      const result = { ...userData }

      // 处理旧字段格式到新字段格式的转换
      Object.keys(fieldMap).forEach(oldField => {
        const newField = fieldMap[oldField]
        if (userData[oldField] !== undefined && userData[newField] === undefined) {
          result[newField] = userData[oldField]
        }
      })

      // 确保所有字段都有值
      const normalizedData = {
        id: result.id || result.u_id,
        username: result.username || result.u_name,
        name: result.name || result.u_name || result.username,
        department: result.department || result.u_belong,
        phone: result.phone || result.u_phone,
        email: result.email || result.u_email,
        role: result.role || result.u_role,
        avatar: result.avatar || result.u_avatar,
        created_at: result.created_at,
        updated_at: result.updated_at || new Date().toISOString()
      }

      console.log('规范化后的用户数据:', normalizedData)
      return normalizedData
    },

    // 格式化最后更新时间
    formatLastUpdateTime() {
      if (!this.lastUserUpdate) return ''

      const now = Date.now()
      const diff = now - this.lastUserUpdate

      if (diff < 60000) {
        return '刚刚更新'
      } else if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前更新`
      } else if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前更新`
      } else {
        return `${Math.floor(diff / 86400000)}天前更新`
      }
    },

    // 头像上传相关方法
    handleAvatarSuccess(res, file) {
      console.log('头像上传响应:', res)
      if (res.code === 20000 || res.status === 'success') {
        this.userForm.avatar = res.data?.url || res.data?.avatar
        this.$message.success('头像上传成功')
        // 直接更新用户信息以保存头像URL
        this.saveUserInfo()
      } else {
        this.$message.error('头像上传失败：' + (res.message || '未知错误'))
      }
    },

    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG/PNG 格式!')
      }
      if (!isLt10M) {
        this.$message.error('上传头像图片大小不能超过 10MB!')
      }
      return isJPG && isLt10M
    },

    // 修改密码
    changePassword() {
      this.$refs.passwordForm.validate(async valid => {
        if (valid) {
          this.passwordLoading = true
          try {
            await changePassword(this.passwordForm)
            this.$message.success('密码修改成功，请重新登录')
            this.showChangePassword = false
            // 退出登录
            await this.$store.dispatch('user/logout')
            this.$router.push('/login')
          } catch (error) {
            this.$message.error('密码修改失败：' + (error.message || '未知错误'))
          } finally {
            this.passwordLoading = false
          }
        }
      })
    },

    // 启动自动刷新
    startAutoRefresh() {
      this.stopAutoRefresh()
      this.refreshTimer = setInterval(() => {
        this.fetchUserInfo()
      }, this.refreshInterval)
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },


  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.profile-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.info-section {
  text-align: center;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.user-name {
  margin: 15px 0 8px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.user-id {
  margin: 10px 0;
  color: #909399;
  font-size: 13px;
}

.role-tag {
  margin: 5px 0;
}

.card-footer {
  padding: 15px 0 5px;
  border-top: 1px solid #f0f2f5;
  margin-top: 10px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-btns {
  float: right;
}

/* 统计信息样式 */
.stat-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 5px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ecf5ff;
  color: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-title {
  font-size: 13px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

/* 详细信息样式 */
.user-detail-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 10px 0;
}

.detail-item {
  flex: 1 1 45%;
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 6px;
  min-width: 250px;
}

.detail-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f0f9eb;
  color: #67c23a;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
}

.detail-label {
  color: #606266;
  width: 90px;
  font-weight: 500;
}

.detail-value {
  flex: 1;
  color: #303133;
  font-weight: 400;
}

/* 表单样式 */
.user-form {
  max-width: 100%;
  padding: 20px 40px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  margin-top: 4px;
}

/* 头像上传样式 */
.avatar-upload {
  text-align: center;
  margin-bottom: 30px;
}

.avatar-uploader {
  display: inline-block;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
  transform: scale(1.02);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
  transition: all 0.3s;
}

.avatar-uploader-icon:hover {
  color: #409EFF;
}

.avatar {
  width: 120px;
  height: 120px;
  display: block;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-tip {
  margin-top: 10px;
  color: #909399;
  font-size: 13px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .user-detail-container {
    flex-direction: column;
  }

  .detail-item {
    flex: 1 1 100%;
  }

  .user-form {
    padding: 20px 10px;
  }
}
</style>
