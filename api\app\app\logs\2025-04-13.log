2025/04/13 15:56:58 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 15:56:58 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 15:56:58 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 15:57:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:57:43] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/13 15:57:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:57:43] "POST /api/login HTTP/1.1" 200 -
2025/04/13 15:57:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:57:44] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 15:57:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:57:44] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 15:57:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:57:50] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 15:57:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:57:50] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 15:57:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:57:50] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 15:57:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:57:50] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 15:57:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:57:56] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 15:57:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:57:56] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 15:58:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:58:02] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 15:58:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:58:02] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 15:58:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:58:02] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 15:58:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 15:58:02] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:00:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:11] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:00:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:11] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:00:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:12] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:00:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:12] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:00:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:12] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:00:12 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:12] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:00:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:29] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:00:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:29] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:00:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:29] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:00:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:29] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:00:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:29] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:00:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:29] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:00:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:53] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:00:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:53] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:00:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:54] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:00:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:54] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:00:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:54] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:00:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:00:54] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:01:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:01:10] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:01:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:01:10] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:01:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:01:11] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:01:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:01:11] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:01:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:01:11] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:01:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:01:11] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:01:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:01:41] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:01:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:01:42] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:01:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:01:42] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:01:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:01:42] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:01:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:01:42] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:01:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:01:42] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:02:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:02:48] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:02:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:02:48] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:03:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:03:11] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:03:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:03:11] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:03:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:03:31] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:03:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:03:31] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:04:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:04:15] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:04:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:04:16] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:05:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:05:13] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:05:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:05:13] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:05:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:05:32] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/13 16:05:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:05:32] "POST /api/logout HTTP/1.1" 200 -
2025/04/13 16:05:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:05:37] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/13 16:05:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:05:37] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/13 16:09:37 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:10:15 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:10:22 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:10:26 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:10:26 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 16:10:26 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 16:10:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:10:33] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/13 16:10:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:10:33] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/13 16:10:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:10:41] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/13 16:10:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:10:41] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/13 16:10:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:10:51] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/13 16:10:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:10:51] "[31m[1mPOST /api/login HTTP/1.1[0m" 401 -
2025/04/13 16:16:26 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:16:52 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:19:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:29] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/13 16:19:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:29] "POST /api/login HTTP/1.1" 200 -
2025/04/13 16:19:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:31] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:19:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:31] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:19:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:34] "OPTIONS /api/users HTTP/1.1" 200 -
2025/04/13 16:19:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:34] "GET /api/users HTTP/1.1" 200 -
2025/04/13 16:19:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:37] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:19:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:37] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:19:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:37] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:19:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:37] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:19:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:40] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:19:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:40] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:19:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:40] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:19:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:40] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:19:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:48] "OPTIONS /api/bikes?page=1&limit=10&user_id=10 HTTP/1.1" 200 -
2025/04/13 16:19:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:48] "GET /api/bikes?page=1&limit=10&user_id=10 HTTP/1.1" 200 -
2025/04/13 16:19:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:49] "OPTIONS /api/users HTTP/1.1" 200 -
2025/04/13 16:19:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:19:49] "GET /api/users HTTP/1.1" 200 -
2025/04/13 16:24:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:24:25] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:24:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:24:25] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:24:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:24:28] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:24:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:24:28] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:24:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:24:28] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:24:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:24:28] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:24:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:24:29] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:24:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:24:33] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:24:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:24:33] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:24:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:24:34] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:24:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:24:34] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:28:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:28:21] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:28:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:28:21] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:28:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:28:26] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:28:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:28:26] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:28:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:28:27] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:28:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:28:27] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:28:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:28:33] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:28:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:28:33] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:28:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:28:33] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:28:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:28:33] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:28:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:28:35] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:28:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:28:35] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:31:38 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:32:29 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:33:07 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:33:47 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:34:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:34:28] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:34:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:34:28] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:34:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:34:40] "OPTIONS /api/users/2 HTTP/1.1" 200 -
2025/04/13 16:34:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:34:40] "GET /api/users/2 HTTP/1.1" 200 -
2025/04/13 16:34:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:34:48] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:34:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:34:48] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:34:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:34:48] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:34:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:34:48] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:38:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:38:22] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:38:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:38:22] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:38:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:38:24] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:38:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:38:24] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:38:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:38:24] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:38:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:38:24] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:39:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:39:08] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:39:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:39:08] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:39:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:39:08] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:39:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:39:08] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:39:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:39:08] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:39:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:39:08] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:39:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:39:48] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:39:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:39:48] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:39:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:39:58] "OPTIONS /api/users/8 HTTP/1.1" 200 -
2025/04/13 16:39:58 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025/04/13 16:39:58 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.

2025/04/13 16:39:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:39:58] "[35m[1mDELETE /api/users/8 HTTP/1.1[0m" 500 -
2025/04/13 16:44:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:44:18] "OPTIONS /api/users/8 HTTP/1.1" 200 -
2025/04/13 16:44:18 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025/04/13 16:44:18 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.

2025/04/13 16:44:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:44:18] "[35m[1mDELETE /api/users/8 HTTP/1.1[0m" 500 -
2025/04/13 16:44:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:44:22] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:44:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:44:22] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:44:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:44:23] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:44:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:44:23] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:44:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:44:27] "OPTIONS /api/users/8 HTTP/1.1" 200 -
2025/04/13 16:44:27 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025/04/13 16:44:27 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.

2025/04/13 16:44:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:44:27] "[35m[1mDELETE /api/users/8 HTTP/1.1[0m" 500 -
2025/04/13 16:45:50 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:47:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:47:24] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:47:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:47:24] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:47:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:47:25] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:47:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:47:25] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:47:33 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:47:33 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 16:47:33 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 16:47:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:47:52] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:47:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:47:52] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:47:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:47:53] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:47:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:47:53] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:47:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:47:57] "OPTIONS /api/users/8 HTTP/1.1" 200 -
2025/04/13 16:47:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:47:57] "DELETE /api/users/8 HTTP/1.1" 200 -
2025/04/13 16:48:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:15] "OPTIONS /api/users/2 HTTP/1.1" 200 -
2025/04/13 16:48:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:15] "DELETE /api/users/2 HTTP/1.1" 200 -
2025/04/13 16:48:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:18] "OPTIONS /api/users/3 HTTP/1.1" 200 -
2025/04/13 16:48:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:18] "DELETE /api/users/3 HTTP/1.1" 200 -
2025/04/13 16:48:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:20] "OPTIONS /api/users/5 HTTP/1.1" 200 -
2025/04/13 16:48:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:20] "DELETE /api/users/5 HTTP/1.1" 200 -
2025/04/13 16:48:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:22] "OPTIONS /api/users/6 HTTP/1.1" 200 -
2025/04/13 16:48:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:23] "DELETE /api/users/6 HTTP/1.1" 200 -
2025/04/13 16:48:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:24] "OPTIONS /api/users/7 HTTP/1.1" 200 -
2025/04/13 16:48:25 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:25] "DELETE /api/users/7 HTTP/1.1" 200 -
2025/04/13 16:48:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:28] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/13 16:48:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:28] "GET /api/users/9 HTTP/1.1" 200 -
2025/04/13 16:48:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:32] "OPTIONS /api/users/9 HTTP/1.1" 200 -
2025/04/13 16:48:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:32] "PUT /api/users/9 HTTP/1.1" 200 -
2025/04/13 16:48:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:32] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:48:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:32] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:48:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:36] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/13 16:48:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:36] "POST /api/logout HTTP/1.1" 200 -
2025/04/13 16:48:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:40] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/13 16:48:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:40] "POST /api/login HTTP/1.1" 200 -
2025/04/13 16:48:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:41] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:48:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:41] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:48:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:44] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:48:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:44] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:48:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:48] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:48:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:48] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:48:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:48] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:48:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:48:48] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:49:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:49:53] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:49:53 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:49:53] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:52:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:52:02] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:52:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:52:02] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:52:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:52:02] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:52:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:52:02] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:52:12 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 16:52:12 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 16:52:12 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 16:52:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:52:21] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:52:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:52:21] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:52:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:52:22] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:52:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:52:22] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:52:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:52:55] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:52:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:52:55] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:52:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:52:56] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:52:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:52:56] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:53:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:11] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/13 16:53:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:11] "POST /api/logout HTTP/1.1" 200 -
2025/04/13 16:53:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:16] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/13 16:53:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:24] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/13 16:53:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:24] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025/04/13 16:53:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:24] "OPTIONS /api/register HTTP/1.1" 200 -
2025/04/13 16:53:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:24] "[35m[1mPOST /api/register HTTP/1.1[0m" 201 -
2025/04/13 16:53:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:26] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/13 16:53:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:26] "POST /api/login HTTP/1.1" 200 -
2025/04/13 16:53:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:27] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:27] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:32] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:53:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:32] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:53:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:32] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:53:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:32] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:53:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:35] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:35] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:35] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:35] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:37] "OPTIONS /api/bikes?page=1&limit=10&user_id=11 HTTP/1.1" 200 -
2025/04/13 16:53:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:37] "GET /api/bikes?page=1&limit=10&user_id=11 HTTP/1.1" 200 -
2025/04/13 16:53:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:38] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:38] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:43] "OPTIONS /api/bikes?page=1&limit=10&user_id=11 HTTP/1.1" 200 -
2025/04/13 16:53:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:43] "GET /api/bikes?page=1&limit=10&user_id=11 HTTP/1.1" 200 -
2025/04/13 16:53:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:47] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:47] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:47] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:47] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:48] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:53:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:48] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:53:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:48] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:53:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:48] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:53:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:51] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/13 16:53:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:51] "POST /api/logout HTTP/1.1" 200 -
2025/04/13 16:53:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:56] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/13 16:53:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:56] "POST /api/login HTTP/1.1" 200 -
2025/04/13 16:53:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:56] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:53:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:56] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:53:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:56] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:53:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:56] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:53:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:57] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:53:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:53:57] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:54:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:54:02] "OPTIONS /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:54:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:54:02] "GET /api/users?page=1&per_page=10 HTTP/1.1" 200 -
2025/04/13 16:54:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:54:21] "OPTIONS /api/users/13 HTTP/1.1" 200 -
2025/04/13 16:54:21 flask_api routes.py[471] delete_user() INFO: 删除了用户ID=13关联的Players记录
2025/04/13 16:54:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:54:21] "DELETE /api/users/13 HTTP/1.1" 200 -
2025/04/13 16:54:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:54:40] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 16:54:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:54:40] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 16:54:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:54:58] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:54:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:54:58] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/13 16:54:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:54:58] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 16:54:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:54:58] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 16:55:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:11] "OPTIONS /api/bikes/16 HTTP/1.1" 200 -
2025/04/13 16:55:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:11] "PUT /api/bikes/16 HTTP/1.1" 200 -
2025/04/13 16:55:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:11] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 16:55:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:11] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 16:55:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:17] "OPTIONS /api/bikes/16 HTTP/1.1" 200 -
2025/04/13 16:55:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:17] "PUT /api/bikes/16 HTTP/1.1" 200 -
2025/04/13 16:55:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:17] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 16:55:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:17] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 16:55:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:22] "PUT /api/bikes/16 HTTP/1.1" 200 -
2025/04/13 16:55:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:22] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 16:55:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:28] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:55:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:28] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/13 16:55:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:28] "GET /api/bikes HTTP/1.1" 200 -
2025/04/13 16:55:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:28] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/13 16:55:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:45] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:55:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:45] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 16:55:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:45] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:55:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:45] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 16:55:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:46] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 16:55:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 16:55:46] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 17:23:46 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 17:23:46 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 17:23:46 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 17:25:21 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 17:25:21 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 17:25:21 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 17:26:06 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 17:26:06 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 17:26:06 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 17:26:18 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 17:26:33 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 17:26:42 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 17:26:42 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 17:26:42 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 17:28:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:28:14] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 17:28:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:28:14] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 17:28:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:28:14] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 17:28:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:28:14] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/13 17:28:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:28:19] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 17:28:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:28:19] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 17:36:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:36:22] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 17:36:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:36:22] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 17:40:59 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 17:40:59 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 17:40:59 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 17:41:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:41:03] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 17:41:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:41:03] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 17:45:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:45:06] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 17:45:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:45:06] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 17:54:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:54:36] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 17:54:36 flask_api __init__.py[234] not_found() ERROR: 路由未找到: /api/parkinglots, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDUzNDQzNiwianRpIjoiNzI5MTQzN2ItZTk1ZS00NTA2LWI0M2MtMTBiNjZjYTU4NTlhIiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQ1MzQ0MzYsImNzcmYiOiI3M2FjMWVmNS01YWFjLTQ4OTUtYTUxOS0yMmI3OWJmMDhkOWIiLCJleHAiOjE3NDQ2MjA4MzZ9.98IylxkY7H4zcujmb3xjwKXJGtndxx05HV6FNycHTSM

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9529

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9529/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/13 17:54:36 root __init__.py[235] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/13 17:54:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:54:36] "[33mGET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1[0m" 404 -
2025/04/13 17:54:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:54:38] "OPTIONS /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/13 17:54:38 flask_api __init__.py[234] not_found() ERROR: 路由未找到: /api/parking-records/all, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDUzNDQzNiwianRpIjoiNzI5MTQzN2ItZTk1ZS00NTA2LWI0M2MtMTBiNjZjYTU4NTlhIiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQ1MzQ0MzYsImNzcmYiOiI3M2FjMWVmNS01YWFjLTQ4OTUtYTUxOS0yMmI3OWJmMDhkOWIiLCJleHAiOjE3NDQ2MjA4MzZ9.98IylxkY7H4zcujmb3xjwKXJGtndxx05HV6FNycHTSM

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9529

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9529/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/13 17:54:38 root __init__.py[235] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/13 17:54:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:54:38] "[33mGET /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1[0m" 404 -
2025/04/13 17:54:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:54:40] "OPTIONS /api/parking-records/stats HTTP/1.1" 200 -
2025/04/13 17:54:40 flask_api __init__.py[234] not_found() ERROR: 路由未找到: /api/parking-records/stats, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDUzNDQzNiwianRpIjoiNzI5MTQzN2ItZTk1ZS00NTA2LWI0M2MtMTBiNjZjYTU4NTlhIiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQ1MzQ0MzYsImNzcmYiOiI3M2FjMWVmNS01YWFjLTQ4OTUtYTUxOS0yMmI3OWJmMDhkOWIiLCJleHAiOjE3NDQ2MjA4MzZ9.98IylxkY7H4zcujmb3xjwKXJGtndxx05HV6FNycHTSM

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9529

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9529/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/13 17:54:40 root __init__.py[235] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/13 17:54:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:54:40] "[33mGET /api/parking-records/stats HTTP/1.1[0m" 404 -
2025/04/13 17:58:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:58:57] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 17:58:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:58:57] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 17:58:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:58:58] "OPTIONS /api/parking-records/stats HTTP/1.1" 200 -
2025/04/13 17:58:58 flask_api __init__.py[234] not_found() ERROR: 路由未找到: /api/parking-records/stats, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDUzNDQzNiwianRpIjoiNzI5MTQzN2ItZTk1ZS00NTA2LWI0M2MtMTBiNjZjYTU4NTlhIiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQ1MzQ0MzYsImNzcmYiOiI3M2FjMWVmNS01YWFjLTQ4OTUtYTUxOS0yMmI3OWJmMDhkOWIiLCJleHAiOjE3NDQ2MjA4MzZ9.98IylxkY7H4zcujmb3xjwKXJGtndxx05HV6FNycHTSM

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9529

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9529/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/13 17:58:58 root __init__.py[235] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/13 17:58:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:58:58] "[33mGET /api/parking-records/stats HTTP/1.1[0m" 404 -
2025/04/13 17:59:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:59:00] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 17:59:00 flask_api __init__.py[234] not_found() ERROR: 路由未找到: /api/parkinglots, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDUzNDQzNiwianRpIjoiNzI5MTQzN2ItZTk1ZS00NTA2LWI0M2MtMTBiNjZjYTU4NTlhIiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQ1MzQ0MzYsImNzcmYiOiI3M2FjMWVmNS01YWFjLTQ4OTUtYTUxOS0yMmI3OWJmMDhkOWIiLCJleHAiOjE3NDQ2MjA4MzZ9.98IylxkY7H4zcujmb3xjwKXJGtndxx05HV6FNycHTSM

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9529

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9529/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/13 17:59:00 root __init__.py[235] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/13 17:59:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:59:00] "[33mGET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1[0m" 404 -
2025/04/13 17:59:11 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 17:59:11 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 17:59:11 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 17:59:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:59:31] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 17:59:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:59:31] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 17:59:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:59:33] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 17:59:33 flask_api routes.py[46] get_all_parkinglots() ERROR: 获取停车场列表出错: type object 'ParkingLots' has no attribute 'query'
2025/04/13 17:59:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:59:33] "[35m[1mGET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1[0m" 500 -
2025/04/13 17:59:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:59:35] "OPTIONS /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/13 17:59:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:59:35] "GET /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/13 17:59:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:59:36] "OPTIONS /api/parking-records/stats HTTP/1.1" 200 -
2025/04/13 17:59:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 17:59:36] "GET /api/parking-records/stats HTTP/1.1" 200 -
2025/04/13 18:02:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:02:02] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 18:02:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:02:02] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 18:02:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:02:03] "OPTIONS /api/parking-records/stats HTTP/1.1" 200 -
2025/04/13 18:02:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:02:03] "GET /api/parking-records/stats HTTP/1.1" 200 -
2025/04/13 18:02:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:02:05] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 18:02:05 flask_api routes.py[46] get_all_parkinglots() ERROR: 获取停车场列表出错: type object 'ParkingLots' has no attribute 'query'
2025/04/13 18:02:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:02:05] "[35m[1mGET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1[0m" 500 -
2025/04/13 18:02:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:02:07] "OPTIONS /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/13 18:02:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:02:07] "GET /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/13 18:02:29 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 18:02:29 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 18:02:29 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 18:02:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:02:54] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 18:02:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:02:54] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 18:02:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:02:58] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 18:02:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:02:58] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 18:03:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:03:01] "OPTIONS /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/13 18:03:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:03:01] "GET /api/parking-records/all?page=1&limit=10&status=&vehicle_id=&parking_lot_id=&start_date=&end_date= HTTP/1.1" 200 -
2025/04/13 18:03:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:03:02] "OPTIONS /api/parking-records/stats HTTP/1.1" 200 -
2025/04/13 18:03:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:03:02] "GET /api/parking-records/stats HTTP/1.1" 200 -
2025/04/13 18:03:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:03:05] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 18:03:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:03:05] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 18:06:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:06:02] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/04/13 18:06:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:06:02] "[31m[1mPOST /api/parkinglots HTTP/1.1[0m" 403 -
2025/04/13 18:08:01 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 18:08:01 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 18:08:01 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 18:08:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:08:10] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 18:08:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:08:10] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 18:08:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:08:14] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 18:08:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:08:14] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 18:08:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:08:20] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/04/13 18:08:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:08:21] "[31m[1mPOST /api/parkinglots HTTP/1.1[0m" 403 -
2025/04/13 18:10:37 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 18:10:45 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 18:12:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:09] "OPTIONS /api/logout HTTP/1.1" 200 -
2025/04/13 18:12:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:09] "POST /api/logout HTTP/1.1" 200 -
2025/04/13 18:12:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:13] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/13 18:12:13 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:13] "POST /api/login HTTP/1.1" 200 -
2025/04/13 18:12:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:14] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 18:12:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:16] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 18:12:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:17] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 18:12:25 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/13 18:12:25 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/13 18:12:25 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/13 18:12:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:33] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/13 18:12:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:34] "POST /api/login HTTP/1.1" 200 -
2025/04/13 18:12:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:35] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/13 18:12:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:35] "GET /api/users/me HTTP/1.1" 200 -
2025/04/13 18:12:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:38] "OPTIONS /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 18:12:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:38] "GET /api/parkinglots?page=1&limit=10&search=&status=&sort_field=id&sort_order=asc HTTP/1.1" 200 -
2025/04/13 18:12:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:45] "OPTIONS /api/parkinglots HTTP/1.1" 200 -
2025/04/13 18:12:45 flask_api routes.py[120] create_parkinglot() ERROR: 创建停车场出错: __init__() got an unexpected keyword argument 'occupied_spaces'
2025/04/13 18:12:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [13/Apr/2025 18:12:45] "[35m[1mPOST /api/parkinglots HTTP/1.1[0m" 500 -
