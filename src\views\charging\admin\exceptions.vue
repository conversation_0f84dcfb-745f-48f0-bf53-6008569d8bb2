<template>
  <div class="app-container charging-exceptions-container">
    <permission-wrapper :allowed-roles="['admin']" message="您没有权限访问充电异常管理数据，此功能仅对管理员开放">
      <el-card class="filter-container">
        <div class="filter-item">
          <el-form :inline="true" :model="listQuery" class="demo-form-inline">
            <el-form-item label="异常类型">
              <el-select v-model="listQuery.type" placeholder="选择异常类型" clearable @change="handleFilter">
                <el-option label="充电接口故障" value="connector" />
                <el-option label="设备无法启动" value="startup" />
                <el-option label="充电中断" value="interruption" />
                <el-option label="其他故障" value="other" />
              </el-select>
            </el-form-item>
            <el-form-item label="处理状态">
              <el-select v-model="listQuery.handled" placeholder="选择处理状态" clearable @change="handleFilter">
                <el-option label="未处理" :value="false" />
                <el-option label="已处理" :value="true" />
              </el-select>
            </el-form-item>
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleDateRangeChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleFilter">查询</el-button>
              <el-button type="default" @click="resetFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div slot="header" class="stat-header">
            <span>异常总数</span>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total || 0 }}</div>
            <div class="stat-icon">
              <i class="el-icon-warning-outline"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div slot="header" class="stat-header">
            <span>未处理异常</span>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.unhandled || 0 }}</div>
            <div class="stat-icon red">
              <i class="el-icon-error"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div slot="header" class="stat-header">
            <span>已处理异常</span>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.handled || 0 }}</div>
            <div class="stat-icon green">
              <i class="el-icon-success"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div slot="header" class="stat-header">
            <span>处理率</span>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.handleRate || '0%' }}</div>
            <div class="stat-icon blue">
              <i class="el-icon-data-analysis"></i>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-card class="list-container">
      <div class="list-header">
        <div class="header-title">
          <i class="el-icon-error"></i>
          <span>充电异常列表</span>
        </div>
        <div class="header-actions">
          <el-button type="text" @click="fetchData">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="加载中..."
        border
        fit
        highlight-current-row
        :header-cell-style="{background:'#f5f7fa'}"
        @sort-change="handleSortChange"
      >
        <el-table-column
          align="center"
          label="ID"
          width="80"
          prop="id"
          sortable="custom"
        >
          <template slot-scope="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="车辆信息"
          width="130"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.vehicle">
              {{ scope.row.vehicle.number }}<br>
              <small>{{ scope.row.vehicle.brand }} {{ scope.row.vehicle.color }}</small>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="用户信息"
          width="120"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.user">
              {{ scope.row.user.name }}<br>
              <small>ID: {{ scope.row.user.u_id }}</small>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="车位信息"
          width="120"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.parking_space">
              {{ scope.row.parking_space.space_number }}<br>
              <small>{{ scope.row.parking_space.type_text }}</small>
            </div>
            <div v-else-if="scope.row.space_id">
              车位ID: {{ scope.row.space_id }}
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="停车场"
          width="120"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.parking_lot">
              {{ scope.row.parking_lot.name }}
            </div>
            <div v-else-if="scope.row.parking_lot_id">
              停车场ID: {{ scope.row.parking_lot_id }}
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="异常类型"
          width="120"
        >
          <template slot-scope="scope">
            {{ getExceptionTypeName(scope.row.type) }}
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="异常时间"
          width="180"
          prop="time"
          sortable="custom"
        >
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.time) }}
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="处理状态"
          width="100"
        >
          <template slot-scope="scope">
            <el-tag :type="scope.row.handled ? 'success' : 'danger'">
              {{ scope.row.handled ? '已处理' : '未处理' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="处理人"
          width="100"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.handled">
              {{ scope.row.processor || '-' }}
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="处理时间"
          width="150"
          prop="process_time"
          sortable="custom"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.handled && scope.row.process_time">
              {{ formatDateTime(scope.row.process_time) }}
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="处理结果"
          min-width="180"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.handled">
              {{ scope.row.process_result || '-' }}
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          label="操作"
          width="150"
        >
          <template slot-scope="scope">
            <el-button
              v-if="!scope.row.handled"
              size="mini"
              type="success"
              @click="handleProcess(scope.row)"
            >处理</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="listQuery.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="listQuery.per_page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </el-card>



    <!-- 处理异常对话框 -->
    <el-dialog title="处理异常" :visible.sync="processDialogVisible" width="500px">
      <el-form ref="processForm" :rules="processRules" :model="processTemp" label-position="right" label-width="110px">
        <el-form-item label="处理人" prop="handler">
          <el-input v-model="processTemp.handler" placeholder="请输入处理人姓名" />
        </el-form-item>
        <el-form-item label="处理结果" prop="handle_result">
          <el-input
            v-model="processTemp.handle_result"
            type="textarea"
            :rows="3"
            placeholder="请输入处理结果"
          />
        </el-form-item>
        <el-form-item label="通知用户">
          <el-switch v-model="processTemp.notify_user" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="processDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitProcess">确认</el-button>
      </div>
    </el-dialog>
    </permission-wrapper>
  </div>
</template>

<script>
import { getChargingExceptions, getChargingExceptionStats, processChargingException, deleteChargingException } from '@/api/charging'
import PermissionWrapper from '@/components/PermissionWrapper'

export default {
  name: 'ChargingExceptions',
  components: {
    PermissionWrapper
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        per_page: 10,
        type: undefined,
        handled: undefined,
        start_date: undefined,
        end_date: undefined,
        sort_field: 'id',
        sort_order: 'asc'
      },
      dateRange: null,
      stats: {
        total: 0,
        handled: 0,
        unhandled: 0,
        handleRate: '0%'
      },

      processDialogVisible: false,
      processTemp: {
        exception_id: undefined,
        handler: '',
        handle_result: '',
        notify_user: false
      },
      processRules: {
        handler: [{ required: true, message: '请输入处理人姓名', trigger: 'blur' }],
        handle_result: [{ required: true, message: '请输入处理结果', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.fetchData()
    this.fetchStats()
  },
  methods: {
    // 获取异常列表
    async fetchData() {
      this.listLoading = true
      try {
        const params = { ...this.listQuery }
        const response = await getChargingExceptions(params)
        this.list = response.data.items || []
        this.total = response.data.total || 0
      } catch (error) {
        console.error('获取充电异常列表失败:', error)
        this.$message.error('获取充电异常列表失败')
      } finally {
        this.listLoading = false
      }
    },
    // 获取异常统计数据
    async fetchStats() {
      try {
        const response = await getChargingExceptionStats()
        console.log('获取到的异常统计数据:', response.data)
        this.stats = response.data || {
          total: 0,
          handled: 0,
          unhandled: 0,
          handleRate: '0%'
        }
      } catch (error) {
        console.error('获取异常统计数据失败:', error)
      }
    },
    // 获取异常类型名称
    getExceptionTypeName(type) {
      const typeMap = {
        'connector': '充电接口故障',
        'startup': '设备无法启动',
        'interruption': '充电中断',
        'other': '其他故障',
        // 兼容旧数据
        'charging_gun': '充电接口故障',
        'communication': '设备无法启动',
        'power': '充电中断',
        'software': '其他故障',
        'user_operation': '其他故障'
      }
      return typeMap[type] || type
    },
    // 格式化日期时间
    formatDateTime(dateStr) {
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    // 处理日期范围变化
    handleDateRangeChange(val) {
      if (val) {
        this.listQuery.start_date = val[0]
        this.listQuery.end_date = val[1]
      } else {
        this.listQuery.start_date = undefined
        this.listQuery.end_date = undefined
      }
    },
    // 处理筛选
    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    },
    // 重置筛选条件
    resetFilter() {
      this.listQuery = {
        page: 1,
        per_page: 10,
        type: undefined,
        handled: undefined,
        start_date: undefined,
        end_date: undefined,
        sort_field: 'id',
        sort_order: 'asc'
      }
      this.dateRange = null
      this.fetchData()
    },
    // 处理页面大小变化
    handleSizeChange(val) {
      this.listQuery.per_page = val
      this.fetchData()
    },
    // 处理页码变化
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.fetchData()
    },
    // 处理表格排序变化
    handleSortChange({ column, prop, order }) {
      console.log('表格排序变化:', prop, order)

      if (!prop || !order) {
        return
      }

      // 映射排序字段
      const fieldMap = {
        'id': 'id',
        'time': 'time',
        'process_time': 'process_time'
      }

      // 映射排序顺序
      const orderMap = {
        'ascending': 'asc',
        'descending': 'desc'
      }

      // 设置排序参数
      this.listQuery.sort_field = fieldMap[prop] || 'id'
      this.listQuery.sort_order = orderMap[order] || 'asc'

      // 重置页码并重新获取数据
      this.listQuery.page = 1
      this.fetchData()
    },


    // 获取故障严重程度对应的标签类型
    getSeverityType(severity) {
      const typeMap = {
        'low': 'info',
        'medium': 'warning',
        'high': 'danger',
        'critical': 'danger'
      }
      return typeMap[severity] || 'info'
    },

    // 获取故障状态对应的标签类型
    getStatusType(status) {
      const typeMap = {
        0: 'danger',  // 待处理
        1: 'warning', // 处理中
        2: 'success', // 已完成
        3: 'info'     // 已关闭
      }
      return typeMap[status] || 'info'
    },

    // 查看故障详情
    viewFaultDetail(faultId) {
      // 跳转到充电故障详情页面
      this.$router.push(`/charging-admin/faults?fault_id=${faultId}`)
    },
    // 处理异常
    handleProcess(row) {
      this.processTemp = {
        exception_id: row.id,
        handler: '',
        handle_result: '',
        notify_user: false
      }

      this.processDialogVisible = true
      this.$nextTick(() => {
        this.$refs['processForm'].clearValidate()
      })
    },
    // 提交处理
    submitProcess() {
      this.$refs['processForm'].validate(async (valid) => {
        if (valid) {
          try {
            console.log('处理异常，ID:', this.processTemp.exception_id, '数据:', this.processTemp)
            await processChargingException(this.processTemp.exception_id, {
              processor: this.processTemp.handler,
              process_result: this.processTemp.handle_result
            })
            this.processDialogVisible = false
            this.$message({
              type: 'success',
              message: '处理异常成功!'
            })
            this.fetchData()
            this.fetchStats()
          } catch (error) {
            console.error('处理异常失败:', error)
            this.$message.error('处理异常失败')
          }
        }
      })
    },

    // 处理删除异常
    handleDelete(row) {
      this.$confirm('此操作将永久删除该异常记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          console.log('删除异常，ID:', row.id)
          await deleteChargingException(row.id)
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.fetchData()
          this.fetchStats()
        } catch (error) {
          console.error('删除异常失败:', error)
          this.$message.error('删除异常失败')
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.charging-exceptions-container {
  .filter-container {
    margin-bottom: 20px;
  }

  .stat-card {
    margin-bottom: 20px;

    .stat-header {
      font-size: 16px;
      font-weight: 500;
    }

    .stat-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .stat-value {
        font-size: 24px;
        font-weight: bold;
      }

      .stat-icon {
        font-size: 36px;
        color: #409EFF;

        &.red {
          color: #F56C6C;
        }

        &.green {
          color: #67C23A;
        }

        &.blue {
          color: #409EFF;
        }
      }
    }
  }

  .list-container {
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .header-title {
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;

        i {
          margin-right: 10px;
          color: #F56C6C;
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }

  .exception-detail {
    .detail-section {
      margin-bottom: 20px;

      h3 {
        font-size: 16px;
        margin-bottom: 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #ebeef5;
      }

      .detail-item {
        display: flex;
        margin-bottom: 8px;

        .label {
          width: 100px;
          color: #606266;
          font-weight: 500;
        }

        .value {
          flex: 1;
        }
      }
    }
  }
}
</style>
