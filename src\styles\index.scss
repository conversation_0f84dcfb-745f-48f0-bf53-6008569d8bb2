@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './parking-center.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  background-color: $pageBgColor;
  color: $primaryTextColor;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 20px;

  // 页面标题样式
  .page-title {
    margin-bottom: 20px;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $primaryTextColor;
      margin: 0 0 8px 0;
    }

    .subtitle {
      font-size: 14px;
      color: $secondaryTextColor;
      margin: 0;
    }
  }

  // 卡片容器
  .card-container {
    background-color: $cardBgColor;
    border-radius: 8px;
    box-shadow: $boxShadow;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s;

    &:hover {
      box-shadow: $hoverShadow;
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: $primaryTextColor;
      margin: 0 0 15px 0;
      padding-bottom: 10px;
      border-bottom: 1px solid $lightBorderColor;
    }
  }

  // 数据统计卡片
  .stat-card {
    background: linear-gradient(135deg, mix(white, $primaryColor, 90%), mix(white, $secondaryColor, 90%));
    border-radius: 8px;
    padding: 20px;
    box-shadow: $boxShadow;
    transition: all 0.3s;
    height: 100%;

    &:hover {
      transform: translateY(-5px);
      box-shadow: $hoverShadow;
    }

    .stat-title {
      font-size: 14px;
      color: $secondaryTextColor;
      margin-bottom: 10px;
    }

    .stat-value {
      font-size: 28px;
      font-weight: 700;
      color: $primaryTextColor;
    }

    .stat-icon {
      float: right;
      font-size: 36px;
      color: rgba($primaryColor, 0.3);
    }
  }
}
