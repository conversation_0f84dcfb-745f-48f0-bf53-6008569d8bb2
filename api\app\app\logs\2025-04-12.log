2025/04/12 14:17:48 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/12 14:17:48 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/12 14:17:48 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/12 14:18:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:18:01] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/12 14:18:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:18:01] "POST /api/login HTTP/1.1" 200 -
2025/04/12 14:18:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:18:02] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:18:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:18:02] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:18:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:18:04] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:18:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:18:04] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:18:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:18:08] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:18:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:18:08] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:18:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:18:14] "OPTIONS /api/bikes/7 HTTP/1.1" 200 -
2025/04/12 14:18:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:18:14] "[31m[1mPUT /api/bikes/7 HTTP/1.1[0m" 403 -
2025/04/12 14:20:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:20:18] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:20:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:20:18] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:20:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:20:32] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:20:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:20:32] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:21:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:21:49] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:21:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:21:49] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:21:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:21:55] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:21:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:21:55] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:22:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:22:10] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:22:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:22:10] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:22:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:22:10] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:22:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:22:10] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:22:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:22:15] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:22:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:22:15] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:23:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:23:38] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:23:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:23:38] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:23:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:23:42] "OPTIONS /bikes/10 HTTP/1.1" 200 -
2025/04/12 14:23:42 flask_api __init__.py[246] handle_exception() ERROR: 未处理的异常: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025/04/12 14:23:42 flask_api __init__.py[248] handle_exception() ERROR: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.

2025/04/12 14:23:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:23:42] "[35m[1mPUT /bikes/10 HTTP/1.1[0m" 500 -
2025/04/12 14:24:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:24:35] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:24:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:24:35] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:24:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:24:39] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:24:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:24:39] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:24:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:24:39] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:24:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:24:44] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:24:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:24:44] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:24:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:24:49] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:24:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:24:59] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/12 14:24:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:24:59] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/12 14:24:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:24:59] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:24:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:24:59] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:25:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:25:05] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:25:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:25:05] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:25:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:25:06] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:25:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:25:06] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:02] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:02] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:08] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:26:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:08] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:26:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:08] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:08 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:08] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:14] "OPTIONS /api/bikes/11 HTTP/1.1" 200 -
2025/04/12 14:26:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:14] "PUT /api/bikes/11 HTTP/1.1" 200 -
2025/04/12 14:26:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:14] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:14] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:18] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:19] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:19] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:19] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:19 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:19] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:21] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:21 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:21] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:24] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:26:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:24] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:26:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:24] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:32] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/12 14:26:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:32] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/12 14:26:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:32] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:26:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:26:32] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:28:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:28:02] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:28:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:28:02] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:28:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:28:07] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:28:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:28:07] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:28:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:28:07] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:28:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:28:42] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:28:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:28:42] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:28:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:28:46] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:28:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:28:46] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:28:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:28:46] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:28:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:28:51] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:28:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:28:51] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:29:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:29:35] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:29:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:29:35] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:29:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:29:39] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:29:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:29:39] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:29:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:29:39] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:29:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:29:42] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:29:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:29:42] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:30:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:30:33] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:30:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:30:33] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:30:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:30:37] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:30:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:30:37] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:30:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:30:37] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:30:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:30:42] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:30:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:30:42] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:32:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:32:41] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:32:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:32:41] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:32:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:32:45] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:32:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:32:45] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:32:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:32:46] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:32:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:32:50] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:32:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:32:50] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 14:32:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:32:50] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:32:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:32:50] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:37:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:37:04] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:37:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:37:04] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:49:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:49:56] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:49:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:49:56] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:49:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:49:59] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:49:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:49:59] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:51:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:51:29] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:51:29 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:51:29] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:51:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:51:35] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:51:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:51:35] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:51:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:51:35] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:51:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:51:35] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:51:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:51:38] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:51:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:51:38] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 14:51:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:51:38] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:51:38 flask_api __init__.py[227] not_found() ERROR: 路由未找到: /api/bikes/stats, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDQzODY4MSwianRpIjoiMGZmMjAxMzAtODZlZS00YTYzLTkzZGMtOTEzMDQ5YWQ3ZWE5IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQ0Mzg2ODEsImNzcmYiOiIwMjEwMDYwMS01OTgwLTQ1NjktYTNkOS01NmFlNjdiMTZmYjQiLCJleHAiOjE3NDQ1MjUwODF9.BQhmza-wbqym2CylkNCJmw_7HATFUy91M6Ozbo-fgt4

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/12 14:51:38 root __init__.py[228] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/12 14:51:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:51:38] "[33mGET /api/bikes/stats HTTP/1.1[0m" 404 -
2025/04/12 14:53:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:53:48] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:53:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:53:48] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 14:53:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:53:48] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:53:48 flask_api __init__.py[227] not_found() ERROR: 路由未找到: /api/bikes/stats, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDQzODY4MSwianRpIjoiMGZmMjAxMzAtODZlZS00YTYzLTkzZGMtOTEzMDQ5YWQ3ZWE5IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQ0Mzg2ODEsImNzcmYiOiIwMjEwMDYwMS01OTgwLTQ1NjktYTNkOS01NmFlNjdiMTZmYjQiLCJleHAiOjE3NDQ1MjUwODF9.BQhmza-wbqym2CylkNCJmw_7HATFUy91M6Ozbo-fgt4

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/12 14:53:48 root __init__.py[228] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/12 14:53:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:53:48] "[33mGET /api/bikes/stats HTTP/1.1[0m" 404 -
2025/04/12 14:57:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:30] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:57:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:30] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:57:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:31] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:57:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:31] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 14:57:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:31] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:57:31 flask_api __init__.py[227] not_found() ERROR: 路由未找到: /api/bikes/stats, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDQzODY4MSwianRpIjoiMGZmMjAxMzAtODZlZS00YTYzLTkzZGMtOTEzMDQ5YWQ3ZWE5IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQ0Mzg2ODEsImNzcmYiOiIwMjEwMDYwMS01OTgwLTQ1NjktYTNkOS01NmFlNjdiMTZmYjQiLCJleHAiOjE3NDQ1MjUwODF9.BQhmza-wbqym2CylkNCJmw_7HATFUy91M6Ozbo-fgt4

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/12 14:57:31 root __init__.py[228] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/12 14:57:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:31] "[33mGET /api/bikes/stats HTTP/1.1[0m" 404 -
2025/04/12 14:57:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:42] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:57:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:42] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 14:57:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:42] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:57:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:42] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 14:57:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:46] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:57:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:46] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 14:57:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:46] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:57:46 flask_api __init__.py[227] not_found() ERROR: 路由未找到: /api/bikes/stats, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDQzODY4MSwianRpIjoiMGZmMjAxMzAtODZlZS00YTYzLTkzZGMtOTEzMDQ5YWQ3ZWE5IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQ0Mzg2ODEsImNzcmYiOiIwMjEwMDYwMS01OTgwLTQ1NjktYTNkOS01NmFlNjdiMTZmYjQiLCJleHAiOjE3NDQ1MjUwODF9.BQhmza-wbqym2CylkNCJmw_7HATFUy91M6Ozbo-fgt4

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/12 14:57:46 root __init__.py[228] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/12 14:57:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:46] "[33mGET /api/bikes/stats HTTP/1.1[0m" 404 -
2025/04/12 14:57:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:54] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 14:57:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:54] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 14:57:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:54] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 14:57:54 flask_api __init__.py[227] not_found() ERROR: 路由未找到: /api/bikes/stats, 方法: GET, 头信息: Host: 127.0.0.1:5000

Connection: keep-alive

Sec-Ch-Ua-Platform: "Windows"

Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0NDQzODY4MSwianRpIjoiMGZmMjAxMzAtODZlZS00YTYzLTkzZGMtOTEzMDQ5YWQ3ZWE5IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5Ijo5LCJuYmYiOjE3NDQ0Mzg2ODEsImNzcmYiOiIwMjEwMDYwMS01OTgwLTQ1NjktYTNkOS01NmFlNjdiMTZmYjQiLCJleHAiOjE3NDQ1MjUwODF9.BQhmza-wbqym2CylkNCJmw_7HATFUy91M6Ozbo-fgt4

User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

Accept: application/json, text/plain, */*

Sec-Ch-Ua: "Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"

Sec-Ch-Ua-Mobile: ?0

Origin: http://localhost:9528

Sec-Fetch-Site: cross-site

Sec-Fetch-Mode: cors

Sec-Fetch-Dest: empty

Referer: http://localhost:9528/

Accept-Encoding: gzip, deflate, br, zstd

Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6




2025/04/12 14:57:54 root __init__.py[228] not_found() ERROR: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025/04/12 14:57:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 14:57:54] "[33mGET /api/bikes/stats HTTP/1.1[0m" 404 -
2025/04/12 15:14:31 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/12 15:14:31 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/12 15:14:31 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/12 15:14:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:14:36] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:14:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:14:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:14:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:14:37] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:14:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:14:37] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:14:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:14:37] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:14:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:14:37] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:18:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:32] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:18:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:32] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:18:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:32] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:18:32 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:32] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:18:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:36] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:18:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:18:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:36] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:18:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:36] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:18:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:47] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 15:18:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:47] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 15:18:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:48] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:18:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:48] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:18:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:52] "OPTIONS /api/bikes/11 HTTP/1.1" 200 -
2025/04/12 15:18:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:52] "PUT /api/bikes/11 HTTP/1.1" 200 -
2025/04/12 15:18:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:52] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:18:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:54] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:18:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:54] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:18:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:58] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:18:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:58] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:18:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:59] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:18:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:59] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:18:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:18:59] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:23:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:23:55] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:23:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:23:55] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:23:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:23:56] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:23:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:23:56] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:23:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:23:56] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:23:56 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:23:56] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:24:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:03] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 15:24:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:03] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 15:24:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:03] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:24:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:03] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:24:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:05] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:24:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:17] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:24:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:17] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:24:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:18] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:24:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:18] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:24:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:18] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:24:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:18] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:24:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:34] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 15:24:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:34] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 15:24:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:34] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:24:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:34] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:24:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:38] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:24:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:42] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:24:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:42] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:24:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:43] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:24:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:43] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:24:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:43] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:24:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:24:43] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:32:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:11] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:32:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:11] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:32:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:11] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:32:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:11] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:32:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:16] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:32:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:16] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:32:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:17] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:32:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:17] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:32:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:17] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:32:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:17] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:32:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:27] "OPTIONS /api/bikes/11 HTTP/1.1" 200 -
2025/04/12 15:32:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:27] "PUT /api/bikes/11 HTTP/1.1" 200 -
2025/04/12 15:32:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:27] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:32:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:27] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:32:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:27] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:32:27 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:27] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:32:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:39] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/12 15:32:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:39] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/12 15:32:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:39] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:32:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:39] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:32:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:40] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:32:40 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:40] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:32:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:47] "OPTIONS /api/bikes/13 HTTP/1.1" 200 -
2025/04/12 15:32:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:47] "PUT /api/bikes/13 HTTP/1.1" 200 -
2025/04/12 15:32:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:47] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:32:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:47] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:32:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:47] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:32:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:47] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:32:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:51] "OPTIONS /api/bikes/11 HTTP/1.1" 200 -
2025/04/12 15:32:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:51] "PUT /api/bikes/11 HTTP/1.1" 200 -
2025/04/12 15:32:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:51] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:32:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:32:51] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:33:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:33:09] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:33:09 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:33:09] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/12 15:38:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:05] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:38:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:05] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:38:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:05] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:38:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:05] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:38:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:15] "OPTIONS /api/bikes/14 HTTP/1.1" 200 -
2025/04/12 15:38:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:15] "PUT /api/bikes/14 HTTP/1.1" 200 -
2025/04/12 15:38:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:15] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:38:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:15] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:38:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:15] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:38:15 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:15] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:38:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:22] "OPTIONS /api/bikes/14 HTTP/1.1" 200 -
2025/04/12 15:38:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:22] "PUT /api/bikes/14 HTTP/1.1" 200 -
2025/04/12 15:38:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:22] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:38:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:23] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:38:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:23] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:38:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:23] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:38:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:43] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:38:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:43] "[35m[1mPOST /api/bikes HTTP/1.1[0m" 201 -
2025/04/12 15:38:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:43] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:38:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:43] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:38:43 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:43] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:38:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:47] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:38:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:51] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:38:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:51] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:38:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:51] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:38:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:51] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:38:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:51] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:38:51 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:38:51] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:39:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:39:02] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:39:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:39:02] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:39:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:39:03] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:39:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:39:33] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:39:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:39:33] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:39:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:39:33] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:39:33 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:39:33] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:39:44] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:39:44] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:39:44] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:39:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:39:44] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:46:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:46:10] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:46:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:46:10] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:46:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:46:10] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:46:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:46:10] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:46:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:46:17] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:46:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:46:17] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:46:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:46:18] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:46:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:46:18] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:46:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:46:18] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:46:18 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:46:18] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:47:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:47:37] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:47:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:47:37] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:47:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:47:37] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:47:37 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:47:37] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:48:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:48:07] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:48:07 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:48:07] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:48:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:48:38] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:48:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:48:38] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:48:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:48:59] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:48:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:48:59] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:48:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:48:59] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:48:59 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:48:59] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:49:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:49:11] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:49:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:49:11] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:49:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:49:11] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:49:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:49:11] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:56:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:56:46] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:56:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:56:46] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:57:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:10] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:57:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:10] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:57:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:10] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:57:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:10] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:57:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:16] "OPTIONS /api/bikes/15 HTTP/1.1" 200 -
2025/04/12 15:57:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:16] "PUT /api/bikes/15 HTTP/1.1" 200 -
2025/04/12 15:57:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:17] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 15:57:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:17] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:57:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:17] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 15:57:17 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:17] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 15:57:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:57] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:57:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:57] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:57:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:57] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:57:57 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:57:57] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:58:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:58:28] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:58:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:58:28] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:58:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:58:58] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:58:58 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:58:58] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 15:59:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:59:28] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 15:59:28 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 15:59:28] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:00:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:00:20] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:00:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:00:20] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:00:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:00:20] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:00:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:00:20] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:00:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:00:50] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:00:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:00:50] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:01:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:03] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:01:03 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:03] "PUT /api/users/me HTTP/1.1" 200 -
2025/04/12 16:01:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:04] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:01:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:04] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:01:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:20] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:01:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:20] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:01:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:31] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 16:01:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:31] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 16:01:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:31] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 16:01:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:31] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 16:01:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:35] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:01:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:35] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:01:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:35] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:01:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:01:35] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:02:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:02:06] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:02:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:02:06] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:02:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:02:36] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:02:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:02:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:03:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:03:06] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:03:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:03:06] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:03:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:03:36] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:03:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:03:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:04:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:04:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:04:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:04:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:04:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:04:49] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:04:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:04:49] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:04:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:04:49] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:04:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:04:49] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:05:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:05:20] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:05:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:05:20] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:05:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:05:50] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:05:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:05:50] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:06:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:06:20] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:06:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:06:20] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:06:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:06:49] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:06:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:06:49] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:06:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:06:52] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:06:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:06:52] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:06:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:06:52] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:06:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:06:52] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:06:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:06:55] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/12 16:06:55 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:06:55] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/12 16:07:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:07:05] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:07:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:07:05] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:07:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:07:05] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:07:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:07:05] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:07:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:07:35] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:07:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:07:35] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:08:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:08:05] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:08:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:08:05] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:08:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:08:36] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:08:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:08:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:09:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:09:06] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:09:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:09:06] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:09:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:09:36] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:09:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:09:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:10:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:10:06] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:10:06 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:10:06] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:10:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:10:36] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:10:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:10:36] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:11:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:11:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:11:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:11:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:12:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:12:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:12:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:12:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:13:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:13:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:13:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:13:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:14:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:14:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:14:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:14:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:15:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:15:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:15:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:15:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:16:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:16:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:16:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:16:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:17:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:17:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:17:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:17:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:18:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:18:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:18:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:18:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:19:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:19:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:19:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:19:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:20:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:20:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:20:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:20:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:21:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:21:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:21:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:21:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:22:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:22:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:22:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:22:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:23:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:23:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:23:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:23:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:24:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:24:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:24:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:24:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:25:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:25:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:25:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:25:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:26:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:26:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:26:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:26:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:27:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:27:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:27:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:27:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:28:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:28:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:28:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:28:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:29:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:29:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:29:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:29:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:30:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:30:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:30:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:30:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:31:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:31:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:31:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:31:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:32:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:32:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:32:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:32:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:33:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:33:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:33:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:33:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:34:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:34:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:34:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:34:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:35:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:35:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:35:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:35:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:36:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:36:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:36:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:36:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:37:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:37:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:37:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:37:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:38:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:38:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:38:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:38:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:39:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:39:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:39:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:39:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:40:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:40:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:40:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:40:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:41:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:41:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:41:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:41:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:42:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:42:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:42:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:42:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:43:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:43:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:43:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:43:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:44:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:44:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:44:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:44:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:45:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:45:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:45:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:45:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:46:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:46:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:46:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:46:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:47:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:47:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:47:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:47:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:48:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:48:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:48:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:48:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:49:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:49:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:49:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:49:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:50:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:50:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:50:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:50:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:51:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:51:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:51:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:51:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:52:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:52:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:52:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:52:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 16:53:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:53:34] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 16:53:34 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 16:53:34] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 18:58:09 flask_api __init__.py[70] create_app() INFO: Flask Rest Api startup
2025/04/12 18:58:09 werkzeug _internal.py[97] _log() INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025/04/12 18:58:09 werkzeug _internal.py[97] _log() INFO: [33mPress CTRL+C to quit[0m
2025/04/12 18:58:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 18:58:49] "OPTIONS /api/login HTTP/1.1" 200 -
2025/04/12 18:58:49 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 18:58:49] "POST /api/login HTTP/1.1" 200 -
2025/04/12 18:58:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 18:58:50] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 18:58:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 18:58:50] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 18:58:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 18:58:52] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 18:58:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 18:58:52] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 18:58:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 18:58:54] "OPTIONS /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/12 18:58:54 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 18:58:54] "GET /api/bikes?user_id=9 HTTP/1.1" 200 -
2025/04/12 19:01:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:01:45] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 19:01:45 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:01:45] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:01:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:01:46] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:01:46 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:01:46] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:04:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:04:47] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 19:04:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:04:47] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:04:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:04:47] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 19:04:47 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:04:47] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:08:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:10] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 19:08:10 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:10] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:08:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:11] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 19:08:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:11] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:08:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:11] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 19:08:11 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:11] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:08:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:14] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:08:14 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:14] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:08:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:16] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:08:16 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:16] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:08:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:24] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 19:08:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:24] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 19:08:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:24] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:08:24 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:24] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:08:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:35] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 19:08:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:35] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 19:08:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:35] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:08:35 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:35] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:08:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:38] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 19:08:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:38] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:08:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:38] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 19:08:38 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:38] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:08:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:42] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:08:42 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:08:42] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:37:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:20] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 19:37:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:20] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:37:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:20] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:37:20 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:20] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:37:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:22] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:37:22 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:22] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:37:23 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:23] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:37:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:26] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 19:37:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:26] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:37:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:26] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 19:37:26 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:26] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:37:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:30] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 19:37:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:30] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 19:37:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:30] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:37:30 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:30] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:37:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:31] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:37:31 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:31] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:37:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:41] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 19:37:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:41] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 19:37:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:41] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:37:41 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:41] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:37:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:50] "OPTIONS /api/bikes/11 HTTP/1.1" 200 -
2025/04/12 19:37:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:50] "PUT /api/bikes/11 HTTP/1.1" 200 -
2025/04/12 19:37:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:50] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:37:50 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:50] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:37:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:52] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 19:37:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:52] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:37:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:52] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 19:37:52 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:37:52] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:42:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:36] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:42:36 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:36] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:42:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:39] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 19:42:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:39] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:42:39 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:39] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:42:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:44] "OPTIONS /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 19:42:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:44] "PUT /api/bikes/10 HTTP/1.1" 200 -
2025/04/12 19:42:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:44] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:42:44 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:44] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:42:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:48] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 19:42:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:48] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:42:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:48] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 19:42:48 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:42:48] "GET /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:43:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:00] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 19:43:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:00] "OPTIONS /api/users/me HTTP/1.1" 200 -
2025/04/12 19:43:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:00] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:43:00 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:00] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:43:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:01] "OPTIONS /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:43:01 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:01] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:43:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:02] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:43:02 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:02] "GET /api/users/me HTTP/1.1" 200 -
2025/04/12 19:43:04 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:04] "GET /api/bikes?page=1&limit=10&user_id=9 HTTP/1.1" 200 -
2025/04/12 19:43:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:05] "OPTIONS /api/bikes HTTP/1.1" 200 -
2025/04/12 19:43:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:05] "OPTIONS /api/bikes/stats HTTP/1.1" 200 -
2025/04/12 19:43:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:05] "GET /api/bikes HTTP/1.1" 200 -
2025/04/12 19:43:05 werkzeug _internal.py[97] _log() INFO: 127.0.0.1 - - [12/Apr/2025 19:43:05] "GET /api/bikes/stats HTTP/1.1" 200 -
