"""
字段转换工具
用于统一前后端字段命名，减少不必要的字段映射和转换
"""
import logging

# 字段映射定义
FIELD_MAPPINGS = {
    # 车辆相关字段映射
    'bikes': {
        'frontend_to_backend': {
            'id': 'b_id',
            'bike_number': 'b_num',
            'user_id': 'belong_to',
            'type': 'b_type'
        },
        'backend_to_frontend': {
            'b_id': 'id',
            'b_num': 'bike_number',
            'belong_to': 'user_id',
            'b_type': 'type'
        }
    },
    # 用户相关字段映射
    'users': {
        'frontend_to_backend': {
            'id': 'u_id',
            'username': 'u_name',
            'password': 'u_pwd',
            'phone': 'u_phone',
            'email': 'u_email',
            'department': 'u_belong'
        },
        'backend_to_frontend': {
            'u_id': 'id',
            'u_name': 'username',
            'u_pwd': 'password',
            'u_phone': 'phone',
            'u_email': 'email',
            'u_belong': 'department'
        }
    },
    # 停车场相关字段映射
    'parking': {
        'frontend_to_backend': {
            'space_id': 'parking_space_id',
            'lot_id': 'parking_lot_id'
        },
        'backend_to_frontend': {
            'parking_space_id': 'space_id',
            'parking_lot_id': 'lot_id'
        }
    }
}

# 状态值映射
STATUS_MAPPINGS = {
    # 车辆状态映射
    'bikes': {
        'frontend_to_backend': {
            'available': '可用',
            'unavailable': '废弃',
            'normal': '可用',
            'disabled': '废弃'
        },
        'backend_to_frontend': {
            '可用': 'available',
            '废弃': 'unavailable'
        }
    },
    # 停车场状态映射
    'parking_lots': {
        'frontend_to_backend': {
            'active': 1,
            'inactive': 0
        },
        'backend_to_frontend': {
            1: 'active',
            0: 'inactive'
        }
    },
    # 车位状态映射
    'parking_spaces': {
        'frontend_to_backend': {
            'available': 0,
            'occupied': 1,
            'maintenance': 2
        },
        'backend_to_frontend': {
            0: 'available',
            1: 'occupied',
            2: 'maintenance'
        }
    },
    # 停车记录状态映射
    'parking_records': {
        'frontend_to_backend': {
            'active': 0,
            'completed': 1,
            'abnormal': 2
        },
        'backend_to_frontend': {
            0: 'active',
            1: 'completed',
            2: 'abnormal'
        }
    }
}

def convert_fields(data, entity_type, direction='frontend_to_backend'):
    """
    转换字段名称
    
    Args:
        data: 要转换的数据字典
        entity_type: 实体类型，如 'bikes', 'users', 'parking'
        direction: 转换方向，'frontend_to_backend' 或 'backend_to_frontend'
        
    Returns:
        转换后的数据字典
    """
    if not data or not isinstance(data, dict):
        return data
        
    if entity_type not in FIELD_MAPPINGS:
        logging.warning(f"未找到实体类型 '{entity_type}' 的字段映射")
        return data
        
    result = data.copy()
    mapping = FIELD_MAPPINGS[entity_type][direction]
    
    for source_field, target_field in mapping.items():
        if source_field in result:
            result[target_field] = result.pop(source_field)
            
    return result

def convert_status(status_value, entity_type, direction='frontend_to_backend'):
    """
    转换状态值
    
    Args:
        status_value: 要转换的状态值
        entity_type: 实体类型，如 'bikes', 'parking_lots', 'parking_spaces', 'parking_records'
        direction: 转换方向，'frontend_to_backend' 或 'backend_to_frontend'
        
    Returns:
        转换后的状态值，如果没有找到映射则返回原值
    """
    if entity_type not in STATUS_MAPPINGS:
        logging.warning(f"未找到实体类型 '{entity_type}' 的状态映射")
        return status_value
        
    mapping = STATUS_MAPPINGS[entity_type][direction]
    
    return mapping.get(status_value, status_value)

def convert_list(data_list, entity_type, direction='frontend_to_backend'):
    """
    转换列表中的所有项
    
    Args:
        data_list: 要转换的数据列表
        entity_type: 实体类型
        direction: 转换方向
        
    Returns:
        转换后的数据列表
    """
    if not data_list or not isinstance(data_list, list):
        return data_list
        
    return [convert_fields(item, entity_type, direction) for item in data_list]

def standardize_response(response_data, entity_type=None):
    """
    标准化API响应数据
    
    Args:
        response_data: API响应数据
        entity_type: 实体类型，如果为None则不进行字段转换
        
    Returns:
        标准化后的响应数据
    """
    # 确保响应数据格式正确
    if not isinstance(response_data, dict):
        return {'data': response_data, 'message': '操作成功', 'status': 'success'}
        
    # 如果已经是标准格式，直接返回
    if 'data' in response_data and 'message' in response_data and 'status' in response_data:
        # 如果需要转换字段，只转换data部分
        if entity_type and 'data' in response_data and response_data['data']:
            if isinstance(response_data['data'], dict):
                response_data['data'] = convert_fields(response_data['data'], entity_type, 'backend_to_frontend')
            elif isinstance(response_data['data'], list):
                response_data['data'] = convert_list(response_data['data'], entity_type, 'backend_to_frontend')
        return response_data
        
    # 构建标准响应格式
    standard_response = {
        'data': response_data.get('data', response_data),
        'message': response_data.get('message', '操作成功'),
        'status': response_data.get('status', 'success')
    }
    
    # 如果需要转换字段
    if entity_type and 'data' in standard_response and standard_response['data']:
        if isinstance(standard_response['data'], dict):
            standard_response['data'] = convert_fields(standard_response['data'], entity_type, 'backend_to_frontend')
        elif isinstance(standard_response['data'], list):
            standard_response['data'] = convert_list(standard_response['data'], entity_type, 'backend_to_frontend')
            
    return standard_response
