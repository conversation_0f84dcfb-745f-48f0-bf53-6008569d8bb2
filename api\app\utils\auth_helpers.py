from flask_jwt_extended import get_jwt_identity, get_jwt
from flask import current_app, g
from app.utils.password_utils import verify_password, hash_password, get_salt_if_none

# 用于存储获取当前用户信息的函数
# 这些函数将在应用实例化后被设置
_get_player_func = None
_get_user_func = None
_is_admin_func = None

def set_auth_helper_functions(get_player_func, get_user_func, is_admin_func):
    """设置认证辅助函数引用，避免循环导入"""
    global _get_player_func, _get_user_func, _is_admin_func
    _get_player_func = get_player_func
    _get_user_func = get_user_func
    _is_admin_func = is_admin_func

def get_current_player_id():
    """获取当前登录的用户ID (从JWT令牌)"""
    try:
        identity = get_jwt_identity()
        current_app.logger.debug(f"当前JWT身份: {identity}")
        return identity
    except Exception as e:
        current_app.logger.error(f"获取JWT身份时出错: {str(e)}")
        return None

def get_current_player():
    """获取当前登录的Players对象"""
    try:
        player_id = get_current_player_id()
        if not player_id:
            return None

        if _get_player_func:
            player = _get_player_func(player_id)
            current_app.logger.debug(f"获取到玩家: {player}")
            return player
    except Exception as e:
        current_app.logger.error(f"获取当前玩家时出错: {str(e)}")
    return None

def get_current_user_id():
    """
    从JWT令牌中获取当前用户ID
    """
    try:
        # 从JWT令牌中获取标识符
        identity = get_jwt_identity()
        if identity is None:
            print("JWT令牌中没有身份信息")
            return None

        # 尝试将身份转换为整数（用户ID）
        try:
            user_id = int(identity)
            return user_id
        except (ValueError, TypeError):
            print(f"无法将JWT身份转换为用户ID: {identity}")
            return None
    except Exception as e:
        print(f"获取当前用户ID时出错: {str(e)}")
        return None

def get_current_user():
    """获取当前登录用户的Users对象"""
    try:
        from app.users.models import Users

        # 从JWT令牌中获取用户ID
        user_id = get_current_user_id()
        if not user_id:
            return None

        # 直接通过ID查询用户
        user = Users.query.get(user_id)
        return user
    except Exception as e:
        from flask import current_app
        if current_app:
            current_app.logger.error(f"获取当前用户时出错: {str(e)}")
    return None

def is_admin():
    """检查当前用户是否为管理员"""
    try:
        from app.users.models import Users
        from flask_jwt_extended import get_jwt

        # 从JWT令牌中获取用户角色
        jwt_claims = get_jwt()
        user_role = jwt_claims.get('role', 'user')

        # 如果JWT令牌中有角色信息，直接使用
        if user_role == 'admin':
            return True

        # 否则，尝试从数据库中获取用户信息
        user_id = get_current_user_id()
        if user_id:
            user = Users.query.get(user_id)
            if user and hasattr(user, 'u_role'):
                return user.u_role == 'admin'
    except Exception as e:
        from flask import current_app
        if current_app:
            current_app.logger.error(f"检查管理员权限时出错: {str(e)}")
    return False

