<template>
  <div class="app-container">
    <div class="page-header">
      <el-button icon="el-icon-back" @click="goBack">返回</el-button>
    </div>
    
    <div v-loading="loading">
      <violation-detail
        v-if="!loading && violationDetail.id"
        :violation-detail="violationDetail"
        :appeals="appeals"
        :evidences="evidences"
        :can-edit="true"
        :can-upload="true"
        :can-appeal="false"
        :can-handle-appeal="true"
        :process-violation-method="processViolation"
        :process-appeal-method="processAppeal"
        @edit="handleEdit"
        @refresh="fetchData"
      />
    </div>
    
    <!-- 编辑违规记录对话框 -->
    <el-dialog title="编辑违规记录" :visible.sync="editDialogVisible" width="600px">
      <violation-form
        v-if="editDialogVisible"
        :is-edit="true"
        :initial-data="violationDetail"
        :submit-method="updateViolation"
        @success="handleEditSuccess"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getViolationDetail, handleViolation, handleAppeal } from '@/api/violations'
import ViolationDetail from '../components/ViolationDetail'
import ViolationForm from '../components/ViolationForm'

export default {
  name: 'AdminViolationDetail',
  components: {
    ViolationDetail,
    ViolationForm
  },
  data() {
    return {
      loading: true,
      violationDetail: {},
      appeals: [],
      evidences: [],
      editDialogVisible: false
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      const id = this.$route.params.id
      
      getViolationDetail(id)
        .then(response => {
          this.violationDetail = response.data.violation
          this.appeals = response.data.appeals
          this.evidences = response.data.evidences
          this.loading = false
        })
        .catch(error => {
          console.error(error)
          this.$message.error('获取违规详情失败')
          this.loading = false
        })
    },
    processViolation(id, data) {
      return handleViolation(id, data)
    },
    processAppeal(id, data) {
      return handleAppeal(id, data)
    },
    handleEdit() {
      this.editDialogVisible = true
    },
    updateViolation(data) {
      return handleViolation(this.violationDetail.id, data)
    },
    handleEditSuccess() {
      this.editDialogVisible = false
      this.fetchData()
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
  }
}
</style>
