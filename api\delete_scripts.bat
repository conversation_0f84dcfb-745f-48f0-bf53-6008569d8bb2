@echo off
echo 正在删除不需要的测试脚本...

REM 删除测试脚本
del direct_test.py
del minimal_test.py
del simple_test.py
del test_admin_parking.py
del test_admin_parking_complete.py
del test_admin_parking_simple.py
del test_admin_resources.py
del test_api.py
del test_api_response.py
del test_bike_permissions.py
del test_frontend_auth.py
del test_login.py
del test_login_admin.py
del test_login_final.py
del test_parking_functionality.py
del test_parking_records_api.py

REM 删除检查脚本
del check_active_parking_records.py
del check_admin.py
del check_admin_parking_records.py
del check_admin_parking_records_fixed.py
del check_admin_user.py
del check_admin_vehicles.py
del check_admin_vehicles_status.py
del check_bikes_table.py
del check_current_db.py
del check_db.py
del check_db_path.py
del check_db_schema.py
del check_db_structure.py
del check_jwt_auth.py
del check_jwt_headers.py
del check_login_db.py
del check_login_process.py
del check_parking_consistency.py
del check_parking_lots.py
del check_server_logs.py
del check_server_logs_simple.py
del check_spaces.py
del check_table_structure.py
del check_users.py
del check_user_records.py
del check_user_role.py

REM 删除修复脚本
del fix_admin2_password.py
del fix_admin_login.py
del fix_admin_user.py
del fix_admin_vehicles.py
del fix_admin_vehicles_again.py
del fix_admin_vehicles_final.py
del fix_admin_vehicles_refresh.py
del fix_admin_vehicle_ownership.py
del fix_admin_vehicle_plates.py
del fix_and_create_parking_records.py
del fix_and_create_parking_records_fixed.py
del fix_bikes_api.py
del fix_database.py
del fix_parking_data.py
del fix_parking_records.py
del fix_parking_records_data.py
del fix_superadmin.py
del fix_superadmin_final.py
del fix_sys_db.py
del fix_users_table.py
del fix_user_passwords.py

REM 删除其他调试脚本
del debug_bikes_api.py
del direct_curl.py
del end_all_parking_records.py
del reset_admin.py
del reset_migrations.py
del reset_spaces.py

echo 删除完成！
